{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/app-call-server.ts"], "sourcesContent": ["import { startTransition } from 'react'\nimport { ACTION_SERVER_ACTION } from './components/router-reducer/router-reducer-types'\nimport { dispatchAppRouterAction } from './components/use-action-queue'\n\nexport async function callServer(actionId: string, actionArgs: any[]) {\n  return new Promise((resolve, reject) => {\n    startTransition(() => {\n      dispatchAppRouterAction({\n        type: ACTION_SERVER_ACTION,\n        actionId,\n        actionArgs,\n        resolve,\n        reject,\n      })\n    })\n  })\n}\n"], "names": ["startTransition", "ACTION_SERVER_ACTION", "dispatchAppRouterAction", "callServer", "actionId", "actionArgs", "Promise", "resolve", "reject", "type"], "mappings": ";;;AAAA,SAASA,eAAe,QAAQ,QAAO;AACvC,SAASC,oBAAoB,QAAQ,mDAAkD;AACvF,SAASC,uBAAuB,QAAQ,gCAA+B;;;;AAEhE,eAAeC,WAAWC,QAAgB,EAAEC,UAAiB;IAClE,OAAO,IAAIC,QAAQ,CAACC,SAASC;+KAC3BR,kBAAAA,EAAgB;8MACdE,0BAAAA,EAAwB;gBACtBO,6NAAMR,uBAAAA;gBACNG;gBACAC;gBACAE;gBACAC;YACF;QACF;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/app-find-source-map-url.ts"], "sourcesContent": ["const basePath = process.env.__NEXT_ROUTER_BASEPATH || ''\nconst pathname = `${basePath}/__nextjs_source-map`\n\nexport const findSourceMapURL =\n  process.env.NODE_ENV === 'development'\n    ? function findSourceMapURL(filename: string): string | null {\n        if (filename === '') {\n          return null\n        }\n\n        if (\n          filename.startsWith(document.location.origin) &&\n          filename.includes('/_next/static')\n        ) {\n          // This is a request for a client chunk. This can only happen when\n          // using Turbopack. In this case, since we control how those source\n          // maps are generated, we can safely assume that the sourceMappingURL\n          // is relative to the filename, with an added `.map` extension. The\n          // browser can just request this file, and it gets served through the\n          // normal dev server, without the need to route this through\n          // the `/__nextjs_source-map` dev middleware.\n          return `${filename}.map`\n        }\n\n        const url = new URL(pathname, document.location.origin)\n        url.searchParams.set('filename', filename)\n\n        return url.href\n      }\n    : undefined\n"], "names": ["basePath", "process", "env", "__NEXT_ROUTER_BASEPATH", "pathname", "findSourceMapURL", "NODE_ENV", "filename", "startsWith", "document", "location", "origin", "includes", "url", "URL", "searchParams", "set", "href", "undefined"], "mappings": ";;;AAAA,MAAMA,WAAWC,QAAQC,GAAG,CAACC,sBAAsB,MAAI;AACvD,MAAMC,WAAY,KAAEJ,WAAS;AAEtB,MAAMK,mBACXJ,QAAQC,GAAG,CAACI,QAAQ,KAAK,cACrB,SAASD,iBAAiBE,QAAgB;IACxC,IAAIA,aAAa,IAAI;QACnB,OAAO;IACT;IAEA,IACEA,SAASC,UAAU,CAACC,SAASC,QAAQ,CAACC,MAAM,KAC5CJ,SAASK,QAAQ,CAAC,kBAClB;QACA,kEAAkE;QAClE,mEAAmE;QACnE,qEAAqE;QACrE,mEAAmE;QACnE,qEAAqE;QACrE,4DAA4D;QAC5D,6CAA6C;QAC7C,OAAQ,KAAEL,WAAS;IACrB;IAEA,MAAMM,MAAM,IAAIC,IAAIV,UAAUK,SAASC,QAAQ,CAACC,MAAM;IACtDE,IAAIE,YAAY,CAACC,GAAG,CAAC,YAAYT;IAEjC,OAAOM,IAAII,IAAI;AACjB,IACAC,UAAS", "ignoreList": [0]}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/flight-data-helpers.ts"], "sourcesContent": ["import type {\n  CacheNodeSeedData,\n  FlightData,\n  FlightDataPath,\n  FlightRouterState,\n  FlightSegmentPath,\n  Segment,\n} from '../server/app-render/types'\nimport type { HeadData } from '../shared/lib/app-router-context.shared-runtime'\n\nexport type NormalizedFlightData = {\n  /**\n   * The full `FlightSegmentPath` inclusive of the final `Segment`\n   */\n  segmentPath: FlightSegmentPath\n  /**\n   * The `FlightSegmentPath` exclusive of the final `Segment`\n   */\n  pathToSegment: FlightSegmentPath\n  segment: Segment\n  tree: FlightRouterState\n  seedData: CacheNodeSeedData | null\n  head: HeadData\n  isHeadPartial: boolean\n  isRootRender: boolean\n}\n\n// TODO: We should only have to export `normalizeFlightData`, however because the initial flight data\n// that gets passed to `createInitialRouterState` doesn't conform to the `FlightDataPath` type (it's missing the root segment)\n// we're currently exporting it so we can use it directly. This should be fixed as part of the unification of\n// the different ways we express `FlightSegmentPath`.\nexport function getFlightDataPartsFromPath(\n  flightDataPath: FlightDataPath\n): NormalizedFlightData {\n  // Pick the last 4 items from the `FlightDataPath` to get the [tree, seedData, viewport, isHeadPartial].\n  const flightDataPathLength = 4\n  // tree, seedData, and head are *always* the last three items in the `FlightDataPath`.\n  const [tree, seedData, head, isHeadPartial] =\n    flightDataPath.slice(-flightDataPathLength)\n  // The `FlightSegmentPath` is everything except the last three items. For a root render, it won't be present.\n  const segmentPath = flightDataPath.slice(0, -flightDataPathLength)\n\n  return {\n    // TODO: Unify these two segment path helpers. We are inconsistently pushing an empty segment (\"\")\n    // to the start of the segment path in some places which makes it hard to use solely the segment path.\n    // Look for \"// TODO-APP: remove ''\" in the codebase.\n    pathToSegment: segmentPath.slice(0, -1),\n    segmentPath,\n    // if the `FlightDataPath` corresponds with the root, there'll be no segment path,\n    // in which case we default to ''.\n    segment: segmentPath[segmentPath.length - 1] ?? '',\n    tree,\n    seedData,\n    head,\n    isHeadPartial,\n    isRootRender: flightDataPath.length === flightDataPathLength,\n  }\n}\n\nexport function getNextFlightSegmentPath(\n  flightSegmentPath: FlightSegmentPath\n): FlightSegmentPath {\n  // Since `FlightSegmentPath` is a repeated tuple of `Segment` and `ParallelRouteKey`, we slice off two items\n  // to get the next segment path.\n  return flightSegmentPath.slice(2)\n}\n\nexport function normalizeFlightData(\n  flightData: FlightData\n): NormalizedFlightData[] | string {\n  // FlightData can be a string when the server didn't respond with a proper flight response,\n  // or when a redirect happens, to signal to the client that it needs to perform an MPA navigation.\n  if (typeof flightData === 'string') {\n    return flightData\n  }\n\n  return flightData.map(getFlightDataPartsFromPath)\n}\n"], "names": ["getFlightDataPartsFromPath", "flightDataPath", "flightDataPathLength", "tree", "seedData", "head", "isHeadPartial", "slice", "segmentPath", "pathToSegment", "segment", "length", "isRootRender", "getNextFlightSegmentPath", "flightSegmentPath", "normalizeFlightData", "flightData", "map"], "mappings": "AA2BA,qGAAqG;AACrG,8HAA8H;AAC9H,6GAA6G;AAC7G,qDAAqD;;;;;;AAC9C,SAASA,2BACdC,cAA8B;IAE9B,wGAAwG;IACxG,MAAMC,uBAAuB;IAC7B,sFAAsF;IACtF,MAAM,CAACC,MAAMC,UAAUC,MAAMC,cAAc,GACzCL,eAAeM,KAAK,CAAC,CAACL;IACxB,6GAA6G;IAC7G,MAAMM,cAAcP,eAAeM,KAAK,CAAC,GAAG,CAACL;QAUlCM;IARX,OAAO;QACL,kGAAkG;QAClG,sGAAsG;QACtG,qDAAqD;QACrDC,eAAeD,YAAYD,KAAK,CAAC,GAAG,CAAC;QACrCC;QACA,kFAAkF;QAClF,kCAAkC;QAClCE,SAASF,CAAAA,gBAAAA,WAAW,CAACA,YAAYG,MAAM,GAAG,EAAE,KAAA,OAAnCH,gBAAuC;QAChDL;QACAC;QACAC;QACAC;QACAM,cAAcX,eAAeU,MAAM,KAAKT;IAC1C;AACF;AAEO,SAASW,yBACdC,iBAAoC;IAEpC,4GAA4G;IAC5G,gCAAgC;IAChC,OAAOA,kBAAkBP,KAAK,CAAC;AACjC;AAEO,SAASQ,oBACdC,UAAsB;IAEtB,2FAA2F;IAC3F,kGAAkG;IAClG,IAAI,OAAOA,eAAe,UAAU;QAClC,OAAOA;IACT;IAEA,OAAOA,WAAWC,GAAG,CAACjB;AACxB", "ignoreList": [0]}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/app-build-id.ts"], "sourcesContent": ["// This gets assigned as a side-effect during app initialization. Because it\n// represents the build used to create the JS bundle, it should never change\n// after being set, so we store it in a global variable.\n//\n// When performing RSC requests, if the incoming data has a different build ID,\n// we perform an MPA navigation/refresh to load the updated build and ensure\n// that the client and server in sync.\n\n// Starts as an empty string. In practice, because setAppBuildId is called\n// during initialization before hydration starts, this will always get\n// reassigned to the actual build ID before it's ever needed by a navigation.\n// If for some reasons it didn't, due to a bug or race condition, then on\n// navigation the build comparision would fail and trigger an MPA navigation.\nlet globalBuildId: string = ''\n\nexport function setAppBuildId(buildId: string) {\n  globalBuildId = buildId\n}\n\nexport function getAppBuildId(): string {\n  return globalBuildId\n}\n"], "names": ["globalBuildId", "setAppBuildId", "buildId", "getAppBuildId"], "mappings": "AAAA,4EAA4E;AAC5E,4EAA4E;AAC5E,wDAAwD;AACxD,EAAE;AACF,+EAA+E;AAC/E,4EAA4E;AAC5E,sCAAsC;AAEtC,0EAA0E;AAC1E,sEAAsE;AACtE,6EAA6E;AAC7E,yEAAyE;AACzE,6EAA6E;;;;;AAC7E,IAAIA,gBAAwB;AAErB,SAASC,cAAcC,OAAe;IAC3CF,gBAAgBE;AAClB;AAEO,SAASC;IACd,OAAOH;AACT", "ignoreList": [0]}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/request/search-params.browser.dev.ts"], "sourcesContent": ["import type { SearchParams } from '../../server/request/search-params'\n\nimport { ReflectAdapter } from '../../server/web/spec-extension/adapters/reflect'\nimport {\n  describeStringPropertyAccess,\n  describeHasCheckingStringProperty,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\n\ninterface CacheLifetime {}\nconst CachedSearchParams = new WeakMap<CacheLifetime, Promise<SearchParams>>()\n\nexport function makeUntrackedExoticSearchParamsWithDevWarnings(\n  underlyingSearchParams: SearchParams\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  const promise = Promise.resolve(underlyingSearchParams)\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n      ;(promise as any)[prop] = underlyingSearchParams[prop]\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          warnForSyncAccess(expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return Reflect.set(target, prop, value, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          warnForSyncAccess(expression)\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      warnForSyncSpread()\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedSearchParams.set(underlyingSearchParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction warnForSyncAccess(expression: string) {\n  console.error(\n    `A searchParam property was accessed directly with ${expression}. ` +\n      `\\`searchParams\\` should be unwrapped with \\`React.use()\\` before accessing its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction warnForSyncSpread() {\n  console.error(\n    `The keys of \\`searchParams\\` were accessed directly. ` +\n      `\\`searchParams\\` should be unwrapped with \\`React.use()\\` before accessing its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n"], "names": ["ReflectAdapter", "describeStringPropertyAccess", "describeHasCheckingStringProperty", "wellKnownProperties", "CachedSearchParams", "WeakMap", "makeUntrackedExoticSearchParamsWithDevWarnings", "underlyingSearchParams", "cachedSearchParams", "get", "proxiedProperties", "Set", "unproxiedProperties", "promise", "Promise", "resolve", "Object", "keys", "for<PERSON>ach", "prop", "has", "push", "add", "proxiedPromise", "Proxy", "target", "receiver", "Reflect", "expression", "warnForSyncAccess", "set", "value", "delete", "ownKeys", "warnForSyncSpread", "console", "error"], "mappings": ";;;AAEA,SAASA,cAAc,QAAQ,mDAAkD;AACjF,SACEC,4BAA4B,EAC5BC,iCAAiC,EACjCC,mBAAmB,QACd,uCAAsC;;;AAG7C,MAAMC,qBAAqB,IAAIC;AAExB,SAASC,+CACdC,sBAAoC;IAEpC,MAAMC,qBAAqBJ,mBAAmBK,GAAG,CAACF;IAClD,IAAIC,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7C,MAAMC,UAAUC,QAAQC,OAAO,CAACR;IAEhCS,OAAOC,IAAI,CAACV,wBAAwBW,OAAO,CAAC,CAACC;QAC3C,8LAAIhB,sBAAAA,CAAoBiB,GAAG,CAACD,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClEP,oBAAoBS,IAAI,CAACF;QAC3B,OAAO;YACLT,kBAAkBY,GAAG,CAACH;YACpBN,OAAe,CAACM,KAAK,GAAGZ,sBAAsB,CAACY,KAAK;QACxD;IACF;IAEA,MAAMI,iBAAiB,IAAIC,MAAMX,SAAS;QACxCJ,KAAIgB,MAAM,EAAEN,IAAI,EAAEO,QAAQ;YACxB,IAAI,OAAOP,SAAS,UAAU;gBAC5B,IACE,2LAAChB,sBAAAA,CAAoBiB,GAAG,CAACD,SACxBT,CAAAA,kBAAkBU,GAAG,CAACD,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BQ,QAAQP,GAAG,CAACK,QAAQN,UAAU,KAAI,GACpC;oBACA,MAAMS,cAAa3B,4NAAAA,EAA6B,gBAAgBkB;oBAChEU,kBAAkBD;gBACpB;YACF;YACA,gNAAO5B,iBAAAA,CAAeS,GAAG,CAACgB,QAAQN,MAAMO;QAC1C;QACAI,KAAIL,MAAM,EAAEN,IAAI,EAAEY,KAAK,EAAEL,QAAQ;YAC/B,IAAI,OAAOP,SAAS,UAAU;gBAC5BT,kBAAkBsB,MAAM,CAACb;YAC3B;YACA,OAAOQ,QAAQG,GAAG,CAACL,QAAQN,MAAMY,OAAOL;QAC1C;QACAN,KAAIK,MAAM,EAAEN,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IACE,2LAAChB,sBAAAA,CAAoBiB,GAAG,CAACD,SACxBT,CAAAA,kBAAkBU,GAAG,CAACD,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BQ,QAAQP,GAAG,CAACK,QAAQN,UAAU,KAAI,GACpC;oBACA,MAAMS,cAAa1B,iOAAAA,EACjB,gBACAiB;oBAEFU,kBAAkBD;gBACpB;YACF;YACA,OAAOD,QAAQP,GAAG,CAACK,QAAQN;QAC7B;QACAc,SAAQR,MAAM;YACZS;YACA,OAAOP,QAAQM,OAAO,CAACR;QACzB;IACF;IAEArB,mBAAmB0B,GAAG,CAACvB,wBAAwBgB;IAC/C,OAAOA;AACT;AAEA,SAASM,kBAAkBD,UAAkB;IAC3CO,QAAQC,KAAK,CACV,uDAAoDR,aAAW,OAC7D,4FACA;AAEP;AAEA,SAASM;IACPC,QAAQC,KAAK,CACV,wDACE,4FACA;AAEP", "ignoreList": [0]}}, {"offset": {"line": 220, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/request/search-params.browser.ts"], "sourcesContent": ["export const createRenderSearchParamsFromClient =\n  process.env.NODE_ENV === 'development'\n    ? (\n        require('./search-params.browser.dev') as typeof import('./search-params.browser.dev')\n      ).makeUntrackedExoticSearchParamsWithDevWarnings\n    : (\n        require('./search-params.browser.prod') as typeof import('./search-params.browser.prod')\n      ).makeUntrackedExoticSearchParams\n"], "names": ["createRenderSearchParamsFromClient", "process", "env", "NODE_ENV", "require", "makeUntrackedExoticSearchParamsWithDevWarnings", "makeUntrackedExoticSearchParams"], "mappings": ";;;AAAO,MAAMA,qCACXC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAEnBC,QAAQ,gIACRC,8CAA8C,GAChD,AACED,QAAQ,gCACRE,+BAA+B,CAAA", "ignoreList": [0]}}, {"offset": {"line": 230, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/request/params.browser.dev.ts"], "sourcesContent": ["import type { Params } from '../../server/request/params'\n\nimport { ReflectAdapter } from '../../server/web/spec-extension/adapters/reflect'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport {\n  describeStringPropertyAccess,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\n\ninterface CacheLifetime {}\nconst CachedParams = new WeakMap<CacheLifetime, Promise<Params>>()\n\nexport function makeDynamicallyTrackedExoticParamsWithDevWarnings(\n  underlyingParams: Params\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingParams)\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      proxiedProperties.add(prop)\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          // We are accessing a property that was proxied to the promise instance\n          proxiedProperties.has(prop)\n        ) {\n          const expression = describeStringPropertyAccess('params', prop)\n          warnForSyncAccess(expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return ReflectAdapter.set(target, prop, value, receiver)\n    },\n    ownKeys(target) {\n      warnForEnumeration(unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedParams.set(underlyingParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction warnForSyncAccess(expression: string) {\n  console.error(\n    `A param property was accessed directly with ${expression}. \\`params\\` is now a Promise and should be unwrapped with \\`React.use()\\` before accessing properties of the underlying params object. In this version of Next.js direct access to param properties is still supported to facilitate migration but in a future version you will be required to unwrap \\`params\\` with \\`React.use()\\`.`\n  )\n}\n\nfunction warnForEnumeration(missingProperties: Array<string>) {\n  if (missingProperties.length) {\n    const describedMissingProperties =\n      describeListOfPropertyNames(missingProperties)\n    console.error(\n      `params are being enumerated incompletely missing these properties: ${describedMissingProperties}. ` +\n        `\\`params\\` should be unwrapped with \\`React.use()\\` before using its value. ` +\n        `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n    )\n  } else {\n    console.error(\n      `params are being enumerated. ` +\n        `\\`params\\` should be unwrapped with \\`React.use()\\` before using its value. ` +\n        `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n    )\n  }\n}\n\nfunction describeListOfPropertyNames(properties: Array<string>) {\n  switch (properties.length) {\n    case 0:\n      throw new InvariantError(\n        'Expected describeListOfPropertyNames to be called with a non-empty list of strings.'\n      )\n    case 1:\n      return `\\`${properties[0]}\\``\n    case 2:\n      return `\\`${properties[0]}\\` and \\`${properties[1]}\\``\n    default: {\n      let description = ''\n      for (let i = 0; i < properties.length - 1; i++) {\n        description += `\\`${properties[i]}\\`, `\n      }\n      description += `, and \\`${properties[properties.length - 1]}\\``\n      return description\n    }\n  }\n}\n"], "names": ["ReflectAdapter", "InvariantError", "describeStringPropertyAccess", "wellKnownProperties", "C<PERSON>d<PERSON><PERSON><PERSON>", "WeakMap", "makeDynamicallyTrackedExoticParamsWithDevWarnings", "underlyingParams", "cachedParams", "get", "promise", "Promise", "resolve", "proxiedProperties", "Set", "unproxiedProperties", "Object", "keys", "for<PERSON>ach", "prop", "has", "add", "proxiedPromise", "Proxy", "target", "receiver", "expression", "warnForSyncAccess", "set", "value", "delete", "ownKeys", "warnForEnumeration", "Reflect", "console", "error", "missingProperties", "length", "describedMissingProperties", "describeListOfPropertyNames", "properties", "description", "i"], "mappings": ";;;AAEA,SAASA,cAAc,QAAQ,mDAAkD;AACjF,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SACEC,4BAA4B,EAC5BC,mBAAmB,QACd,uCAAsC;;;;AAG7C,MAAMC,eAAe,IAAIC;AAElB,SAASC,kDACdC,gBAAwB;IAExB,MAAMC,eAAeJ,aAAaK,GAAG,CAACF;IACtC,IAAIC,cAAc;QAChB,OAAOA;IACT;IAEA,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAME,UAAUC,QAAQC,OAAO,CAACL;IAEhC,MAAMM,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7CC,OAAOC,IAAI,CAACV,kBAAkBW,OAAO,CAAC,CAACC;QACrC,IAAIhB,gNAAAA,CAAoBiB,GAAG,CAACD,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACLN,kBAAkBQ,GAAG,CAACF;YACpBT,OAAe,CAACS,KAAK,GAAGZ,gBAAgB,CAACY,KAAK;QAClD;IACF;IAEA,MAAMG,iBAAiB,IAAIC,MAAMb,SAAS;QACxCD,KAAIe,MAAM,EAAEL,IAAI,EAAEM,QAAQ;YACxB,IAAI,OAAON,SAAS,UAAU;gBAC5B,IACE,AACAN,kBAAkBO,GAAG,CAACD,OACtB,0CAFuE;oBAGvE,MAAMO,2MAAaxB,+BAAAA,EAA6B,UAAUiB;oBAC1DQ,kBAAkBD;gBACpB;YACF;YACA,gNAAO1B,iBAAAA,CAAeS,GAAG,CAACe,QAAQL,MAAMM;QAC1C;QACAG,KAAIJ,MAAM,EAAEL,IAAI,EAAEU,KAAK,EAAEJ,QAAQ;YAC/B,IAAI,OAAON,SAAS,UAAU;gBAC5BN,kBAAkBiB,MAAM,CAACX;YAC3B;YACA,gNAAOnB,iBAAAA,CAAe4B,GAAG,CAACJ,QAAQL,MAAMU,OAAOJ;QACjD;QACAM,SAAQP,MAAM;YACZQ,mBAAmBjB;YACnB,OAAOkB,QAAQF,OAAO,CAACP;QACzB;IACF;IAEApB,aAAawB,GAAG,CAACrB,kBAAkBe;IACnC,OAAOA;AACT;AAEA,SAASK,kBAAkBD,UAAkB;IAC3CQ,QAAQC,KAAK,CACV,iDAA8CT,aAAW;AAE9D;AAEA,SAASM,mBAAmBI,iBAAgC;IAC1D,IAAIA,kBAAkBC,MAAM,EAAE;QAC5B,MAAMC,6BACJC,4BAA4BH;QAC9BF,QAAQC,KAAK,CACV,wEAAqEG,6BAA2B,OAC9F,6EACA;IAEP,OAAO;QACLJ,QAAQC,KAAK,CACV,kCACE,6EACA;IAEP;AACF;AAEA,SAASI,4BAA4BC,UAAyB;IAC5D,OAAQA,WAAWH,MAAM;QACvB,KAAK;YACH,MAAM,OAAA,cAEL,CAFK,uLAAIpC,iBAAAA,CACR,wFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,KAAK;YACH,OAAQ,MAAIuC,UAAU,CAAC,EAAE,GAAC;QAC5B,KAAK;YACH,OAAQ,MAAIA,UAAU,CAAC,EAAE,GAAC,YAAWA,UAAU,CAAC,EAAE,GAAC;QACrD;YAAS;gBACP,IAAIC,cAAc;gBAClB,IAAK,IAAIC,IAAI,GAAGA,IAAIF,WAAWH,MAAM,GAAG,GAAGK,IAAK;oBAC9CD,eAAgB,MAAID,UAAU,CAACE,EAAE,GAAC;gBACpC;gBACAD,eAAgB,YAAUD,UAAU,CAACA,WAAWH,MAAM,GAAG,EAAE,GAAC;gBAC5D,OAAOI;YACT;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 324, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/request/params.browser.ts"], "sourcesContent": ["export const createRenderParamsFromClient =\n  process.env.NODE_ENV === 'development'\n    ? (require('./params.browser.dev') as typeof import('./params.browser.dev'))\n        .makeDynamicallyTrackedExoticParamsWithDevWarnings\n    : (\n        require('./params.browser.prod') as typeof import('./params.browser.prod')\n      ).makeUntrackedExoticParams\n"], "names": ["createRenderParamsFromClient", "process", "env", "NODE_ENV", "require", "makeDynamicallyTrackedExoticParamsWithDevWarnings", "makeUntrackedExoticParams"], "mappings": ";;;AAAO,MAAMA,+BACXC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cACpBC,QAAQ,yHACNC,iDAAiD,GACpD,AACED,QAAQ,yBACRE,yBAAyB,CAAA", "ignoreList": [0]}}, {"offset": {"line": 334, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/normalize-trailing-slash.ts"], "sourcesContent": ["import { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { parsePath } from '../shared/lib/router/utils/parse-path'\n\n/**\n * Normalizes the trailing slash of a path according to the `trailingSlash` option\n * in `next.config.js`.\n */\nexport const normalizePathTrailingSlash = (path: string) => {\n  if (!path.startsWith('/') || process.env.__NEXT_MANUAL_TRAILING_SLASH) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  if (process.env.__NEXT_TRAILING_SLASH) {\n    if (/\\.[^/]+\\/?$/.test(pathname)) {\n      return `${removeTrailingSlash(pathname)}${query}${hash}`\n    } else if (pathname.endsWith('/')) {\n      return `${pathname}${query}${hash}`\n    } else {\n      return `${pathname}/${query}${hash}`\n    }\n  }\n\n  return `${removeTrailingSlash(pathname)}${query}${hash}`\n}\n"], "names": ["removeTrailingSlash", "parsePath", "normalizePathTrailingSlash", "path", "startsWith", "process", "env", "__NEXT_MANUAL_TRAILING_SLASH", "pathname", "query", "hash", "__NEXT_TRAILING_SLASH", "test", "endsWith"], "mappings": ";;;AAAA,SAASA,mBAAmB,QAAQ,mDAAkD;AACtF,SAASC,SAAS,QAAQ,wCAAuC;;;AAM1D,MAAMC,6BAA6B,CAACC;IACzC,IAAI,CAACA,KAAKC,UAAU,CAAC,QAAQC,QAAQC,GAAG,CAACC,4BAA4B,EAAE;QACrE,OAAOJ;IACT;IAEA,MAAM,EAAEK,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE,wMAAGT,YAAAA,EAAUE;IAC5C,IAAIE,QAAQC,GAAG,CAACK,qBAAqB,EAAE;;IAQvC;IAEA,OAAQ,wNAAEX,sBAAAA,EAAoBQ,YAAYC,QAAQC;AACpD,EAAC", "ignoreList": [0]}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/add-base-path.ts"], "sourcesContent": ["import { addPathPrefix } from '../shared/lib/router/utils/add-path-prefix'\nimport { normalizePathTrailingSlash } from './normalize-trailing-slash'\n\nconst basePath = (process.env.__NEXT_ROUTER_BASEPATH as string) || ''\n\nexport function addBasePath(path: string, required?: boolean): string {\n  return normalizePathTrailingSlash(\n    process.env.__NEXT_MANUAL_CLIENT_BASE_PATH && !required\n      ? path\n      : addPathPrefix(path, basePath)\n  )\n}\n"], "names": ["addPathPrefix", "normalizePathTrailingSlash", "basePath", "process", "env", "__NEXT_ROUTER_BASEPATH", "addBasePath", "path", "required", "__NEXT_MANUAL_CLIENT_BASE_PATH"], "mappings": ";;;AAAA,SAASA,aAAa,QAAQ,6CAA4C;AAC1E,SAASC,0BAA0B,QAAQ,6BAA4B;;;AAEvE,MAAMC,WAAYC,QAAQC,GAAG,CAACC,sBAAsB,MAAe;AAE5D,SAASC,YAAYC,IAAY,EAAEC,QAAkB;IAC1D,mMAAOP,6BAAAA,EACLE,QAAQC,GAAG,CAACK,8BAA8B,IAAI,CAACD,WAC3CD,gOACAP,gBAAAA,EAAcO,MAAML;AAE5B", "ignoreList": [0]}}, {"offset": {"line": 374, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/has-base-path.ts"], "sourcesContent": ["import { pathHasPrefix } from '../shared/lib/router/utils/path-has-prefix'\n\nconst basePath = (process.env.__NEXT_ROUTER_BASEPATH as string) || ''\n\nexport function hasBasePath(path: string): boolean {\n  return pathHasPrefix(path, basePath)\n}\n"], "names": ["pathHasPrefix", "basePath", "process", "env", "__NEXT_ROUTER_BASEPATH", "has<PERSON>ase<PERSON><PERSON>", "path"], "mappings": ";;;AAAA,SAASA,aAAa,QAAQ,6CAA4C;;AAE1E,MAAMC,WAAYC,QAAQC,GAAG,CAACC,sBAAsB,MAAe;AAE5D,SAASC,YAAYC,IAAY;IACtC,oNAAON,gBAAAA,EAAcM,MAAML;AAC7B", "ignoreList": [0]}}, {"offset": {"line": 389, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/remove-base-path.ts"], "sourcesContent": ["import { hasBasePath } from './has-base-path'\n\nconst basePath = (process.env.__NEXT_ROUTER_BASEPATH as string) || ''\n\nexport function removeBasePath(path: string): string {\n  if (process.env.__NEXT_MANUAL_CLIENT_BASE_PATH) {\n    if (!hasBasePath(path)) {\n      return path\n    }\n  }\n\n  // Can't trim the basePath if it has zero length!\n  if (basePath.length === 0) return path\n\n  path = path.slice(basePath.length)\n  if (!path.startsWith('/')) path = `/${path}`\n  return path\n}\n"], "names": ["has<PERSON>ase<PERSON><PERSON>", "basePath", "process", "env", "__NEXT_ROUTER_BASEPATH", "removeBasePath", "path", "__NEXT_MANUAL_CLIENT_BASE_PATH", "length", "slice", "startsWith"], "mappings": ";;;AAAA,SAASA,WAAW,QAAQ,kBAAiB;;AAE7C,MAAMC,WAAYC,QAAQC,GAAG,CAACC,sBAAsB,MAAe;AAE5D,SAASC,eAAeC,IAAY;IACzC,IAAIJ,QAAQC,GAAG,CAACI,uBAAgC,OAAF;;IAI9C;IAEA,iDAAiD;IACjD,IAAIN,SAASO,MAAM,KAAK,GAAG,OAAOF;IAElCA,OAAOA,KAAKG,KAAK,CAACR,SAASO,MAAM;IACjC,IAAI,CAACF,KAAKI,UAAU,CAAC,MAAMJ,OAAQ,MAAGA;IACtC,OAAOA;AACT", "ignoreList": [0]}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/assign-location.ts"], "sourcesContent": ["import { addBasePath } from './add-base-path'\n\n/**\n * Function to correctly assign location to URL\n *\n * The method will add basePath, and will also correctly add location (including if it is a relative path)\n * @param location Location that should be added to the url\n * @param url Base URL to which the location should be assigned\n */\nexport function assignLocation(location: string, url: URL): URL {\n  if (location.startsWith('.')) {\n    const urlBase = url.origin + url.pathname\n    return new URL(\n      // In order for a relative path to be added to the current url correctly, the current url must end with a slash\n      // new URL('./relative', 'https://example.com/subdir').href -> 'https://example.com/relative'\n      // new URL('./relative', 'https://example.com/subdir/').href -> 'https://example.com/subdir/relative'\n      (urlBase.endsWith('/') ? urlBase : urlBase + '/') + location\n    )\n  }\n\n  return new URL(addBasePath(location), url.href)\n}\n"], "names": ["addBasePath", "assignLocation", "location", "url", "startsWith", "urlBase", "origin", "pathname", "URL", "endsWith", "href"], "mappings": ";;;AAAA,SAASA,WAAW,QAAQ,kBAAiB;;AAStC,SAASC,eAAeC,QAAgB,EAAEC,GAAQ;IACvD,IAAID,SAASE,UAAU,CAAC,MAAM;QAC5B,MAAMC,UAAUF,IAAIG,MAAM,GAAGH,IAAII,QAAQ;QACzC,OAAO,IAAIC,IAIT,AAHA,AACA,6FAA6F,kBADkB;QAE/G,qGAAqG;QACpGH,CAAAA,QAAQI,QAAQ,CAAC,OAAOJ,UAAUA,UAAU,GAAE,IAAKH;IAExD;IAEA,OAAO,IAAIM,qLAAIR,cAAAA,EAAYE,WAAWC,IAAIO,IAAI;AAChD", "ignoreList": [0]}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/dev/dev-build-indicator/internal/dev-build-indicator.ts"], "sourcesContent": ["import { initialize } from './initialize'\n\nconst NOOP = () => {}\n\nexport const devBuildIndicator = {\n  /** Shows build indicator when Next.js is compiling. Requires initialize() first. */\n  show: NOOP,\n  /** Hides build indicator when Next.js finishes compiling. Requires initialize() first. */\n  hide: NOOP,\n  /** Sets up the build indicator UI component. Call this before using show/hide. */\n  initialize,\n}\n"], "names": ["initialize", "NOOP", "devBuildIndicator", "show", "hide"], "mappings": ";;;AAAA,SAASA,UAAU,QAAQ,eAAc;;AAEzC,MAAMC,OAAO,KAAO;AAEb,MAAMC,oBAAoB;IAC/B,kFAAkF,GAClFC,MAAMF;IACN,wFAAwF,GACxFG,MAAMH;IACN,gFAAgF,mOAChFD,aAAAA;AACF,EAAC", "ignoreList": [0]}}, {"offset": {"line": 448, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/dev/dev-build-indicator/internal/initialize.ts"], "sourcesContent": ["/*\n * Singleton store to track whether the app is currently being built\n * Used by the dev tools indicator of the new overlay to show build status\n */\n\nimport { devBuildIndicator } from './dev-build-indicator'\nimport { useSyncExternalStore } from 'react'\n\nlet isVisible = false\nlet listeners: Array<() => void> = []\n\nconst subscribe = (listener: () => void) => {\n  listeners.push(listener)\n  return () => {\n    listeners = listeners.filter((l) => l !== listener)\n  }\n}\n\nconst getSnapshot = () => isVisible\n\nexport function useIsDevBuilding() {\n  return useSyncExternalStore(subscribe, getSnapshot)\n}\n\nexport function initialize() {\n  devBuildIndicator.show = () => {\n    isVisible = true\n    listeners.forEach((listener) => listener())\n  }\n\n  devBuildIndicator.hide = () => {\n    isVisible = false\n    listeners.forEach((listener) => listener())\n  }\n}\n"], "names": ["devBuildIndicator", "useSyncExternalStore", "isVisible", "listeners", "subscribe", "listener", "push", "filter", "l", "getSnapshot", "useIsDevBuilding", "initialize", "show", "for<PERSON>ach", "hide"], "mappings": "AAAA;;;CAGC;;;;AAED,SAASA,iBAAiB,QAAQ,wBAAuB;AACzD,SAASC,oBAAoB,QAAQ,QAAO;;;AAE5C,IAAIC,YAAY;AAChB,IAAIC,YAA+B,EAAE;AAErC,MAAMC,YAAY,CAACC;IACjBF,UAAUG,IAAI,CAACD;IACf,OAAO;QACLF,YAAYA,UAAUI,MAAM,CAAC,CAACC,IAAMA,MAAMH;IAC5C;AACF;AAEA,MAAMI,cAAc,IAAMP;AAEnB,SAASQ;IACd,8KAAOT,uBAAAA,EAAqBG,WAAWK;AACzC;AAEO,SAASE;uOACdX,oBAAAA,CAAkBY,IAAI,GAAG;QACvBV,YAAY;QACZC,UAAUU,OAAO,CAAC,CAACR,WAAaA;IAClC;uOAEAL,oBAAAA,CAAkBc,IAAI,GAAG;QACvBZ,YAAY;QACZC,UAAUU,OAAO,CAAC,CAACR,WAAaA;IAClC;AACF", "ignoreList": [0]}}, {"offset": {"line": 487, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events.ts"], "sourcesContent": ["import {\n  HMR_ACTIONS_SENT_TO_BROWSER,\n  type HMR_ACTION_TYPES,\n} from '../../../../server/dev/hot-reloader-types'\nimport { devBuildIndicator } from './dev-build-indicator'\n\n/**\n * Handles HMR events to control the dev build indicator visibility.\n * Shows indicator when building and hides it when build completes or syncs.\n */\nexport const handleDevBuildIndicatorHmrEvents = (obj: HMR_ACTION_TYPES) => {\n  try {\n    if (!('action' in obj)) {\n      return\n    }\n\n    // eslint-disable-next-line default-case\n    switch (obj.action) {\n      case HMR_ACTIONS_SENT_TO_BROWSER.BUILDING:\n        devBuildIndicator.show()\n        break\n      case HMR_ACTIONS_SENT_TO_BROWSER.BUILT:\n      case HMR_ACTIONS_SENT_TO_BROWSER.SYNC:\n        devBuildIndicator.hide()\n        break\n    }\n  } catch {}\n}\n"], "names": ["HMR_ACTIONS_SENT_TO_BROWSER", "devBuildIndicator", "handleDevBuildIndicatorHmrEvents", "obj", "action", "BUILDING", "show", "BUILT", "SYNC", "hide"], "mappings": ";;;AAAA,SACEA,2BAA2B,QAEtB,4CAA2C;AAClD,SAASC,iBAAiB,QAAQ,wBAAuB;;;AAMlD,MAAMC,mCAAmC,CAACC;IAC/C,IAAI;QACF,IAAI,CAAE,CAAA,YAAYA,GAAE,GAAI;YACtB;QACF;QAEA,wCAAwC;QACxC,OAAQA,IAAIC,MAAM;YAChB,8LAAKJ,8BAAAA,CAA4BK,QAAQ;mPACvCJ,oBAAAA,CAAkBK,IAAI;gBACtB;YACF,8LAAKN,8BAAAA,CAA4BO,KAAK;YACtC,8LAAKP,8BAAAA,CAA4BQ,IAAI;mPACnCP,oBAAAA,CAAkBQ,IAAI;gBACtB;QACJ;IACF,EAAE,OAAA,GAAM,CAAC;AACX,EAAC", "ignoreList": [0]}}, {"offset": {"line": 517, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/lib/console.ts"], "sourcesContent": ["import isError from '../../lib/is-error'\n\nfunction formatObject(arg: unknown, depth: number) {\n  switch (typeof arg) {\n    case 'object':\n      if (arg === null) {\n        return 'null'\n      } else if (Array.isArray(arg)) {\n        let result = '['\n        if (depth < 1) {\n          for (let i = 0; i < arg.length; i++) {\n            if (result !== '[') {\n              result += ','\n            }\n            if (Object.prototype.hasOwnProperty.call(arg, i)) {\n              result += formatObject(arg[i], depth + 1)\n            }\n          }\n        } else {\n          result += arg.length > 0 ? '...' : ''\n        }\n        result += ']'\n        return result\n      } else if (arg instanceof Error) {\n        return arg + ''\n      } else {\n        const keys = Object.keys(arg)\n        let result = '{'\n        if (depth < 1) {\n          for (let i = 0; i < keys.length; i++) {\n            const key = keys[i]\n            const desc = Object.getOwnPropertyDescriptor(arg, 'key')\n            if (desc && !desc.get && !desc.set) {\n              const jsonKey = JSON.stringify(key)\n              if (jsonKey !== '\"' + key + '\"') {\n                result += jsonKey + ': '\n              } else {\n                result += key + ': '\n              }\n              result += formatObject(desc.value, depth + 1)\n            }\n          }\n        } else {\n          result += keys.length > 0 ? '...' : ''\n        }\n        result += '}'\n        return result\n      }\n    case 'string':\n      return JSON.stringify(arg)\n    default:\n      return String(arg)\n  }\n}\n\nexport function formatConsoleArgs(args: unknown[]): string {\n  let message: string\n  let idx: number\n  if (typeof args[0] === 'string') {\n    message = args[0]\n    idx = 1\n  } else {\n    message = ''\n    idx = 0\n  }\n  let result = ''\n  let startQuote = false\n  for (let i = 0; i < message.length; ++i) {\n    const char = message[i]\n    if (char !== '%' || i === message.length - 1 || idx >= args.length) {\n      result += char\n      continue\n    }\n\n    const code = message[++i]\n    switch (code) {\n      case 'c': {\n        // TODO: We should colorize with HTML instead of turning into a string.\n        // Ignore for now.\n        result = startQuote ? `${result}]` : `[${result}`\n        startQuote = !startQuote\n        idx++\n        break\n      }\n      case 'O':\n      case 'o': {\n        result += formatObject(args[idx++], 0)\n        break\n      }\n      case 'd':\n      case 'i': {\n        result += parseInt(args[idx++] as any, 10)\n        break\n      }\n      case 'f': {\n        result += parseFloat(args[idx++] as any)\n        break\n      }\n      case 's': {\n        result += String(args[idx++])\n        break\n      }\n      default:\n        result += '%' + code\n    }\n  }\n\n  for (; idx < args.length; idx++) {\n    result += (idx > 0 ? ' ' : '') + formatObject(args[idx], 0)\n  }\n\n  return result\n}\n\nexport function parseConsoleArgs(args: unknown[]): {\n  environmentName: string | null\n  error: Error | null\n} {\n  // See\n  // https://github.com/facebook/react/blob/65a56d0e99261481c721334a3ec4561d173594cd/packages/react-devtools-shared/src/backend/flight/renderer.js#L88-L93\n  //\n  // Logs replayed from the server look like this:\n  // [\n  //   \"%c%s%c %o\\n\\n%s\\n\\n%s\\n\",\n  //   \"background: #e6e6e6; ...\",\n  //   \" Server \", // can also be e.g. \" Prerender \"\n  //   \"\",\n  //   Error,\n  //   \"The above error occurred in the <Page> component.\",\n  //   ...\n  // ]\n  if (\n    args.length > 3 &&\n    typeof args[0] === 'string' &&\n    args[0].startsWith('%c%s%c ') &&\n    typeof args[1] === 'string' &&\n    typeof args[2] === 'string' &&\n    typeof args[3] === 'string'\n  ) {\n    const environmentName = args[2]\n    const maybeError = args[4]\n\n    return {\n      environmentName: environmentName.trim(),\n      error: isError(maybeError) ? maybeError : null,\n    }\n  }\n\n  return {\n    environmentName: null,\n    error: null,\n  }\n}\n"], "names": ["isError", "formatObject", "arg", "depth", "Array", "isArray", "result", "i", "length", "Object", "prototype", "hasOwnProperty", "call", "Error", "keys", "key", "desc", "getOwnPropertyDescriptor", "get", "set", "jsonKey", "JSON", "stringify", "value", "String", "formatConsoleArgs", "args", "message", "idx", "startQuote", "char", "code", "parseInt", "parseFloat", "parseConsoleArgs", "startsWith", "environmentName", "maybeError", "trim", "error"], "mappings": ";;;;AAAA,OAAOA,aAAa,qBAAoB;;AAExC,SAASC,aAAaC,GAAY,EAAEC,KAAa;IAC/C,OAAQ,OAAOD;QACb,KAAK;YACH,IAAIA,QAAQ,MAAM;gBAChB,OAAO;YACT,OAAO,IAAIE,MAAMC,OAAO,CAACH,MAAM;gBAC7B,IAAII,SAAS;gBACb,IAAIH,QAAQ,GAAG;oBACb,IAAK,IAAII,IAAI,GAAGA,IAAIL,IAAIM,MAAM,EAAED,IAAK;wBACnC,IAAID,WAAW,KAAK;4BAClBA,UAAU;wBACZ;wBACA,IAAIG,OAAOC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACV,KAAKK,IAAI;4BAChDD,UAAUL,aAAaC,GAAG,CAACK,EAAE,EAAEJ,QAAQ;wBACzC;oBACF;gBACF,OAAO;oBACLG,UAAUJ,IAAIM,MAAM,GAAG,IAAI,QAAQ;gBACrC;gBACAF,UAAU;gBACV,OAAOA;YACT,OAAO,IAAIJ,eAAeW,OAAO;gBAC/B,OAAOX,MAAM;YACf,OAAO;gBACL,MAAMY,OAAOL,OAAOK,IAAI,CAACZ;gBACzB,IAAII,SAAS;gBACb,IAAIH,QAAQ,GAAG;oBACb,IAAK,IAAII,IAAI,GAAGA,IAAIO,KAAKN,MAAM,EAAED,IAAK;wBACpC,MAAMQ,MAAMD,IAAI,CAACP,EAAE;wBACnB,MAAMS,OAAOP,OAAOQ,wBAAwB,CAACf,KAAK;wBAClD,IAAIc,QAAQ,CAACA,KAAKE,GAAG,IAAI,CAACF,KAAKG,GAAG,EAAE;4BAClC,MAAMC,UAAUC,KAAKC,SAAS,CAACP;4BAC/B,IAAIK,YAAY,MAAML,MAAM,KAAK;gCAC/BT,UAAUc,UAAU;4BACtB,OAAO;gCACLd,UAAUS,MAAM;4BAClB;4BACAT,UAAUL,aAAae,KAAKO,KAAK,EAAEpB,QAAQ;wBAC7C;oBACF;gBACF,OAAO;oBACLG,UAAUQ,KAAKN,MAAM,GAAG,IAAI,QAAQ;gBACtC;gBACAF,UAAU;gBACV,OAAOA;YACT;QACF,KAAK;YACH,OAAOe,KAAKC,SAAS,CAACpB;QACxB;YACE,OAAOsB,OAAOtB;IAClB;AACF;AAEO,SAASuB,kBAAkBC,IAAe;IAC/C,IAAIC;IACJ,IAAIC;IACJ,IAAI,OAAOF,IAAI,CAAC,EAAE,KAAK,UAAU;QAC/BC,UAAUD,IAAI,CAAC,EAAE;QACjBE,MAAM;IACR,OAAO;QACLD,UAAU;QACVC,MAAM;IACR;IACA,IAAItB,SAAS;IACb,IAAIuB,aAAa;IACjB,IAAK,IAAItB,IAAI,GAAGA,IAAIoB,QAAQnB,MAAM,EAAE,EAAED,EAAG;QACvC,MAAMuB,OAAOH,OAAO,CAACpB,EAAE;QACvB,IAAIuB,SAAS,OAAOvB,MAAMoB,QAAQnB,MAAM,GAAG,KAAKoB,OAAOF,KAAKlB,MAAM,EAAE;YAClEF,UAAUwB;YACV;QACF;QAEA,MAAMC,OAAOJ,OAAO,CAAC,EAAEpB,EAAE;QACzB,OAAQwB;YACN,KAAK;gBAAK;oBACR,uEAAuE;oBACvE,kBAAkB;oBAClBzB,SAASuB,aAAc,KAAEvB,SAAO,MAAM,MAAGA;oBACzCuB,aAAa,CAACA;oBACdD;oBACA;gBACF;YACA,KAAK;YACL,KAAK;gBAAK;oBACRtB,UAAUL,aAAayB,IAAI,CAACE,MAAM,EAAE;oBACpC;gBACF;YACA,KAAK;YACL,KAAK;gBAAK;oBACRtB,UAAU0B,SAASN,IAAI,CAACE,MAAM,EAAS;oBACvC;gBACF;YACA,KAAK;gBAAK;oBACRtB,UAAU2B,WAAWP,IAAI,CAACE,MAAM;oBAChC;gBACF;YACA,KAAK;gBAAK;oBACRtB,UAAUkB,OAAOE,IAAI,CAACE,MAAM;oBAC5B;gBACF;YACA;gBACEtB,UAAU,MAAMyB;QACpB;IACF;IAEA,MAAOH,MAAMF,KAAKlB,MAAM,EAAEoB,MAAO;QAC/BtB,UAAWsB,CAAAA,MAAM,IAAI,MAAM,EAAC,IAAK3B,aAAayB,IAAI,CAACE,IAAI,EAAE;IAC3D;IAEA,OAAOtB;AACT;AAEO,SAAS4B,iBAAiBR,IAAe;IAI9C,MAAM;IACN,wJAAwJ;IACxJ,EAAE;IACF,gDAAgD;IAChD,IAAI;IACJ,+BAA+B;IAC/B,gCAAgC;IAChC,kDAAkD;IAClD,QAAQ;IACR,WAAW;IACX,yDAAyD;IACzD,QAAQ;IACR,IAAI;IACJ,IACEA,KAAKlB,MAAM,GAAG,KACd,OAAOkB,IAAI,CAAC,EAAE,KAAK,YACnBA,IAAI,CAAC,EAAE,CAACS,UAAU,CAAC,cACnB,OAAOT,IAAI,CAAC,EAAE,KAAK,YACnB,OAAOA,IAAI,CAAC,EAAE,KAAK,YACnB,OAAOA,IAAI,CAAC,EAAE,KAAK,UACnB;QACA,MAAMU,kBAAkBV,IAAI,CAAC,EAAE;QAC/B,MAAMW,aAAaX,IAAI,CAAC,EAAE;QAE1B,OAAO;YACLU,iBAAiBA,gBAAgBE,IAAI;YACrCC,6KAAOvC,UAAAA,EAAQqC,cAAcA,aAAa;QAC5C;IACF;IAEA,OAAO;QACLD,iBAAiB;QACjBG,OAAO;IACT;AACF", "ignoreList": [0]}}]}