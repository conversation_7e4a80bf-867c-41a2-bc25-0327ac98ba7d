"use client"

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { vehiclesApi } from "@/lib/api/vehicles.api"
import toast from "react-hot-toast"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Calendar, Edit, MoreHorizontal, <PERSON>hare, <PERSON><PERSON>he<PERSON>, Star, Trash2, Users } from "lucide-react"
import Image from "next/image"
import Swal from 'sweetalert2'
import { RefreshCw } from 'lucide-react'

const statusLabels: Record<string, string> = {
  active: "Activo",
  rented: "Rentado",
  rejected: "Rechazado",
  inactive: "Inactivo",
  pending: "Pendiente de aprobación",
}

const statusColors: Record<string, string> = {
  active: "bg-green-100 text-green-800",
  rented: "bg-blue-100 text-blue-800",
  rejected: "bg-red-100 text-red-800",
  inactive: "bg-gray-100 text-gray-800",
  pending: "bg-purple-100 text-purple-800",
}

export default function VehicleDetailPage() {
  const router = useRouter()
  const queryClient = useQueryClient()
  const params = useParams<{ id: string }>()

  // Obtener detalles del vehículo
  const { data: vehicle, isLoading, error } = useQuery({
    queryKey: ['host-vehicle', params.id],
    // queryFn: () => vehiclesApi.getMyVehicleById(params.id),
    queryFn: () => vehiclesApi.host.getById(params.id as string),
  })

  const approvalHistory = vehicle?.approvalHistory || [];

  const isRejected = vehicle?.status === 'rejected';

  const rejectionReason = approvalHistory.reverse().find((item) => item.action === 'rejected')?.reason;

  // Mutación para actualizar estado
  const updateStatusMutation = useMutation({
    mutationFn: ({ id, status }: { id: string, status: string }) =>
      vehiclesApi.updateStatus(id, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vehicle', params.id] })
      toast.success("Estado actualizado exitosamente.")
    },
    onError: () => {

      toast.error("No se pudo actualizar el estado. Intenta de nuevo más tarde.")
    }
  })

  // Mutación para eliminar vehículo
  const deleteVehicleMutation = useMutation({
    mutationFn: (id: string) => vehiclesApi.host.delete(id),
    onSuccess: () => {

      toast.success("Vehículo eliminado exitosamente.")
      router.push("/dashboard/host/vehicles")
    },
    onError: () => {
      toast.error("No se pudo eliminar el vehículo. Intenta de nuevo más tarde.")
    }
  })

  // Mutación para solicitar revisión
  const requestReviewMutation = useMutation({
    mutationFn: (id: string) => vehiclesApi.host.requestReview(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vehicle', params.id] })
      toast.success("Solicitud de revisión enviada correctamente")
    },
    onError: () => {
      toast.error("No se pudo solicitar la revisión. Intenta de nuevo más tarde.")
    }
  })

  // Función para manejar la solicitud de revisión
  const handleRequestReview = () => {
    Swal.fire({
      title: '¿Solicitar nueva revisión?',
      text: `¿Has corregido los problemas y deseas solicitar una nueva revisión para tu vehículo ${vehicle?.make} ${vehicle?.model}?`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Sí, solicitar revisión',
      cancelButtonText: 'Cancelar'
    }).then((result) => {
      if (result.isConfirmed) {
        requestReviewMutation.mutate(params.id as string)
      }
    })
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (error || !vehicle) {
    return (
      <div className="flex flex-col items-center justify-center h-96 space-y-4">
        <h2 className="text-2xl font-bold">Error al cargar el vehículo</h2>
        <p className="text-muted-foreground">No se pudo cargar la información del vehículo.</p>
        <Button onClick={() => router.push("/dashboard/host/vehicles")}>
          Volver a mis vehículos
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header con acciones */}
      <div className="flex justify-between items-start flex-wrap gap-y-2">
        <div>
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm" onClick={() => router.push("/dashboard/host/vehicles")}>
              ← Volver
            </Button>
            <Badge className={statusColors[vehicle.status as keyof typeof statusColors] || statusColors.pending}>
              {statusLabels[vehicle.status as keyof typeof statusLabels] || "Desconocido"}
            </Badge>
          </div>

        </div>
        <div className="flex space-x-2 !justify-self-end">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <MoreHorizontal className="h-4 w-4 mr-2" />
                Acciones
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => router.push(`/dashboard/host/vehicles/${vehicle.id}/edit`)}>
                <Edit className="h-4 w-4 mr-2" />
                Editar vehículo
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => router.push(`/dashboard/host/vehicles/${vehicle.id}/calendar`)}>
                <Calendar className="h-4 w-4 mr-2" />
                Gestionar disponibilidad
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => window.open(`/vehicles/${vehicle.id}`, '_blank')}>
                <Share className="h-4 w-4 mr-2" />
                Ver como cliente
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-red-600"
                onClick={() => {
                  if (vehicle.status === 'active') {
                    updateStatusMutation.mutate({ id: vehicle.id, status: 'inactive' })
                  } else if (vehicle.status === 'inactive') {
                    updateStatusMutation.mutate({ id: vehicle.id, status: 'active' })
                  }
                }}
              >
                <ShieldCheck className="h-4 w-4 mr-2" />
                {vehicle.status === 'active' ? 'Desactivar' : 'Activar'} vehículo
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive">
                <Trash2 className="h-4 w-4 mr-2" />
                Eliminar
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>¿Estás seguro?</AlertDialogTitle>
                <AlertDialogDescription>
                  Esta acción no se puede deshacer. Eliminarás permanentemente este vehículo
                  y no podrá ser recuperado.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancelar</AlertDialogCancel>
                <AlertDialogAction
                  onClick={() => deleteVehicleMutation.mutate(vehicle.id)}
                  className="bg-red-600 hover:bg-red-700"
                >
                  Eliminar
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>
      <div>
        <h1 className="text-3xl font-bold mt-2">
          {vehicle.make} {vehicle.model} {vehicle.year}
        </h1>
        <p className="text-muted-foreground">
          {vehicle.color} • {vehicle.plate}
        </p>
      </div>

      {/* {isRejected && rejectionReason && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Vehículo rechazado</AlertTitle>
          <AlertDescription>
            {rejectionReason || "No se proporcionó una razón."}
          </AlertDescription>
        </Alert>
      )} */}

      {isRejected && (
        <div className="mt-4">
          <Card>
            <CardHeader className='text-red-700'>
              <CardTitle>Vehículo Rechazado</CardTitle>
              <CardDescription className='text-red-700'>
                Tu vehículo fue rechazado por el siguiente motivo:
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="p-3 bg-red-50 border border-red-200 rounded-md mb-4">
                <p className="text-red-800">{rejectionReason || "No se proporcionó un motivo específico."}</p>
              </div>
              <p className="mb-4 text-sm text-muted-foreground">
                Si has corregido los problemas mencionados, puedes solicitar una nueva revisión.
              </p>
              <Button
                // className="w-full"
                onClick={handleRequestReview}
                disabled={requestReviewMutation.isPending}
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                {requestReviewMutation.isPending ? 'Enviando solicitud...' : 'Solicitar Nueva Revisión'}
              </Button>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Contenido principal */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 overflow-hidden">
        {/* Columna izquierda - Información principal */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Galería</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="relative h-64 rounded-lg overflow-hidden">
                  <Image
                    src={Array.isArray(vehicle.images) && vehicle.images.length > 0
                      ? vehicle.images[0]
                      : "/placeholder.svg?height=300&width=500"}
                    alt={`${vehicle.make} ${vehicle.model}`}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  {/* {[1, 2, 3, 4].map((index) => ( */}
                  {vehicle.images?.length > 0 && vehicle.images.slice(1, 5).map((image, index) => (
                    <div key={index} className="relative h-28 rounded-lg overflow-hidden">
                      <Image
                        src={Array.isArray(vehicle.images) && vehicle.images.length > index
                          ? vehicle.images[index]
                          : "/placeholder.svg?height=150&width=200"}
                        alt={`${vehicle.make} ${vehicle.model} ${index + 1}`}
                        fill
                        className="object-cover"
                      />
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tabs de información */}
          <Tabs defaultValue="details">
            <TabsList className="grid grid-cols-3 mb-4">
              <TabsTrigger value="details">Detalles</TabsTrigger>
              <TabsTrigger value="features">Características</TabsTrigger>
              <TabsTrigger value="documents">Documentación</TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Información General</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground">Marca</h4>
                      <p>{vehicle.make}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground">Modelo</h4>
                      <p>{vehicle.model}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground">Año</h4>
                      <p>{vehicle.year}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground">Color</h4>
                      <p>{vehicle.color}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground">VIN</h4>
                      <p>{vehicle.vin}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground">Placas</h4>
                      <p>{vehicle.plate}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Descripción</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="whitespace-pre-line">{vehicle.description}</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Reglas y Restricciones</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="whitespace-pre-line">{vehicle.features?.rules || "No se han especificado reglas."}</p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="features" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Especificaciones Técnicas</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground">Transmisión</h4>
                      <p>{vehicle.transmission === 'automatic' ? 'Automática' : 'Manual'}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground">Combustible</h4>
                      <p>
                        {vehicle.features?.fuelType === 'gasoline' ? 'Gasolina' :
                          vehicle.features?.fuelType === 'diesel' ? 'Diésel' :
                            vehicle.features?.fuelType === 'electric' ? 'Eléctrico' :
                              vehicle.features?.fuelType === 'hybrid' ? 'Híbrido' : 'No especificado'}
                      </p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground">Asientos</h4>
                      <p>{vehicle.features?.seats || 'No especificado'}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground">Kilometraje</h4>
                      <p>{vehicle.features?.mileage ? `${vehicle.features.mileage} km` : 'No especificado'}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Comodidades</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-2">
                    {vehicle.amenities && vehicle.amenities.length > 0 ? (
                      vehicle.amenities.map((amenity, index) => (
                        <div key={index} className="flex items-center">
                          <div className="h-2 w-2 rounded-full bg-primary mr-2"></div>
                          <span>{amenity}</span>
                        </div>
                      ))
                    ) : (
                      <p>No se han especificado comodidades.</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="documents" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Documentación Legal</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground">Tarjeta de Circulación</h4>
                      <p>{vehicle.features?.registrationNumber || 'No especificado'}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground">Póliza de Seguro</h4>
                      <p>{vehicle.features?.insurancePolicy || 'No especificado'}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground">Ubicación de Entrega</h4>
                      <p>{vehicle.features?.location || 'No especificado'}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Columna derecha - Estadísticas y acciones */}
        <div className="space-y-6">
          {/* Tarifa */}
          <Card>
            <CardHeader>
              <CardTitle>Tarifa</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">${vehicle.price} <span className="text-sm font-normal text-muted-foreground">/ día</span></div>

              <div className="mt-4 space-y-2">
                {vehicle.features?.weeklyRate && (
                  <div className="flex justify-between">
                    <span>Tarifa semanal:</span>
                    <span className="font-medium">${vehicle.features.weeklyRate}</span>
                  </div>
                )}
                {vehicle.features?.monthlyRate && (
                  <div className="flex justify-between">
                    <span>Tarifa mensual:</span>
                    <span className="font-medium">${vehicle.features.monthlyRate}</span>
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full" onClick={() => router.push(`/dashboard/host/vehicles/${vehicle.id}/edit#pricing`)}>
                <Edit className="h-4 w-4 mr-2" />
                Editar precios
              </Button>
            </CardFooter>
          </Card>

          {/* Estadísticas */}
          <Card>
            <CardHeader>
              <CardTitle>Estadísticas</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Users className="h-5 w-5 mr-2 text-muted-foreground" />
                    <span>Total de rentas</span>
                  </div>
                  <span className="font-medium">{vehicle.reviews || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Star className="h-5 w-5 mr-2 text-muted-foreground" />
                    <span>Calificación</span>
                  </div>
                  <div className="flex items-center">
                    <span className="font-medium mr-1">{vehicle.rating?.toFixed(1) || '0.0'}</span>
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-4 w-4 ${i < Math.floor(vehicle.rating || 0)
                            ? "fill-yellow-400 text-yellow-400"
                            : "fill-gray-200 text-gray-200"
                            }`}
                        />
                      ))}
                    </div>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 mr-2 text-muted-foreground" />
                    <span>Días rentados</span>
                  </div>
                  <span className="font-medium">0</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Acciones rápidas */}
          <Card>
            <CardHeader>
              <CardTitle>Acciones Rápidas</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button className="w-full" onClick={() => router.push(`/dashboard/host/vehicles/${vehicle.id}/calendar`)}>
                <Calendar className="h-4 w-4 mr-2" />
                Gestionar disponibilidad
              </Button>
              <Button variant="outline" className="w-full" onClick={() => router.push(`/dashboard/host/vehicles/${vehicle.id}/edit`)}>
                <Edit className="h-4 w-4 mr-2" />
                Editar vehículo
              </Button>
              <Button variant="secondary" className="w-full" onClick={() => window.open(`/vehicles/${vehicle.id}`, '_blank')}>
                <Share className="h-4 w-4 mr-2" />
                Ver en página pública
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

