{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/(dashboard)/dashboard/page.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useEffect } from \"react\"\r\nimport { useRouter } from \"next/navigation\"\r\n\r\nexport default function DashboardRedirect() {\r\n  const router = useRouter()\r\n\r\n  useEffect(() => {\r\n    // Mock user role - en producción vendría del contexto de autenticación\r\n    const userRole = \"admin\" // Cambiar según el usuario autenticado\r\n    router.push(`/dashboard/${userRole}`)\r\n  }, [router])\r\n\r\n  return (\r\n    <div className=\"flex items-center justify-center h-full\">\r\n      <div className=\"text-center\">\r\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto\"></div>\r\n        <p className=\"mt-2 text-muted-foreground\">Redirigiendo...</p>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,iMAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QACR,uEAAuE;QACvE,MAAM,WAAW,QAAQ,uCAAuC;;QAChE,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,UAAU;IACtC,GAAG;QAAC;KAAO;IAEX,qBACE,kMAAC;QAAI,WAAU;kBACb,cAAA,kMAAC;YAAI,WAAU;;8BACb,kMAAC;oBAAI,WAAU;;;;;;8BACf,kMAAC;oBAAE,WAAU;8BAA6B;;;;;;;;;;;;;;;;;AAIlD"}}]}