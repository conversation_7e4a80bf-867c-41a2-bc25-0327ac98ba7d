{"version": 3, "sources": [], "sections": [{"offset": {"line": 20, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/favicon.ico.mjs (structured image object)"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 256, height: 256 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,8GAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;AAAI"}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\nimport { DateTime } from \"luxon\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));\r\n\r\n/**\r\n * Normaliza una fecha ISO a un objeto Date de JavaScript establecido a medianoche en la zona horaria local\r\n * @param dateStr Fecha en formato ISO string\r\n * @returns Objeto Date normalizado\r\n */\r\nexport function normalizeDate(dateStr: string): Date {\r\n  return DateTime.fromISO(dateStr)\r\n    .setZone('local')\r\n    .startOf('day')\r\n    .toJSDate();\r\n}\r\n\r\n/**\r\n * Convierte un array de fechas ISO a objetos Date normalizados\r\n * @param dateStrings Array de fechas en formato ISO string\r\n * @returns Array de objetos Date normalizados\r\n */\r\nexport function normalizeDates(dateStrings: string[]): Date[] {\r\n  if (!dateStrings || !Array.isArray(dateStrings)) return [];\r\n  return dateStrings.map(normalizeDate);\r\n}\r\n\r\n/**\r\n * Convierte un ISO a un string con formato \"dd de MMM de yyyy\"\r\n * @param date Fecha en formato ISO string\r\n * @returns String formateado\r\n */\r\nexport function formatISODate(date: string): string {\r\n  return DateTime.fromISO(date)\r\n    .setZone('local')\r\n    .toFormat('d \\'de\\' MMM \\'de\\' yyyy', { locale: 'es' });\r\n}\r\n\r\n/**\r\n * Verifica si una fecha está en un array de fechas deshabilitadas\r\n * @param date Fecha a verificar\r\n * @param disabledDates Array de fechas deshabilitadas\r\n * @returns true si la fecha está deshabilitada, false en caso contrario\r\n */\r\nexport function isDateDisabled(date: Date, disabledDates: Date[]): boolean {\r\n  const luxonDate = DateTime.fromJSDate(date)\r\n    .setZone('local')\r\n    .startOf('day');\r\n\r\n  return disabledDates.some(disabledDate => {\r\n    const luxonDisabledDate = DateTime.fromJSDate(disabledDate);\r\n    return luxonDate.hasSame(luxonDisabledDate, 'day');\r\n  });\r\n}\r\n\r\n/**\r\n * Formatea una fecha a un string en formato: \"dd de MMM de yyyy\"\r\n * @param date Fecha a formatear\r\n * @returns String formateado\r\n */\r\nexport function formatDate(date: string) {\r\n  return DateTime.fromISO(date)\r\n    .setZone('local')\r\n    .toFormat('d \\'de\\' MMMM, yyyy', { locale: 'es' });\r\n}\r\n\r\n/** \r\n * Formatea un número a un string en formato de moneda local, por default MXN\r\n * @param value Número a formatear\r\n * @returns String formateado\r\n */\r\nexport function formatCurrency(value: number, currency: string = 'MXN') {\r\n  return new Intl.NumberFormat('es-MX', { style: 'currency', currency }).format(value);\r\n\r\n}"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AAAA;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,mKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,6IAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,QAAQ,CAAC,KAAe,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AAOzE,SAAS,cAAc,OAAe;IAC3C,OAAO,uLAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,SACrB,OAAO,CAAC,SACR,OAAO,CAAC,OACR,QAAQ;AACb;AAOO,SAAS,eAAe,WAAqB;IAClD,IAAI,CAAC,eAAe,CAAC,MAAM,OAAO,CAAC,cAAc,OAAO,EAAE;IAC1D,OAAO,YAAY,GAAG,CAAC;AACzB;AAOO,SAAS,cAAc,IAAY;IACxC,OAAO,uLAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,MACrB,OAAO,CAAC,SACR,QAAQ,CAAC,4BAA4B;QAAE,QAAQ;IAAK;AACzD;AAQO,SAAS,eAAe,IAAU,EAAE,aAAqB;IAC9D,MAAM,YAAY,uLAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,MACnC,OAAO,CAAC,SACR,OAAO,CAAC;IAEX,OAAO,cAAc,IAAI,CAAC,CAAA;QACxB,MAAM,oBAAoB,uLAAA,CAAA,WAAQ,CAAC,UAAU,CAAC;QAC9C,OAAO,UAAU,OAAO,CAAC,mBAAmB;IAC9C;AACF;AAOO,SAAS,WAAW,IAAY;IACrC,OAAO,uLAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,MACrB,OAAO,CAAC,SACR,QAAQ,CAAC,uBAAuB;QAAE,QAAQ;IAAK;AACpD;AAOO,SAAS,eAAe,KAAa,EAAE,WAAmB,KAAK;IACpE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QAAE,OAAO;QAAY;IAAS,GAAG,MAAM,CAAC;AAEhF"}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/react-scan.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ReactScan = registerClientReference(\n    function() { throw new Error(\"Attempted to call ReactScan() from the server but ReactScan is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/react-scan.tsx <module evaluation>\",\n    \"ReactScan\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,yMAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,wDACA"}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/react-scan.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ReactScan = registerClientReference(\n    function() { throw new Error(\"Attempted to call ReactScan() from the server but ReactScan is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/react-scan.tsx\",\n    \"ReactScan\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,yMAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,oCACA"}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/providers/Providers.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Providers = registerClientReference(\n    function() { throw new Error(\"Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/Providers.tsx <module evaluation>\",\n    \"Providers\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,yMAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6DACA"}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/providers/Providers.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Providers = registerClientReference(\n    function() { throw new Error(\"Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/Providers.tsx\",\n    \"Providers\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,yMAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yCACA"}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.module.css [app-edge-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_e531dabc-module__QGiZLq__className\",\n  \"variable\": \"geist_e531dabc-module__QGiZLq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,6JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,6JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,6JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0]}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.module.css [app-edge-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_68a01160-module__YLcDdW__className\",\n  \"variable\": \"geist_mono_68a01160-module__YLcDdW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,kKAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,kKAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,kKAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0]}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/layout.tsx"], "sourcesContent": ["import \"./globals.css\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport type { Metadata } from \"next\";\r\nimport { ReactScan } from \"./react-scan\";\r\nimport { Toaster } from \"react-hot-toast\";\r\nimport { Providers } from \"@/providers/Providers\";\r\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from \"next/font/google\";\r\n\r\nconst geistSans = Geist({\r\n  variable: \"--font-geist-sans\",\r\n  subsets: [\"latin\"],\r\n});\r\n\r\nconst geistMono = Geist_Mono({\r\n  variable: \"--font-geist-mono\",\r\n  subsets: [\"latin\"],\r\n});\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"Autoop\",\r\n  description: \"Autoop\",\r\n};\r\n\r\nexport const runtime = 'edge';\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) {\r\n  return (\r\n    <html lang=\"en\" className='hydrated' suppressHydrationWarning>\r\n      <body\r\n        className={cn(\r\n          `${geistSans.variable} ${geistMono.variable} antialiased`,\r\n          \"relative min-h-screen bg-background text-foreground flex flex-col\",\r\n        )}\r\n      >\r\n        <Toaster />\r\n        <Providers>\r\n          <ReactScan />\r\n          {children}\r\n        </Providers>\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;AACA;AACA;;;;;;;;;;;AAaO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEO,MAAM,UAAU;AAER,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,qNAAC;QAAK,MAAK;QAAK,WAAU;QAAW,wBAAwB;kBAC3D,cAAA,qNAAC;YACC,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,GAAG,iJAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,sJAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,YAAY,CAAC,EACzD;;8BAGF,qNAAC,+JAAA,CAAA,UAAO;;;;;8BACR,qNAAC,sIAAA,CAAA,YAAS;;sCACR,qNAAC,oIAAA,CAAA,YAAS;;;;;wBACT;;;;;;;;;;;;;;;;;;AAKX"}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/theme-provider-dashboard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const DashboardThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardThemeProvider() from the server but DashboardThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/theme-provider-dashboard.tsx <module evaluation>\",\n    \"DashboardThemeProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,yBAAyB,CAAA,GAAA,yMAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,6EACA"}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/theme-provider-dashboard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const DashboardThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardThemeProvider() from the server but DashboardThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/theme-provider-dashboard.tsx\",\n    \"DashboardThemeProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,yBAAyB,CAAA,GAAA,yMAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,yDACA"}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 351, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/(dashboard)/dashboard/layout-2.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(dashboard)/dashboard/layout-2.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(dashboard)/dashboard/layout-2.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,yMAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8S,GAC3U,4EACA"}}, {"offset": {"line": 365, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/(dashboard)/dashboard/layout-2.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(dashboard)/dashboard/layout-2.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(dashboard)/dashboard/layout-2.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,yMAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0R,GACvT,wDACA"}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 389, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/UserTypeModal.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const UserTypeModal = registerClientReference(\n    function() { throw new Error(\"Attempted to call UserTypeModal() from the server but UserTypeModal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/UserTypeModal.tsx <module evaluation>\",\n    \"UserTypeModal\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,yMAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,kEACA"}}, {"offset": {"line": 403, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/UserTypeModal.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const UserTypeModal = registerClientReference(\n    function() { throw new Error(\"Attempted to call UserTypeModal() from the server but UserTypeModal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/UserTypeModal.tsx\",\n    \"UserTypeModal\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,yMAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8CACA"}}, {"offset": {"line": 417, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 427, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/(dashboard)/dashboard/layout.tsx"], "sourcesContent": ["import { getServerSession } from '@/actions/getSession'\r\nimport { UserProvider } from '../../../../context/user-context'\r\nimport { DashboardThemeProvider } from '@/components/theme-provider-dashboard'\r\nimport DashboardLayout2 from './layout-2'\r\nimport { UserTypeModal } from '@/components/UserTypeModal'\r\n\r\n\r\nexport default async function DashboardLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode\r\n}) {\r\n\r\n  const session = await getServerSession();\r\n\r\n  const role = session?.user.role;\r\n  const userType = session?.user.userType;\r\n  // console.log('Session on [dashboard layout]:', session);\r\n\r\n  return (\r\n    <>\r\n      <DashboardThemeProvider attribute=\"class\" defaultTheme=\"light\" enableSystem disableTransitionOnChange>\r\n        <UserProvider session={session}>\r\n\r\n          {\r\n            role !== 'admin' && !userType ? (\r\n              <>\r\n                {/* Show modal to select user type here */}\r\n                <UserTypeModal />\r\n              </>\r\n            ) : (\r\n              <DashboardLayout2>\r\n                {children}\r\n              </DashboardLayout2>\r\n            )\r\n          }\r\n        </UserProvider>\r\n      </DashboardThemeProvider>\r\n    </>\r\n  )\r\n}\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;;;;;;AAEA;AACA;AACA;;;;;;;AAGe,eAAe,gBAAgB,EAC5C,QAAQ,EAGT;IAEC,MAAM,UAAU,MAAM,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD;IAErC,MAAM,OAAO,SAAS,KAAK;IAC3B,MAAM,WAAW,SAAS,KAAK;IAC/B,0DAA0D;IAE1D,qBACE;kBACE,cAAA,qNAAC,4JAAA,CAAA,yBAAsB;YAAC,WAAU;YAAQ,cAAa;YAAQ,YAAY;YAAC,yBAAyB;sBACnG,cAAA,qNAAC;gBAAa,SAAS;0BAGnB,SAAS,WAAW,CAAC,yBACnB;8BAEE,cAAA,qNAAC,2IAAA,CAAA,gBAAa;;;;;kDAGhB,qNAAC,gKAAA,CAAA,UAAgB;8BACd;;;;;;;;;;;;;;;;;AAQjB"}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,qNAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,qNAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,qNAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,qNAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,qNAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,qNAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,qNAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf"}}, {"offset": {"line": 593, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/client/client-dashboard-stats.tsx"], "sourcesContent": ["import { Arrow<PERSON>p, Car, Calendar, CreditCard, Star } from \"lucide-react\"\r\nimport { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from \"@/components/ui/card\"\r\n\r\nexport function ClientDashboardStats() {\r\n  return (\r\n    <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\r\n      <Card>\r\n        <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n          <CardTitle className=\"text-sm font-medium\">Reservas Activas</CardTitle>\r\n          <Calendar className=\"h-4 w-4 text-muted-foreground\" />\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"text-2xl font-bold\">2</div>\r\n          <p className=\"text-xs text-muted-foreground flex items-center\">\r\n            <span className=\"text-green-500 flex items-center mr-1\">\r\n              <ArrowUp className=\"h-3 w-3 mr-1\" />1\r\n            </span>\r\n            nueva esta semana\r\n          </p>\r\n        </CardContent>\r\n      </Card>\r\n      <Card>\r\n        <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n          <CardTitle className=\"text-sm font-medium\">Total Rentas</CardTitle>\r\n          <Car className=\"h-4 w-4 text-muted-foreground\" />\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"text-2xl font-bold\">12</div>\r\n          <p className=\"text-xs text-muted-foreground flex items-center\">\r\n            <span className=\"text-green-500 flex items-center mr-1\">\r\n              <ArrowUp className=\"h-3 w-3 mr-1\" />3\r\n            </span>\r\n            este mes\r\n          </p>\r\n        </CardContent>\r\n      </Card>\r\n      <Card>\r\n        <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n          <CardTitle className=\"text-sm font-medium\">Gasto Total</CardTitle>\r\n          <CreditCard className=\"h-4 w-4 text-muted-foreground\" />\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"text-2xl font-bold\">$3,240</div>\r\n          <p className=\"text-xs text-muted-foreground flex items-center\">\r\n            <span className=\"text-green-500 flex items-center mr-1\">\r\n              <ArrowUp className=\"h-3 w-3 mr-1\" />\r\n              $450\r\n            </span>\r\n            este mes\r\n          </p>\r\n        </CardContent>\r\n      </Card>\r\n      <Card>\r\n        <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n          <CardTitle className=\"text-sm font-medium\">Mi Calificación</CardTitle>\r\n          <Star className=\"h-4 w-4 text-muted-foreground\" />\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"text-2xl font-bold\">4.9</div>\r\n          <p className=\"text-xs text-muted-foreground flex items-center\">\r\n            <span>Basado en 12 reseñas</span>\r\n          </p>\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;;AAEO,SAAS;IACd,qBACE,qNAAC;QAAI,WAAU;;0BACb,qNAAC,wIAAA,CAAA,OAAI;;kCACH,qNAAC,wIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,qNAAC,wIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAsB;;;;;;0CAC3C,qNAAC,kNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;kCAEtB,qNAAC,wIAAA,CAAA,cAAW;;0CACV,qNAAC;gCAAI,WAAU;0CAAqB;;;;;;0CACpC,qNAAC;gCAAE,WAAU;;kDACX,qNAAC;wCAAK,WAAU;;0DACd,qNAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;oCAC/B;;;;;;;;;;;;;;;;;;;0BAKb,qNAAC,wIAAA,CAAA,OAAI;;kCACH,qNAAC,wIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,qNAAC,wIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAsB;;;;;;0CAC3C,qNAAC,wMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;;;;;;;kCAEjB,qNAAC,wIAAA,CAAA,cAAW;;0CACV,qNAAC;gCAAI,WAAU;0CAAqB;;;;;;0CACpC,qNAAC;gCAAE,WAAU;;kDACX,qNAAC;wCAAK,WAAU;;0DACd,qNAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;oCAC/B;;;;;;;;;;;;;;;;;;;0BAKb,qNAAC,wIAAA,CAAA,OAAI;;kCACH,qNAAC,wIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,qNAAC,wIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAsB;;;;;;0CAC3C,qNAAC,0NAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;kCAExB,qNAAC,wIAAA,CAAA,cAAW;;0CACV,qNAAC;gCAAI,WAAU;0CAAqB;;;;;;0CACpC,qNAAC;gCAAE,WAAU;;kDACX,qNAAC;wCAAK,WAAU;;0DACd,qNAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;oCAE/B;;;;;;;;;;;;;;;;;;;0BAKb,qNAAC,wIAAA,CAAA,OAAI;;kCACH,qNAAC,wIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,qNAAC,wIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAsB;;;;;;0CAC3C,qNAAC,0MAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;kCAElB,qNAAC,wIAAA,CAAA,cAAW;;0CACV,qNAAC;gCAAI,WAAU;0CAAqB;;;;;;0CACpC,qNAAC;gCAAE,WAAU;0CACX,cAAA,qNAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlB"}}, {"offset": {"line": 910, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n  VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: ButtonProps) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,wKAAA,CAAA,MAAG,AAAD,EACvB,8cACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OACS;IACZ,MAAM,OAAO,UAAU,wKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,qNAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf"}}, {"offset": {"line": 967, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        success:\r\n          // \"border-transparent bg-success text-success-foreground [a&]:hover:bg-success/90\",\r\n          \"border-transparent bg-green-600 text-green-foreground [a&]:hover:bg-green-700 text-white\",\r\n        warning:\r\n          \"border-transparent bg-warning text-warning-foreground [a&]:hover:bg-warning/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,wKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,SACE,oFAAoF;YACpF;YACF,SACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,wKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,qNAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf"}}, {"offset": {"line": 1016, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/client/client-recent-activity.tsx"], "sourcesContent": ["import Link from \"next/link\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from \"@/components/ui/card\"\r\nimport { Badge } from \"@/components/ui/badge\"\r\n\r\nconst activities = [\r\n  {\r\n    id: 1,\r\n    type: \"reservation_confirmed\",\r\n    title: \"Reserva confirmada\",\r\n    description: \"Tesla Model 3 - 3 días\",\r\n    time: \"Hace 2 horas\",\r\n    status: \"success\",\r\n  },\r\n  {\r\n    id: 2,\r\n    type: \"payment_completed\",\r\n    title: \"Pago procesado\",\r\n    description: \"$360.00 - Reserva #1234\",\r\n    time: \"Hace 1 día\",\r\n    status: \"success\",\r\n  },\r\n  {\r\n    id: 3,\r\n    type: \"review_submitted\",\r\n    title: \"Reseña enviada\",\r\n    description: \"BMW X5 - 5 estrellas\",\r\n    time: \"Hace 3 días\",\r\n    status: \"info\",\r\n  },\r\n  {\r\n    id: 4,\r\n    type: \"reservation_completed\",\r\n    title: \"Reserva completada\",\r\n    description: \"Mercedes E-Class - 2 días\",\r\n    time: \"Hace 1 semana\",\r\n    status: \"completed\",\r\n  },\r\n]\r\n\r\nexport function ClientRecentActivity() {\r\n  return (\r\n    <Card>\r\n      <CardHeader className=\"flex flex-row items-center justify-between pb-2\">\r\n        <CardTitle className=\"text-md font-medium\">Actividad Reciente</CardTitle>\r\n        <Button variant=\"ghost\" size=\"sm\" asChild>\r\n          <Link href=\"/dashboard/client/history\">Ver Todo</Link>\r\n        </Button>\r\n      </CardHeader>\r\n      <CardContent>\r\n        <div className=\"space-y-4\">\r\n          {activities.map((activity) => (\r\n            <div key={activity.id} className=\"flex items-start gap-4\">\r\n              <div className=\"h-2 w-2 rounded-full bg-primary mt-2\" />\r\n              <div className=\"flex-1 space-y-1\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <p className=\"text-sm font-medium\">{activity.title}</p>\r\n                  <Badge\r\n                    variant={\r\n                      activity.status === \"success\" ? \"default\" : activity.status === \"info\" ? \"secondary\" : \"outline\"\r\n                    }\r\n                    className=\"text-xs\"\r\n                  >\r\n                    {activity.status === \"success\" ? \"Exitoso\" : activity.status === \"info\" ? \"Info\" : \"Completado\"}\r\n                  </Badge>\r\n                </div>\r\n                <p className=\"text-sm text-muted-foreground\">{activity.description}</p>\r\n                <p className=\"text-xs text-muted-foreground\">{activity.time}</p>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,aAAa;IACjB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;IACV;CACD;AAEM,SAAS;IACd,qBACE,qNAAC,wIAAA,CAAA,OAAI;;0BACH,qNAAC,wIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,qNAAC,wIAAA,CAAA,YAAS;wBAAC,WAAU;kCAAsB;;;;;;kCAC3C,qNAAC,0IAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAK,OAAO;kCACvC,cAAA,qNAAC,2KAAA,CAAA,UAAI;4BAAC,MAAK;sCAA4B;;;;;;;;;;;;;;;;;0BAG3C,qNAAC,wIAAA,CAAA,cAAW;0BACV,cAAA,qNAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,yBACf,qNAAC;4BAAsB,WAAU;;8CAC/B,qNAAC;oCAAI,WAAU;;;;;;8CACf,qNAAC;oCAAI,WAAU;;sDACb,qNAAC;4CAAI,WAAU;;8DACb,qNAAC;oDAAE,WAAU;8DAAuB,SAAS,KAAK;;;;;;8DAClD,qNAAC,yIAAA,CAAA,QAAK;oDACJ,SACE,SAAS,MAAM,KAAK,YAAY,YAAY,SAAS,MAAM,KAAK,SAAS,cAAc;oDAEzF,WAAU;8DAET,SAAS,MAAM,KAAK,YAAY,YAAY,SAAS,MAAM,KAAK,SAAS,SAAS;;;;;;;;;;;;sDAGvF,qNAAC;4CAAE,WAAU;sDAAiC,SAAS,WAAW;;;;;;sDAClE,qNAAC;4CAAE,WAAU;sDAAiC,SAAS,IAAI;;;;;;;;;;;;;2BAfrD,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;AAuBjC"}}, {"offset": {"line": 1193, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/client/client-recommendations.tsx"], "sourcesContent": ["import Link from \"next/link\"\r\nimport Image from \"next/image\"\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\"\r\nimport { Badge } from \"@/components/ui/badge\"\r\nimport { Star, MapPin } from \"lucide-react\"\r\n\r\nconst recommendations = [\r\n  {\r\n    id: 1,\r\n    name: \"Tesla Model Y\",\r\n    image: \"/placeholder.svg?height=120&width=200\",\r\n    host: \"Carlos Mendoza\",\r\n    location: \"Ciudad de México\",\r\n    dailyRate: 180,\r\n    rating: 4.9,\r\n    reviews: 23,\r\n    features: [\"Eléctrico\", \"SUV\", \"5 asientos\"],\r\n  },\r\n  {\r\n    id: 2,\r\n    name: \"Audi A4\",\r\n    image: \"/placeholder.svg?height=120&width=200\",\r\n    host: \"Ana García\",\r\n    location: \"Guadalajara\",\r\n    dailyRate: 140,\r\n    rating: 4.8,\r\n    reviews: 18,\r\n    features: [\"Sedan\", \"Lujo\", \"Automático\"],\r\n  },\r\n  {\r\n    id: 3,\r\n    name: \"Jeep Wrangler\",\r\n    image: \"/placeholder.svg?height=120&width=200\",\r\n    host: \"<PERSON>\",\r\n    location: \"Cancún\",\r\n    dailyRate: 160,\r\n    rating: 4.7,\r\n    reviews: 31,\r\n    features: [\"SUV\", \"4x4\", \"Aventura\"],\r\n  },\r\n]\r\n\r\nexport function ClientRecommendations() {\r\n  return (\r\n    <Card>\r\n      <CardHeader className=\"flex flex-row items-center justify-between pb-2\">\r\n        <CardTitle className=\"text-md font-medium\">Recomendados para Ti</CardTitle>\r\n        <Button variant=\"ghost\" size=\"sm\" asChild>\r\n          <Link href=\"/dashboard/client/search\">Ver Más</Link>\r\n        </Button>\r\n      </CardHeader>\r\n      <CardContent>\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n          {recommendations.map((vehicle) => (\r\n            <div key={vehicle.id} className=\"border rounded-lg overflow-hidden hover:shadow-md transition-shadow\">\r\n              <div className=\"relative h-32\">\r\n                <Image src={vehicle.image || \"/placeholder.svg\"} alt={vehicle.name} fill className=\"object-cover\" />\r\n              </div>\r\n              <div className=\"p-4 space-y-3\">\r\n                <div>\r\n                  <h3 className=\"font-medium text-sm\">{vehicle.name}</h3>\r\n                  <p className=\"text-xs text-muted-foreground\">por {vehicle.host}</p>\r\n                </div>\r\n\r\n                <div className=\"flex items-center gap-1 text-xs text-muted-foreground\">\r\n                  <MapPin className=\"h-3 w-3\" />\r\n                  <span>{vehicle.location}</span>\r\n                </div>\r\n\r\n                <div className=\"flex items-center gap-1\">\r\n                  <Star className=\"h-3 w-3 fill-yellow-400 text-yellow-400\" />\r\n                  <span className=\"text-xs font-medium\">{vehicle.rating}</span>\r\n                  <span className=\"text-xs text-muted-foreground\">({vehicle.reviews})</span>\r\n                </div>\r\n\r\n                <div className=\"flex flex-wrap gap-1\">\r\n                  {vehicle.features.map((feature, index) => (\r\n                    <Badge key={index} variant=\"secondary\" className=\"text-xs\">\r\n                      {feature}\r\n                    </Badge>\r\n                  ))}\r\n                </div>\r\n\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div>\r\n                    <span className=\"text-lg font-bold\">${vehicle.dailyRate}</span>\r\n                    <span className=\"text-xs text-muted-foreground\">/día</span>\r\n                  </div>\r\n                  <Button size=\"sm\" className=\"text-xs\">\r\n                    Ver detalles\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;AAEA,MAAM,kBAAkB;IACtB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,WAAW;QACX,QAAQ;QACR,SAAS;QACT,UAAU;YAAC;YAAa;YAAO;SAAa;IAC9C;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,WAAW;QACX,QAAQ;QACR,SAAS;QACT,UAAU;YAAC;YAAS;YAAQ;SAAa;IAC3C;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,WAAW;QACX,QAAQ;QACR,SAAS;QACT,UAAU;YAAC;YAAO;YAAO;SAAW;IACtC;CACD;AAEM,SAAS;IACd,qBACE,qNAAC,wIAAA,CAAA,OAAI;;0BACH,qNAAC,wIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,qNAAC,wIAAA,CAAA,YAAS;wBAAC,WAAU;kCAAsB;;;;;;kCAC3C,qNAAC,0IAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAK,OAAO;kCACvC,cAAA,qNAAC,2KAAA,CAAA,UAAI;4BAAC,MAAK;sCAA2B;;;;;;;;;;;;;;;;;0BAG1C,qNAAC,wIAAA,CAAA,cAAW;0BACV,cAAA,qNAAC;oBAAI,WAAU;8BACZ,gBAAgB,GAAG,CAAC,CAAC,wBACpB,qNAAC;4BAAqB,WAAU;;8CAC9B,qNAAC;oCAAI,WAAU;8CACb,cAAA,qNAAC,iLAAA,CAAA,UAAK;wCAAC,KAAK,QAAQ,KAAK,IAAI;wCAAoB,KAAK,QAAQ,IAAI;wCAAE,IAAI;wCAAC,WAAU;;;;;;;;;;;8CAErF,qNAAC;oCAAI,WAAU;;sDACb,qNAAC;;8DACC,qNAAC;oDAAG,WAAU;8DAAuB,QAAQ,IAAI;;;;;;8DACjD,qNAAC;oDAAE,WAAU;;wDAAgC;wDAAK,QAAQ,IAAI;;;;;;;;;;;;;sDAGhE,qNAAC;4CAAI,WAAU;;8DACb,qNAAC,kNAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,qNAAC;8DAAM,QAAQ,QAAQ;;;;;;;;;;;;sDAGzB,qNAAC;4CAAI,WAAU;;8DACb,qNAAC,0MAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,qNAAC;oDAAK,WAAU;8DAAuB,QAAQ,MAAM;;;;;;8DACrD,qNAAC;oDAAK,WAAU;;wDAAgC;wDAAE,QAAQ,OAAO;wDAAC;;;;;;;;;;;;;sDAGpE,qNAAC;4CAAI,WAAU;sDACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC9B,qNAAC,yIAAA,CAAA,QAAK;oDAAa,SAAQ;oDAAY,WAAU;8DAC9C;mDADS;;;;;;;;;;sDAMhB,qNAAC;4CAAI,WAAU;;8DACb,qNAAC;;sEACC,qNAAC;4DAAK,WAAU;;gEAAoB;gEAAE,QAAQ,SAAS;;;;;;;sEACvD,qNAAC;4DAAK,WAAU;sEAAgC;;;;;;;;;;;;8DAElD,qNAAC,0IAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;2BAlClC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;AA6ChC"}}, {"offset": {"line": 1504, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/client/client-upcoming-reservations.tsx"], "sourcesContent": ["import Link from \"next/link\"\r\nimport Image from \"next/image\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from \"@/components/ui/card\"\r\nimport { Badge } from \"@/components/ui/badge\"\r\n\r\nconst upcomingReservations = [\r\n  {\r\n    id: 1,\r\n    vehicle: {\r\n      name: \"Tesla Model 3\",\r\n      image: \"/placeholder.svg?height=40&width=40\",\r\n      host: \"<PERSON>\",\r\n    },\r\n    dates: {\r\n      start: \"15 Mar\",\r\n      end: \"18 Mar\",\r\n      days: 3,\r\n    },\r\n    amount: \"$360.00\",\r\n    status: \"confirmed\",\r\n    location: \"Ciudad de México\",\r\n  },\r\n  {\r\n    id: 2,\r\n    vehicle: {\r\n      name: \"BMW X5\",\r\n      image: \"/placeholder.svg?height=40&width=40\",\r\n      host: \"<PERSON>\",\r\n    },\r\n    dates: {\r\n      start: \"22 Mar\",\r\n      end: \"25 Mar\",\r\n      days: 3,\r\n    },\r\n    amount: \"$450.00\",\r\n    status: \"pending\",\r\n    location: \"Guadalajara\",\r\n  },\r\n]\r\n\r\nexport function ClientUpcomingReservations() {\r\n  return (\r\n    <Card>\r\n      <CardHeader className=\"flex flex-row items-center justify-between pb-2\">\r\n        <CardTitle className=\"text-md font-medium\">Próximas <PERSON></CardTitle>\r\n        <Button variant=\"ghost\" size=\"sm\" asChild>\r\n          <Link href=\"/dashboard/client/reservations\">Ver Todas</Link>\r\n        </Button>\r\n      </CardHeader>\r\n      <CardContent>\r\n        <div className=\"space-y-4\">\r\n          {upcomingReservations.map((reservation) => (\r\n            <div key={reservation.id} className=\"flex items-center gap-4 p-4 border rounded-lg\">\r\n              <div className=\"h-12 w-12 rounded overflow-hidden\">\r\n                <Image\r\n                  src={reservation.vehicle.image || \"/placeholder.svg\"}\r\n                  alt={reservation.vehicle.name}\r\n                  width={48}\r\n                  height={48}\r\n                  className=\"h-full w-full object-cover\"\r\n                />\r\n              </div>\r\n              <div className=\"flex-1\">\r\n                <div className=\"flex items-center justify-between mb-2\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <p className=\"text-sm font-medium\">{reservation.vehicle.name}</p>\r\n                    <Badge variant={reservation.status === \"confirmed\" ? \"default\" : \"secondary\"} className=\"text-xs\">\r\n                      {reservation.status === \"confirmed\" ? \"Confirmado\" : \"Pendiente\"}\r\n                    </Badge>\r\n                  </div>\r\n                  <span className=\"text-sm font-medium\">{reservation.amount}</span>\r\n                </div>\r\n                <p className=\"text-xs text-muted-foreground mb-1\">Anfitrión: {reservation.vehicle.host}</p>\r\n                <p className=\"text-xs text-muted-foreground mb-2\">📍 {reservation.location}</p>\r\n                <div className=\"flex items-center justify-between text-xs text-muted-foreground\">\r\n                  <span>\r\n                    {reservation.dates.start} - {reservation.dates.end} ({reservation.dates.days} días)\r\n                  </span>\r\n                  <div className=\"flex gap-2\">\r\n                    <Button size=\"sm\" variant=\"outline\" className=\"h-6 text-xs\">\r\n                      Ver detalles\r\n                    </Button>\r\n                    {reservation.status === \"pending\" && (\r\n                      <Button size=\"sm\" variant=\"destructive\" className=\"h-6 text-xs\">\r\n                        Cancelar\r\n                      </Button>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,uBAAuB;IAC3B;QACE,IAAI;QACJ,SAAS;YACP,MAAM;YACN,OAAO;YACP,MAAM;QACR;QACA,OAAO;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA,QAAQ;QACR,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,SAAS;YACP,MAAM;YACN,OAAO;YACP,MAAM;QACR;QACA,OAAO;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA,QAAQ;QACR,QAAQ;QACR,UAAU;IACZ;CACD;AAEM,SAAS;IACd,qBACE,qNAAC,wIAAA,CAAA,OAAI;;0BACH,qNAAC,wIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,qNAAC,wIAAA,CAAA,YAAS;wBAAC,WAAU;kCAAsB;;;;;;kCAC3C,qNAAC,0IAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAK,OAAO;kCACvC,cAAA,qNAAC,2KAAA,CAAA,UAAI;4BAAC,MAAK;sCAAiC;;;;;;;;;;;;;;;;;0BAGhD,qNAAC,wIAAA,CAAA,cAAW;0BACV,cAAA,qNAAC;oBAAI,WAAU;8BACZ,qBAAqB,GAAG,CAAC,CAAC,4BACzB,qNAAC;4BAAyB,WAAU;;8CAClC,qNAAC;oCAAI,WAAU;8CACb,cAAA,qNAAC,iLAAA,CAAA,UAAK;wCACJ,KAAK,YAAY,OAAO,CAAC,KAAK,IAAI;wCAClC,KAAK,YAAY,OAAO,CAAC,IAAI;wCAC7B,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAGd,qNAAC;oCAAI,WAAU;;sDACb,qNAAC;4CAAI,WAAU;;8DACb,qNAAC;oDAAI,WAAU;;sEACb,qNAAC;4DAAE,WAAU;sEAAuB,YAAY,OAAO,CAAC,IAAI;;;;;;sEAC5D,qNAAC,yIAAA,CAAA,QAAK;4DAAC,SAAS,YAAY,MAAM,KAAK,cAAc,YAAY;4DAAa,WAAU;sEACrF,YAAY,MAAM,KAAK,cAAc,eAAe;;;;;;;;;;;;8DAGzD,qNAAC;oDAAK,WAAU;8DAAuB,YAAY,MAAM;;;;;;;;;;;;sDAE3D,qNAAC;4CAAE,WAAU;;gDAAqC;gDAAY,YAAY,OAAO,CAAC,IAAI;;;;;;;sDACtF,qNAAC;4CAAE,WAAU;;gDAAqC;gDAAI,YAAY,QAAQ;;;;;;;sDAC1E,qNAAC;4CAAI,WAAU;;8DACb,qNAAC;;wDACE,YAAY,KAAK,CAAC,KAAK;wDAAC;wDAAI,YAAY,KAAK,CAAC,GAAG;wDAAC;wDAAG,YAAY,KAAK,CAAC,IAAI;wDAAC;;;;;;;8DAE/E,qNAAC;oDAAI,WAAU;;sEACb,qNAAC,0IAAA,CAAA,SAAM;4DAAC,MAAK;4DAAK,SAAQ;4DAAU,WAAU;sEAAc;;;;;;wDAG3D,YAAY,MAAM,KAAK,2BACtB,qNAAC,0IAAA,CAAA,SAAM;4DAAC,MAAK;4DAAK,SAAQ;4DAAc,WAAU;sEAAc;;;;;;;;;;;;;;;;;;;;;;;;;2BA/BhE,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;AA4CpC"}}, {"offset": {"line": 1770, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/(dashboard)/dashboard/client/page.tsx"], "sourcesContent": ["import { ClientDashboardStats } from \"@/components/client/client-dashboard-stats\"\r\nimport { ClientRecentActivity } from \"@/components/client/client-recent-activity\"\r\nimport { ClientRecommendations } from \"@/components/client/client-recommendations\"\r\nimport { ClientUpcomingReservations } from \"@/components/client/client-upcoming-reservations\"\r\n\r\nexport default function ClientDashboard() {\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex flex-col space-y-2\">\r\n        <h1 className=\"text-2xl font-bold\">Mi Dashboard</h1>\r\n        <p className=\"text-muted-foreground\">Bienvenido de vuelta. Encuentra tu próximo vehículo perfecto.</p>\r\n      </div>\r\n\r\n      <ClientDashboardStats />\r\n\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n        <ClientUpcomingReservations />\r\n        <ClientRecentActivity />\r\n      </div>\r\n\r\n      <ClientRecommendations />\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEe,SAAS;IACtB,qBACE,qNAAC;QAAI,WAAU;;0BACb,qNAAC;gBAAI,WAAU;;kCACb,qNAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,qNAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAGvC,qNAAC,oKAAA,CAAA,uBAAoB;;;;;0BAErB,qNAAC;gBAAI,WAAU;;kCACb,qNAAC,0KAAA,CAAA,6BAA0B;;;;;kCAC3B,qNAAC,oKAAA,CAAA,uBAAoB;;;;;;;;;;;0BAGvB,qNAAC,iKAAA,CAAA,wBAAqB;;;;;;;;;;;AAG5B"}}, {"offset": {"line": 1860, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/dev/hmr-client/hmr-client.ts"], "sourcesContent": ["/// <reference path=\"../../../shared/runtime-types.d.ts\" />\r\n/// <reference path=\"../../runtime/base/dev-globals.d.ts\" />\r\n/// <reference path=\"../../runtime/base/dev-protocol.d.ts\" />\r\n/// <reference path=\"../../runtime/base/dev-extensions.ts\" />\r\n\r\ntype SendMessage = (msg: any) => void;\r\nexport type WebSocketMessage =\r\n  | {\r\n      type: \"turbopack-connected\";\r\n    }\r\n  | {\r\n      type: \"turbopack-message\";\r\n      data: Record<string, any>;\r\n    };\r\n\r\n\r\nexport type ClientOptions = {\r\n  addMessageListener: (cb: (msg: WebSocketMessage) => void) => void;\r\n  sendMessage: SendMessage;\r\n  onUpdateError: (err: unknown) => void;\r\n};\r\n\r\nexport function connect({\r\n  addMessageListener,\r\n  sendMessage,\r\n  onUpdateError = console.error,\r\n}: ClientOptions) {\r\n  addMessageListener((msg) => {\r\n    switch (msg.type) {\r\n      case \"turbopack-connected\":\r\n        handleSocketConnected(sendMessage);\r\n        break;\r\n      default:\r\n        try {\r\n          if (Array.isArray(msg.data)) {\r\n            for (let i = 0; i < msg.data.length; i++) {\r\n              handleSocketMessage(msg.data[i] as ServerMessage);\r\n            }\r\n          } else {\r\n            handleSocketMessage(msg.data as ServerMessage);\r\n          }\r\n          applyAggregatedUpdates();\r\n        } catch (e: unknown) {\r\n          console.warn(\r\n            \"[Fast Refresh] performing full reload\\n\\n\" +\r\n              \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" +\r\n              \"You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n\" +\r\n              \"Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n\" +\r\n              \"It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n\" +\r\n              \"Fast Refresh requires at least one parent function component in your React tree.\"\r\n          );\r\n          onUpdateError(e);\r\n          location.reload();\r\n        }\r\n        break;\r\n    }\r\n  });\r\n\r\n  const queued = globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS;\r\n  if (queued != null && !Array.isArray(queued)) {\r\n    throw new Error(\"A separate HMR handler was already registered\");\r\n  }\r\n  globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS = {\r\n    push: ([chunkPath, callback]: [ChunkListPath, UpdateCallback]) => {\r\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback);\r\n    },\r\n  };\r\n\r\n  if (Array.isArray(queued)) {\r\n    for (const [chunkPath, callback] of queued) {\r\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback);\r\n    }\r\n  }\r\n}\r\n\r\ntype UpdateCallbackSet = {\r\n  callbacks: Set<UpdateCallback>;\r\n  unsubscribe: () => void;\r\n};\r\n\r\nconst updateCallbackSets: Map<ResourceKey, UpdateCallbackSet> = new Map();\r\n\r\nfunction sendJSON(sendMessage: SendMessage, message: ClientMessage) {\r\n  sendMessage(JSON.stringify(message));\r\n}\r\n\r\ntype ResourceKey = string;\r\n\r\nfunction resourceKey(resource: ResourceIdentifier): ResourceKey {\r\n  return JSON.stringify({\r\n    path: resource.path,\r\n    headers: resource.headers || null,\r\n  });\r\n}\r\n\r\nfunction subscribeToUpdates(\r\n  sendMessage: SendMessage,\r\n  resource: ResourceIdentifier\r\n): () => void {\r\n  sendJSON(sendMessage, {\r\n    type: \"turbopack-subscribe\",\r\n    ...resource,\r\n  });\r\n\r\n  return () => {\r\n    sendJSON(sendMessage, {\r\n      type: \"turbopack-unsubscribe\",\r\n      ...resource,\r\n    });\r\n  };\r\n}\r\n\r\nfunction handleSocketConnected(sendMessage: SendMessage) {\r\n  for (const key of updateCallbackSets.keys()) {\r\n    subscribeToUpdates(sendMessage, JSON.parse(key));\r\n  }\r\n}\r\n\r\n// we aggregate all pending updates until the issues are resolved\r\nconst chunkListsWithPendingUpdates: Map<ResourceKey, PartialServerMessage> =\r\n  new Map();\r\n\r\nfunction aggregateUpdates(msg: PartialServerMessage) {\r\n  const key = resourceKey(msg.resource);\r\n  let aggregated = chunkListsWithPendingUpdates.get(key);\r\n\r\n  if (aggregated) {\r\n    aggregated.instruction = mergeChunkListUpdates(\r\n      aggregated.instruction,\r\n      msg.instruction\r\n    );\r\n  } else {\r\n    chunkListsWithPendingUpdates.set(key, msg);\r\n  }\r\n}\r\n\r\nfunction applyAggregatedUpdates() {\r\n  if (chunkListsWithPendingUpdates.size === 0) return;\r\n  hooks.beforeRefresh();\r\n  for (const msg of chunkListsWithPendingUpdates.values()) {\r\n    triggerUpdate(msg);\r\n  }\r\n  chunkListsWithPendingUpdates.clear();\r\n  finalizeUpdate();\r\n}\r\n\r\nfunction mergeChunkListUpdates(\r\n  updateA: ChunkListUpdate,\r\n  updateB: ChunkListUpdate\r\n): ChunkListUpdate {\r\n  let chunks;\r\n  if (updateA.chunks != null) {\r\n    if (updateB.chunks == null) {\r\n      chunks = updateA.chunks;\r\n    } else {\r\n      chunks = mergeChunkListChunks(updateA.chunks, updateB.chunks);\r\n    }\r\n  } else if (updateB.chunks != null) {\r\n    chunks = updateB.chunks;\r\n  }\r\n\r\n  let merged;\r\n  if (updateA.merged != null) {\r\n    if (updateB.merged == null) {\r\n      merged = updateA.merged;\r\n    } else {\r\n      // Since `merged` is an array of updates, we need to merge them all into\r\n      // one, consistent update.\r\n      // Since there can only be `EcmascriptMergeUpdates` in the array, there is\r\n      // no need to key on the `type` field.\r\n      let update = updateA.merged[0];\r\n      for (let i = 1; i < updateA.merged.length; i++) {\r\n        update = mergeChunkListEcmascriptMergedUpdates(\r\n          update,\r\n          updateA.merged[i]\r\n        );\r\n      }\r\n\r\n      for (let i = 0; i < updateB.merged.length; i++) {\r\n        update = mergeChunkListEcmascriptMergedUpdates(\r\n          update,\r\n          updateB.merged[i]\r\n        );\r\n      }\r\n\r\n      merged = [update];\r\n    }\r\n  } else if (updateB.merged != null) {\r\n    merged = updateB.merged;\r\n  }\r\n\r\n  return {\r\n    type: \"ChunkListUpdate\",\r\n    chunks,\r\n    merged,\r\n  };\r\n}\r\n\r\nfunction mergeChunkListChunks(\r\n  chunksA: Record<ChunkPath, ChunkUpdate>,\r\n  chunksB: Record<ChunkPath, ChunkUpdate>\r\n): Record<ChunkPath, ChunkUpdate> {\r\n  const chunks: Record<ChunkPath, ChunkUpdate> = {};\r\n\r\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<[ChunkPath, ChunkUpdate]>) {\r\n    const chunkUpdateB = chunksB[chunkPath];\r\n    if (chunkUpdateB != null) {\r\n      const mergedUpdate = mergeChunkUpdates(chunkUpdateA, chunkUpdateB);\r\n      if (mergedUpdate != null) {\r\n        chunks[chunkPath] = mergedUpdate;\r\n      }\r\n    } else {\r\n      chunks[chunkPath] = chunkUpdateA;\r\n    }\r\n  }\r\n\r\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<[ChunkPath, ChunkUpdate]>) {\r\n    if (chunks[chunkPath] == null) {\r\n      chunks[chunkPath] = chunkUpdateB;\r\n    }\r\n  }\r\n\r\n  return chunks;\r\n}\r\n\r\nfunction mergeChunkUpdates(\r\n  updateA: ChunkUpdate,\r\n  updateB: ChunkUpdate\r\n): ChunkUpdate | undefined {\r\n  if (\r\n    (updateA.type === \"added\" && updateB.type === \"deleted\") ||\r\n    (updateA.type === \"deleted\" && updateB.type === \"added\")\r\n  ) {\r\n    return undefined;\r\n  }\r\n\r\n  if (updateA.type === \"partial\") {\r\n    invariant(updateA.instruction, \"Partial updates are unsupported\");\r\n  }\r\n\r\n  if (updateB.type === \"partial\") {\r\n    invariant(updateB.instruction, \"Partial updates are unsupported\");\r\n  }\r\n\r\n  return undefined;\r\n}\r\n\r\nfunction mergeChunkListEcmascriptMergedUpdates(\r\n  mergedA: EcmascriptMergedUpdate,\r\n  mergedB: EcmascriptMergedUpdate\r\n): EcmascriptMergedUpdate {\r\n  const entries = mergeEcmascriptChunkEntries(mergedA.entries, mergedB.entries);\r\n  const chunks = mergeEcmascriptChunksUpdates(mergedA.chunks, mergedB.chunks);\r\n\r\n  return {\r\n    type: \"EcmascriptMergedUpdate\",\r\n    entries,\r\n    chunks,\r\n  };\r\n}\r\n\r\nfunction mergeEcmascriptChunkEntries(\r\n  entriesA: Record<ModuleId, EcmascriptModuleEntry> | undefined,\r\n  entriesB: Record<ModuleId, EcmascriptModuleEntry> | undefined\r\n): Record<ModuleId, EcmascriptModuleEntry> {\r\n  return { ...entriesA, ...entriesB };\r\n}\r\n\r\nfunction mergeEcmascriptChunksUpdates(\r\n  chunksA: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined,\r\n  chunksB: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined\r\n): Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined {\r\n  if (chunksA == null) {\r\n    return chunksB;\r\n  }\r\n\r\n  if (chunksB == null) {\r\n    return chunksA;\r\n  }\r\n\r\n  const chunks: Record<ChunkPath, EcmascriptMergedChunkUpdate> = {};\r\n\r\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<[ChunkPath, EcmascriptMergedChunkUpdate]>) {\r\n    const chunkUpdateB = chunksB[chunkPath];\r\n    if (chunkUpdateB != null) {\r\n      const mergedUpdate = mergeEcmascriptChunkUpdates(\r\n        chunkUpdateA,\r\n        chunkUpdateB\r\n      );\r\n      if (mergedUpdate != null) {\r\n        chunks[chunkPath] = mergedUpdate;\r\n      }\r\n    } else {\r\n      chunks[chunkPath] = chunkUpdateA;\r\n    }\r\n  }\r\n\r\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<[ChunkPath, EcmascriptMergedChunkUpdate]>) {\r\n    if (chunks[chunkPath] == null) {\r\n      chunks[chunkPath] = chunkUpdateB;\r\n    }\r\n  }\r\n\r\n  if (Object.keys(chunks).length === 0) {\r\n    return undefined;\r\n  }\r\n\r\n  return chunks;\r\n}\r\n\r\nfunction mergeEcmascriptChunkUpdates(\r\n  updateA: EcmascriptMergedChunkUpdate,\r\n  updateB: EcmascriptMergedChunkUpdate\r\n): EcmascriptMergedChunkUpdate | undefined {\r\n  if (updateA.type === \"added\" && updateB.type === \"deleted\") {\r\n    // These two completely cancel each other out.\r\n    return undefined;\r\n  }\r\n\r\n  if (updateA.type === \"deleted\" && updateB.type === \"added\") {\r\n    const added = [];\r\n    const deleted = [];\r\n    const deletedModules = new Set(updateA.modules ?? []);\r\n    const addedModules = new Set(updateB.modules ?? []);\r\n\r\n    for (const moduleId of addedModules) {\r\n      if (!deletedModules.has(moduleId)) {\r\n        added.push(moduleId);\r\n      }\r\n    }\r\n\r\n    for (const moduleId of deletedModules) {\r\n      if (!addedModules.has(moduleId)) {\r\n        deleted.push(moduleId);\r\n      }\r\n    }\r\n\r\n    if (added.length === 0 && deleted.length === 0) {\r\n      return undefined;\r\n    }\r\n\r\n    return {\r\n      type: \"partial\",\r\n      added,\r\n      deleted,\r\n    };\r\n  }\r\n\r\n  if (updateA.type === \"partial\" && updateB.type === \"partial\") {\r\n    const added = new Set([...(updateA.added ?? []), ...(updateB.added ?? [])]);\r\n    const deleted = new Set([\r\n      ...(updateA.deleted ?? []),\r\n      ...(updateB.deleted ?? []),\r\n    ]);\r\n\r\n    if (updateB.added != null) {\r\n      for (const moduleId of updateB.added) {\r\n        deleted.delete(moduleId);\r\n      }\r\n    }\r\n\r\n    if (updateB.deleted != null) {\r\n      for (const moduleId of updateB.deleted) {\r\n        added.delete(moduleId);\r\n      }\r\n    }\r\n\r\n    return {\r\n      type: \"partial\",\r\n      added: [...added],\r\n      deleted: [...deleted],\r\n    };\r\n  }\r\n\r\n  if (updateA.type === \"added\" && updateB.type === \"partial\") {\r\n    const modules = new Set([\r\n      ...(updateA.modules ?? []),\r\n      ...(updateB.added ?? []),\r\n    ]);\r\n\r\n    for (const moduleId of updateB.deleted ?? []) {\r\n      modules.delete(moduleId);\r\n    }\r\n\r\n    return {\r\n      type: \"added\",\r\n      modules: [...modules],\r\n    };\r\n  }\r\n\r\n  if (updateA.type === \"partial\" && updateB.type === \"deleted\") {\r\n    // We could eagerly return `updateB` here, but this would potentially be\r\n    // incorrect if `updateA` has added modules.\r\n\r\n    const modules = new Set(updateB.modules ?? []);\r\n\r\n    if (updateA.added != null) {\r\n      for (const moduleId of updateA.added) {\r\n        modules.delete(moduleId);\r\n      }\r\n    }\r\n\r\n    return {\r\n      type: \"deleted\",\r\n      modules: [...modules],\r\n    };\r\n  }\r\n\r\n  // Any other update combination is invalid.\r\n\r\n  return undefined;\r\n}\r\n\r\nfunction invariant(_: never, message: string): never {\r\n  throw new Error(`Invariant: ${message}`);\r\n}\r\n\r\nconst CRITICAL = [\"bug\", \"error\", \"fatal\"];\r\n\r\nfunction compareByList(list: any[], a: any, b: any) {\r\n  const aI = list.indexOf(a) + 1 || list.length;\r\n  const bI = list.indexOf(b) + 1 || list.length;\r\n  return aI - bI;\r\n}\r\n\r\nconst chunksWithIssues: Map<ResourceKey, Issue[]> = new Map();\r\n\r\nfunction emitIssues() {\r\n  const issues = [];\r\n  const deduplicationSet = new Set();\r\n\r\n  for (const [_, chunkIssues] of chunksWithIssues) {\r\n    for (const chunkIssue of chunkIssues) {\r\n      if (deduplicationSet.has(chunkIssue.formatted)) continue;\r\n\r\n      issues.push(chunkIssue);\r\n      deduplicationSet.add(chunkIssue.formatted);\r\n    }\r\n  }\r\n\r\n  sortIssues(issues);\r\n\r\n  hooks.issues(issues);\r\n}\r\n\r\nfunction handleIssues(msg: ServerMessage): boolean {\r\n  const key = resourceKey(msg.resource);\r\n  let hasCriticalIssues = false;\r\n\r\n  for (const issue of msg.issues) {\r\n    if (CRITICAL.includes(issue.severity)) {\r\n      hasCriticalIssues = true;\r\n    }\r\n  }\r\n\r\n  if (msg.issues.length > 0) {\r\n    chunksWithIssues.set(key, msg.issues);\r\n  } else if (chunksWithIssues.has(key)) {\r\n    chunksWithIssues.delete(key);\r\n  }\r\n\r\n  emitIssues();\r\n\r\n  return hasCriticalIssues;\r\n}\r\n\r\nconst SEVERITY_ORDER = [\"bug\", \"fatal\", \"error\", \"warning\", \"info\", \"log\"];\r\nconst CATEGORY_ORDER = [\r\n  \"parse\",\r\n  \"resolve\",\r\n  \"code generation\",\r\n  \"rendering\",\r\n  \"typescript\",\r\n  \"other\",\r\n];\r\n\r\nfunction sortIssues(issues: Issue[]) {\r\n  issues.sort((a, b) => {\r\n    const first = compareByList(SEVERITY_ORDER, a.severity, b.severity);\r\n    if (first !== 0) return first;\r\n    return compareByList(CATEGORY_ORDER, a.category, b.category);\r\n  });\r\n}\r\n\r\nconst hooks = {\r\n  beforeRefresh: () => {},\r\n  refresh: () => {},\r\n  buildOk: () => {},\r\n  issues: (_issues: Issue[]) => {},\r\n};\r\n\r\nexport function setHooks(newHooks: typeof hooks) {\r\n  Object.assign(hooks, newHooks);\r\n}\r\n\r\nfunction handleSocketMessage(msg: ServerMessage) {\r\n  sortIssues(msg.issues);\r\n\r\n  handleIssues(msg);\r\n\r\n  switch (msg.type) {\r\n    case \"issues\":\r\n      // issues are already handled\r\n      break;\r\n    case \"partial\":\r\n      // aggregate updates\r\n      aggregateUpdates(msg);\r\n      break;\r\n    default:\r\n      // run single update\r\n      const runHooks = chunkListsWithPendingUpdates.size === 0;\r\n      if (runHooks) hooks.beforeRefresh();\r\n      triggerUpdate(msg);\r\n      if (runHooks) finalizeUpdate();\r\n      break;\r\n  }\r\n}\r\n\r\nfunction finalizeUpdate() {\r\n  hooks.refresh();\r\n  hooks.buildOk();\r\n\r\n  // This is used by the Next.js integration test suite to notify it when HMR\r\n  // updates have been completed.\r\n  // TODO: Only run this in test environments (gate by `process.env.__NEXT_TEST_MODE`)\r\n  if (globalThis.__NEXT_HMR_CB) {\r\n    globalThis.__NEXT_HMR_CB();\r\n    globalThis.__NEXT_HMR_CB = null;\r\n  }\r\n}\r\n\r\nfunction subscribeToChunkUpdate(\r\n  chunkListPath: ChunkListPath,\r\n  sendMessage: SendMessage,\r\n  callback: UpdateCallback\r\n): () => void {\r\n  return subscribeToUpdate(\r\n    {\r\n      path: chunkListPath,\r\n    },\r\n    sendMessage,\r\n    callback\r\n  );\r\n}\r\n\r\nexport function subscribeToUpdate(\r\n  resource: ResourceIdentifier,\r\n  sendMessage: SendMessage,\r\n  callback: UpdateCallback\r\n) {\r\n  const key = resourceKey(resource);\r\n  let callbackSet: UpdateCallbackSet;\r\n  const existingCallbackSet = updateCallbackSets.get(key);\r\n  if (!existingCallbackSet) {\r\n    callbackSet = {\r\n      callbacks: new Set([callback]),\r\n      unsubscribe: subscribeToUpdates(sendMessage, resource),\r\n    };\r\n    updateCallbackSets.set(key, callbackSet);\r\n  } else {\r\n    existingCallbackSet.callbacks.add(callback);\r\n    callbackSet = existingCallbackSet;\r\n  }\r\n\r\n  return () => {\r\n    callbackSet.callbacks.delete(callback);\r\n\r\n    if (callbackSet.callbacks.size === 0) {\r\n      callbackSet.unsubscribe();\r\n      updateCallbackSets.delete(key);\r\n    }\r\n  };\r\n}\r\n\r\nfunction triggerUpdate(msg: ServerMessage) {\r\n  const key = resourceKey(msg.resource);\r\n  const callbackSet = updateCallbackSets.get(key);\r\n  if (!callbackSet) {\r\n    return;\r\n  }\r\n\r\n  for (const callback of callbackSet.callbacks) {\r\n    callback(msg);\r\n  }\r\n\r\n  if (msg.type === \"notFound\") {\r\n    // This indicates that the resource which we subscribed to either does not exist or\r\n    // has been deleted. In either case, we should clear all update callbacks, so if a\r\n    // new subscription is created for the same resource, it will send a new \"subscribe\"\r\n    // message to the server.\r\n    // No need to send an \"unsubscribe\" message to the server, it will have already\r\n    // dropped the update stream before sending the \"notFound\" message.\r\n    updateCallbackSets.delete(key);\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D,4DAA4D;AAC5D,6DAA6D;AAC7D,6DAA6D;;;;;;AAmBtD,SAAS,QAAQ,EACtB,kBAAkB,EAClB,WAAW,EACX,gBAAgB,QAAQ,KAAK,EACf;IACd,mBAAmB,CAAC;QAClB,OAAQ,IAAI,IAAI;YACd,KAAK;gBACH,sBAAsB;gBACtB;YACF;gBACE,IAAI;oBACF,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,GAAG;wBAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,IAAK;4BACxC,oBAAoB,IAAI,IAAI,CAAC,EAAE;wBACjC;oBACF,OAAO;wBACL,oBAAoB,IAAI,IAAI;oBAC9B;oBACA;gBACF,EAAE,OAAO,GAAY;oBACnB,QAAQ,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;oBAEJ,cAAc;oBACd,SAAS,MAAM;gBACjB;gBACA;QACJ;IACF;IAEA,MAAM,SAAS,WAAW,gCAAgC;IAC1D,IAAI,UAAU,QAAQ,CAAC,MAAM,OAAO,CAAC,SAAS;QAC5C,MAAM,IAAI,MAAM;IAClB;IACA,WAAW,gCAAgC,GAAG;QAC5C,MAAM,CAAC,CAAC,WAAW,SAA0C;YAC3D,uBAAuB,WAAW,aAAa;QACjD;IACF;IAEA,IAAI,MAAM,OAAO,CAAC,SAAS;QACzB,KAAK,MAAM,CAAC,WAAW,SAAS,IAAI,OAAQ;YAC1C,uBAAuB,WAAW,aAAa;QACjD;IACF;AACF;AAOA,MAAM,qBAA0D,IAAI;AAEpE,SAAS,SAAS,WAAwB,EAAE,OAAsB;IAChE,YAAY,KAAK,SAAS,CAAC;AAC7B;AAIA,SAAS,YAAY,QAA4B;IAC/C,OAAO,KAAK,SAAS,CAAC;QACpB,MAAM,SAAS,IAAI;QACnB,SAAS,SAAS,OAAO,IAAI;IAC/B;AACF;AAEA,SAAS,mBACP,WAAwB,EACxB,QAA4B;IAE5B,SAAS,aAAa;QACpB,MAAM;QACN,GAAG,QAAQ;IACb;IAEA,OAAO;QACL,SAAS,aAAa;YACpB,MAAM;YACN,GAAG,QAAQ;QACb;IACF;AACF;AAEA,SAAS,sBAAsB,WAAwB;IACrD,KAAK,MAAM,OAAO,mBAAmB,IAAI,GAAI;QAC3C,mBAAmB,aAAa,KAAK,KAAK,CAAC;IAC7C;AACF;AAEA,iEAAiE;AACjE,MAAM,+BACJ,IAAI;AAEN,SAAS,iBAAiB,GAAyB;IACjD,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,aAAa,6BAA6B,GAAG,CAAC;IAElD,IAAI,YAAY;QACd,WAAW,WAAW,GAAG,sBACvB,WAAW,WAAW,EACtB,IAAI,WAAW;IAEnB,OAAO;QACL,6BAA6B,GAAG,CAAC,KAAK;IACxC;AACF;AAEA,SAAS;IACP,IAAI,6BAA6B,IAAI,KAAK,GAAG;IAC7C,MAAM,aAAa;IACnB,KAAK,MAAM,OAAO,6BAA6B,MAAM,GAAI;QACvD,cAAc;IAChB;IACA,6BAA6B,KAAK;IAClC;AACF;AAEA,SAAS,sBACP,OAAwB,EACxB,OAAwB;IAExB,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,SAAS,qBAAqB,QAAQ,MAAM,EAAE,QAAQ,MAAM;QAC9D;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,wEAAwE;YACxE,0BAA0B;YAC1B,0EAA0E;YAC1E,sCAAsC;YACtC,IAAI,SAAS,QAAQ,MAAM,CAAC,EAAE;YAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,SAAS;gBAAC;aAAO;QACnB;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,qBACP,OAAuC,EACvC,OAAuC;IAEvC,MAAM,SAAyC,CAAC;IAEhD,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6C;QAClG,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,kBAAkB,cAAc;YACrD,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6C;QAClG,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,kBACP,OAAoB,EACpB,OAAoB;IAEpB,IACE,AAAC,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,aAC7C,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAChD;QACA,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,OAAO;AACT;AAEA,SAAS,sCACP,OAA+B,EAC/B,OAA+B;IAE/B,MAAM,UAAU,4BAA4B,QAAQ,OAAO,EAAE,QAAQ,OAAO;IAC5E,MAAM,SAAS,6BAA6B,QAAQ,MAAM,EAAE,QAAQ,MAAM;IAE1E,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,4BACP,QAA6D,EAC7D,QAA6D;IAE7D,OAAO;QAAE,GAAG,QAAQ;QAAE,GAAG,QAAQ;IAAC;AACpC;AAEA,SAAS,6BACP,OAAmE,EACnE,OAAmE;IAEnE,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,MAAM,SAAyD,CAAC;IAEhE,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6D;QAClH,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,4BACnB,cACA;YAEF,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6D;QAClH,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,4BACP,OAAoC,EACpC,OAAoC;IAEpC,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,8CAA8C;QAC9C,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAAS;QAC1D,MAAM,QAAQ,EAAE;QAChB,MAAM,UAAU,EAAE;QAClB,MAAM,iBAAiB,IAAI,IAAI,QAAQ,OAAO,IAAI,EAAE;QACpD,MAAM,eAAe,IAAI,IAAI,QAAQ,OAAO,IAAI,EAAE;QAElD,KAAK,MAAM,YAAY,aAAc;YACnC,IAAI,CAAC,eAAe,GAAG,CAAC,WAAW;gBACjC,MAAM,IAAI,CAAC;YACb;QACF;QAEA,KAAK,MAAM,YAAY,eAAgB;YACrC,IAAI,CAAC,aAAa,GAAG,CAAC,WAAW;gBAC/B,QAAQ,IAAI,CAAC;YACf;QACF;QAEA,IAAI,MAAM,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;YAC9C,OAAO;QACT;QAEA,OAAO;YACL,MAAM;YACN;YACA;QACF;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;QAC5D,MAAM,QAAQ,IAAI,IAAI;eAAK,QAAQ,KAAK,IAAI,EAAE;eAAO,QAAQ,KAAK,IAAI,EAAE;SAAE;QAC1E,MAAM,UAAU,IAAI,IAAI;eAClB,QAAQ,OAAO,IAAI,EAAE;eACrB,QAAQ,OAAO,IAAI,EAAE;SAC1B;QAED,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,IAAI,QAAQ,OAAO,IAAI,MAAM;YAC3B,KAAK,MAAM,YAAY,QAAQ,OAAO,CAAE;gBACtC,MAAM,MAAM,CAAC;YACf;QACF;QAEA,OAAO;YACL,MAAM;YACN,OAAO;mBAAI;aAAM;YACjB,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,MAAM,UAAU,IAAI,IAAI;eAClB,QAAQ,OAAO,IAAI,EAAE;eACrB,QAAQ,KAAK,IAAI,EAAE;SACxB;QAED,KAAK,MAAM,YAAY,QAAQ,OAAO,IAAI,EAAE,CAAE;YAC5C,QAAQ,MAAM,CAAC;QACjB;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;QAC5D,wEAAwE;QACxE,4CAA4C;QAE5C,MAAM,UAAU,IAAI,IAAI,QAAQ,OAAO,IAAI,EAAE;QAE7C,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,2CAA2C;IAE3C,OAAO;AACT;AAEA,SAAS,UAAU,CAAQ,EAAE,OAAe;IAC1C,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS;AACzC;AAEA,MAAM,WAAW;IAAC;IAAO;IAAS;CAAQ;AAE1C,SAAS,cAAc,IAAW,EAAE,CAAM,EAAE,CAAM;IAChD,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,OAAO,KAAK;AACd;AAEA,MAAM,mBAA8C,IAAI;AAExD,SAAS;IACP,MAAM,SAAS,EAAE;IACjB,MAAM,mBAAmB,IAAI;IAE7B,KAAK,MAAM,CAAC,GAAG,YAAY,IAAI,iBAAkB;QAC/C,KAAK,MAAM,cAAc,YAAa;YACpC,IAAI,iBAAiB,GAAG,CAAC,WAAW,SAAS,GAAG;YAEhD,OAAO,IAAI,CAAC;YACZ,iBAAiB,GAAG,CAAC,WAAW,SAAS;QAC3C;IACF;IAEA,WAAW;IAEX,MAAM,MAAM,CAAC;AACf;AAEA,SAAS,aAAa,GAAkB;IACtC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,oBAAoB;IAExB,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;QAC9B,IAAI,SAAS,QAAQ,CAAC,MAAM,QAAQ,GAAG;YACrC,oBAAoB;QACtB;IACF;IAEA,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG;QACzB,iBAAiB,GAAG,CAAC,KAAK,IAAI,MAAM;IACtC,OAAO,IAAI,iBAAiB,GAAG,CAAC,MAAM;QACpC,iBAAiB,MAAM,CAAC;IAC1B;IAEA;IAEA,OAAO;AACT;AAEA,MAAM,iBAAiB;IAAC;IAAO;IAAS;IAAS;IAAW;IAAQ;CAAM;AAC1E,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAAS,WAAW,MAAe;IACjC,OAAO,IAAI,CAAC,CAAC,GAAG;QACd,MAAM,QAAQ,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;QAClE,IAAI,UAAU,GAAG,OAAO;QACxB,OAAO,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;IAC7D;AACF;AAEA,MAAM,QAAQ;IACZ,eAAe,KAAO;IACtB,SAAS,KAAO;IAChB,SAAS,KAAO;IAChB,QAAQ,CAAC,WAAsB;AACjC;AAEO,SAAS,SAAS,QAAsB;IAC7C,OAAO,MAAM,CAAC,OAAO;AACvB;AAEA,SAAS,oBAAoB,GAAkB;IAC7C,WAAW,IAAI,MAAM;IAErB,aAAa;IAEb,OAAQ,IAAI,IAAI;QACd,KAAK;YAEH;QACF,KAAK;YACH,oBAAoB;YACpB,iBAAiB;YACjB;QACF;YACE,oBAAoB;YACpB,MAAM,WAAW,6BAA6B,IAAI,KAAK;YACvD,IAAI,UAAU,MAAM,aAAa;YACjC,cAAc;YACd,IAAI,UAAU;YACd;IACJ;AACF;AAEA,SAAS;IACP,MAAM,OAAO;IACb,MAAM,OAAO;IAEb,2EAA2E;IAC3E,+BAA+B;IAC/B,oFAAoF;IACpF,IAAI,WAAW,aAAa,EAAE;QAC5B,WAAW,aAAa;QACxB,WAAW,aAAa,GAAG;IAC7B;AACF;AAEA,SAAS,uBACP,aAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,OAAO,kBACL;QACE,MAAM;IACR,GACA,aACA;AAEJ;AAEO,SAAS,kBACd,QAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,MAAM,MAAM,YAAY;IACxB,IAAI;IACJ,MAAM,sBAAsB,mBAAmB,GAAG,CAAC;IACnD,IAAI,CAAC,qBAAqB;QACxB,cAAc;YACZ,WAAW,IAAI,IAAI;gBAAC;aAAS;YAC7B,aAAa,mBAAmB,aAAa;QAC/C;QACA,mBAAmB,GAAG,CAAC,KAAK;IAC9B,OAAO;QACL,oBAAoB,SAAS,CAAC,GAAG,CAAC;QAClC,cAAc;IAChB;IAEA,OAAO;QACL,YAAY,SAAS,CAAC,MAAM,CAAC;QAE7B,IAAI,YAAY,SAAS,CAAC,IAAI,KAAK,GAAG;YACpC,YAAY,WAAW;YACvB,mBAAmB,MAAM,CAAC;QAC5B;IACF;AACF;AAEA,SAAS,cAAc,GAAkB;IACvC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,MAAM,cAAc,mBAAmB,GAAG,CAAC;IAC3C,IAAI,CAAC,aAAa;QAChB;IACF;IAEA,KAAK,MAAM,YAAY,YAAY,SAAS,CAAE;QAC5C,SAAS;IACX;IAEA,IAAI,IAAI,IAAI,KAAK,YAAY;QAC3B,mFAAmF;QACnF,kFAAkF;QAClF,oFAAoF;QACpF,yBAAyB;QACzB,+EAA+E;QAC/E,mEAAmE;QACnE,mBAAmB,MAAM,CAAC;IAC5B;AACF", "ignoreList": [0]}}]}