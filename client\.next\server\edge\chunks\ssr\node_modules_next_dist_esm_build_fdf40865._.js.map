{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/build/output/log.ts"], "sourcesContent": ["import { bold, green, magenta, red, yellow, white } from '../../lib/picocolors'\nimport { LRUCache } from '../../server/lib/lru-cache'\n\nexport const prefixes = {\n  wait: white(bold('○')),\n  error: red(bold('⨯')),\n  warn: yellow(bold('⚠')),\n  ready: '▲', // no color\n  info: white(bold(' ')),\n  event: green(bold('✓')),\n  trace: magenta(bold('»')),\n} as const\n\nconst LOGGING_METHOD = {\n  log: 'log',\n  warn: 'warn',\n  error: 'error',\n} as const\n\nfunction prefixedLog(prefixType: keyof typeof prefixes, ...message: any[]) {\n  if ((message[0] === '' || message[0] === undefined) && message.length === 1) {\n    message.shift()\n  }\n\n  const consoleMethod: keyof typeof LOGGING_METHOD =\n    prefixType in LOGGING_METHOD\n      ? LOGGING_METHOD[prefixType as keyof typeof LOGGING_METHOD]\n      : 'log'\n\n  const prefix = prefixes[prefixType]\n  // If there's no message, don't print the prefix but a new line\n  if (message.length === 0) {\n    console[consoleMethod]('')\n  } else {\n    // Ensure if there's ANSI escape codes it's concatenated into one string.\n    // Chrome DevTool can only handle color if it's in one string.\n    if (message.length === 1 && typeof message[0] === 'string') {\n      console[consoleMethod](' ' + prefix + ' ' + message[0])\n    } else {\n      console[consoleMethod](' ' + prefix, ...message)\n    }\n  }\n}\n\nexport function bootstrap(...message: string[]) {\n  // logging format: ' <prefix> <message>'\n  // e.g. ' ✓ Compiled successfully'\n  // Add spaces to align with the indent of other logs\n  console.log('   ' + message.join(' '))\n}\n\nexport function wait(...message: any[]) {\n  prefixedLog('wait', ...message)\n}\n\nexport function error(...message: any[]) {\n  prefixedLog('error', ...message)\n}\n\nexport function warn(...message: any[]) {\n  prefixedLog('warn', ...message)\n}\n\nexport function ready(...message: any[]) {\n  prefixedLog('ready', ...message)\n}\n\nexport function info(...message: any[]) {\n  prefixedLog('info', ...message)\n}\n\nexport function event(...message: any[]) {\n  prefixedLog('event', ...message)\n}\n\nexport function trace(...message: any[]) {\n  prefixedLog('trace', ...message)\n}\n\nconst warnOnceCache = new LRUCache<string>(10_000, (value) => value.length)\nexport function warnOnce(...message: any[]) {\n  const key = message.join(' ')\n  if (!warnOnceCache.has(key)) {\n    warnOnceCache.set(key, key)\n    warn(...message)\n  }\n}\n"], "names": ["bold", "green", "magenta", "red", "yellow", "white", "L<PERSON><PERSON><PERSON>", "prefixes", "wait", "error", "warn", "ready", "info", "event", "trace", "LOGGING_METHOD", "log", "prefixedLog", "prefixType", "message", "undefined", "length", "shift", "consoleMethod", "prefix", "console", "bootstrap", "join", "warnOnceCache", "value", "warnOnce", "key", "has", "set"], "mappings": ";;;;;;;;;;;;AAAA,SAASA,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,QAAQ,uBAAsB;AAC/E,SAASC,QAAQ,QAAQ,6BAA4B;;;AAE9C,MAAMC,WAAW;IACtBC,2KAAMH,QAAAA,uKAAML,OAAAA,EAAK;IACjBS,4KAAON,MAAAA,MAAIH,wKAAAA,EAAK;IAChBU,2KAAMN,SAAAA,uKAAOJ,OAAAA,EAAK;IAClBW,OAAO;IACPC,MAAMP,6KAAAA,uKAAML,OAAAA,EAAK;IACjBa,4KAAOZ,QAAAA,GAAMD,2KAAAA,EAAK;IAClBc,4KAAOZ,UAAAA,uKAAQF,OAAAA,EAAK;AACtB,EAAU;AAEV,MAAMe,iBAAiB;IACrBC,KAAK;IACLN,MAAM;IACND,OAAO;AACT;AAEA,SAASQ,YAAYC,UAAiC,EAAE,GAAGC,OAAc;IACvE,IAAKA,CAAAA,OAAO,CAAC,EAAE,KAAK,MAAMA,OAAO,CAAC,EAAE,KAAKC,SAAQ,KAAMD,QAAQE,MAAM,KAAK,GAAG;QAC3EF,QAAQG,KAAK;IACf;IAEA,MAAMC,gBACJL,cAAcH,iBACVA,cAAc,CAACG,WAA0C,GACzD;IAEN,MAAMM,SAASjB,QAAQ,CAACW,WAAW;IACnC,+DAA+D;IAC/D,IAAIC,QAAQE,MAAM,KAAK,GAAG;QACxBI,OAAO,CAACF,cAAc,CAAC;IACzB,OAAO;QACL,yEAAyE;QACzE,8DAA8D;QAC9D,IAAIJ,QAAQE,MAAM,KAAK,KAAK,OAAOF,OAAO,CAAC,EAAE,KAAK,UAAU;YAC1DM,OAAO,CAACF,cAAc,CAAC,MAAMC,SAAS,MAAML,OAAO,CAAC,EAAE;QACxD,OAAO;YACLM,OAAO,CAACF,cAAc,CAAC,MAAMC,WAAWL;QAC1C;IACF;AACF;AAEO,SAASO,UAAU,GAAGP,OAAiB;IAC5C,wCAAwC;IACxC,kCAAkC;IAClC,oDAAoD;IACpDM,QAAQT,GAAG,CAAC,QAAQG,QAAQQ,IAAI,CAAC;AACnC;AAEO,SAASnB,KAAK,GAAGW,OAAc;IACpCF,YAAY,WAAWE;AACzB;AAEO,SAASV,MAAM,GAAGU,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEO,SAAST,KAAK,GAAGS,OAAc;IACpCF,YAAY,WAAWE;AACzB;AAEO,SAASR,MAAM,GAAGQ,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEO,SAASP,KAAK,GAAGO,OAAc;IACpCF,YAAY,WAAWE;AACzB;AAEO,SAASN,MAAM,GAAGM,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEO,SAASL,MAAM,GAAGK,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEA,MAAMS,gBAAgB,iLAAItB,WAAAA,CAAiB,OAAQ,CAACuB,QAAUA,MAAMR,MAAM;AACnE,SAASS,SAAS,GAAGX,OAAc;IACxC,MAAMY,MAAMZ,QAAQQ,IAAI,CAAC;IACzB,IAAI,CAACC,cAAcI,GAAG,CAACD,MAAM;QAC3BH,cAAcK,GAAG,CAACF,KAAKA;QACvBrB,QAAQS;IACV;AACF", "ignoreList": [0]}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/build/webpack/loaders/next-edge-ssr-loader/render.ts"], "sourcesContent": ["import type { NextConfigComplete } from '../../../../server/config-shared'\n\nimport type { DocumentType, AppType } from '../../../../shared/lib/utils'\nimport type { BuildManifest } from '../../../../server/get-page-files'\nimport type {\n  DynamicCssManifest,\n  ReactLoadableManifest,\n} from '../../../../server/load-components'\nimport type { ClientReferenceManifest } from '../../plugins/flight-manifest-plugin'\nimport type { NextFontManifest } from '../../plugins/next-font-manifest-plugin'\nimport type { NextFetchEvent } from '../../../../server/web/spec-extension/fetch-event'\n\nimport WebServer from '../../../../server/web-server'\nimport {\n  WebNextRequest,\n  WebNextResponse,\n} from '../../../../server/base-http/web'\nimport { SERVER_RUNTIME } from '../../../../lib/constants'\nimport type { ManifestRewriteRoute } from '../../..'\nimport { normalizeAppPath } from '../../../../shared/lib/router/utils/app-paths'\nimport type { SizeLimit } from '../../../../types'\nimport { internal_getCurrentFunctionWaitUntil } from '../../../../server/web/internal-edge-wait-until'\nimport type { PAGE_TYPES } from '../../../../lib/page-types'\nimport type { NextRequestHint } from '../../../../server/web/adapter'\n\nexport function getRender({\n  dev,\n  page,\n  appMod,\n  pageMod,\n  errorMod,\n  error500Mod,\n  pagesType,\n  Document,\n  buildManifest,\n  reactLoadableManifest,\n  dynamicCssManifest,\n  interceptionRouteRewrites,\n  renderToHTML,\n  clientReferenceManifest,\n  subresourceIntegrityManifest,\n  serverActionsManifest,\n  serverActions,\n  config,\n  buildId,\n  nextFontManifest,\n  incrementalCacheHandler,\n}: {\n  pagesType: PAGE_TYPES\n  dev: boolean\n  page: string\n  appMod: any\n  pageMod: any\n  errorMod: any\n  error500Mod: any\n  renderToHTML?: any\n  Document: DocumentType\n  buildManifest: BuildManifest\n  reactLoadableManifest: ReactLoadableManifest\n  dynamicCssManifest?: DynamicCssManifest\n  subresourceIntegrityManifest?: Record<string, string>\n  interceptionRouteRewrites?: ManifestRewriteRoute[]\n  clientReferenceManifest?: ClientReferenceManifest\n  serverActionsManifest?: any\n  serverActions?: {\n    bodySizeLimit?: SizeLimit\n    allowedOrigins?: string[]\n  }\n  config: NextConfigComplete\n  buildId: string\n  nextFontManifest: NextFontManifest\n  incrementalCacheHandler?: any\n}) {\n  const isAppPath = pagesType === 'app'\n  const baseLoadComponentResult = {\n    dev,\n    buildManifest,\n    reactLoadableManifest,\n    dynamicCssManifest,\n    subresourceIntegrityManifest,\n    Document,\n    App: appMod?.default as AppType,\n    clientReferenceManifest,\n  }\n\n  const server = new WebServer({\n    dev,\n    buildId,\n    conf: config,\n    minimalMode: true,\n    webServerConfig: {\n      page,\n      pathname: isAppPath ? normalizeAppPath(page) : page,\n      pagesType,\n      interceptionRouteRewrites,\n      extendRenderOpts: {\n        runtime: SERVER_RUNTIME.experimentalEdge,\n        supportsDynamicResponse: true,\n        disableOptimizedLoading: true,\n        serverActionsManifest,\n        serverActions,\n        nextFontManifest,\n      },\n      renderToHTML,\n      incrementalCacheHandler,\n      loadComponent: async (inputPage) => {\n        if (inputPage === page) {\n          return {\n            ...baseLoadComponentResult,\n            Component: pageMod.default,\n            pageConfig: pageMod.config || {},\n            getStaticProps: pageMod.getStaticProps,\n            getServerSideProps: pageMod.getServerSideProps,\n            getStaticPaths: pageMod.getStaticPaths,\n            ComponentMod: pageMod,\n            isAppPath: !!pageMod.__next_app__,\n            page: inputPage,\n            routeModule: pageMod.routeModule,\n          }\n        }\n\n        // If there is a custom 500 page, we need to handle it separately.\n        if (inputPage === '/500' && error500Mod) {\n          return {\n            ...baseLoadComponentResult,\n            Component: error500Mod.default,\n            pageConfig: error500Mod.config || {},\n            getStaticProps: error500Mod.getStaticProps,\n            getServerSideProps: error500Mod.getServerSideProps,\n            getStaticPaths: error500Mod.getStaticPaths,\n            ComponentMod: error500Mod,\n            page: inputPage,\n            routeModule: error500Mod.routeModule,\n          }\n        }\n\n        if (inputPage === '/_error') {\n          return {\n            ...baseLoadComponentResult,\n            Component: errorMod.default,\n            pageConfig: errorMod.config || {},\n            getStaticProps: errorMod.getStaticProps,\n            getServerSideProps: errorMod.getServerSideProps,\n            getStaticPaths: errorMod.getStaticPaths,\n            ComponentMod: errorMod,\n            page: inputPage,\n            routeModule: errorMod.routeModule,\n          }\n        }\n\n        return null\n      },\n    },\n  })\n\n  const handler = server.getRequestHandler()\n\n  return async function render(\n    request: NextRequestHint,\n    event?: NextFetchEvent\n  ) {\n    const extendedReq = new WebNextRequest(request)\n    const extendedRes = new WebNextResponse(undefined)\n\n    handler(extendedReq, extendedRes)\n    const result = await extendedRes.toResponse()\n    request.fetchMetrics = extendedReq.fetchMetrics\n\n    if (event?.waitUntil) {\n      // TODO(after):\n      // remove `internal_runWithWaitUntil` and the `internal-edge-wait-until` module\n      // when consumers switch to `after`.\n      const waitUntilPromise = internal_getCurrentFunctionWaitUntil()\n      if (waitUntilPromise) {\n        event.waitUntil(waitUntilPromise)\n      }\n    }\n\n    return result\n  }\n}\n"], "names": ["WebServer", "WebNextRequest", "WebNextResponse", "SERVER_RUNTIME", "normalizeAppPath", "internal_getCurrentFunctionWaitUntil", "getRender", "dev", "page", "appMod", "pageMod", "errorMod", "error500Mod", "pagesType", "Document", "buildManifest", "reactLoadableManifest", "dynamicCssManifest", "interceptionRouteRewrites", "renderToHTML", "clientReferenceManifest", "subresourceIntegrityManifest", "serverActionsManifest", "serverActions", "config", "buildId", "nextFontManifest", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "isAppPath", "baseLoadComponentResult", "App", "default", "server", "conf", "minimalMode", "webServerConfig", "pathname", "extendRenderOpts", "runtime", "experimentalEdge", "supportsDynamicResponse", "disableOptimizedLoading", "loadComponent", "inputPage", "Component", "pageConfig", "getStaticProps", "getServerSideProps", "getStaticPaths", "ComponentMod", "__next_app__", "routeModule", "handler", "getRequestHandler", "render", "request", "event", "extendedReq", "extendedRes", "undefined", "result", "toResponse", "fetchMetrics", "waitUntil", "waitUntilPromise"], "mappings": ";;;AAYA,OAAOA,eAAe,gCAA+B;AACrD,SACEC,cAAc,EACdC,eAAe,QACV,mCAAkC;AACzC,SAASC,cAAc,QAAQ,4BAA2B;AAE1D,SAASC,gBAAgB,QAAQ,gDAA+C;AAEhF,SAASC,oCAAoC,QAAQ,kDAAiD;;;;;;AAI/F,SAASC,UAAU,EACxBC,GAAG,EACHC,IAAI,EACJC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,WAAW,EACXC,SAAS,EACTC,QAAQ,EACRC,aAAa,EACbC,qBAAqB,EACrBC,kBAAkB,EAClBC,yBAAyB,EACzBC,YAAY,EACZC,uBAAuB,EACvBC,4BAA4B,EAC5BC,qBAAqB,EACrBC,aAAa,EACbC,MAAM,EACNC,OAAO,EACPC,gBAAgB,EAChBC,uBAAuB,EA0BxB;IACC,MAAMC,YAAYf,cAAc;IAChC,MAAMgB,0BAA0B;QAC9BtB;QACAQ;QACAC;QACAC;QACAI;QACAP;QACAgB,GAAG,EAAErB,UAAAA,OAAAA,KAAAA,IAAAA,OAAQsB,OAAO;QACpBX;IACF;IAEA,MAAMY,SAAS,2KAAIhC,UAAAA,CAAU;QAC3BO;QACAkB;QACAQ,MAAMT;QACNU,aAAa;QACbC,iBAAiB;YACf3B;YACA4B,UAAUR,gNAAYxB,mBAAAA,EAAiBI,QAAQA;YAC/CK;YACAK;YACAmB,kBAAkB;gBAChBC,wKAASnC,kBAAAA,CAAeoC,gBAAgB;gBACxCC,yBAAyB;gBACzBC,yBAAyB;gBACzBnB;gBACAC;gBACAG;YACF;YACAP;YACAQ;YACAe,eAAe,OAAOC;gBACpB,IAAIA,cAAcnC,MAAM;oBACtB,OAAO;wBACL,GAAGqB,uBAAuB;wBAC1Be,WAAWlC,QAAQqB,OAAO;wBAC1Bc,YAAYnC,QAAQc,MAAM,IAAI,CAAC;wBAC/BsB,gBAAgBpC,QAAQoC,cAAc;wBACtCC,oBAAoBrC,QAAQqC,kBAAkB;wBAC9CC,gBAAgBtC,QAAQsC,cAAc;wBACtCC,cAAcvC;wBACdkB,WAAW,CAAC,CAAClB,QAAQwC,YAAY;wBACjC1C,MAAMmC;wBACNQ,aAAazC,QAAQyC,WAAW;oBAClC;gBACF;gBAEA,kEAAkE;gBAClE,IAAIR,cAAc,UAAU/B,aAAa;oBACvC,OAAO;wBACL,GAAGiB,uBAAuB;wBAC1Be,WAAWhC,YAAYmB,OAAO;wBAC9Bc,YAAYjC,YAAYY,MAAM,IAAI,CAAC;wBACnCsB,gBAAgBlC,YAAYkC,cAAc;wBAC1CC,oBAAoBnC,YAAYmC,kBAAkB;wBAClDC,gBAAgBpC,YAAYoC,cAAc;wBAC1CC,cAAcrC;wBACdJ,MAAMmC;wBACNQ,aAAavC,YAAYuC,WAAW;oBACtC;gBACF;gBAEA,IAAIR,cAAc,WAAW;oBAC3B,OAAO;wBACL,GAAGd,uBAAuB;wBAC1Be,WAAWjC,SAASoB,OAAO;wBAC3Bc,YAAYlC,SAASa,MAAM,IAAI,CAAC;wBAChCsB,gBAAgBnC,SAASmC,cAAc;wBACvCC,oBAAoBpC,SAASoC,kBAAkB;wBAC/CC,gBAAgBrC,SAASqC,cAAc;wBACvCC,cAActC;wBACdH,MAAMmC;wBACNQ,aAAaxC,SAASwC,WAAW;oBACnC;gBACF;gBAEA,OAAO;YACT;QACF;IACF;IAEA,MAAMC,UAAUpB,OAAOqB,iBAAiB;IAExC,OAAO,eAAeC,OACpBC,OAAwB,EACxBC,KAAsB;QAEtB,MAAMC,cAAc,IAAIxD,8LAAAA,CAAesD;QACvC,MAAMG,cAAc,iLAAIxD,kBAAAA,CAAgByD;QAExCP,QAAQK,aAAaC;QACrB,MAAME,SAAS,MAAMF,YAAYG,UAAU;QAC3CN,QAAQO,YAAY,GAAGL,YAAYK,YAAY;QAE/C,IAAIN,SAAAA,OAAAA,KAAAA,IAAAA,MAAOO,SAAS,EAAE;YACpB,eAAe;YACf,+EAA+E;YAC/E,oCAAoC;YACpC,MAAMC,yNAAmB3D,uCAAAA;YACzB,IAAI2D,kBAAkB;gBACpBR,MAAMO,SAAS,CAACC;YAClB;QACF;QAEA,OAAOJ;IACT;AACF", "ignoreList": [0]}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/build/output/log.ts"], "sourcesContent": ["import { bold, green, magenta, red, yellow, white } from '../../lib/picocolors'\nimport { LRUCache } from '../../server/lib/lru-cache'\n\nexport const prefixes = {\n  wait: white(bold('○')),\n  error: red(bold('⨯')),\n  warn: yellow(bold('⚠')),\n  ready: '▲', // no color\n  info: white(bold(' ')),\n  event: green(bold('✓')),\n  trace: magenta(bold('»')),\n} as const\n\nconst LOGGING_METHOD = {\n  log: 'log',\n  warn: 'warn',\n  error: 'error',\n} as const\n\nfunction prefixedLog(prefixType: keyof typeof prefixes, ...message: any[]) {\n  if ((message[0] === '' || message[0] === undefined) && message.length === 1) {\n    message.shift()\n  }\n\n  const consoleMethod: keyof typeof LOGGING_METHOD =\n    prefixType in LOGGING_METHOD\n      ? LOGGING_METHOD[prefixType as keyof typeof LOGGING_METHOD]\n      : 'log'\n\n  const prefix = prefixes[prefixType]\n  // If there's no message, don't print the prefix but a new line\n  if (message.length === 0) {\n    console[consoleMethod]('')\n  } else {\n    // Ensure if there's ANSI escape codes it's concatenated into one string.\n    // Chrome DevTool can only handle color if it's in one string.\n    if (message.length === 1 && typeof message[0] === 'string') {\n      console[consoleMethod](' ' + prefix + ' ' + message[0])\n    } else {\n      console[consoleMethod](' ' + prefix, ...message)\n    }\n  }\n}\n\nexport function bootstrap(...message: string[]) {\n  // logging format: ' <prefix> <message>'\n  // e.g. ' ✓ Compiled successfully'\n  // Add spaces to align with the indent of other logs\n  console.log('   ' + message.join(' '))\n}\n\nexport function wait(...message: any[]) {\n  prefixedLog('wait', ...message)\n}\n\nexport function error(...message: any[]) {\n  prefixedLog('error', ...message)\n}\n\nexport function warn(...message: any[]) {\n  prefixedLog('warn', ...message)\n}\n\nexport function ready(...message: any[]) {\n  prefixedLog('ready', ...message)\n}\n\nexport function info(...message: any[]) {\n  prefixedLog('info', ...message)\n}\n\nexport function event(...message: any[]) {\n  prefixedLog('event', ...message)\n}\n\nexport function trace(...message: any[]) {\n  prefixedLog('trace', ...message)\n}\n\nconst warnOnceCache = new LRUCache<string>(10_000, (value) => value.length)\nexport function warnOnce(...message: any[]) {\n  const key = message.join(' ')\n  if (!warnOnceCache.has(key)) {\n    warnOnceCache.set(key, key)\n    warn(...message)\n  }\n}\n"], "names": ["bold", "green", "magenta", "red", "yellow", "white", "L<PERSON><PERSON><PERSON>", "prefixes", "wait", "error", "warn", "ready", "info", "event", "trace", "LOGGING_METHOD", "log", "prefixedLog", "prefixType", "message", "undefined", "length", "shift", "consoleMethod", "prefix", "console", "bootstrap", "join", "warnOnceCache", "value", "warnOnce", "key", "has", "set"], "mappings": ";;;;;;;;;;;;AAAA,SAASA,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,QAAQ,uBAAsB;AAC/E,SAASC,QAAQ,QAAQ,6BAA4B;;;AAE9C,MAAMC,WAAW;IACtBC,2KAAMH,QAAAA,uKAAML,OAAAA,EAAK;IACjBS,4KAAON,MAAAA,MAAIH,wKAAAA,EAAK;IAChBU,2KAAMN,SAAAA,uKAAOJ,OAAAA,EAAK;IAClBW,OAAO;IACPC,MAAMP,6KAAAA,uKAAML,OAAAA,EAAK;IACjBa,4KAAOZ,QAAAA,GAAMD,2KAAAA,EAAK;IAClBc,4KAAOZ,UAAAA,uKAAQF,OAAAA,EAAK;AACtB,EAAU;AAEV,MAAMe,iBAAiB;IACrBC,KAAK;IACLN,MAAM;IACND,OAAO;AACT;AAEA,SAASQ,YAAYC,UAAiC,EAAE,GAAGC,OAAc;IACvE,IAAKA,CAAAA,OAAO,CAAC,EAAE,KAAK,MAAMA,OAAO,CAAC,EAAE,KAAKC,SAAQ,KAAMD,QAAQE,MAAM,KAAK,GAAG;QAC3EF,QAAQG,KAAK;IACf;IAEA,MAAMC,gBACJL,cAAcH,iBACVA,cAAc,CAACG,WAA0C,GACzD;IAEN,MAAMM,SAASjB,QAAQ,CAACW,WAAW;IACnC,+DAA+D;IAC/D,IAAIC,QAAQE,MAAM,KAAK,GAAG;QACxBI,OAAO,CAACF,cAAc,CAAC;IACzB,OAAO;QACL,yEAAyE;QACzE,8DAA8D;QAC9D,IAAIJ,QAAQE,MAAM,KAAK,KAAK,OAAOF,OAAO,CAAC,EAAE,KAAK,UAAU;YAC1DM,OAAO,CAACF,cAAc,CAAC,MAAMC,SAAS,MAAML,OAAO,CAAC,EAAE;QACxD,OAAO;YACLM,OAAO,CAACF,cAAc,CAAC,MAAMC,WAAWL;QAC1C;IACF;AACF;AAEO,SAASO,UAAU,GAAGP,OAAiB;IAC5C,wCAAwC;IACxC,kCAAkC;IAClC,oDAAoD;IACpDM,QAAQT,GAAG,CAAC,QAAQG,QAAQQ,IAAI,CAAC;AACnC;AAEO,SAASnB,KAAK,GAAGW,OAAc;IACpCF,YAAY,WAAWE;AACzB;AAEO,SAASV,MAAM,GAAGU,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEO,SAAST,KAAK,GAAGS,OAAc;IACpCF,YAAY,WAAWE;AACzB;AAEO,SAASR,MAAM,GAAGQ,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEO,SAASP,KAAK,GAAGO,OAAc;IACpCF,YAAY,WAAWE;AACzB;AAEO,SAASN,MAAM,GAAGM,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEO,SAASL,MAAM,GAAGK,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEA,MAAMS,gBAAgB,iLAAItB,WAAAA,CAAiB,OAAQ,CAACuB,QAAUA,MAAMR,MAAM;AACnE,SAASS,SAAS,GAAGX,OAAc;IACxC,MAAMY,MAAMZ,QAAQQ,IAAI,CAAC;IACzB,IAAI,CAACC,cAAcI,GAAG,CAACD,MAAM;QAC3BH,cAAcK,GAAG,CAACF,KAAKA;QACvBrB,QAAQS;IACV;AACF", "ignoreList": [0]}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,KAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA;wBAEzG,YAAA;4BAAA;4BAAA,CACA,kCAD4D;4BAC5D,IAAO,MAAMC,cAAc,IAAIX,mBAAmB;kCAChDY,QAAAA,CAAAA,CAAY;gCAAA,QAAA;oCAAA,IAAA;oCAAA;iCAAA;;+BACVC,MAAMZ,UAAUa,QAAQ;;yBACxBC,MAAM;8BACNC,IAAAA,CAAAA;oBAAAA,CAAU;iBAAA;;iBACV,2CAA2C;sBAC3CC,IAAAA,CAAAA;YAAAA;SAAAA,CAAY;;SACZC,UAAU;cACVC,IAAAA;YAAAA,CAAU,EAAE,GAAA;gBACd,OAAA,QAAA;wBAAA;4BACAC,KAAAA,CAAAA,GAAAA,2NAAAA,CAAAA,CAAU,qBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,qTAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACRC,OAAAA,GAAAA,qTAAAA,CAAAA,EAAYnB,QAAAA,CAAAA,KAAAA,CAAAA,CAAAA,EAAAA,qTAAAA,CAAAA,UAAAA,CAAAA,MAAAA,EAAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACA;qBAAA", "ignoreList": [0]}}, {"offset": {"line": 534, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/build/templates/edge-ssr-app.ts"], "sourcesContent": ["import '../../server/web/globals'\nimport { adapter } from '../../server/web/adapter'\nimport { getRender } from '../webpack/loaders/next-edge-ssr-loader/render'\nimport { IncrementalCache } from '../../server/lib/incremental-cache'\n\nimport { renderToHTMLOrFlight as renderToHTML } from '../../server/app-render/app-render'\nimport * as pageMod from 'VAR_USERLAND'\n\nimport type { DocumentType } from '../../shared/lib/utils'\nimport type { BuildManifest } from '../../server/get-page-files'\nimport type { RequestData } from '../../server/web/types'\nimport type { NextConfigComplete } from '../../server/config-shared'\nimport { PAGE_TYPES } from '../../lib/page-types'\nimport { setReferenceManifestsSingleton } from '../../server/app-render/encryption-utils'\nimport { createServerModuleMap } from '../../server/app-render/action-utils'\nimport { initializeCacheHandlers } from '../../server/use-cache/handlers'\n\ndeclare const incrementalCacheHandler: any\n// OPTIONAL_IMPORT:incrementalCacheHandler\n\n// Initialize the cache handlers interface.\ninitializeCacheHandlers()\n\nconst Document: DocumentType = null!\nconst appMod = null\nconst errorMod = null\nconst error500Mod = null\n\n// injected by the loader afterwards.\ndeclare const sriEnabled: boolean\ndeclare const isServerComponent: boolean\ndeclare const dev: boolean\ndeclare const serverActions: any\ndeclare const nextConfig: NextConfigComplete\n// INJECT:sriEnabled\n// INJECT:isServerComponent\n// INJECT:dev\n// INJECT:serverActions\n// INJECT:nextConfig\n\nconst maybeJSONParse = (str?: string) => (str ? JSON.parse(str) : undefined)\n\nconst buildManifest: BuildManifest = self.__BUILD_MANIFEST as any\nconst reactLoadableManifest = maybeJSONParse(self.__REACT_LOADABLE_MANIFEST)\nconst rscManifest = self.__RSC_MANIFEST?.['VAR_PAGE']\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST)\nconst subresourceIntegrityManifest = sriEnabled\n  ? maybeJSONParse(self.__SUBRESOURCE_INTEGRITY_MANIFEST)\n  : undefined\nconst nextFontManifest = maybeJSONParse(self.__NEXT_FONT_MANIFEST)\n\nconst interceptionRouteRewrites =\n  maybeJSONParse(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST) ?? []\n\nif (rscManifest && rscServerManifest) {\n  setReferenceManifestsSingleton({\n    page: 'VAR_PAGE',\n    clientReferenceManifest: rscManifest,\n    serverActionsManifest: rscServerManifest,\n    serverModuleMap: createServerModuleMap({\n      serverActionsManifest: rscServerManifest,\n    }),\n  })\n}\n\nconst render = getRender({\n  pagesType: PAGE_TYPES.APP,\n  dev,\n  page: 'VAR_PAGE',\n  appMod,\n  pageMod,\n  errorMod,\n  error500Mod,\n  Document,\n  buildManifest,\n  renderToHTML,\n  reactLoadableManifest,\n  clientReferenceManifest: isServerComponent ? rscManifest : null,\n  serverActionsManifest: isServerComponent ? rscServerManifest : null,\n  serverActions: isServerComponent ? serverActions : undefined,\n  subresourceIntegrityManifest,\n  config: nextConfig,\n  buildId: process.env.__NEXT_BUILD_ID!,\n  nextFontManifest,\n  incrementalCacheHandler,\n  interceptionRouteRewrites,\n})\n\nexport const ComponentMod = pageMod\n\nexport default function nHandler(opts: { page: string; request: RequestData }) {\n  return adapter({\n    ...opts,\n    IncrementalCache,\n    handler: render,\n  })\n}\n"], "names": ["self", "adapter", "getRender", "IncrementalCache", "renderToHTMLOrFlight", "renderToHTML", "pageMod", "PAGE_TYPES", "setReferenceManifestsSingleton", "createServerModuleMap", "initializeCacheHandlers", "Document", "appMod", "errorMod", "error500Mod", "maybeJSONParse", "str", "JSON", "parse", "undefined", "buildManifest", "__BUILD_MANIFEST", "reactLoadableManifest", "__REACT_LOADABLE_MANIFEST", "rscManifest", "__RSC_MANIFEST", "rscServerManifest", "__RSC_SERVER_MANIFEST", "subresourceIntegrityManifest", "sriEnabled", "__SUBRESOURCE_INTEGRITY_MANIFEST", "nextFontManifest", "__NEXT_FONT_MANIFEST", "interceptionRouteRewrites", "__INTERCEPTION_ROUTE_REWRITE_MANIFEST", "page", "clientReferenceManifest", "serverActionsManifest", "serverModuleMap", "render", "pagesType", "APP", "dev", "isServerComponent", "serverActions", "config", "nextConfig", "buildId", "process", "env", "__NEXT_BUILD_ID", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "ComponentMod", "nH<PERSON><PERSON>", "opts", "handler"], "mappings": ";;;;AAAA,OAAO,2BAA0B;AACjC,SAASC,OAAO,QAAQ,2BAA0B;AAClD,SAASC,SAAS,QAAQ,iDAAgD;AAC1E,SAASC,gBAAgB,QAAQ,qCAAoC;AAErE,SAASC,wBAAwBC,YAAY,QAAQ,qCAAoC;AACzF,YAAYC,aAAa,eAAc;AAMvC,SAASC,UAAU,QAAQ,uBAAsB;AACjD,SAASC,8BAA8B,QAAQ,2CAA0C;AACzF,SAASC,qBAAqB,QAAQ,uCAAsC;AAC5E,SAASC,uBAAuB,QAAQ,kCAAiC;IA6BrDV;;;;;;;;;;;AA1BpB,MAAA,0BAAA,UAA0C;AAE1C,2CAA2C;sLAC3CU,0BAAAA;AAEA,MAAMC,WAAyB;AAC/B,MAAMC,SAAS;AACf,MAAMC,WAAW;AACjB,MAAMC,cAAc;AAQpB,MAAA,aAAA,CAAoB;AACpB,MAAA,oBAAA,CAA2B;AAC3B,MAAA,MAAA,CAAa;AACb,MAAA,gBAAA,CAAuB;AACvB,MAAA,aAAA;IAAoB,cAAA;IAAA,kBAAA;IAAA,sBAAA;IAAA,gBAAA;IAAA,OAAA,CAAA;IAAA,gBAAA;QAAA,+BAAA;QAAA,sBAAA;QAAA,iCAAA;QAAA,+BAAA;QAAA,uBAAA;QAAA,kBAAA;QAAA,SAAA;QAAA,kBAAA;QAAA,cAAA;QAAA,qCAAA;QAAA,qBAAA;QAAA,wBAAA;QAAA,yBAAA;QAAA,sBAAA;QAAA,eAAA;QAAA,qBAAA;QAAA,wBAAA;QAAA,iBAAA;QAAA,OAAA;QAAA,iBAAA;QAAA,aAAA;QAAA,YAAA;QAAA,uBAAA;QAAA,qCAAA;QAAA,SAAA;QAAA,OAAA;QAAA,yBAAA;QAAA,iBAAA,CAAA;QAAA,aAAA;YAAA,WAAA;gBAAA,SAAA;gBAAA,cAAA;gBAAA,UAAA;YAAA;YAAA,WAAA;gBAAA,SAAA;gBAAA,cAAA;gBAAA,UAAA;YAAA;YAAA,WAAA;gBAAA,SAAA;gBAAA,cAAA;gBAAA,UAAA;YAAA;YAAA,SAAA;gBAAA,SAAA;gBAAA,cAAA;gBAAA,UAAA;YAAA;YAAA,QAAA;gBAAA,SAAA;gBAAA,cAAA;gBAAA,UAAA;YAAA;YAAA,SAAA;gBAAA,SAAA;gBAAA,cAAA;gBAAA,UAAA;YAAA;YAAA,OAAA;gBAAA,SAAA;gBAAA,cAAA;gBAAA,UAAA;YAAA;QAAA;QAAA,uBAAA;QAAA,QAAA;QAAA,aAAA;QAAA,2BAAA;QAAA,2BAAA;QAAA,gBAAA;QAAA,kBAAA;QAAA,eAAA;QAAA,yBAAA;QAAA,sBAAA;QAAA,kBAAA;QAAA,YAAA;QAAA,aAAA;QAAA,uBAAA;QAAA,uBAAA;QAAA,sBAAA;QAAA,WAAA;QAAA,2BAAA;QAAA,uBAAA;QAAA,0BAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;SAAA;QAAA,4BAAA;QAAA,6BAAA;QAAA,yBAAA;QAAA,OAAA;QAAA,SAAA;QAAA,mBAAA;QAAA,iBAAA;QAAA,gBAAA;QAAA,sBAAA;QAAA,oBAAA;QAAA,qBAAA;QAAA,mBAAA;QAAA,eAAA;QAAA,cAAA;QAAA,kBAAA;QAAA,sBAAA;QAAA,iBAAA;QAAA,mBAAA;QAAA,8BAAA;QAAA,uBAAA;QAAA,wBAAA;IAAA;IAAA,UAAA;QAAA,eAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;SAAA;QAAA,cAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;SAAA;QAAA,QAAA;QAAA,UAAA;QAAA,cAAA;QAAA,WAAA,EAAA;QAAA,uBAAA;QAAA,mBAAA;QAAA,WAAA;YAAA;SAAA;QAAA,uBAAA;QAAA,yBAAA;QAAA,kBAAA;YAAA;gBAAA,YAAA;gBAAA,YAAA;gBAAA,QAAA;gBAAA,YAAA;YAAA;YAAA;gBAAA,YAAA;gBAAA,YAAA;gBAAA,QAAA;gBAAA,YAAA;YAAA;YAAA;gBAAA,YAAA;gBAAA,YAAA;gBAAA,QAAA;gBAAA,YAAA;YAAA;YAAA;gBAAA,YAAA;gBAAA,YAAA;gBAAA,QAAA;gBAAA,YAAA;YAAA;SAAA;QAAA,eAAA;IAAA;IAAA,kBAAA;QAAA;QAAA;QAAA;QAAA;KAAA;IAAA,4BAAA;IAAA,mBAAA;IAAA,qBAAA;IAAA,qBAAA;QAAA,uBAAA;YAAA,aAAA;YAAA,qBAAA;YAAA,yBAAA;QAAA;QAAA,UAAA;YAAA,aAAA;YAAA,qBAAA;YAAA,yBAAA;QAAA;IAAA;IAAA,WAAA;IAAA,gBAAA;IAAA,eAAA,CAAA;IAAA,iBAAA;IAAA,eAAA;IAAA,YAAA;IAAA,8BAAA;IAAA,6BAAA;IAAA,QAAA;IAAA,eAAA;IAAA,iBAAA;QAAA,yBAAA;QAAA,YAAA;IAAA;IAAA,UAAA;IAAA,aAAA;QAAA,WAAA;QAAA,SAAA;YAAA,QAAA;gBAAA,WAAA;oBAAA,WAAA;wBAAA;4BAAA,UAAA;4BAAA,WAAA;gCAAA,iBAAA;gCAAA,OAAA;gCAAA,wBAAA;oCAAA;wCAAA;wCAAA;4CAAA,kBAAA;wCAAA;qCAAA;iCAAA;4BAAA;wBAAA;qBAAA;oBAAA,YAAA;gBAAA;YAAA;YAAA,QAAA;gBAAA,WAAA;oBAAA,WAAA;wBAAA;4BAAA,UAAA;4BAAA,WAAA;gCAAA,iBAAA;gCAAA,OAAA;gCAAA,wBAAA;oCAAA;wCAAA;wCAAA;4CAAA,kBAAA;wCAAA;qCAAA;iCAAA;4BAAA;wBAAA;qBAAA;oBAAA,YAAA;gBAAA;YAAA;YAAA,SAAA;gBAAA,WAAA;oBAAA,WAAA;wBAAA;4BAAA,UAAA;4BAAA,WAAA;gCAAA,iBAAA;gCAAA,OAAA;gCAAA,wBAAA;oCAAA;wCAAA;wCAAA;4CAAA,kBAAA;wCAAA;qCAAA;iCAAA;4BAAA;wBAAA;qBAAA;oBAAA,YAAA;gBAAA;YAAA;YAAA,SAAA;gBAAA,WAAA;oBAAA,WAAA;wBAAA;4BAAA,UAAA;4BAAA,WAAA;gCAAA,iBAAA;gCAAA,OAAA;gCAAA,wBAAA;oCAAA;wCAAA;wCAAA;4CAAA,kBAAA;wCAAA;qCAAA;iCAAA;4BAAA;wBAAA;qBAAA;oBAAA,YAAA;gBAAA;YAAA;QAAA;QAAA,gBAAA;QAAA,qBAAA;QAAA,aAAA;IAAA;IAAA,+BAAA;IAAA,iCAAA;IAAA,0BAAA;IAAA,sBAAA;IAAA,YAAA;IAAA,iBAAA;IAAA,OAAA;QAAA,iBAAA;IAAA;IAAA,gBAAA;IAAA,YAAA;IAAA,UAAA;QAAA,QAAA;QAAA,sBAAA;IAAA;IAAA,+BAAA;IAAA,iBAAA,CAAA;IAAA,mBAAA;IAAA,iBAAA;IAAA,oBAAA;QAAA,aAAA;IAAA;IAAA,mBAAA;QAAA,kBAAA;QAAA,qBAAA;IAAA;IAAA,mBAAA;IAAA,uBAAA,CAAA;IAAA,uBAAA,CAAA;IAAA,+BAAA;IAAA,UAAA;IAAA,cAAA;QAAA,qBAAA;QAAA,gBAAA;IAAA;IAAA,6BAAA;IAAA,WAAA;AAAA;AAEpB,MAAMC,iBAAiB,CAACC,MAAkBA,MAAMC,KAAKC,KAAK,CAACF,OAAOG;AAElE,MAAMC,gBAA+BpB,KAAKqB,gBAAgB;AAC1D,MAAMC,wBAAwBP,eAAef,KAAKuB,yBAAyB;AAC3E,MAAMC,cAAAA,CAAcxB,uBAAAA,KAAKyB,cAAc,KAAA,OAAA,KAAA,IAAnBzB,oBAAqB,CAAC,WAAW,YAAA;AACrD,MAAM0B,oBAAoBX,eAAef,KAAK2B,qBAAqB;AACnE,MAAMC,+BAA+BC,aACjCd,eAAef,KAAK8B,gCAAgC,YACpDX;AACJ,MAAMY,mBAAmBhB,eAAef,KAAKgC,oBAAoB;AAEjE,MAAMC,4BACJlB,eAAef,KAAKkC,qCAAqC,KAAK,EAAE;AAElE,IAAIV,eAAeE,mBAAmB;sMACpClB,iCAAAA,EAA+B;QAC7B2B,MAAM;QACNC,yBAAyBZ;QACzBa,uBAAuBX;QACvBY,+MAAiB7B,wBAAAA,EAAsB;YACrC4B,uBAAuBX;QACzB;IACF;AACF;AAEA,MAAMa,SAASrC,sOAAAA,EAAU;IACvBsC,+KAAWjC,aAAAA,CAAWkC,GAAG;IACzBC;IACAP,MAAM;IACNvB;aACAN;IACAO;IACAC;IACAH;IACAS;0MACAf,uBAAAA;IACAiB;IACAc,yBAAyBO,uCAAoBnB,cAAc;IAC3Da,uBAAuBM,uCAAoBjB,oBAAoB;IAC/DkB,eAAeD,uCAAoBC,gBAAgBzB;IACnDS;IACAiB,QAAQC;IACRC,SAASC,QAAQC,GAAG,CAACC,eAAe;IACpCnB;IACAoB;IACAlB;AACF;AAEO,MAAMmB,eAAe9C,QAAO;AAEpB,SAAS+C,SAASC,IAA4C;IAC3E,mLAAOrD,UAAAA,EAAQ;QACb,GAAGqD,IAAI;wNACPnD,mBAAAA;QACAoD,SAAShB;IACX;AACF", "ignoreList": [0]}}]}