{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/match-segments.ts"], "sourcesContent": ["import type { Segment } from '../../server/app-render/types'\n\nexport const matchSegment = (\n  existingSegment: Segment,\n  segment: Segment\n): boolean => {\n  // segment is either Array or string\n  if (typeof existingSegment === 'string') {\n    if (typeof segment === 'string') {\n      // Common case: segment is just a string\n      return existingSegment === segment\n    }\n    return false\n  }\n\n  if (typeof segment === 'string') {\n    return false\n  }\n  return existingSegment[0] === segment[0] && existingSegment[1] === segment[1]\n}\n"], "names": ["matchSegment", "existingSegment", "segment"], "mappings": ";;;AAEO,MAAMA,eAAe,CAC1BC,iBACAC;IAEA,oCAAoC;IACpC,IAAI,OAAOD,oBAAoB,UAAU;QACvC,IAAI,OAAOC,YAAY,UAAU;YAC/B,wCAAwC;YACxC,OAAOD,oBAAoBC;QAC7B;QACA,OAAO;IACT;IAEA,IAAI,OAAOA,YAAY,UAAU;QAC/B,OAAO;IACT;IACA,OAAOD,eAAe,CAAC,EAAE,KAAKC,OAAO,CAAC,EAAE,IAAID,eAAe,CAAC,EAAE,KAAKC,OAAO,CAAC,EAAE;AAC/E,EAAC", "ignoreList": [0]}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/parallel-route-default.tsx"], "sourcesContent": ["import { notFound } from './not-found'\n\nexport const PARALLEL_ROUTE_DEFAULT_PATH =\n  'next/dist/client/components/parallel-route-default.js'\n\nexport default function ParallelRouteDefault() {\n  notFound()\n}\n"], "names": ["notFound", "PARALLEL_ROUTE_DEFAULT_PATH", "<PERSON>llel<PERSON><PERSON><PERSON>ault"], "mappings": ";;;;AAAA,SAASA,QAAQ,QAAQ,cAAa;;AAE/B,MAAMC,8BACX,wDAAuD;AAE1C,SAASC;4LACtBF,WAAAA;AACF", "ignoreList": [0]}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/esm/client/components/app-router.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/app-router.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/esm/client/components/app-router.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/app-router.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/app-router.tsx"], "sourcesContent": ["'use client'\n\nimport React, {\n  use,\n  useEffect,\n  useMemo,\n  startTransition,\n  useInsertionEffect,\n  useDeferredValue,\n} from 'react'\nimport {\n  AppRouterContext,\n  LayoutRouterContext,\n  GlobalLayoutRouterContext,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport type { CacheNode } from '../../shared/lib/app-router-context.shared-runtime'\nimport { ACTION_RESTORE } from './router-reducer/router-reducer-types'\nimport type { AppRouterState } from './router-reducer/router-reducer-types'\nimport { createHrefFromUrl } from './router-reducer/create-href-from-url'\nimport {\n  SearchParamsContext,\n  PathnameContext,\n  PathParamsContext,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport { dispatchAppRouterAction, useActionQueue } from './use-action-queue'\nimport {\n  default as DefaultGlobalError,\n  ErrorBoundary,\n  type GlobalErrorComponent,\n} from './error-boundary'\nimport { isBot } from '../../shared/lib/router/utils/is-bot'\nimport { addBasePath } from '../add-base-path'\nimport { AppRouterAnnouncer } from './app-router-announcer'\nimport { RedirectBoundary } from './redirect-boundary'\nimport { findHeadInCache } from './router-reducer/reducers/find-head-in-cache'\nimport { unresolvedThenable } from './unresolved-thenable'\nimport { removeBasePath } from '../remove-base-path'\nimport { hasBasePath } from '../has-base-path'\nimport { getSelectedParams } from './router-reducer/compute-changed-path'\nimport type { FlightRouterState } from '../../server/app-render/types'\nimport { useNavFailureHandler } from './nav-failure-handler'\nimport {\n  dispatchTraverseAction,\n  publicAppRouterInstance,\n  type AppRouterActionQueue,\n} from './app-router-instance'\nimport { getRedirectTypeFromError, getURLFromRedirectError } from './redirect'\nimport { isRedirectError, RedirectType } from './redirect-error'\nimport { pingVisibleLinks } from './links'\n\nconst globalMutable: {\n  pendingMpaPath?: string\n} = {}\n\nexport function isExternalURL(url: URL) {\n  return url.origin !== window.location.origin\n}\n\n/**\n * Given a link href, constructs the URL that should be prefetched. Returns null\n * in cases where prefetching should be disabled, like external URLs, or\n * during development.\n * @param href The href passed to <Link>, router.prefetch(), or similar\n * @returns A URL object to prefetch, or null if prefetching should be disabled\n */\nexport function createPrefetchURL(href: string): URL | null {\n  // Don't prefetch for bots as they don't navigate.\n  if (isBot(window.navigator.userAgent)) {\n    return null\n  }\n\n  let url: URL\n  try {\n    url = new URL(addBasePath(href), window.location.href)\n  } catch (_) {\n    // TODO: Does this need to throw or can we just console.error instead? Does\n    // anyone rely on this throwing? (Seems unlikely.)\n    throw new Error(\n      `Cannot prefetch '${href}' because it cannot be converted to a URL.`\n    )\n  }\n\n  // Don't prefetch during development (improves compilation performance)\n  if (process.env.NODE_ENV === 'development') {\n    return null\n  }\n\n  // External urls can't be prefetched in the same way.\n  if (isExternalURL(url)) {\n    return null\n  }\n\n  return url\n}\n\nfunction HistoryUpdater({\n  appRouterState,\n}: {\n  appRouterState: AppRouterState\n}) {\n  useInsertionEffect(() => {\n    if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n      // clear pending URL as navigation is no longer\n      // in flight\n      window.next.__pendingUrl = undefined\n    }\n\n    const { tree, pushRef, canonicalUrl } = appRouterState\n    const historyState = {\n      ...(pushRef.preserveCustomHistoryState ? window.history.state : {}),\n      // Identifier is shortened intentionally.\n      // __NA is used to identify if the history entry can be handled by the app-router.\n      // __N is used to identify if the history entry can be handled by the old router.\n      __NA: true,\n      __PRIVATE_NEXTJS_INTERNALS_TREE: tree,\n    }\n    if (\n      pushRef.pendingPush &&\n      // Skip pushing an additional history entry if the canonicalUrl is the same as the current url.\n      // This mirrors the browser behavior for normal navigation.\n      createHrefFromUrl(new URL(window.location.href)) !== canonicalUrl\n    ) {\n      // This intentionally mutates React state, pushRef is overwritten to ensure additional push/replace calls do not trigger an additional history entry.\n      pushRef.pendingPush = false\n      window.history.pushState(historyState, '', canonicalUrl)\n    } else {\n      window.history.replaceState(historyState, '', canonicalUrl)\n    }\n  }, [appRouterState])\n\n  useEffect(() => {\n    // The Next-Url and the base tree may affect the result of a prefetch\n    // task. Re-prefetch all visible links with the updated values. In most\n    // cases, this will not result in any new network requests, only if\n    // the prefetch result actually varies on one of these inputs.\n    if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n      pingVisibleLinks(appRouterState.nextUrl, appRouterState.tree)\n    }\n  }, [appRouterState.nextUrl, appRouterState.tree])\n\n  return null\n}\n\nexport function createEmptyCacheNode(): CacheNode {\n  return {\n    lazyData: null,\n    rsc: null,\n    prefetchRsc: null,\n    head: null,\n    prefetchHead: null,\n    parallelRoutes: new Map(),\n    loading: null,\n    navigatedAt: -1,\n  }\n}\n\nfunction copyNextJsInternalHistoryState(data: any) {\n  if (data == null) data = {}\n  const currentState = window.history.state\n  const __NA = currentState?.__NA\n  if (__NA) {\n    data.__NA = __NA\n  }\n  const __PRIVATE_NEXTJS_INTERNALS_TREE =\n    currentState?.__PRIVATE_NEXTJS_INTERNALS_TREE\n  if (__PRIVATE_NEXTJS_INTERNALS_TREE) {\n    data.__PRIVATE_NEXTJS_INTERNALS_TREE = __PRIVATE_NEXTJS_INTERNALS_TREE\n  }\n\n  return data\n}\n\nfunction Head({\n  headCacheNode,\n}: {\n  headCacheNode: CacheNode | null\n}): React.ReactNode {\n  // If this segment has a `prefetchHead`, it's the statically prefetched data.\n  // We should use that on initial render instead of `head`. Then we'll switch\n  // to `head` when the dynamic response streams in.\n  const head = headCacheNode !== null ? headCacheNode.head : null\n  const prefetchHead =\n    headCacheNode !== null ? headCacheNode.prefetchHead : null\n\n  // If no prefetch data is available, then we go straight to rendering `head`.\n  const resolvedPrefetchRsc = prefetchHead !== null ? prefetchHead : head\n\n  // We use `useDeferredValue` to handle switching between the prefetched and\n  // final values. The second argument is returned on initial render, then it\n  // re-renders with the first argument.\n  return useDeferredValue(head, resolvedPrefetchRsc)\n}\n\n/**\n * The global router that wraps the application components.\n */\nfunction Router({\n  actionQueue,\n  assetPrefix,\n  globalError,\n}: {\n  actionQueue: AppRouterActionQueue\n  assetPrefix: string\n  globalError: [GlobalErrorComponent, React.ReactNode]\n}) {\n  const state = useActionQueue(actionQueue)\n  const { canonicalUrl } = state\n  // Add memoized pathname/query for useSearchParams and usePathname.\n  const { searchParams, pathname } = useMemo(() => {\n    const url = new URL(\n      canonicalUrl,\n      typeof window === 'undefined' ? 'http://n' : window.location.href\n    )\n\n    return {\n      // This is turned into a readonly class in `useSearchParams`\n      searchParams: url.searchParams,\n      pathname: hasBasePath(url.pathname)\n        ? removeBasePath(url.pathname)\n        : url.pathname,\n    }\n  }, [canonicalUrl])\n\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const { cache, prefetchCache, tree } = state\n\n    // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      // Add `window.nd` for debugging purposes.\n      // This is not meant for use in applications as concurrent rendering will affect the cache/tree/router.\n      // @ts-ignore this is for debugging\n      window.nd = {\n        router: publicAppRouterInstance,\n        cache,\n        prefetchCache,\n        tree,\n      }\n    }, [cache, prefetchCache, tree])\n  }\n\n  useEffect(() => {\n    // If the app is restored from bfcache, it's possible that\n    // pushRef.mpaNavigation is true, which would mean that any re-render of this component\n    // would trigger the mpa navigation logic again from the lines below.\n    // This will restore the router to the initial state in the event that the app is restored from bfcache.\n    function handlePageShow(event: PageTransitionEvent) {\n      if (\n        !event.persisted ||\n        !window.history.state?.__PRIVATE_NEXTJS_INTERNALS_TREE\n      ) {\n        return\n      }\n\n      // Clear the pendingMpaPath value so that a subsequent MPA navigation to the same URL can be triggered.\n      // This is necessary because if the browser restored from bfcache, the pendingMpaPath would still be set to the value\n      // of the last MPA navigation.\n      globalMutable.pendingMpaPath = undefined\n\n      dispatchAppRouterAction({\n        type: ACTION_RESTORE,\n        url: new URL(window.location.href),\n        tree: window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE,\n      })\n    }\n\n    window.addEventListener('pageshow', handlePageShow)\n\n    return () => {\n      window.removeEventListener('pageshow', handlePageShow)\n    }\n  }, [])\n\n  useEffect(() => {\n    // Ensure that any redirect errors that bubble up outside of the RedirectBoundary\n    // are caught and handled by the router.\n    function handleUnhandledRedirect(\n      event: ErrorEvent | PromiseRejectionEvent\n    ) {\n      const error = 'reason' in event ? event.reason : event.error\n      if (isRedirectError(error)) {\n        event.preventDefault()\n        const url = getURLFromRedirectError(error)\n        const redirectType = getRedirectTypeFromError(error)\n        // TODO: This should access the router methods directly, rather than\n        // go through the public interface.\n        if (redirectType === RedirectType.push) {\n          publicAppRouterInstance.push(url, {})\n        } else {\n          publicAppRouterInstance.replace(url, {})\n        }\n      }\n    }\n    window.addEventListener('error', handleUnhandledRedirect)\n    window.addEventListener('unhandledrejection', handleUnhandledRedirect)\n\n    return () => {\n      window.removeEventListener('error', handleUnhandledRedirect)\n      window.removeEventListener('unhandledrejection', handleUnhandledRedirect)\n    }\n  }, [])\n\n  // When mpaNavigation flag is set do a hard navigation to the new url.\n  // Infinitely suspend because we don't actually want to rerender any child\n  // components with the new URL and any entangled state updates shouldn't\n  // commit either (eg: useTransition isPending should stay true until the page\n  // unloads).\n  //\n  // This is a side effect in render. Don't try this at home, kids. It's\n  // probably safe because we know this is a singleton component and it's never\n  // in <Offscreen>. At least I hope so. (It will run twice in dev strict mode,\n  // but that's... fine?)\n  const { pushRef } = state\n  if (pushRef.mpaNavigation) {\n    // if there's a re-render, we don't want to trigger another redirect if one is already in flight to the same URL\n    if (globalMutable.pendingMpaPath !== canonicalUrl) {\n      const location = window.location\n      if (pushRef.pendingPush) {\n        location.assign(canonicalUrl)\n      } else {\n        location.replace(canonicalUrl)\n      }\n\n      globalMutable.pendingMpaPath = canonicalUrl\n    }\n    // TODO-APP: Should we listen to navigateerror here to catch failed\n    // navigations somehow? And should we call window.stop() if a SPA navigation\n    // should interrupt an MPA one?\n    use(unresolvedThenable)\n  }\n\n  useEffect(() => {\n    const originalPushState = window.history.pushState.bind(window.history)\n    const originalReplaceState = window.history.replaceState.bind(\n      window.history\n    )\n\n    // Ensure the canonical URL in the Next.js Router is updated when the URL is changed so that `usePathname` and `useSearchParams` hold the pushed values.\n    const applyUrlFromHistoryPushReplace = (\n      url: string | URL | null | undefined\n    ) => {\n      const href = window.location.href\n      const tree: FlightRouterState | undefined =\n        window.history.state?.__PRIVATE_NEXTJS_INTERNALS_TREE\n\n      startTransition(() => {\n        dispatchAppRouterAction({\n          type: ACTION_RESTORE,\n          url: new URL(url ?? href, href),\n          tree,\n        })\n      })\n    }\n\n    /**\n     * Patch pushState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */\n    window.history.pushState = function pushState(\n      data: any,\n      _unused: string,\n      url?: string | URL | null\n    ): void {\n      // Avoid a loop when Next.js internals trigger pushState/replaceState\n      if (data?.__NA || data?._N) {\n        return originalPushState(data, _unused, url)\n      }\n\n      data = copyNextJsInternalHistoryState(data)\n\n      if (url) {\n        applyUrlFromHistoryPushReplace(url)\n      }\n\n      return originalPushState(data, _unused, url)\n    }\n\n    /**\n     * Patch replaceState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */\n    window.history.replaceState = function replaceState(\n      data: any,\n      _unused: string,\n      url?: string | URL | null\n    ): void {\n      // Avoid a loop when Next.js internals trigger pushState/replaceState\n      if (data?.__NA || data?._N) {\n        return originalReplaceState(data, _unused, url)\n      }\n      data = copyNextJsInternalHistoryState(data)\n\n      if (url) {\n        applyUrlFromHistoryPushReplace(url)\n      }\n      return originalReplaceState(data, _unused, url)\n    }\n\n    /**\n     * Handle popstate event, this is used to handle back/forward in the browser.\n     * By default dispatches ACTION_RESTORE, however if the history entry was not pushed/replaced by app-router it will reload the page.\n     * That case can happen when the old router injected the history entry.\n     */\n    const onPopState = (event: PopStateEvent) => {\n      if (!event.state) {\n        // TODO-APP: this case only happens when pushState/replaceState was called outside of Next.js. It should probably reload the page in this case.\n        return\n      }\n\n      // This case happens when the history entry was pushed by the `pages` router.\n      if (!event.state.__NA) {\n        window.location.reload()\n        return\n      }\n\n      // TODO-APP: Ideally the back button should not use startTransition as it should apply the updates synchronously\n      // Without startTransition works if the cache is there for this path\n      startTransition(() => {\n        dispatchTraverseAction(\n          window.location.href,\n          event.state.__PRIVATE_NEXTJS_INTERNALS_TREE\n        )\n      })\n    }\n\n    // Register popstate event to call onPopstate.\n    window.addEventListener('popstate', onPopState)\n    return () => {\n      window.history.pushState = originalPushState\n      window.history.replaceState = originalReplaceState\n      window.removeEventListener('popstate', onPopState)\n    }\n  }, [])\n\n  const { cache, tree, nextUrl, focusAndScrollRef } = state\n\n  const matchingHead = useMemo(() => {\n    return findHeadInCache(cache, tree[1])\n  }, [cache, tree])\n\n  // Add memoized pathParams for useParams.\n  const pathParams = useMemo(() => {\n    return getSelectedParams(tree)\n  }, [tree])\n\n  const layoutRouterContext = useMemo(() => {\n    return {\n      parentTree: tree,\n      parentCacheNode: cache,\n      parentSegmentPath: null,\n      // Root node always has `url`\n      // Provided in AppTreeContext to ensure it can be overwritten in layout-router\n      url: canonicalUrl,\n    }\n  }, [tree, cache, canonicalUrl])\n\n  const globalLayoutRouterContext = useMemo(() => {\n    return {\n      tree,\n      focusAndScrollRef,\n      nextUrl,\n    }\n  }, [tree, focusAndScrollRef, nextUrl])\n\n  let head\n  if (matchingHead !== null) {\n    // The head is wrapped in an extra component so we can use\n    // `useDeferredValue` to swap between the prefetched and final versions of\n    // the head. (This is what LayoutRouter does for segment data, too.)\n    //\n    // The `key` is used to remount the component whenever the head moves to\n    // a different segment.\n    const [headCacheNode, headKey] = matchingHead\n    head = <Head key={headKey} headCacheNode={headCacheNode} />\n  } else {\n    head = null\n  }\n\n  let content = (\n    <RedirectBoundary>\n      {head}\n      {cache.rsc}\n      <AppRouterAnnouncer tree={tree} />\n    </RedirectBoundary>\n  )\n\n  if (process.env.NODE_ENV !== 'production') {\n    // In development, we apply few error boundaries and hot-reloader:\n    // - DevRootHTTPAccessFallbackBoundary: avoid using navigation API like notFound() in root layout\n    // - HotReloader:\n    //  - hot-reload the app when the code changes\n    //  - render dev overlay\n    //  - catch runtime errors and display global-error when necessary\n    if (typeof window !== 'undefined') {\n      const { DevRootHTTPAccessFallbackBoundary } =\n        require('./dev-root-http-access-fallback-boundary') as typeof import('./dev-root-http-access-fallback-boundary')\n      content = (\n        <DevRootHTTPAccessFallbackBoundary>\n          {content}\n        </DevRootHTTPAccessFallbackBoundary>\n      )\n    }\n    const HotReloader: typeof import('./react-dev-overlay/app/hot-reloader-client').default =\n      require('./react-dev-overlay/app/hot-reloader-client').default\n\n    content = (\n      <HotReloader assetPrefix={assetPrefix} globalError={globalError}>\n        {content}\n      </HotReloader>\n    )\n  } else {\n    // In production, we only apply the user-customized global error boundary.\n    content = (\n      <ErrorBoundary\n        errorComponent={globalError[0]}\n        errorStyles={globalError[1]}\n      >\n        {content}\n      </ErrorBoundary>\n    )\n  }\n\n  return (\n    <>\n      <HistoryUpdater appRouterState={state} />\n      <RuntimeStyles />\n      <PathParamsContext.Provider value={pathParams}>\n        <PathnameContext.Provider value={pathname}>\n          <SearchParamsContext.Provider value={searchParams}>\n            <GlobalLayoutRouterContext.Provider\n              value={globalLayoutRouterContext}\n            >\n              {/* TODO: We should be able to remove this context. useRouter\n                  should import from app-router-instance instead. It's only\n                  necessary because useRouter is shared between Pages and\n                  App Router. We should fork that module, then remove this\n                  context provider. */}\n              <AppRouterContext.Provider value={publicAppRouterInstance}>\n                <LayoutRouterContext.Provider value={layoutRouterContext}>\n                  {content}\n                </LayoutRouterContext.Provider>\n              </AppRouterContext.Provider>\n            </GlobalLayoutRouterContext.Provider>\n          </SearchParamsContext.Provider>\n        </PathnameContext.Provider>\n      </PathParamsContext.Provider>\n    </>\n  )\n}\n\nexport default function AppRouter({\n  actionQueue,\n  globalErrorComponentAndStyles: [globalErrorComponent, globalErrorStyles],\n  assetPrefix,\n}: {\n  actionQueue: AppRouterActionQueue\n  globalErrorComponentAndStyles: [GlobalErrorComponent, React.ReactNode]\n  assetPrefix: string\n}) {\n  useNavFailureHandler()\n\n  return (\n    <ErrorBoundary\n      // At the very top level, use the default GlobalError component as the final fallback.\n      // When the app router itself fails, which means the framework itself fails, we show the default error.\n      errorComponent={DefaultGlobalError}\n    >\n      <Router\n        actionQueue={actionQueue}\n        assetPrefix={assetPrefix}\n        globalError={[globalErrorComponent, globalErrorStyles]}\n      />\n    </ErrorBoundary>\n  )\n}\n\nconst runtimeStyles = new Set<string>()\nlet runtimeStyleChanged = new Set<() => void>()\n\nglobalThis._N_E_STYLE_LOAD = function (href: string) {\n  let len = runtimeStyles.size\n  runtimeStyles.add(href)\n  if (runtimeStyles.size !== len) {\n    runtimeStyleChanged.forEach((cb) => cb())\n  }\n  // TODO figure out how to get a promise here\n  // But maybe it's not necessary as react would block rendering until it's loaded\n  return Promise.resolve()\n}\n\nfunction RuntimeStyles() {\n  const [, forceUpdate] = React.useState(0)\n  const renderedStylesSize = runtimeStyles.size\n  useEffect(() => {\n    const changed = () => forceUpdate((c) => c + 1)\n    runtimeStyleChanged.add(changed)\n    if (renderedStylesSize !== runtimeStyles.size) {\n      changed()\n    }\n    return () => {\n      runtimeStyleChanged.delete(changed)\n    }\n  }, [renderedStylesSize, forceUpdate])\n\n  const dplId = process.env.NEXT_DEPLOYMENT_ID\n    ? `?dpl=${process.env.NEXT_DEPLOYMENT_ID}`\n    : ''\n  return [...runtimeStyles].map((href, i) => (\n    <link\n      key={i}\n      rel=\"stylesheet\"\n      href={`${href}${dplId}`}\n      // @ts-ignore\n      precedence=\"next\"\n      // TODO figure out crossOrigin and nonce\n      // crossOrigin={TODO}\n      // nonce={TODO}\n    />\n  ))\n}\n"], "names": ["React", "use", "useEffect", "useMemo", "startTransition", "useInsertionEffect", "useDeferredValue", "AppRouterContext", "LayoutRouterContext", "GlobalLayoutRouterContext", "ACTION_RESTORE", "createHrefFromUrl", "SearchParamsContext", "PathnameContext", "PathParamsContext", "dispatchAppRouterAction", "useActionQueue", "default", "DefaultGlobalError", "Error<PERSON>ou<PERSON><PERSON>", "isBot", "addBasePath", "AppRouterAnnouncer", "RedirectBoundary", "findHeadInCache", "unresolvedThenable", "removeBasePath", "has<PERSON>ase<PERSON><PERSON>", "getSelectedParams", "useNavFailureHandler", "dispatchTraverseAction", "publicAppRouterInstance", "getRedirectTypeFromError", "getURLFromRedirectError", "isRedirectError", "RedirectType", "pingVisibleLinks", "globalMutable", "isExternalURL", "url", "origin", "window", "location", "createPrefetchURL", "href", "navigator", "userAgent", "URL", "_", "Error", "process", "env", "NODE_ENV", "HistoryUpdater", "appRouterState", "__NEXT_APP_NAV_FAIL_HANDLING", "next", "__pendingUrl", "undefined", "tree", "pushRef", "canonicalUrl", "historyState", "preserveCustomHistoryState", "history", "state", "__NA", "__PRIVATE_NEXTJS_INTERNALS_TREE", "pendingPush", "pushState", "replaceState", "__NEXT_CLIENT_SEGMENT_CACHE", "nextUrl", "createEmptyCacheNode", "lazyData", "rsc", "prefetchRsc", "head", "prefetchHead", "parallelRoutes", "Map", "loading", "navigatedAt", "copyNextJsInternalHistoryState", "data", "currentState", "Head", "headCacheNode", "resolvedPrefetchRsc", "Router", "actionQueue", "assetPrefix", "globalError", "searchParams", "pathname", "cache", "prefetchCache", "nd", "router", "handlePageShow", "event", "persisted", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "addEventListener", "removeEventListener", "handleUnhandledRedirect", "error", "reason", "preventDefault", "redirectType", "push", "replace", "mpaNavigation", "assign", "originalPushState", "bind", "originalReplaceState", "applyUrlFromHistoryPushReplace", "_unused", "_N", "onPopState", "reload", "focusAndScrollRef", "matchingHead", "pathParams", "layoutRouterContext", "parentTree", "parentCacheNode", "parentSegmentPath", "globalLayoutRouterContext", "head<PERSON><PERSON>", "content", "DevRootHTTPAccessFallbackBoundary", "require", "HotReloader", "errorComponent", "errorStyles", "RuntimeStyles", "Provider", "value", "AppRouter", "globalErrorComponentAndStyles", "globalErrorComponent", "globalErrorStyles", "runtimeStyles", "Set", "runtimeStyleChanged", "globalThis", "_N_E_STYLE_LOAD", "len", "size", "add", "for<PERSON>ach", "cb", "Promise", "resolve", "forceUpdate", "useState", "renderedStylesSize", "changed", "c", "delete", "dplId", "NEXT_DEPLOYMENT_ID", "map", "i", "link", "rel", "precedence"], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/promise-queue.ts"], "sourcesContent": ["/*\n    This is a simple promise queue that allows you to limit the number of concurrent promises\n    that are running at any given time. It's used to limit the number of concurrent\n    prefetch requests that are being made to the server but could be used for other\n    things as well.\n*/\nexport class PromiseQueue {\n  #maxConcurrency: number\n  #runningCount: number\n  #queue: Array<{\n    promiseFn: Promise<any>\n    task: () => void\n  }>\n\n  constructor(maxConcurrency = 5) {\n    this.#maxConcurrency = maxConcurrency\n    this.#runningCount = 0\n    this.#queue = []\n  }\n\n  enqueue<T>(promiseFn: () => Promise<T>): Promise<T> {\n    let taskResolve: (value: T | PromiseLike<T>) => void\n    let taskReject: (reason?: any) => void\n\n    const taskPromise = new Promise((resolve, reject) => {\n      taskResolve = resolve\n      taskReject = reject\n    }) as Promise<T>\n\n    const task = async () => {\n      try {\n        this.#runningCount++\n        const result = await promiseFn()\n        taskResolve(result)\n      } catch (error) {\n        taskReject(error)\n      } finally {\n        this.#runningCount--\n        this.#processNext()\n      }\n    }\n\n    const enqueueResult = { promiseFn: taskPromise, task }\n    // wonder if we should take a LIFO approach here\n    this.#queue.push(enqueueResult)\n    this.#processNext()\n\n    return taskPromise\n  }\n\n  bump(promiseFn: Promise<any>) {\n    const index = this.#queue.findIndex((item) => item.promiseFn === promiseFn)\n\n    if (index > -1) {\n      const bumpedItem = this.#queue.splice(index, 1)[0]\n      this.#queue.unshift(bumpedItem)\n      this.#processNext(true)\n    }\n  }\n\n  #processNext(forced = false) {\n    if (\n      (this.#runningCount < this.#maxConcurrency || forced) &&\n      this.#queue.length > 0\n    ) {\n      this.#queue.shift()?.task()\n    }\n  }\n}\n"], "names": ["PromiseQueue", "enqueue", "promiseFn", "taskResolve", "taskReject", "taskPromise", "Promise", "resolve", "reject", "task", "result", "error", "enqueueResult", "push", "bump", "index", "findIndex", "item", "bumpedItem", "splice", "unshift", "constructor", "maxConcurrency", "forced", "length", "shift"], "mappings": "AAAA;;;;;AAKA;;;AAAA;;;;IAEE,kBAAA,WAAA,GAAA,CAAA,GAAA,iLAAA,CAAA,IAAA,EAAA,oBACA,gBAAA,WAAA,GAAA,CAAA,GAAA,iLAAA,CAAA,IAAA,EAAA,kBACA,SAAA,WAAA,GAAA,CAAA,GAAA,iLAAA,CAAA,IAAA,EAAA,WAmDA,eAAA,WAAA,GAAA,CAAA,GAAA,iLAAA,CAAA,IAAA,EAAA;AAtDK,MAAMA;IAcXC,QAAWC,SAA2B,EAAc;QAClD,IAAIC;QACJ,IAAIC;QAEJ,MAAMC,cAAc,IAAIC,QAAQ,CAACC,SAASC;YACxCL,cAAcI;YACdH,aAAaI;QACf;QAEA,MAAMC,OAAO;YACX,IAAI;gBACF,2LAAA,EAAA,IAAI,EAAC,cAAA,CAAA,cAAA;gBACL,MAAMC,SAAS,MAAMR;gBACrBC,YAAYO;YACd,EAAE,OAAOC,OAAO;gBACdP,WAAWO;YACb,SAAU;gBACR,2LAAA,EAAA,IAAI,EAAC,cAAA,CAAA,cAAA;uMACL,IAAA,EAAA,IAAI,EAAC,aAAA,CAAA,aAAA;YACP;QACF;QAEA,MAAMC,gBAAgB;YAAEV,WAAWG;YAAaI;QAAK;QACrD,gDAAgD;+LAChD,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,CAAOI,IAAI,CAACD;+LACjB,IAAA,EAAA,IAAI,EAAC,aAAA,CAAA,aAAA;QAEL,OAAOP;IACT;IAEAS,KAAKZ,SAAuB,EAAE;QAC5B,MAAMa,+LAAQ,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,CAAOC,SAAS,CAAC,CAACC,OAASA,KAAKf,SAAS,KAAKA;QAEjE,IAAIa,QAAQ,CAAC,GAAG;YACd,MAAMG,oMAAa,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,CAAOC,MAAM,CAACJ,OAAO,EAAE,CAAC,EAAE;mMAClD,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,CAAOK,OAAO,CAACF;aACpB,0LAAA,EAAA,IAAI,EAAC,aAAA,CAAA,aAAA,CAAa;QACpB;IACF;IA5CAG,YAAYC,iBAAiB,CAAC,CAAE;QA8ChC,OAAA,cAAA,CAAA,IAAA,EAAA,cAAA;mBAAA;;QArDA,OAAA,cAAA,CAAA,IAAA,EAAA,iBAAA;;mBAAA,KAAA;;QACA,OAAA,cAAA,CAAA,IAAA,EAAA,eAAA;;mBAAA,KAAA;;QACA,OAAA,cAAA,CAAA,IAAA,EAAA,QAAA;;mBAAA,KAAA;;+LAME,IAAA,EAAA,IAAI,EAAC,gBAAA,CAAA,gBAAA,GAAkBA;SACvB,0LAAA,EAAA,IAAI,EAAC,cAAA,CAAA,cAAA,GAAgB;+LACrB,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,GAAS,EAAE;IAClB;AAkDF;AARE,SAAA,YAAaC,MAAc;IAAdA,IAAAA,WAAAA,KAAAA,GAAAA,SAAS;IACpB,IACG,wLAAA,IAAA,EAAA,IAAI,EAAC,cAAA,CAAA,cAAA,GAAgB,2LAAA,EAAA,IAAI,EAAC,gBAAA,CAAA,gBAAA,IAAmBA,MAAK,4LACnD,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,CAAOC,MAAM,GAAG,GACrB;YACA;SAAA,sOAAA,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,CAAOC,KAAK,EAAA,KAAA,OAAA,KAAA,IAAjB,6CAAqBhB,IAAI;IAC3B;AACF", "ignoreList": [0]}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/segment-cache.ts"], "sourcesContent": ["/**\n * Entry point to the Segment Cache implementation.\n *\n * All code related to the Segment Cache lives `segment-cache-impl` directory.\n * Callers access it through this indirection.\n *\n * This is to ensure the code is dead code eliminated from the bundle if the\n * flag is disabled.\n *\n * TODO: This is super tedious. Since experimental flags are an essential part\n * of our workflow, we should establish a better pattern for dead code\n * elimination. Ideally it would be done at the bundler level, like how React's\n * build process works. In the React repo, you don't even need to add any extra\n * configuration per experiment — if the code is not reachable, it gets stripped\n * from the build automatically by Rollup. Or, shorter term, we could stub out\n * experimental modules at build time by updating the build config, i.e. a more\n * automated version of what I'm doing manually in this file.\n */\n\nexport type { NavigationResult } from './segment-cache-impl/navigation'\nexport type { PrefetchTask } from './segment-cache-impl/scheduler'\n\nconst notEnabled: any = () => {\n  throw new Error(\n    'Segment Cache experiment is not enabled. This is a bug in Next.js.'\n  )\n}\n\nexport const prefetch: typeof import('./segment-cache-impl/prefetch').prefetch =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/prefetch').prefetch(...args)\n      }\n    : notEnabled\n\nexport const navigate: typeof import('./segment-cache-impl/navigation').navigate =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/navigation').navigate(...args)\n      }\n    : notEnabled\n\nexport const revalidateEntireCache: typeof import('./segment-cache-impl/cache').revalidateEntireCache =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/cache').revalidateEntireCache(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const getCurrentCacheVersion: typeof import('./segment-cache-impl/cache').getCurrentCacheVersion =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/cache').getCurrentCacheVersion(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const schedulePrefetchTask: typeof import('./segment-cache-impl/scheduler').schedulePrefetchTask =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/scheduler').schedulePrefetchTask(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const cancelPrefetchTask: typeof import('./segment-cache-impl/scheduler').cancelPrefetchTask =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/scheduler').cancelPrefetchTask(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const reschedulePrefetchTask: typeof import('./segment-cache-impl/scheduler').reschedulePrefetchTask =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/scheduler').reschedulePrefetchTask(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const createCacheKey: typeof import('./segment-cache-impl/cache-key').createCacheKey =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/cache-key').createCacheKey(...args)\n      }\n    : notEnabled\n\n/**\n * Below are public constants. They're small enough that we don't need to\n * DCE them.\n */\n\nexport const enum NavigationResultTag {\n  MPA,\n  Success,\n  NoOp,\n  Async,\n}\n\n/**\n * The priority of the prefetch task. Higher numbers are higher priority.\n */\nexport const enum PrefetchPriority {\n  /**\n   * Assigned to any visible link that was hovered/touched at some point. This\n   * is not removed on mouse exit, because a link that was momentarily\n   * hovered is more likely to to be interacted with than one that was not.\n   */\n  Intent = 2,\n  /**\n   * The default priority for prefetch tasks.\n   */\n  Default = 1,\n  /**\n   * Assigned to tasks when they spawn non-blocking background work, like\n   * revalidating a partially cached entry to see if more data is available.\n   */\n  Background = 0,\n}\n"], "names": ["notEnabled", "Error", "prefetch", "process", "env", "__NEXT_CLIENT_SEGMENT_CACHE", "args", "require", "navigate", "revalidateEntireCache", "getCurrentCacheVersion", "schedulePrefetchTask", "cancelPrefetchTask", "reschedulePrefetchTask", "createCacheKey", "NavigationResultTag", "PrefetchPriority"], "mappings": "AAAA;;;;;;;;;;;;;;;;;CAiBC;;;;;;;;;;;;AAKD,MAAMA,aAAkB;IACtB,MAAM,OAAA,cAEL,CAFK,IAAIC,MACR,uEADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEO,MAAMC,WACXC,QAAQC,GAAG,CAACC,2BAA2B,GACnC,mCAGAL,WAAU;AAET,MAAMQ,WACXL,QAAQC,GAAG,CAACC,2BAA2B,GACnC,mCAGAL,WAAU;AAET,MAAMS,wBACXN,QAAQC,GAAG,CAACC,2BAA2B,GACnC,mCAKAL,WAAU;AAET,MAAMU,yBACXP,QAAQC,GAAG,CAACC,2BAA2B,GACnC,mCAKAL,WAAU;AAET,MAAMW,uBACXR,QAAQC,GAAG,CAACC,2BAA2B,GACnC,mCAKAL,WAAU;AAET,MAAMY,qBACXT,QAAQC,GAAG,CAACC,2BAA2B,GACnC,mCAKAL,WAAU;AAET,MAAMa,yBACXV,QAAQC,GAAG,CAACC,2BAA2B,GACnC,mCAKAL,WAAU;AAET,MAAMc,iBACXX,QAAQC,GAAG,CAACC,2BAA2B,GACnC,mCAGAL,WAAU;AAOT,IAAWe,sBAAAA,WAAAA,GAAAA,SAAAA,mBAAAA;;;;;WAAAA;MAKjB;AAKM,IAAWC,mBAAAA,WAAAA,GAAAA,SAAAA,gBAAAA;IAChB;;;;GAIC,GAAA,gBAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,EAAA,GAAA;IAED;;GAEC,GAAA,gBAAA,CAAA,gBAAA,CAAA,UAAA,GAAA,EAAA,GAAA;IAED;;;GAGC,GAAA,gBAAA,CAAA,gBAAA,CAAA,aAAA,GAAA,EAAA,GAAA;WAdeA;MAgBjB", "ignoreList": [0]}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/use-action-queue.ts"], "sourcesContent": ["import type { Dispatch } from 'react'\nimport React, { use } from 'react'\nimport { isThenable } from '../../shared/lib/is-thenable'\nimport type { AppRouterActionQueue } from './app-router-instance'\nimport type {\n  AppRouterState,\n  ReducerActions,\n  ReducerState,\n} from './router-reducer/router-reducer-types'\n\n// The app router state lives outside of React, so we can import the dispatch\n// method directly wherever we need it, rather than passing it around via props\n// or context.\nlet dispatch: Dispatch<ReducerActions> | null = null\n\nexport function dispatchAppRouterAction(action: ReducerActions) {\n  if (dispatch === null) {\n    throw new Error(\n      'Internal Next.js error: Router action dispatched before initialization.'\n    )\n  }\n  dispatch(action)\n}\n\nexport function useActionQueue(\n  actionQueue: AppRouterActionQueue\n): AppRouterState {\n  const [state, setState] = React.useState<ReducerState>(actionQueue.state)\n\n  // Because of a known issue that requires to decode Flight streams inside the\n  // render phase, we have to be a bit clever and assign the dispatch method to\n  // a module-level variable upon initialization. The useState hook in this\n  // module only exists to synchronize state that lives outside of React.\n  // Ideally, what we'd do instead is pass the state as a prop to root.render;\n  // this is conceptually how we're modeling the app router state, despite the\n  // weird implementation details.\n  if (process.env.NODE_ENV !== 'production') {\n    const useSyncDevRenderIndicator =\n      require('./react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator')\n        .useSyncDevRenderIndicator as typeof import('./react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator').useSyncDevRenderIndicator\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const syncDevRenderIndicator = useSyncDevRenderIndicator()\n\n    dispatch = (action: ReducerActions) => {\n      syncDevRenderIndicator(() => {\n        actionQueue.dispatch(action, setState)\n      })\n    }\n  } else {\n    dispatch = (action: ReducerActions) =>\n      actionQueue.dispatch(action, setState)\n  }\n\n  return isThenable(state) ? use(state) : state\n}\n"], "names": ["React", "use", "isThenable", "dispatch", "dispatchAppRouterAction", "action", "Error", "useActionQueue", "actionQueue", "state", "setState", "useState", "process", "env", "NODE_ENV", "useSyncDevRenderIndicator", "require", "syncDevRenderIndicator"], "mappings": ";;;;AACA,OAAOA,SAASC,GAAG,QAAQ,QAAO;AAClC,SAASC,UAAU,QAAQ,+BAA8B;;;AAQzD,6EAA6E;AAC7E,+EAA+E;AAC/E,cAAc;AACd,IAAIC,WAA4C;AAEzC,SAASC,wBAAwBC,MAAsB;IAC5D,IAAIF,aAAa,MAAM;QACrB,MAAM,OAAA,cAEL,CAFK,IAAIG,MACR,4EADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACAH,SAASE;AACX;AAEO,SAASE,eACdC,WAAiC;IAEjC,MAAM,CAACC,OAAOC,SAAS,yLAAGV,UAAAA,CAAMW,QAAQ,CAAeH,YAAYC,KAAK;IAExE,6EAA6E;IAC7E,6EAA6E;IAC7E,yEAAyE;IACzE,uEAAuE;IACvE,4EAA4E;IAC5E,4EAA4E;IAC5E,gCAAgC;IAChC,IAAIG,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,MAAMC,4BACJC,QAAQ,6KACLD,yBAAyB;QAC9B,sDAAsD;QACtD,MAAME,yBAAyBF;QAE/BZ,WAAW,CAACE;YACVY,uBAAuB;gBACrBT,YAAYL,QAAQ,CAACE,QAAQK;YAC/B;QACF;IACF,OAAO;;IAGP;IAEA,0LAAOR,aAAAA,EAAWO,mMAASR,MAAAA,EAAIQ,SAASA;AAC1C", "ignoreList": [0]}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/links.ts"], "sourcesContent": ["import type { FlightRouterState } from '../../server/app-render/types'\nimport type { AppRouterInstance } from '../../shared/lib/app-router-context.shared-runtime'\nimport { getCurrentAppRouterState } from './app-router-instance'\nimport { createPrefetchURL } from './app-router'\nimport { PrefetchKind } from './router-reducer/router-reducer-types'\nimport { getCurrentCacheVersion } from './segment-cache'\nimport { createCacheKey } from './segment-cache'\nimport {\n  type PrefetchTask,\n  PrefetchPriority,\n  schedulePrefetchTask as scheduleSegmentPrefetchTask,\n  cancelPrefetchTask,\n  reschedulePrefetchTask,\n} from './segment-cache'\nimport { startTransition } from 'react'\n\ntype LinkElement = HTMLAnchorElement | SVGAElement\n\ntype Element = LinkElement | HTMLFormElement\n\n// Properties that are shared between Link and Form instances. We use the same\n// shape for both to prevent a polymorphic de-opt in the VM.\ntype LinkOrFormInstanceShared = {\n  router: AppRouterInstance\n  kind: PrefetchKind.AUTO | PrefetchKind.FULL\n\n  isVisible: boolean\n  wasHoveredOrTouched: boolean\n\n  // The most recently initiated prefetch task. It may or may not have\n  // already completed.  The same prefetch task object can be reused across\n  // multiple prefetches of the same link.\n  prefetchTask: PrefetchTask | null\n\n  // The cache version at the time the task was initiated. This is used to\n  // determine if the cache was invalidated since the task was initiated.\n  cacheVersion: number\n}\n\nexport type FormInstance = LinkOrFormInstanceShared & {\n  prefetchHref: string\n  setOptimisticLinkStatus: null\n}\n\ntype PrefetchableLinkInstance = LinkOrFormInstanceShared & {\n  prefetchHref: string\n  setOptimisticLinkStatus: (status: { pending: boolean }) => void\n}\n\ntype NonPrefetchableLinkInstance = LinkOrFormInstanceShared & {\n  prefetchHref: null\n  setOptimisticLinkStatus: (status: { pending: boolean }) => void\n}\n\ntype PrefetchableInstance = PrefetchableLinkInstance | FormInstance\n\nexport type LinkInstance =\n  | PrefetchableLinkInstance\n  | NonPrefetchableLinkInstance\n\n// Tracks the most recently navigated link instance. When null, indicates\n// the current navigation was not initiated by a link click.\nlet linkForMostRecentNavigation: LinkInstance | null = null\n\n// Status object indicating link is pending\nexport const PENDING_LINK_STATUS = { pending: true }\n\n// Status object indicating link is idle\nexport const IDLE_LINK_STATUS = { pending: false }\n\n// Updates the loading state when navigating between links\n// - Resets the previous link's loading state\n// - Sets the new link's loading state\n// - Updates tracking of current navigation\nexport function setLinkForCurrentNavigation(link: LinkInstance | null) {\n  startTransition(() => {\n    linkForMostRecentNavigation?.setOptimisticLinkStatus(IDLE_LINK_STATUS)\n    link?.setOptimisticLinkStatus(PENDING_LINK_STATUS)\n    linkForMostRecentNavigation = link\n  })\n}\n\n// Unmounts the current link instance from navigation tracking\nexport function unmountLinkForCurrentNavigation(link: LinkInstance) {\n  if (linkForMostRecentNavigation === link) {\n    linkForMostRecentNavigation = null\n  }\n}\n\n// Use a WeakMap to associate a Link instance with its DOM element. This is\n// used by the IntersectionObserver to track the link's visibility.\nconst prefetchable:\n  | WeakMap<Element, PrefetchableInstance>\n  | Map<Element, PrefetchableInstance> =\n  typeof WeakMap === 'function' ? new WeakMap() : new Map()\n\n// A Set of the currently visible links. We re-prefetch visible links after a\n// cache invalidation, or when the current URL changes. It's a separate data\n// structure from the WeakMap above because only the visible links need to\n// be enumerated.\nconst prefetchableAndVisible: Set<PrefetchableInstance> = new Set()\n\n// A single IntersectionObserver instance shared by all <Link> components.\nconst observer: IntersectionObserver | null =\n  typeof IntersectionObserver === 'function'\n    ? new IntersectionObserver(handleIntersect, {\n        rootMargin: '200px',\n      })\n    : null\n\nfunction observeVisibility(element: Element, instance: PrefetchableInstance) {\n  const existingInstance = prefetchable.get(element)\n  if (existingInstance !== undefined) {\n    // This shouldn't happen because each <Link> component should have its own\n    // anchor tag instance, but it's defensive coding to avoid a memory leak in\n    // case there's a logical error somewhere else.\n    unmountPrefetchableInstance(element)\n  }\n  // Only track prefetchable links that have a valid prefetch URL\n  prefetchable.set(element, instance)\n  if (observer !== null) {\n    observer.observe(element)\n  }\n}\n\nfunction coercePrefetchableUrl(href: string): URL | null {\n  try {\n    return createPrefetchURL(href)\n  } catch {\n    // createPrefetchURL sometimes throws an error if an invalid URL is\n    // provided, though I'm not sure if it's actually necessary.\n    // TODO: Consider removing the throw from the inner function, or change it\n    // to reportError. Or maybe the error isn't even necessary for automatic\n    // prefetches, just navigations.\n    const reportErrorFn =\n      typeof reportError === 'function' ? reportError : console.error\n    reportErrorFn(\n      `Cannot prefetch '${href}' because it cannot be converted to a URL.`\n    )\n    return null\n  }\n}\n\nexport function mountLinkInstance(\n  element: LinkElement,\n  href: string,\n  router: AppRouterInstance,\n  kind: PrefetchKind.AUTO | PrefetchKind.FULL,\n  prefetchEnabled: boolean,\n  setOptimisticLinkStatus: (status: { pending: boolean }) => void\n): LinkInstance {\n  if (prefetchEnabled) {\n    const prefetchURL = coercePrefetchableUrl(href)\n    if (prefetchURL !== null) {\n      const instance: PrefetchableLinkInstance = {\n        router,\n        kind,\n        isVisible: false,\n        wasHoveredOrTouched: false,\n        prefetchTask: null,\n        cacheVersion: -1,\n        prefetchHref: prefetchURL.href,\n        setOptimisticLinkStatus,\n      }\n      // We only observe the link's visibility if it's prefetchable. For\n      // example, this excludes links to external URLs.\n      observeVisibility(element, instance)\n      return instance\n    }\n  }\n  // If the link is not prefetchable, we still create an instance so we can\n  // track its optimistic state (i.e. useLinkStatus).\n  const instance: NonPrefetchableLinkInstance = {\n    router,\n    kind,\n    isVisible: false,\n    wasHoveredOrTouched: false,\n    prefetchTask: null,\n    cacheVersion: -1,\n    prefetchHref: null,\n    setOptimisticLinkStatus,\n  }\n  return instance\n}\n\nexport function mountFormInstance(\n  element: HTMLFormElement,\n  href: string,\n  router: AppRouterInstance,\n  kind: PrefetchKind.AUTO | PrefetchKind.FULL\n): void {\n  const prefetchURL = coercePrefetchableUrl(href)\n  if (prefetchURL === null) {\n    // This href is not prefetchable, so we don't track it.\n    // TODO: We currently observe/unobserve a form every time its href changes.\n    // For Links, this isn't a big deal because the href doesn't usually change,\n    // but for forms it's extremely common. We should optimize this.\n    return\n  }\n  const instance: FormInstance = {\n    router,\n    kind,\n    isVisible: false,\n    wasHoveredOrTouched: false,\n    prefetchTask: null,\n    cacheVersion: -1,\n    prefetchHref: prefetchURL.href,\n    setOptimisticLinkStatus: null,\n  }\n  observeVisibility(element, instance)\n}\n\nexport function unmountPrefetchableInstance(element: Element) {\n  const instance = prefetchable.get(element)\n  if (instance !== undefined) {\n    prefetchable.delete(element)\n    prefetchableAndVisible.delete(instance)\n    const prefetchTask = instance.prefetchTask\n    if (prefetchTask !== null) {\n      cancelPrefetchTask(prefetchTask)\n    }\n  }\n  if (observer !== null) {\n    observer.unobserve(element)\n  }\n}\n\nfunction handleIntersect(entries: Array<IntersectionObserverEntry>) {\n  for (const entry of entries) {\n    // Some extremely old browsers or polyfills don't reliably support\n    // isIntersecting so we check intersectionRatio instead. (Do we care? Not\n    // really. But whatever this is fine.)\n    const isVisible = entry.intersectionRatio > 0\n    onLinkVisibilityChanged(entry.target as HTMLAnchorElement, isVisible)\n  }\n}\n\nexport function onLinkVisibilityChanged(element: Element, isVisible: boolean) {\n  if (process.env.NODE_ENV !== 'production') {\n    // Prefetching on viewport is disabled in development for performance\n    // reasons, because it requires compiling the target page.\n    // TODO: Investigate re-enabling this.\n    return\n  }\n\n  const instance = prefetchable.get(element)\n  if (instance === undefined) {\n    return\n  }\n\n  instance.isVisible = isVisible\n  if (isVisible) {\n    prefetchableAndVisible.add(instance)\n  } else {\n    prefetchableAndVisible.delete(instance)\n  }\n  rescheduleLinkPrefetch(instance)\n}\n\nexport function onNavigationIntent(\n  element: HTMLAnchorElement | SVGAElement,\n  unstable_upgradeToDynamicPrefetch: boolean\n) {\n  const instance = prefetchable.get(element)\n  if (instance === undefined) {\n    return\n  }\n  // Prefetch the link on hover/touchstart.\n  if (instance !== undefined) {\n    instance.wasHoveredOrTouched = true\n    if (\n      process.env.__NEXT_DYNAMIC_ON_HOVER &&\n      unstable_upgradeToDynamicPrefetch\n    ) {\n      // Switch to a full, dynamic prefetch\n      instance.kind = PrefetchKind.FULL\n    }\n    rescheduleLinkPrefetch(instance)\n  }\n}\n\nfunction rescheduleLinkPrefetch(instance: PrefetchableInstance) {\n  const existingPrefetchTask = instance.prefetchTask\n\n  if (!instance.isVisible) {\n    // Cancel any in-progress prefetch task. (If it already finished then this\n    // is a no-op.)\n    if (existingPrefetchTask !== null) {\n      cancelPrefetchTask(existingPrefetchTask)\n    }\n    // We don't need to reset the prefetchTask to null upon cancellation; an\n    // old task object can be rescheduled with reschedulePrefetchTask. This is a\n    // micro-optimization but also makes the code simpler (don't need to\n    // worry about whether an old task object is stale).\n    return\n  }\n\n  if (!process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n    // The old prefetch implementation does not have different priority levels.\n    // Just schedule a new prefetch task.\n    prefetchWithOldCacheImplementation(instance)\n    return\n  }\n\n  // In the Segment Cache implementation, we assign a higher priority level to\n  // links that were at one point hovered or touched. Since the queue is last-\n  // in-first-out, the highest priority Link is whichever one was hovered last.\n  //\n  // We also increase the relative priority of links whenever they re-enter the\n  // viewport, as if they were being scheduled for the first time.\n  const priority = instance.wasHoveredOrTouched\n    ? PrefetchPriority.Intent\n    : PrefetchPriority.Default\n  const appRouterState = getCurrentAppRouterState()\n  if (appRouterState !== null) {\n    const treeAtTimeOfPrefetch = appRouterState.tree\n    if (existingPrefetchTask === null) {\n      // Initiate a prefetch task.\n      const nextUrl = appRouterState.nextUrl\n      const cacheKey = createCacheKey(instance.prefetchHref, nextUrl)\n      instance.prefetchTask = scheduleSegmentPrefetchTask(\n        cacheKey,\n        treeAtTimeOfPrefetch,\n        instance.kind === PrefetchKind.FULL,\n        priority\n      )\n    } else {\n      // We already have an old task object that we can reschedule. This is\n      // effectively the same as canceling the old task and creating a new one.\n      reschedulePrefetchTask(\n        existingPrefetchTask,\n        treeAtTimeOfPrefetch,\n        instance.kind === PrefetchKind.FULL,\n        priority\n      )\n    }\n\n    // Keep track of the cache version at the time the prefetch was requested.\n    // This is used to check if the prefetch is stale.\n    instance.cacheVersion = getCurrentCacheVersion()\n  }\n}\n\nexport function pingVisibleLinks(\n  nextUrl: string | null,\n  tree: FlightRouterState\n) {\n  // For each currently visible link, cancel the existing prefetch task (if it\n  // exists) and schedule a new one. This is effectively the same as if all the\n  // visible links left and then re-entered the viewport.\n  //\n  // This is called when the Next-Url or the base tree changes, since those\n  // may affect the result of a prefetch task. It's also called after a\n  // cache invalidation.\n  const currentCacheVersion = getCurrentCacheVersion()\n  for (const instance of prefetchableAndVisible) {\n    const task = instance.prefetchTask\n    if (\n      task !== null &&\n      instance.cacheVersion === currentCacheVersion &&\n      task.key.nextUrl === nextUrl &&\n      task.treeAtTimeOfPrefetch === tree\n    ) {\n      // The cache has not been invalidated, and none of the inputs have\n      // changed. Bail out.\n      continue\n    }\n    // Something changed. Cancel the existing prefetch task and schedule a\n    // new one.\n    if (task !== null) {\n      cancelPrefetchTask(task)\n    }\n    const cacheKey = createCacheKey(instance.prefetchHref, nextUrl)\n    const priority = instance.wasHoveredOrTouched\n      ? PrefetchPriority.Intent\n      : PrefetchPriority.Default\n    instance.prefetchTask = scheduleSegmentPrefetchTask(\n      cacheKey,\n      tree,\n      instance.kind === PrefetchKind.FULL,\n      priority\n    )\n    instance.cacheVersion = getCurrentCacheVersion()\n  }\n}\n\nfunction prefetchWithOldCacheImplementation(instance: PrefetchableInstance) {\n  // This is the path used when the Segment Cache is not enabled.\n  if (typeof window === 'undefined') {\n    return\n  }\n\n  const doPrefetch = async () => {\n    // note that `appRouter.prefetch()` is currently sync,\n    // so we have to wrap this call in an async function to be able to catch() errors below.\n    return instance.router.prefetch(instance.prefetchHref, {\n      kind: instance.kind,\n    })\n  }\n\n  // Prefetch the page if asked (only in the client)\n  // We need to handle a prefetch error here since we may be\n  // loading with priority which can reject but we don't\n  // want to force navigation since this is only a prefetch\n  doPrefetch().catch((err) => {\n    if (process.env.NODE_ENV !== 'production') {\n      // rethrow to show invalid URL errors\n      throw err\n    }\n  })\n}\n"], "names": ["getCurrentAppRouterState", "createPrefetchURL", "PrefetchKind", "getCurrentCacheVersion", "createCacheKey", "PrefetchPriority", "schedulePrefetchTask", "scheduleSegmentPrefetchTask", "cancelPrefetchTask", "reschedulePrefetchTask", "startTransition", "linkForMostRecentNavigation", "PENDING_LINK_STATUS", "pending", "IDLE_LINK_STATUS", "setLinkForCurrentNavigation", "link", "setOptimisticLinkStatus", "unmountLinkForCurrentNavigation", "prefetchable", "WeakMap", "Map", "prefetchableAndVisible", "Set", "observer", "IntersectionObserver", "handleIntersect", "rootMargin", "observeVisibility", "element", "instance", "existingInstance", "get", "undefined", "unmountPrefetchableInstance", "set", "observe", "coercePrefetchableUrl", "href", "reportErrorFn", "reportError", "console", "error", "mountLinkInstance", "router", "kind", "prefetchEnabled", "prefetchURL", "isVisible", "wasHoveredOrTouched", "prefetchTask", "cacheVersion", "prefetchHref", "mountFormInstance", "delete", "unobserve", "entries", "entry", "intersectionRatio", "onLinkVisibilityChanged", "target", "process", "env", "NODE_ENV", "add", "rescheduleLinkPrefetch", "onNavigationIntent", "unstable_upgradeToDynamicPrefetch", "__NEXT_DYNAMIC_ON_HOVER", "FULL", "existingPrefetchTask", "__NEXT_CLIENT_SEGMENT_CACHE", "prefetchWithOldCacheImplementation", "priority", "Intent", "<PERSON><PERSON><PERSON>", "appRouterState", "treeAtTimeOfPrefetch", "tree", "nextUrl", "cache<PERSON>ey", "pingVisibleLinks", "currentCacheVersion", "task", "key", "window", "doPrefetch", "prefetch", "catch", "err"], "mappings": ";;;;;;;;;;;;AAEA,SAASA,wBAAwB,QAAQ,wBAAuB;AAChE,SAASC,iBAAiB,QAAQ,eAAc;AAChD,SAASC,YAAY,QAAQ,wCAAuC;AACpE,SAASC,sBAAsB,QAAQ,kBAAiB;AASxD,SAASO,eAAe,QAAQ,QAAO;;;;;;;;AA8CvC,yEAAyE;AACzE,4DAA4D;AAC5D,IAAIC,8BAAmD;AAGhD,MAAMC,sBAAsB;IAAEC,SAAS;AAAK,EAAC;AAG7C,MAAMC,mBAAmB;IAAED,SAAS;AAAM,EAAC;AAM3C,SAASE,4BAA4BC,IAAyB;8LACnEN,kBAAAA,EAAgB;QACdC,+BAAAA,OAAAA,KAAAA,IAAAA,4BAA6BM,uBAAuB,CAACH;QACrDE,QAAAA,OAAAA,KAAAA,IAAAA,KAAMC,uBAAuB,CAACL;QAC9BD,8BAA8BK;IAChC;AACF;AAGO,SAASE,gCAAgCF,IAAkB;IAChE,IAAIL,gCAAgCK,MAAM;QACxCL,8BAA8B;IAChC;AACF;AAEA,2EAA2E;AAC3E,mEAAmE;AACnE,MAAMQ,eAGJ,OAAOC,YAAY,aAAa,IAAIA,YAAY,IAAIC;AAEtD,6EAA6E;AAC7E,4EAA4E;AAC5E,0EAA0E;AAC1E,iBAAiB;AACjB,MAAMC,yBAAoD,IAAIC;AAE9D,0EAA0E;AAC1E,MAAMC,WACJ,OAAOC,yBAAyB,aAC5B,IAAIA,qBAAqBC,iBAAiB;IACxCC,YAAY;AACd,KACA;AAEN,SAASC,kBAAkBC,OAAgB,EAAEC,QAA8B;IACzE,MAAMC,mBAAmBZ,aAAaa,GAAG,CAACH;IAC1C,IAAIE,qBAAqBE,WAAW;QAClC,0EAA0E;QAC1E,2EAA2E;QAC3E,+CAA+C;QAC/CC,4BAA4BL;IAC9B;IACA,+DAA+D;IAC/DV,aAAagB,GAAG,CAACN,SAASC;IAC1B,IAAIN,aAAa,MAAM;QACrBA,SAASY,OAAO,CAACP;IACnB;AACF;AAEA,SAASQ,sBAAsBC,IAAY;IACzC,IAAI;QACF,gMAAOrC,oBAAAA,EAAkBqC;IAC3B,EAAE,OAAA,GAAM;QACN,mEAAmE;QACnE,4DAA4D;QAC5D,0EAA0E;QAC1E,wEAAwE;QACxE,gCAAgC;QAChC,MAAMC,gBACJ,OAAOC,gBAAgB,aAAaA,cAAcC,QAAQC,KAAK;QACjEH,cACG,sBAAmBD,OAAK;QAE3B,OAAO;IACT;AACF;AAEO,SAASK,kBACdd,OAAoB,EACpBS,IAAY,EACZM,MAAyB,EACzBC,IAA2C,EAC3CC,eAAwB,EACxB7B,uBAA+D;IAE/D,IAAI6B,iBAAiB;QACnB,MAAMC,cAAcV,sBAAsBC;QAC1C,IAAIS,gBAAgB,MAAM;YACxB,MAAMjB,WAAqC;gBACzCc;gBACAC;gBACAG,WAAW;gBACXC,qBAAqB;gBACrBC,cAAc;gBACdC,cAAc,CAAC;gBACfC,cAAcL,YAAYT,IAAI;gBAC9BrB;YACF;YACA,kEAAkE;YAClE,iDAAiD;YACjDW,kBAAkBC,SAASC;YAC3B,OAAOA;QACT;IACF;IACA,yEAAyE;IACzE,mDAAmD;IACnD,MAAMA,WAAwC;QAC5Cc;QACAC;QACAG,WAAW;QACXC,qBAAqB;QACrBC,cAAc;QACdC,cAAc,CAAC;QACfC,cAAc;QACdnC;IACF;IACA,OAAOa;AACT;AAEO,SAASuB,kBACdxB,OAAwB,EACxBS,IAAY,EACZM,MAAyB,EACzBC,IAA2C;IAE3C,MAAME,cAAcV,sBAAsBC;IAC1C,IAAIS,gBAAgB,MAAM;QACxB,uDAAuD;QACvD,2EAA2E;QAC3E,4EAA4E;QAC5E,gEAAgE;QAChE;IACF;IACA,MAAMjB,WAAyB;QAC7Bc;QACAC;QACAG,WAAW;QACXC,qBAAqB;QACrBC,cAAc;QACdC,cAAc,CAAC;QACfC,cAAcL,YAAYT,IAAI;QAC9BrB,yBAAyB;IAC3B;IACAW,kBAAkBC,SAASC;AAC7B;AAEO,SAASI,4BAA4BL,OAAgB;IAC1D,MAAMC,WAAWX,aAAaa,GAAG,CAACH;IAClC,IAAIC,aAAaG,WAAW;QAC1Bd,aAAamC,MAAM,CAACzB;QACpBP,uBAAuBgC,MAAM,CAACxB;QAC9B,MAAMoB,eAAepB,SAASoB,YAAY;QAC1C,IAAIA,iBAAiB,MAAM;wMACzB1C,qBAAAA,EAAmB0C;QACrB;IACF;IACA,IAAI1B,aAAa,MAAM;QACrBA,SAAS+B,SAAS,CAAC1B;IACrB;AACF;AAEA,SAASH,gBAAgB8B,OAAyC;IAChE,KAAK,MAAMC,SAASD,QAAS;QAC3B,kEAAkE;QAClE,yEAAyE;QACzE,sCAAsC;QACtC,MAAMR,YAAYS,MAAMC,iBAAiB,GAAG;QAC5CC,wBAAwBF,MAAMG,MAAM,EAAuBZ;IAC7D;AACF;AAEO,SAASW,wBAAwB9B,OAAgB,EAAEmB,SAAkB;IAC1E,IAAIa,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,qEAAqE;QACrE,0DAA0D;QAC1D,sCAAsC;QACtC;IACF;;IAEA,MAAMjC,WAAWX,aAAaa,GAAG,CAACH;AAYpC;AAEO,SAASqC,mBACdrC,OAAwC,EACxCsC,iCAA0C;IAE1C,MAAMrC,WAAWX,aAAaa,GAAG,CAACH;IAClC,IAAIC,aAAaG,WAAW;QAC1B;IACF;IACA,yCAAyC;IACzC,IAAIH,aAAaG,WAAW;QAC1BH,SAASmB,mBAAmB,GAAG;QAC/B,IACEY,QAAQC,GAAG,CAACM,uBAAuB,AAEnC,IADAD;;QAIF;QACAF,uBAAuBnC;IACzB;AACF;AAEA,SAASmC,uBAAuBnC,QAA8B;IAC5D,MAAMwC,uBAAuBxC,SAASoB,YAAY;IAElD,IAAI,CAACpB,SAASkB,SAAS,EAAE;QACvB,0EAA0E;QAC1E,eAAe;QACf,IAAIsB,yBAAyB,MAAM;wMACjC9D,qBAAAA,EAAmB8D;QACrB;QACA,wEAAwE;QACxE,4EAA4E;QAC5E,oEAAoE;QACpE,oDAAoD;QACpD;IACF;IAEA,IAAI,CAACT,QAAQC,GAAG,CAACS,uBAA6B,IAAF;QAC1C,2EAA2E;QAC3E,qCAAqC;QACrCC,mCAAmC1C;QACnC;IACF;;IAEA,4EAA4E;IAC5E,4EAA4E;IAC5E,6EAA6E;IAC7E,EAAE;IACF,6EAA6E;IAC7E,gEAAgE;IAChE,MAAM2C,WAAW3C,SAASmB,mBAAmB,GACzC5C,iBAAiBqE,MAAM,GACvBrE,iBAAiBsE,OAAO;IAC5B,MAAMC,iBAAiB5E;AA4BzB;AAEO,SAASiF,iBACdF,OAAsB,EACtBD,IAAuB;IAEvB,4EAA4E;IAC5E,6EAA6E;IAC7E,uDAAuD;IACvD,EAAE;IACF,yEAAyE;IACzE,qEAAqE;IACrE,sBAAsB;IACtB,MAAMI,kNAAsB/E,yBAAAA;IAC5B,KAAK,MAAM2B,YAAYR,uBAAwB;QAC7C,MAAM6D,OAAOrD,SAASoB,YAAY;QAClC,IACEiC,SAAS,QACTrD,SAASqB,YAAY,KAAK+B,uBAC1BC,KAAKC,GAAG,CAACL,OAAO,KAAKA,WACrBI,KAAKN,oBAAoB,KAAKC,MAC9B;YAGA;QACF;QACA,sEAAsE;QACtE,WAAW;QACX,IAAIK,SAAS,MAAM;wMACjB3E,qBAAAA,EAAmB2E;QACrB;QACA,MAAMH,WAAW5E,6MAAAA,EAAe0B,SAASsB,YAAY,EAAE2B;QACvD,MAAMN,WAAW3C,SAASmB,mBAAmB,0LACzC5C,oBAAAA,CAAiBqE,MAAM,2LACvBrE,mBAAAA,CAAiBsE,OAAO;QAC5B7C,SAASoB,YAAY,+LAAG3C,uBAAAA,EACtByE,UACAF,MACAhD,SAASe,IAAI,2NAAK3C,gBAAAA,CAAamE,IAAI,EACnCI;QAEF3C,SAASqB,YAAY,+LAAGhD,yBAAAA;IAC1B;AACF;AAEA,SAASqE,mCAAmC1C,QAA8B;IACxE,+DAA+D;IAC/D,IAAI,OAAOuD,WAAW,kBAAa;QACjC;IACF;;IAEA,MAAMC,aAAa;AAkBrB", "ignoreList": [0]}}, {"offset": {"line": 533, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/app-router-instance.ts"], "sourcesContent": ["import {\n  type AppRouterState,\n  type ReducerActions,\n  type ReducerState,\n  ACTION_REFRESH,\n  ACTION_SERVER_ACTION,\n  ACTION_NAVIGATE,\n  ACTION_RESTORE,\n  type NavigateAction,\n  ACTION_HMR_REFRESH,\n  PrefetchKind,\n  ACTION_PREFETCH,\n} from './router-reducer/router-reducer-types'\nimport { reducer } from './router-reducer/router-reducer'\nimport { startTransition } from 'react'\nimport { isThenable } from '../../shared/lib/is-thenable'\nimport { prefetch as prefetchWithSegmentCache } from './segment-cache'\nimport { dispatchAppRouterAction } from './use-action-queue'\nimport { addBasePath } from '../add-base-path'\nimport { createPrefetchURL, isExternalURL } from './app-router'\nimport { prefetchReducer } from './router-reducer/reducers/prefetch-reducer'\nimport type {\n  AppRouterInstance,\n  NavigateOptions,\n  PrefetchOptions,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport { setLinkForCurrentNavigation, type LinkInstance } from './links'\nimport type { FlightRouterState } from '../../server/app-render/types'\nimport type { ClientInstrumentationHooks } from '../app-index'\n\nexport type DispatchStatePromise = React.Dispatch<ReducerState>\n\nexport type AppRouterActionQueue = {\n  state: AppRouterState\n  dispatch: (payload: ReducerActions, setState: DispatchStatePromise) => void\n  action: (state: AppRouterState, action: ReducerActions) => ReducerState\n\n  onRouterTransitionStart:\n    | ((url: string, type: 'push' | 'replace' | 'traverse') => void)\n    | null\n\n  pending: ActionQueueNode | null\n  needsRefresh?: boolean\n  last: ActionQueueNode | null\n}\n\nexport type ActionQueueNode = {\n  payload: ReducerActions\n  next: ActionQueueNode | null\n  resolve: (value: ReducerState) => void\n  reject: (err: Error) => void\n  discarded?: boolean\n}\n\nfunction runRemainingActions(\n  actionQueue: AppRouterActionQueue,\n  setState: DispatchStatePromise\n) {\n  if (actionQueue.pending !== null) {\n    actionQueue.pending = actionQueue.pending.next\n    if (actionQueue.pending !== null) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      runAction({\n        actionQueue,\n        action: actionQueue.pending,\n        setState,\n      })\n    } else {\n      // No more actions are pending, check if a refresh is needed\n      if (actionQueue.needsRefresh) {\n        actionQueue.needsRefresh = false\n        actionQueue.dispatch(\n          {\n            type: ACTION_REFRESH,\n            origin: window.location.origin,\n          },\n          setState\n        )\n      }\n    }\n  }\n}\n\nasync function runAction({\n  actionQueue,\n  action,\n  setState,\n}: {\n  actionQueue: AppRouterActionQueue\n  action: ActionQueueNode\n  setState: DispatchStatePromise\n}) {\n  const prevState = actionQueue.state\n\n  actionQueue.pending = action\n\n  const payload = action.payload\n  const actionResult = actionQueue.action(prevState, payload)\n\n  function handleResult(nextState: AppRouterState) {\n    // if we discarded this action, the state should also be discarded\n    if (action.discarded) {\n      return\n    }\n\n    actionQueue.state = nextState\n\n    runRemainingActions(actionQueue, setState)\n    action.resolve(nextState)\n  }\n\n  // if the action is a promise, set up a callback to resolve it\n  if (isThenable(actionResult)) {\n    actionResult.then(handleResult, (err) => {\n      runRemainingActions(actionQueue, setState)\n      action.reject(err)\n    })\n  } else {\n    handleResult(actionResult)\n  }\n}\n\nfunction dispatchAction(\n  actionQueue: AppRouterActionQueue,\n  payload: ReducerActions,\n  setState: DispatchStatePromise\n) {\n  let resolvers: {\n    resolve: (value: ReducerState) => void\n    reject: (reason: any) => void\n  } = { resolve: setState, reject: () => {} }\n\n  // most of the action types are async with the exception of restore\n  // it's important that restore is handled quickly since it's fired on the popstate event\n  // and we don't want to add any delay on a back/forward nav\n  // this only creates a promise for the async actions\n  if (payload.type !== ACTION_RESTORE) {\n    // Create the promise and assign the resolvers to the object.\n    const deferredPromise = new Promise<AppRouterState>((resolve, reject) => {\n      resolvers = { resolve, reject }\n    })\n\n    startTransition(() => {\n      // we immediately notify React of the pending promise -- the resolver is attached to the action node\n      // and will be called when the associated action promise resolves\n      setState(deferredPromise)\n    })\n  }\n\n  const newAction: ActionQueueNode = {\n    payload,\n    next: null,\n    resolve: resolvers.resolve,\n    reject: resolvers.reject,\n  }\n\n  // Check if the queue is empty\n  if (actionQueue.pending === null) {\n    // The queue is empty, so add the action and start it immediately\n    // Mark this action as the last in the queue\n    actionQueue.last = newAction\n\n    runAction({\n      actionQueue,\n      action: newAction,\n      setState,\n    })\n  } else if (\n    payload.type === ACTION_NAVIGATE ||\n    payload.type === ACTION_RESTORE\n  ) {\n    // Navigations (including back/forward) take priority over any pending actions.\n    // Mark the pending action as discarded (so the state is never applied) and start the navigation action immediately.\n    actionQueue.pending.discarded = true\n\n    // The rest of the current queue should still execute after this navigation.\n    // (Note that it can't contain any earlier navigations, because we always put those into `actionQueue.pending` by calling `runAction`)\n    newAction.next = actionQueue.pending.next\n\n    // if the pending action was a server action, mark the queue as needing a refresh once events are processed\n    if (actionQueue.pending.payload.type === ACTION_SERVER_ACTION) {\n      actionQueue.needsRefresh = true\n    }\n\n    runAction({\n      actionQueue,\n      action: newAction,\n      setState,\n    })\n  } else {\n    // The queue is not empty, so add the action to the end of the queue\n    // It will be started by runRemainingActions after the previous action finishes\n    if (actionQueue.last !== null) {\n      actionQueue.last.next = newAction\n    }\n    actionQueue.last = newAction\n  }\n}\n\nlet globalActionQueue: AppRouterActionQueue | null = null\n\nexport function createMutableActionQueue(\n  initialState: AppRouterState,\n  instrumentationHooks: ClientInstrumentationHooks | null\n): AppRouterActionQueue {\n  const actionQueue: AppRouterActionQueue = {\n    state: initialState,\n    dispatch: (payload: ReducerActions, setState: DispatchStatePromise) =>\n      dispatchAction(actionQueue, payload, setState),\n    action: async (state: AppRouterState, action: ReducerActions) => {\n      const result = reducer(state, action)\n      return result\n    },\n    pending: null,\n    last: null,\n    onRouterTransitionStart:\n      instrumentationHooks !== null &&\n      typeof instrumentationHooks.onRouterTransitionStart === 'function'\n        ? // This profiling hook will be called at the start of every navigation.\n          instrumentationHooks.onRouterTransitionStart\n        : null,\n  }\n\n  if (typeof window !== 'undefined') {\n    // The action queue is lazily created on hydration, but after that point\n    // it doesn't change. So we can store it in a global rather than pass\n    // it around everywhere via props/context.\n    if (globalActionQueue !== null) {\n      throw new Error(\n        'Internal Next.js Error: createMutableActionQueue was called more ' +\n          'than once'\n      )\n    }\n    globalActionQueue = actionQueue\n  }\n\n  return actionQueue\n}\n\nexport function getCurrentAppRouterState(): AppRouterState | null {\n  return globalActionQueue !== null ? globalActionQueue.state : null\n}\n\nfunction getAppRouterActionQueue(): AppRouterActionQueue {\n  if (globalActionQueue === null) {\n    throw new Error(\n      'Internal Next.js error: Router action dispatched before initialization.'\n    )\n  }\n  return globalActionQueue\n}\n\nfunction getProfilingHookForOnNavigationStart() {\n  if (globalActionQueue !== null) {\n    return globalActionQueue.onRouterTransitionStart\n  }\n  return null\n}\n\nexport function dispatchNavigateAction(\n  href: string,\n  navigateType: NavigateAction['navigateType'],\n  shouldScroll: boolean,\n  linkInstanceRef: LinkInstance | null\n): void {\n  // TODO: This stuff could just go into the reducer. Leaving as-is for now\n  // since we're about to rewrite all the router reducer stuff anyway.\n  const url = new URL(addBasePath(href), location.href)\n  if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n    window.next.__pendingUrl = url\n  }\n\n  setLinkForCurrentNavigation(linkInstanceRef)\n\n  const onRouterTransitionStart = getProfilingHookForOnNavigationStart()\n  if (onRouterTransitionStart !== null) {\n    onRouterTransitionStart(href, navigateType)\n  }\n\n  dispatchAppRouterAction({\n    type: ACTION_NAVIGATE,\n    url,\n    isExternalUrl: isExternalURL(url),\n    locationSearch: location.search,\n    shouldScroll,\n    navigateType,\n    allowAliasing: true,\n  })\n}\n\nexport function dispatchTraverseAction(\n  href: string,\n  tree: FlightRouterState | undefined\n) {\n  const onRouterTransitionStart = getProfilingHookForOnNavigationStart()\n  if (onRouterTransitionStart !== null) {\n    onRouterTransitionStart(href, 'traverse')\n  }\n  dispatchAppRouterAction({\n    type: ACTION_RESTORE,\n    url: new URL(href),\n    tree,\n  })\n}\n\n/**\n * The app router that is exposed through `useRouter`. These are public API\n * methods. Internal Next.js code should call the lower level methods directly\n * (although there's lots of existing code that doesn't do that).\n */\nexport const publicAppRouterInstance: AppRouterInstance = {\n  back: () => window.history.back(),\n  forward: () => window.history.forward(),\n  prefetch: process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? // Unlike the old implementation, the Segment Cache doesn't store its\n      // data in the router reducer state; it writes into a global mutable\n      // cache. So we don't need to dispatch an action.\n      (href: string, options?: PrefetchOptions) => {\n        const actionQueue = getAppRouterActionQueue()\n        prefetchWithSegmentCache(\n          href,\n          actionQueue.state.nextUrl,\n          actionQueue.state.tree,\n          options?.kind === PrefetchKind.FULL\n        )\n      }\n    : (href: string, options?: PrefetchOptions) => {\n        // Use the old prefetch implementation.\n        const actionQueue = getAppRouterActionQueue()\n        const url = createPrefetchURL(href)\n        if (url !== null) {\n          // The prefetch reducer doesn't actually update any state or\n          // trigger a rerender. It just writes to a mutable cache. So we\n          // shouldn't bother calling setState/dispatch; we can just re-run\n          // the reducer directly using the current state.\n          // TODO: Refactor this away from a \"reducer\" so it's\n          // less confusing.\n          prefetchReducer(actionQueue.state, {\n            type: ACTION_PREFETCH,\n            url,\n            kind: options?.kind ?? PrefetchKind.FULL,\n          })\n        }\n      },\n  replace: (href: string, options?: NavigateOptions) => {\n    startTransition(() => {\n      dispatchNavigateAction(href, 'replace', options?.scroll ?? true, null)\n    })\n  },\n  push: (href: string, options?: NavigateOptions) => {\n    startTransition(() => {\n      dispatchNavigateAction(href, 'push', options?.scroll ?? true, null)\n    })\n  },\n  refresh: () => {\n    startTransition(() => {\n      dispatchAppRouterAction({\n        type: ACTION_REFRESH,\n        origin: window.location.origin,\n      })\n    })\n  },\n  hmrRefresh: () => {\n    if (process.env.NODE_ENV !== 'development') {\n      throw new Error(\n        'hmrRefresh can only be used in development mode. Please use refresh instead.'\n      )\n    } else {\n      startTransition(() => {\n        dispatchAppRouterAction({\n          type: ACTION_HMR_REFRESH,\n          origin: window.location.origin,\n        })\n      })\n    }\n  },\n}\n\n// Exists for debugging purposes. Don't use in application code.\nif (typeof window !== 'undefined' && window.next) {\n  window.next.router = publicAppRouterInstance\n}\n"], "names": ["ACTION_REFRESH", "ACTION_SERVER_ACTION", "ACTION_NAVIGATE", "ACTION_RESTORE", "ACTION_HMR_REFRESH", "PrefetchKind", "ACTION_PREFETCH", "reducer", "startTransition", "isThenable", "prefetch", "prefetchWithSegmentCache", "dispatchAppRouterAction", "addBasePath", "createPrefetchURL", "isExternalURL", "prefetchReducer", "setLinkForCurrentNavigation", "runRemainingActions", "actionQueue", "setState", "pending", "next", "runAction", "action", "needsRefresh", "dispatch", "type", "origin", "window", "location", "prevState", "state", "payload", "actionResult", "handleResult", "nextState", "discarded", "resolve", "then", "err", "reject", "dispatchAction", "resolvers", "deferred<PERSON><PERSON><PERSON>", "Promise", "newAction", "last", "globalActionQueue", "createMutableActionQueue", "initialState", "<PERSON><PERSON><PERSON><PERSON>", "result", "onRouterTransitionStart", "Error", "getCurrentAppRouterState", "getAppRouterActionQueue", "getProfilingHookForOnNavigationStart", "dispatchNavigateAction", "href", "navigateType", "shouldScroll", "linkInstanceRef", "url", "URL", "process", "env", "__NEXT_APP_NAV_FAIL_HANDLING", "__pendingUrl", "isExternalUrl", "locationSearch", "search", "allowAliasing", "dispatchTraverseAction", "tree", "publicAppRouterInstance", "back", "history", "forward", "__NEXT_CLIENT_SEGMENT_CACHE", "options", "nextUrl", "kind", "FULL", "replace", "scroll", "push", "refresh", "hmrRefresh", "NODE_ENV", "router"], "mappings": ";;;;;;;AAAA,SAIEA,cAAc,EACdC,oBAAoB,EACpBC,eAAe,EACfC,cAAc,EAEdC,kBAAkB,EAClBC,YAAY,EACZC,eAAe,QACV,wCAAuC;AAC9C,SAASC,OAAO,QAAQ,kCAAiC;AACzD,SAASC,eAAe,QAAQ,QAAO;AACvC,SAASC,UAAU,QAAQ,+BAA8B;AACzD,SAASC,YAAYC,wBAAwB,QAAQ,kBAAiB;AACtE,SAASC,uBAAuB,QAAQ,qBAAoB;AAC5D,SAASC,WAAW,QAAQ,mBAAkB;AAC9C,SAASC,iBAAiB,EAAEC,aAAa,QAAQ,eAAc;AAC/D,SAASC,eAAe,QAAQ,6CAA4C;AAM5E,SAASC,2BAA2B,QAA2B,UAAS;;;;;;;;;;;AA4BxE,SAASC,oBACPC,WAAiC,EACjCC,QAA8B;IAE9B,IAAID,YAAYE,OAAO,KAAK,MAAM;QAChCF,YAAYE,OAAO,GAAGF,YAAYE,OAAO,CAACC,IAAI;QAC9C,IAAIH,YAAYE,OAAO,KAAK,MAAM;YAChC,mEAAmE;YACnEE,UAAU;gBACRJ;gBACAK,QAAQL,YAAYE,OAAO;gBAC3BD;YACF;QACF,OAAO;YACL,4DAA4D;YAC5D,IAAID,YAAYM,YAAY,EAAE;gBAC5BN,YAAYM,YAAY,GAAG;gBAC3BN,YAAYO,QAAQ,CAClB;oBACEC,6NAAM3B,iBAAAA;oBACN4B,QAAQC,OAAOC,QAAQ,CAACF,MAAM;gBAChC,GACAR;YAEJ;QACF;IACF;AACF;AAEA,eAAeG,UAAU,KAQxB;IARwB,IAAA,EACvBJ,WAAW,EACXK,MAAM,EACNJ,QAAQ,EAKT,GARwB;IASvB,MAAMW,YAAYZ,YAAYa,KAAK;IAEnCb,YAAYE,OAAO,GAAGG;IAEtB,MAAMS,UAAUT,OAAOS,OAAO;IAC9B,MAAMC,eAAef,YAAYK,MAAM,CAACO,WAAWE;IAEnD,SAASE,aAAaC,SAAyB;QAC7C,kEAAkE;QAClE,IAAIZ,OAAOa,SAAS,EAAE;YACpB;QACF;QAEAlB,YAAYa,KAAK,GAAGI;QAEpBlB,oBAAoBC,aAAaC;QACjCI,OAAOc,OAAO,CAACF;IACjB;IAEA,8DAA8D;IAC9D,uLAAI3B,aAAAA,EAAWyB,eAAe;QAC5BA,aAAaK,IAAI,CAACJ,cAAc,CAACK;YAC/BtB,oBAAoBC,aAAaC;YACjCI,OAAOiB,MAAM,CAACD;QAChB;IACF,OAAO;QACLL,aAAaD;IACf;AACF;AAEA,SAASQ,eACPvB,WAAiC,EACjCc,OAAuB,EACvBb,QAA8B;IAE9B,IAAIuB,YAGA;QAAEL,SAASlB;QAAUqB,QAAQ,KAAO;IAAE;IAE1C,mEAAmE;IACnE,wFAAwF;IACxF,2DAA2D;IAC3D,oDAAoD;IACpD,IAAIR,QAAQN,IAAI,4NAAKxB,iBAAAA,EAAgB;QACnC,6DAA6D;QAC7D,MAAMyC,kBAAkB,IAAIC,QAAwB,CAACP,SAASG;YAC5DE,YAAY;gBAAEL;gBAASG;YAAO;QAChC;kMAEAjC,kBAAAA,EAAgB;YACd,oGAAoG;YACpG,iEAAiE;YACjEY,SAASwB;QACX;IACF;IAEA,MAAME,YAA6B;QACjCb;QACAX,MAAM;QACNgB,SAASK,UAAUL,OAAO;QAC1BG,QAAQE,UAAUF,MAAM;IAC1B;IAEA,8BAA8B;IAC9B,IAAItB,YAAYE,OAAO,KAAK,MAAM;QAChC,iEAAiE;QACjE,4CAA4C;QAC5CF,YAAY4B,IAAI,GAAGD;QAEnBvB,UAAU;YACRJ;YACAK,QAAQsB;YACR1B;QACF;IACF,OAAO,IACLa,QAAQN,IAAI,4NAAKzB,kBAAAA,IACjB+B,QAAQN,IAAI,4NAAKxB,iBAAAA,EACjB;QACA,+EAA+E;QAC/E,oHAAoH;QACpHgB,YAAYE,OAAO,CAACgB,SAAS,GAAG;QAEhC,4EAA4E;QAC5E,sIAAsI;QACtIS,UAAUxB,IAAI,GAAGH,YAAYE,OAAO,CAACC,IAAI;QAEzC,2GAA2G;QAC3G,IAAIH,YAAYE,OAAO,CAACY,OAAO,CAACN,IAAI,4NAAK1B,uBAAAA,EAAsB;YAC7DkB,YAAYM,YAAY,GAAG;QAC7B;QAEAF,UAAU;YACRJ;YACAK,QAAQsB;YACR1B;QACF;IACF,OAAO;QACL,oEAAoE;QACpE,+EAA+E;QAC/E,IAAID,YAAY4B,IAAI,KAAK,MAAM;YAC7B5B,YAAY4B,IAAI,CAACzB,IAAI,GAAGwB;QAC1B;QACA3B,YAAY4B,IAAI,GAAGD;IACrB;AACF;AAEA,IAAIE,oBAAiD;AAE9C,SAASC,yBACdC,YAA4B,EAC5BC,oBAAuD;IAEvD,MAAMhC,cAAoC;QACxCa,OAAOkB;QACPxB,UAAU,CAACO,SAAyBb,WAClCsB,eAAevB,aAAac,SAASb;QACvCI,QAAQ,OAAOQ,OAAuBR;YACpC,MAAM4B,2NAAS7C,UAAAA,EAAQyB,OAAOR;YAC9B,OAAO4B;QACT;QACA/B,SAAS;QACT0B,MAAM;QACNM,yBACEF,yBAAyB,QACzB,OAAOA,qBAAqBE,uBAAuB,KAAK,aAEpDF,qBAAqBE,uBAAuB,GAC5C;IACR;IAEA,IAAI,OAAOxB,WAAW,iBAAa;;IAWnC;IAEA,OAAOV;AACT;AAEO,SAASoC;IACd,OAAOP,sBAAsB,OAAOA,kBAAkBhB,KAAK,GAAG;AAChE;AAEA,SAASwB;IACP,IAAIR,sBAAsB,MAAM;QAC9B,MAAM,OAAA,cAEL,CAFK,IAAIM,MACR,4EADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACA,OAAON;AACT;AAEA,SAASS;IACP,IAAIT,sBAAsB,MAAM;QAC9B,OAAOA,kBAAkBK,uBAAuB;IAClD;IACA,OAAO;AACT;AAEO,SAASK,uBACdC,IAAY,EACZC,YAA4C,EAC5CC,YAAqB,EACrBC,eAAoC;IAEpC,yEAAyE;IACzE,oEAAoE;IACpE,MAAMC,MAAM,IAAIC,qLAAInD,cAAAA,EAAY8C,OAAO7B,SAAS6B,IAAI;IACpD,IAAIM,QAAQC,GAAG,CAACC,uBAA8B,KAAF;;IAE5C;qLAEAlD,8BAAAA,EAA4B6C;IAE5B,MAAMT,0BAA0BI;IAChC,IAAIJ,4BAA4B,MAAM;QACpCA,wBAAwBM,MAAMC;IAChC;QAEAhD,wNAAAA,EAAwB;QACtBe,6NAAMzB,kBAAAA;QACN6D;QACAM,wMAAetD,gBAAAA,EAAcgD;QAC7BO,gBAAgBxC,SAASyC,MAAM;QAC/BV;QACAD;QACAY,eAAe;IACjB;AACF;AAEO,SAASC,uBACdd,IAAY,EACZe,IAAmC;IAEnC,MAAMrB,0BAA0BI;IAChC,IAAIJ,4BAA4B,MAAM;QACpCA,wBAAwBM,MAAM;IAChC;sMACA/C,0BAAAA,EAAwB;QACtBe,MAAMxB,wOAAAA;QACN4D,KAAK,IAAIC,IAAIL;QACbe;IACF;AACF;AAOO,MAAMC,0BAA6C;IACxDC,MAAM,IAAM/C,OAAOgD,OAAO,CAACD,IAAI;IAC/BE,SAAS,IAAMjD,OAAOgD,OAAO,CAACC,OAAO;IACrCpE,UAAUuD,QAAQC,GAAG,CAACa,2BAA2B,GAE7C,mCAWA,CAACpB,MAAcqB,0BAXqD;QAYlE,uCAAuC;QACvC,MAAM7D,cAAcqC;QACpB,MAAMO,OAAMjD,4MAAAA,EAAkB6C;QAC9B,IAAII,QAAQ,MAAM;gBAURiB;YATR,4DAA4D;YAC5D,+DAA+D;YAC/D,iEAAiE;YACjE,gDAAgD;YAChD,oDAAoD;YACpD,kBAAkB;4OAClBhE,kBAAAA,EAAgBG,YAAYa,KAAK,EAAE;gBACjCL,6NAAMrB,kBAAAA;gBACNyD;gBACAmB,MAAMF,CAAAA,gBAAAA,WAAAA,OAAAA,KAAAA,IAAAA,QAASE,IAAI,KAAA,OAAbF,uOAAiB3E,eAAAA,CAAa8E,IAAI;YAC1C;QACF;IACF;IACJC,SAAS,CAACzB,MAAcqB;QACtBxE,4MAAAA,EAAgB;gBAC0BwE;YAAxCtB,uBAAuBC,MAAM,WAAWqB,CAAAA,kBAAAA,WAAAA,OAAAA,KAAAA,IAAAA,QAASK,MAAM,KAAA,OAAfL,kBAAmB,MAAM;QACnE;IACF;IACAM,MAAM,CAAC3B,MAAcqB;kMACnBxE,kBAAAA,EAAgB;gBACuBwE;YAArCtB,uBAAuBC,MAAM,QAAQqB,CAAAA,kBAAAA,WAAAA,OAAAA,KAAAA,IAAAA,QAASK,MAAM,KAAA,OAAfL,kBAAmB,MAAM;QAChE;IACF;IACAO,SAAS;SACP/E,2MAAAA,EAAgB;8MACdI,0BAAAA,EAAwB;gBACtBe,6NAAM3B,iBAAAA;gBACN4B,QAAQC,OAAOC,QAAQ,CAACF,MAAM;YAChC;QACF;IACF;IACA4D,YAAY;QACV,IAAIvB,QAAQC,GAAG,CAACuB,QAAQ,KAAK,UAAe;;QAI5C,OAAO;sMACLjF,kBAAAA,EAAgB;kNACdI,0BAAAA,EAAwB;oBACtBe,6NAAMvB,qBAAAA;oBACNwB,QAAQC,OAAOC,QAAQ,CAACF,MAAM;gBAChC;YACF;QACF;IACF;AACF,EAAC;AAED,gEAAgE;AAChE,IAAI,OAAOC,WAAW,eAAeA,EAAa,KAANP,IAAI;;AAEhD", "ignoreList": [0]}}, {"offset": {"line": 805, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/esm/client/components/dev-root-http-access-fallback-boundary.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/dev-root-http-access-fallback-boundary.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 812, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/esm/client/components/dev-root-http-access-fallback-boundary.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/dev-root-http-access-fallback-boundary.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 820, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/dev-root-http-access-fallback-boundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { HTTPAccessFallbackBoundary } from './http-access-fallback/error-boundary'\n\n// TODO: error on using forbidden and unauthorized in root layout\nexport function bailOnRootNotFound() {\n  throw new Error('notFound() is not allowed to use in root layout')\n}\n\nfunction NotAllowedRootHTTPFallbackError() {\n  bailOnRootNotFound()\n  return null\n}\n\nexport function DevRootHTTPAccessFallbackBoundary({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return (\n    <HTTPAccessFallbackBoundary notFound={<NotAllowedRootHTTPFallbackError />}>\n      {children}\n    </HTTPAccessFallbackBoundary>\n  )\n}\n"], "names": ["React", "HTTPAccessFallbackBoundary", "bailOnRootNotFound", "Error", "NotAllowedRootHTTPFallbackError", "DevRootHTTPAccessFallbackBoundary", "children", "notFound"], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 830, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/app-router-headers.ts"], "sourcesContent": ["export const RSC_HEADER = 'RSC' as const\nexport const ACTION_HEADER = 'Next-Action' as const\n// TODO: Instead of sending the full router state, we only need to send the\n// segment path. Saves bytes. Then we could also use this field for segment\n// prefetches, which also need to specify a particular segment.\nexport const NEXT_ROUTER_STATE_TREE_HEADER = 'Next-Router-State-Tree' as const\nexport const NEXT_ROUTER_PREFETCH_HEADER = 'Next-Router-Prefetch' as const\n// This contains the path to the segment being prefetched.\n// TODO: If we change Next-Router-State-Tree to be a segment path, we can use\n// that instead. Then Next-Router-Prefetch and Next-Router-Segment-Prefetch can\n// be merged into a single enum.\nexport const NEXT_ROUTER_SEGMENT_PREFETCH_HEADER =\n  'Next-Router-Segment-Prefetch' as const\nexport const NEXT_HMR_REFRESH_HEADER = 'Next-HMR-Refresh' as const\nexport const NEXT_HMR_REFRESH_HASH_COOKIE = '__next_hmr_refresh_hash__' as const\nexport const NEXT_URL = 'Next-Url' as const\nexport const RSC_CONTENT_TYPE_HEADER = 'text/x-component' as const\n\nexport const FLIGHT_HEADERS = [\n  RSC_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n] as const\n\nexport const NEXT_RSC_UNION_QUERY = '_rsc' as const\n\nexport const NEXT_ROUTER_STALE_TIME_HEADER = 'x-nextjs-stale-time' as const\nexport const NEXT_DID_POSTPONE_HEADER = 'x-nextjs-postponed' as const\nexport const NEXT_REWRITTEN_PATH_HEADER = 'x-nextjs-rewritten-path' as const\nexport const NEXT_REWRITTEN_QUERY_HEADER = 'x-nextjs-rewritten-query' as const\nexport const NEXT_IS_PRERENDER_HEADER = 'x-nextjs-prerender' as const\n"], "names": ["RSC_HEADER", "ACTION_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_HMR_REFRESH_HEADER", "NEXT_HMR_REFRESH_HASH_COOKIE", "NEXT_URL", "RSC_CONTENT_TYPE_HEADER", "FLIGHT_HEADERS", "NEXT_RSC_UNION_QUERY", "NEXT_ROUTER_STALE_TIME_HEADER", "NEXT_DID_POSTPONE_HEADER", "NEXT_REWRITTEN_PATH_HEADER", "NEXT_REWRITTEN_QUERY_HEADER", "NEXT_IS_PRERENDER_HEADER"], "mappings": ";;;;;;;;;;;;;;;;;;AAAO,MAAMA,aAAa,MAAc;AACjC,MAAMC,gBAAgB,cAAsB;AAI5C,MAAMC,gCAAgC,yBAAiC;AACvE,MAAMC,8BAA8B,uBAA+B;AAKnE,MAAMC,sCACX,+BAAuC;AAClC,MAAMC,0BAA0B,mBAA2B;AAC3D,MAAMC,+BAA+B,4BAAoC;AACzE,MAAMC,WAAW,WAAmB;AACpC,MAAMC,0BAA0B,mBAA2B;AAE3D,MAAMC,iBAAiB;IAC5BT;IACAE;IACAC;IACAE;IACAD;CACD,CAAS;AAEH,MAAMM,uBAAuB,OAAe;AAE5C,MAAMC,gCAAgC,sBAA8B;AACpE,MAAMC,2BAA2B,qBAA6B;AAC9D,MAAMC,6BAA6B,0BAAkC;AACrE,MAAMC,8BAA8B,2BAAmC;AACvE,MAAMC,2BAA2B,qBAA6B", "ignoreList": [0]}}, {"offset": {"line": 876, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/hooks-server-context.ts"], "sourcesContent": ["const DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE'\n\nexport class DynamicServerError extends Error {\n  digest: typeof DYNAMIC_ERROR_CODE = DYNAMIC_ERROR_CODE\n\n  constructor(public readonly description: string) {\n    super(`Dynamic server usage: ${description}`)\n  }\n}\n\nexport function isDynamicServerError(err: unknown): err is DynamicServerError {\n  if (\n    typeof err !== 'object' ||\n    err === null ||\n    !('digest' in err) ||\n    typeof err.digest !== 'string'\n  ) {\n    return false\n  }\n\n  return err.digest === DYNAMIC_ERROR_CODE\n}\n"], "names": ["DYNAMIC_ERROR_CODE", "DynamicServerError", "Error", "constructor", "description", "digest", "isDynamicServerError", "err"], "mappings": ";;;;AAAA,MAAMA,qBAAqB;AAEpB,MAAMC,2BAA2BC;IAGtCC,YAA4BC,WAAmB,CAAE;QAC/C,KAAK,CAAE,2BAAwBA,cAAAA,IAAAA,CADLA,WAAAA,GAAAA,aAAAA,IAAAA,CAF5BC,MAAAA,GAAoCL;IAIpC;AACF;AAEO,SAASM,qBAAqBC,GAAY;IAC/C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,CAAE,CAAA,YAAYA,GAAE,KAChB,OAAOA,IAAIF,MAAM,KAAK,UACtB;QACA,OAAO;IACT;IAEA,OAAOE,IAAIF,MAAM,KAAKL;AACxB", "ignoreList": [0]}}, {"offset": {"line": 898, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/static-generation-bailout.ts"], "sourcesContent": ["const NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT'\n\nexport class StaticGenBailoutError extends Error {\n  public readonly code = NEXT_STATIC_GEN_BAILOUT\n}\n\nexport function isStaticGenBailoutError(\n  error: unknown\n): error is StaticGenBailoutError {\n  if (typeof error !== 'object' || error === null || !('code' in error)) {\n    return false\n  }\n\n  return error.code === NEXT_STATIC_GEN_BAILOUT\n}\n"], "names": ["NEXT_STATIC_GEN_BAILOUT", "StaticGenBailoutError", "Error", "code", "isStaticGenBailoutError", "error"], "mappings": ";;;;AAAA,MAAMA,0BAA0B;AAEzB,MAAMC,8BAA8BC;;QAApC,KAAA,IAAA,OAAA,IAAA,CACWC,IAAAA,GAAOH;;AACzB;AAEO,SAASI,wBACdC,KAAc;IAEd,IAAI,OAAOA,UAAU,YAAYA,UAAU,QAAQ,CAAE,CAAA,UAAUA,KAAI,GAAI;QACrE,OAAO;IACT;IAEA,OAAOA,MAAMF,IAAI,KAAKH;AACxB", "ignoreList": [0]}}, {"offset": {"line": 920, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/http-access-fallback/http-access-fallback.ts"], "sourcesContent": ["export const HTTPAccessErrorStatus = {\n  NOT_FOUND: 404,\n  FORBIDDEN: 403,\n  UNAUTHORIZED: 401,\n}\n\nconst ALLOWED_CODES = new Set(Object.values(HTTPAccessErrorStatus))\n\nexport const HTTP_ERROR_FALLBACK_ERROR_CODE = 'NEXT_HTTP_ERROR_FALLBACK'\n\nexport type HTTPAccessFallbackError = Error & {\n  digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`\n}\n\n/**\n * Checks an error to determine if it's an error generated by\n * the HTTP navigation APIs `notFound()`, `forbidden()` or `unauthorized()`.\n *\n * @param error the error that may reference a HTTP access error\n * @returns true if the error is a HTTP access error\n */\nexport function isHTTPAccessFallbackError(\n  error: unknown\n): error is HTTPAccessFallbackError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n  const [prefix, httpStatus] = error.digest.split(';')\n\n  return (\n    prefix === HTTP_ERROR_FALLBACK_ERROR_CODE &&\n    ALLOWED_CODES.has(Number(httpStatus))\n  )\n}\n\nexport function getAccessFallbackHTTPStatus(\n  error: HTTPAccessFallbackError\n): number {\n  const httpStatus = error.digest.split(';')[1]\n  return Number(httpStatus)\n}\n\nexport function getAccessFallbackErrorTypeByStatus(\n  status: number\n): 'not-found' | 'forbidden' | 'unauthorized' | undefined {\n  switch (status) {\n    case 401:\n      return 'unauthorized'\n    case 403:\n      return 'forbidden'\n    case 404:\n      return 'not-found'\n    default:\n      return\n  }\n}\n"], "names": ["HTTPAccessErrorStatus", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "ALLOWED_CODES", "Set", "Object", "values", "HTTP_ERROR_FALLBACK_ERROR_CODE", "isHTTPAccessFallbackError", "error", "digest", "prefix", "httpStatus", "split", "has", "Number", "getAccessFallbackHTTPStatus", "getAccessFallbackErrorTypeByStatus", "status"], "mappings": ";;;;;;;AAAO,MAAMA,wBAAwB;IACnCC,WAAW;IACXC,WAAW;IACXC,cAAc;AAChB,EAAC;AAED,MAAMC,gBAAgB,IAAIC,IAAIC,OAAOC,MAAM,CAACP;AAErC,MAAMQ,iCAAiC,2BAA0B;AAajE,SAASC,0BACdC,KAAc;IAEd,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IACA,MAAM,CAACC,QAAQC,WAAW,GAAGH,MAAMC,MAAM,CAACG,KAAK,CAAC;IAEhD,OACEF,WAAWJ,kCACXJ,cAAcW,GAAG,CAACC,OAAOH;AAE7B;AAEO,SAASI,4BACdP,KAA8B;IAE9B,MAAMG,aAAaH,MAAMC,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC7C,OAAOE,OAAOH;AAChB;AAEO,SAASK,mCACdC,MAAc;IAEd,OAAQA;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE;IACJ;AACF", "ignoreList": [0]}}, {"offset": {"line": 963, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/http-access-fallback/error-boundary.tsx"], "sourcesContent": ["'use client'\n\n/**\n * HTTPAccessFallbackBoundary is a boundary that catches errors and renders a\n * fallback component for HTTP errors.\n *\n * It receives the status code, and determine if it should render fallbacks for few HTTP 4xx errors.\n *\n * e.g. 404\n * 404 represents not found, and the fallback component pair contains the component and its styles.\n *\n */\n\nimport React, { useContext } from 'react'\nimport { useUntrackedPathname } from '../navigation-untracked'\nimport {\n  HTTPAccessErrorStatus,\n  getAccessFallbackHTTPStatus,\n  getAccessFallbackErrorTypeByStatus,\n  isHTTPAccessFallbackError,\n} from './http-access-fallback'\nimport { warnOnce } from '../../../shared/lib/utils/warn-once'\nimport { MissingSlotContext } from '../../../shared/lib/app-router-context.shared-runtime'\n\ninterface HTTPAccessFallbackBoundaryProps {\n  notFound?: React.ReactNode\n  forbidden?: React.ReactNode\n  unauthorized?: React.ReactNode\n  children: React.ReactNode\n  missingSlots?: Set<string>\n}\n\ninterface HTTPAccessFallbackErrorBoundaryProps\n  extends HTTPAccessFallbackBoundaryProps {\n  pathname: string | null\n  missingSlots?: Set<string>\n}\n\ninterface HTTPAccessBoundaryState {\n  triggeredStatus: number | undefined\n  previousPathname: string | null\n}\n\nclass HTTPAccessFallbackErrorBoundary extends React.Component<\n  HTTPAccessFallbackErrorBoundaryProps,\n  HTTPAccessBoundaryState\n> {\n  constructor(props: HTTPAccessFallbackErrorBoundaryProps) {\n    super(props)\n    this.state = {\n      triggeredStatus: undefined,\n      previousPathname: props.pathname,\n    }\n  }\n\n  componentDidCatch(): void {\n    if (\n      process.env.NODE_ENV === 'development' &&\n      this.props.missingSlots &&\n      this.props.missingSlots.size > 0 &&\n      // A missing children slot is the typical not-found case, so no need to warn\n      !this.props.missingSlots.has('children')\n    ) {\n      let warningMessage =\n        'No default component was found for a parallel route rendered on this page. Falling back to nearest NotFound boundary.\\n' +\n        'Learn more: https://nextjs.org/docs/app/building-your-application/routing/parallel-routes#defaultjs\\n\\n'\n\n      const formattedSlots = Array.from(this.props.missingSlots)\n        .sort((a, b) => a.localeCompare(b))\n        .map((slot) => `@${slot}`)\n        .join(', ')\n\n      warningMessage += 'Missing slots: ' + formattedSlots\n\n      warnOnce(warningMessage)\n    }\n  }\n\n  static getDerivedStateFromError(error: any) {\n    if (isHTTPAccessFallbackError(error)) {\n      const httpStatus = getAccessFallbackHTTPStatus(error)\n      return {\n        triggeredStatus: httpStatus,\n      }\n    }\n    // Re-throw if error is not for 404\n    throw error\n  }\n\n  static getDerivedStateFromProps(\n    props: HTTPAccessFallbackErrorBoundaryProps,\n    state: HTTPAccessBoundaryState\n  ): HTTPAccessBoundaryState | null {\n    /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */\n    if (props.pathname !== state.previousPathname && state.triggeredStatus) {\n      return {\n        triggeredStatus: undefined,\n        previousPathname: props.pathname,\n      }\n    }\n    return {\n      triggeredStatus: state.triggeredStatus,\n      previousPathname: props.pathname,\n    }\n  }\n\n  render() {\n    const { notFound, forbidden, unauthorized, children } = this.props\n    const { triggeredStatus } = this.state\n    const errorComponents = {\n      [HTTPAccessErrorStatus.NOT_FOUND]: notFound,\n      [HTTPAccessErrorStatus.FORBIDDEN]: forbidden,\n      [HTTPAccessErrorStatus.UNAUTHORIZED]: unauthorized,\n    }\n\n    if (triggeredStatus) {\n      const isNotFound =\n        triggeredStatus === HTTPAccessErrorStatus.NOT_FOUND && notFound\n      const isForbidden =\n        triggeredStatus === HTTPAccessErrorStatus.FORBIDDEN && forbidden\n      const isUnauthorized =\n        triggeredStatus === HTTPAccessErrorStatus.UNAUTHORIZED && unauthorized\n\n      // If there's no matched boundary in this layer, keep throwing the error by rendering the children\n      if (!(isNotFound || isForbidden || isUnauthorized)) {\n        return children\n      }\n\n      return (\n        <>\n          <meta name=\"robots\" content=\"noindex\" />\n          {process.env.NODE_ENV === 'development' && (\n            <meta\n              name=\"boundary-next-error\"\n              content={getAccessFallbackErrorTypeByStatus(triggeredStatus)}\n            />\n          )}\n          {errorComponents[triggeredStatus]}\n        </>\n      )\n    }\n\n    return children\n  }\n}\n\nexport function HTTPAccessFallbackBoundary({\n  notFound,\n  forbidden,\n  unauthorized,\n  children,\n}: HTTPAccessFallbackBoundaryProps) {\n  // When we're rendering the missing params shell, this will return null. This\n  // is because we won't be rendering any not found boundaries or error\n  // boundaries for the missing params shell. When this runs on the client\n  // (where these error can occur), we will get the correct pathname.\n  const pathname = useUntrackedPathname()\n  const missingSlots = useContext(MissingSlotContext)\n  const hasErrorFallback = !!(notFound || forbidden || unauthorized)\n\n  if (hasErrorFallback) {\n    return (\n      <HTTPAccessFallbackErrorBoundary\n        pathname={pathname}\n        notFound={notFound}\n        forbidden={forbidden}\n        unauthorized={unauthorized}\n        missingSlots={missingSlots}\n      >\n        {children}\n      </HTTPAccessFallbackErrorBoundary>\n    )\n  }\n\n  return <>{children}</>\n}\n"], "names": ["React", "useContext", "useUntrackedPathname", "HTTPAccessErrorStatus", "getAccessFallbackHTTPStatus", "getAccessFallbackErrorTypeByStatus", "isHTTPAccessFallbackError", "warnOnce", "MissingSlotContext", "HTTPAccessFallbackErrorBoundary", "Component", "componentDidCatch", "process", "env", "NODE_ENV", "props", "missingSlots", "size", "has", "warningMessage", "formattedSlots", "Array", "from", "sort", "a", "b", "localeCompare", "map", "slot", "join", "getDerivedStateFromError", "error", "httpStatus", "triggeredStatus", "getDerivedStateFromProps", "state", "pathname", "previousPathname", "undefined", "render", "notFound", "forbidden", "unauthorized", "children", "errorComponents", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "isNotFound", "isForbidden", "isUnauthorized", "meta", "name", "content", "constructor", "HTTPAccessFallbackBoundary", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;AAEA;;;;;;;;;CASC,GAED,OAAOA,SAASC,UAAU,QAAQ,QAAO;AACzC,SAASC,oBAAoB,QAAQ,0BAAyB;AAC9D,SACEC,qBAAqB,EACrBC,2BAA2B,EAC3BC,kCAAkC,EAClCC,yBAAyB,QACpB,yBAAwB;AAC/B,SAASC,QAAQ,QAAQ,sCAAqC;AAC9D,SAASC,kBAAkB,QAAQ,wDAAuD;AAtB1F;;;;;;;AA2CA,MAAMC,2MAAwCT,UAAAA,CAAMU,SAAS;IAY3DC,oBAA0B;QACxB,IACEC,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzB,IAAI,CAACC,KAAK,CAACC,YAAY,IACvB,IAAI,CAACD,KAAK,CAACC,YAAY,CAACC,IAAI,GAAG,KAC/B,4EAA4E;QAC5E,CAAC,IAAI,CAACF,KAAK,CAACC,YAAY,CAACE,GAAG,CAAC,aAC7B;YACA,IAAIC,iBACF,4HACA;YAEF,MAAMC,iBAAiBC,MAAMC,IAAI,CAAC,IAAI,CAACP,KAAK,CAACC,YAAY,EACtDO,IAAI,CAAC,CAACC,GAAGC,IAAMD,EAAEE,aAAa,CAACD,IAC/BE,GAAG,CAAC,CAACC,OAAU,MAAGA,MAClBC,IAAI,CAAC;YAERV,kBAAkB,oBAAoBC;sMAEtCb,WAAAA,EAASY;QACX;IACF;IAEA,OAAOW,yBAAyBC,KAAU,EAAE;QAC1C,wOAAIzB,4BAAAA,EAA0ByB,QAAQ;YACpC,MAAMC,iBAAa5B,8PAAAA,EAA4B2B;YAC/C,OAAO;gBACLE,iBAAiBD;YACnB;QACF;QACA,mCAAmC;QACnC,MAAMD;IACR;IAEA,OAAOG,yBACLnB,KAA2C,EAC3CoB,KAA8B,EACE;QAChC;;;;;KAKC,GACD,IAAIpB,MAAMqB,QAAQ,KAAKD,MAAME,gBAAgB,IAAIF,MAAMF,eAAe,EAAE;YACtE,OAAO;gBACLA,iBAAiBK;gBACjBD,kBAAkBtB,MAAMqB,QAAQ;YAClC;QACF;QACA,OAAO;YACLH,iBAAiBE,MAAMF,eAAe;YACtCI,kBAAkBtB,MAAMqB,QAAQ;QAClC;IACF;IAEAG,SAAS;QACP,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,YAAY,EAAEC,QAAQ,EAAE,GAAG,IAAI,CAAC5B,KAAK;QAClE,MAAM,EAAEkB,eAAe,EAAE,GAAG,IAAI,CAACE,KAAK;QACtC,MAAMS,kBAAkB;YACtB,iOAACzC,wBAAAA,CAAsB0C,SAAS,CAAC,EAAEL;YACnC,iOAACrC,wBAAAA,CAAsB2C,SAAS,CAAC,EAAEL;YACnC,iOAACtC,wBAAAA,CAAsB4C,YAAY,CAAC,EAAEL;QACxC;QAEA,IAAIT,iBAAiB;YACnB,MAAMe,aACJf,oPAAoB9B,wBAAAA,CAAsB0C,SAAS,IAAIL;YACzD,MAAMS,cACJhB,oPAAoB9B,wBAAAA,CAAsB2C,SAAS,IAAIL;YACzD,MAAMS,iBACJjB,oPAAoB9B,wBAAAA,CAAsB4C,YAAY,IAAIL;YAE5D,kGAAkG;YAClG,IAAI,CAAEM,CAAAA,cAAcC,eAAeC,cAAa,GAAI;gBAClD,OAAOP;YACT;YAEA,OAAA,WAAA,mLACE,OAAA,EAAA,2KAAA,CAAA,WAAA,EAAA;;kNACE,MAAA,EAACQ,QAAAA;wBAAKC,MAAK;wBAASC,SAAQ;;oBAC3BzC,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBAAA,WAAA,mLACxB,MAAA,EAACqC,QAAAA;wBACCC,MAAK;wBACLC,6OAAShD,qCAAAA,EAAmC4B;;oBAG/CW,eAAe,CAACX,gBAAgB;;;QAGvC;QAEA,OAAOU;IACT;IArGAW,YAAYvC,KAA2C,CAAE;QACvD,KAAK,CAACA;QACN,IAAI,CAACoB,KAAK,GAAG;YACXF,iBAAiBK;YACjBD,kBAAkBtB,MAAMqB,QAAQ;QAClC;IACF;AAgGF;AAEO,SAASmB,2BAA2B,KAKT;IALS,IAAA,EACzCf,QAAQ,EACRC,SAAS,EACTC,YAAY,EACZC,QAAQ,EACwB,GALS;IAMzC,6EAA6E;IAC7E,qEAAqE;IACrE,wEAAwE;IACxE,mEAAmE;IACnE,MAAMP,8MAAWlC,uBAAAA;IACjB,MAAMc,gBAAef,mLAAAA,yMAAWO,qBAAAA;IAChC,MAAMgD,mBAAmB,CAAC,CAAEhB,CAAAA,YAAYC,aAAaC,YAAW;IAEhE,IAAIc,kBAAkB;QACpB,OAAA,WAAA,GACE,sLAAA,EAAC/C,iCAAAA;YACC2B,UAAUA;YACVI,UAAUA;YACVC,WAAWA;YACXC,cAAcA;YACd1B,cAAcA;sBAEb2B;;IAGP;IAEA,OAAA,WAAA,mLAAO,MAAA,EAAA,2KAAA,CAAA,WAAA,EAAA;kBAAGA;;AACZ", "ignoreList": [0]}}, {"offset": {"line": 1093, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 1100, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 1108, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/http-access-fallback/error-boundary.tsx"], "sourcesContent": ["'use client'\n\n/**\n * HTTPAccessFallbackBoundary is a boundary that catches errors and renders a\n * fallback component for HTTP errors.\n *\n * It receives the status code, and determine if it should render fallbacks for few HTTP 4xx errors.\n *\n * e.g. 404\n * 404 represents not found, and the fallback component pair contains the component and its styles.\n *\n */\n\nimport React, { useContext } from 'react'\nimport { useUntrackedPathname } from '../navigation-untracked'\nimport {\n  HTTPAccessErrorStatus,\n  getAccessFallbackHTTPStatus,\n  getAccessFallbackErrorTypeByStatus,\n  isHTTPAccessFallbackError,\n} from './http-access-fallback'\nimport { warnOnce } from '../../../shared/lib/utils/warn-once'\nimport { MissingSlotContext } from '../../../shared/lib/app-router-context.shared-runtime'\n\ninterface HTTPAccessFallbackBoundaryProps {\n  notFound?: React.ReactNode\n  forbidden?: React.ReactNode\n  unauthorized?: React.ReactNode\n  children: React.ReactNode\n  missingSlots?: Set<string>\n}\n\ninterface HTTPAccessFallbackErrorBoundaryProps\n  extends HTTPAccessFallbackBoundaryProps {\n  pathname: string | null\n  missingSlots?: Set<string>\n}\n\ninterface HTTPAccessBoundaryState {\n  triggeredStatus: number | undefined\n  previousPathname: string | null\n}\n\nclass HTTPAccessFallbackErrorBoundary extends React.Component<\n  HTTPAccessFallbackErrorBoundaryProps,\n  HTTPAccessBoundaryState\n> {\n  constructor(props: HTTPAccessFallbackErrorBoundaryProps) {\n    super(props)\n    this.state = {\n      triggeredStatus: undefined,\n      previousPathname: props.pathname,\n    }\n  }\n\n  componentDidCatch(): void {\n    if (\n      process.env.NODE_ENV === 'development' &&\n      this.props.missingSlots &&\n      this.props.missingSlots.size > 0 &&\n      // A missing children slot is the typical not-found case, so no need to warn\n      !this.props.missingSlots.has('children')\n    ) {\n      let warningMessage =\n        'No default component was found for a parallel route rendered on this page. Falling back to nearest NotFound boundary.\\n' +\n        'Learn more: https://nextjs.org/docs/app/building-your-application/routing/parallel-routes#defaultjs\\n\\n'\n\n      const formattedSlots = Array.from(this.props.missingSlots)\n        .sort((a, b) => a.localeCompare(b))\n        .map((slot) => `@${slot}`)\n        .join(', ')\n\n      warningMessage += 'Missing slots: ' + formattedSlots\n\n      warnOnce(warningMessage)\n    }\n  }\n\n  static getDerivedStateFromError(error: any) {\n    if (isHTTPAccessFallbackError(error)) {\n      const httpStatus = getAccessFallbackHTTPStatus(error)\n      return {\n        triggeredStatus: httpStatus,\n      }\n    }\n    // Re-throw if error is not for 404\n    throw error\n  }\n\n  static getDerivedStateFromProps(\n    props: HTTPAccessFallbackErrorBoundaryProps,\n    state: HTTPAccessBoundaryState\n  ): HTTPAccessBoundaryState | null {\n    /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */\n    if (props.pathname !== state.previousPathname && state.triggeredStatus) {\n      return {\n        triggeredStatus: undefined,\n        previousPathname: props.pathname,\n      }\n    }\n    return {\n      triggeredStatus: state.triggeredStatus,\n      previousPathname: props.pathname,\n    }\n  }\n\n  render() {\n    const { notFound, forbidden, unauthorized, children } = this.props\n    const { triggeredStatus } = this.state\n    const errorComponents = {\n      [HTTPAccessErrorStatus.NOT_FOUND]: notFound,\n      [HTTPAccessErrorStatus.FORBIDDEN]: forbidden,\n      [HTTPAccessErrorStatus.UNAUTHORIZED]: unauthorized,\n    }\n\n    if (triggeredStatus) {\n      const isNotFound =\n        triggeredStatus === HTTPAccessErrorStatus.NOT_FOUND && notFound\n      const isForbidden =\n        triggeredStatus === HTTPAccessErrorStatus.FORBIDDEN && forbidden\n      const isUnauthorized =\n        triggeredStatus === HTTPAccessErrorStatus.UNAUTHORIZED && unauthorized\n\n      // If there's no matched boundary in this layer, keep throwing the error by rendering the children\n      if (!(isNotFound || isForbidden || isUnauthorized)) {\n        return children\n      }\n\n      return (\n        <>\n          <meta name=\"robots\" content=\"noindex\" />\n          {process.env.NODE_ENV === 'development' && (\n            <meta\n              name=\"boundary-next-error\"\n              content={getAccessFallbackErrorTypeByStatus(triggeredStatus)}\n            />\n          )}\n          {errorComponents[triggeredStatus]}\n        </>\n      )\n    }\n\n    return children\n  }\n}\n\nexport function HTTPAccessFallbackBoundary({\n  notFound,\n  forbidden,\n  unauthorized,\n  children,\n}: HTTPAccessFallbackBoundaryProps) {\n  // When we're rendering the missing params shell, this will return null. This\n  // is because we won't be rendering any not found boundaries or error\n  // boundaries for the missing params shell. When this runs on the client\n  // (where these error can occur), we will get the correct pathname.\n  const pathname = useUntrackedPathname()\n  const missingSlots = useContext(MissingSlotContext)\n  const hasErrorFallback = !!(notFound || forbidden || unauthorized)\n\n  if (hasErrorFallback) {\n    return (\n      <HTTPAccessFallbackErrorBoundary\n        pathname={pathname}\n        notFound={notFound}\n        forbidden={forbidden}\n        unauthorized={unauthorized}\n        missingSlots={missingSlots}\n      >\n        {children}\n      </HTTPAccessFallbackErrorBoundary>\n    )\n  }\n\n  return <>{children}</>\n}\n"], "names": ["React", "useContext", "useUntrackedPathname", "HTTPAccessErrorStatus", "getAccessFallbackHTTPStatus", "getAccessFallbackErrorTypeByStatus", "isHTTPAccessFallbackError", "warnOnce", "MissingSlotContext", "HTTPAccessFallbackErrorBoundary", "Component", "componentDidCatch", "process", "env", "NODE_ENV", "props", "missingSlots", "size", "has", "warningMessage", "formattedSlots", "Array", "from", "sort", "a", "b", "localeCompare", "map", "slot", "join", "getDerivedStateFromError", "error", "httpStatus", "triggeredStatus", "getDerivedStateFromProps", "state", "pathname", "previousPathname", "undefined", "render", "notFound", "forbidden", "unauthorized", "children", "errorComponents", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "isNotFound", "isForbidden", "isUnauthorized", "meta", "name", "content", "constructor", "HTTPAccessFallbackBoundary", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 1118, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/redirect-status-code.ts"], "sourcesContent": ["export enum RedirectStatusCode {\n  SeeOther = 303,\n  TemporaryRedirect = 307,\n  PermanentRedirect = 308,\n}\n"], "names": ["RedirectStatusCode"], "mappings": ";;;AAAO,IAAKA,qBAAAA,WAAAA,GAAAA,SAAAA,kBAAAA;;;;WAAAA;MAIX", "ignoreList": [0]}}, {"offset": {"line": 1133, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/redirect-error.ts"], "sourcesContent": ["import { RedirectStatusCode } from './redirect-status-code'\n\nexport const REDIRECT_ERROR_CODE = 'NEXT_REDIRECT'\n\nexport enum RedirectType {\n  push = 'push',\n  replace = 'replace',\n}\n\nexport type RedirectError = Error & {\n  digest: `${typeof REDIRECT_ERROR_CODE};${RedirectType};${string};${RedirectStatusCode};`\n}\n\n/**\n * Checks an error to determine if it's an error generated by the\n * `redirect(url)` helper.\n *\n * @param error the error that may reference a redirect error\n * @returns true if the error is a redirect error\n */\nexport function isRedirectError(error: unknown): error is RedirectError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n\n  const digest = error.digest.split(';')\n  const [errorCode, type] = digest\n  const destination = digest.slice(2, -2).join(';')\n  const status = digest.at(-2)\n\n  const statusCode = Number(status)\n\n  return (\n    errorCode === REDIRECT_ERROR_CODE &&\n    (type === 'replace' || type === 'push') &&\n    typeof destination === 'string' &&\n    !isNaN(statusCode) &&\n    statusCode in RedirectStatusCode\n  )\n}\n"], "names": ["RedirectStatusCode", "REDIRECT_ERROR_CODE", "RedirectType", "isRedirectError", "error", "digest", "split", "errorCode", "type", "destination", "slice", "join", "status", "at", "statusCode", "Number", "isNaN"], "mappings": ";;;;;AAAA,SAASA,kBAAkB,QAAQ,yBAAwB;;AAEpD,MAAMC,sBAAsB,gBAAe;AAE3C,IAAKC,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;;;WAAAA;MAGX;AAaM,SAASC,gBAAgBC,KAAc;IAC5C,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IAEA,MAAMA,SAASD,MAAMC,MAAM,CAACC,KAAK,CAAC;IAClC,MAAM,CAACC,WAAWC,KAAK,GAAGH;IAC1B,MAAMI,cAAcJ,OAAOK,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;IAC7C,MAAMC,SAASP,OAAOQ,EAAE,CAAC,CAAC;IAE1B,MAAMC,aAAaC,OAAOH;IAE1B,OACEL,cAAcN,uBACbO,CAAAA,SAAS,aAAaA,SAAS,MAAK,KACrC,OAAOC,gBAAgB,YACvB,CAACO,MAAMF,eACPA,gNAAcd,qBAAAA;AAElB", "ignoreList": [0]}}, {"offset": {"line": 1163, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/redirect.ts"], "sourcesContent": ["import { RedirectStatusCode } from './redirect-status-code'\nimport {\n  RedirectType,\n  type RedirectError,\n  isRedirectError,\n  REDIRECT_ERROR_CODE,\n} from './redirect-error'\n\nconst actionAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/action-async-storage.external') as typeof import('../../server/app-render/action-async-storage.external')\n      ).actionAsyncStorage\n    : undefined\n\nexport function getRedirectError(\n  url: string,\n  type: RedirectType,\n  statusCode: RedirectStatusCode = RedirectStatusCode.TemporaryRedirect\n): RedirectError {\n  const error = new Error(REDIRECT_ERROR_CODE) as RedirectError\n  error.digest = `${REDIRECT_ERROR_CODE};${type};${url};${statusCode};`\n  return error\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 307/303 to the caller.\n * - In a Server Action, type defaults to 'push' and 'replace' elsewhere.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function redirect(\n  /** The URL to redirect to */\n  url: string,\n  type?: RedirectType\n): never {\n  type ??= actionAsyncStorage?.getStore()?.isAction\n    ? RedirectType.push\n    : RedirectType.replace\n\n  throw getRedirectError(url, type, RedirectStatusCode.TemporaryRedirect)\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 308/303 to the caller.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function permanentRedirect(\n  /** The URL to redirect to */\n  url: string,\n  type: RedirectType = RedirectType.replace\n): never {\n  throw getRedirectError(url, type, RedirectStatusCode.PermanentRedirect)\n}\n\n/**\n * Returns the encoded URL from the error if it's a RedirectError, null\n * otherwise. Note that this does not validate the URL returned.\n *\n * @param error the error that may be a redirect error\n * @return the url if the error was a redirect error\n */\nexport function getURLFromRedirectError(error: RedirectError): string\nexport function getURLFromRedirectError(error: unknown): string | null {\n  if (!isRedirectError(error)) return null\n\n  // Slices off the beginning of the digest that contains the code and the\n  // separating ';'.\n  return error.digest.split(';').slice(2, -2).join(';')\n}\n\nexport function getRedirectTypeFromError(error: RedirectError): RedirectType {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return error.digest.split(';', 2)[1] as RedirectType\n}\n\nexport function getRedirectStatusCodeFromError(error: RedirectError): number {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return Number(error.digest.split(';').at(-2))\n}\n"], "names": ["RedirectStatusCode", "RedirectType", "isRedirectError", "REDIRECT_ERROR_CODE", "actionAsyncStorage", "window", "require", "undefined", "getRedirectError", "url", "type", "statusCode", "TemporaryRedirect", "error", "Error", "digest", "redirect", "getStore", "isAction", "push", "replace", "permanentRedirect", "PermanentRedirect", "getURLFromRedirectError", "split", "slice", "join", "getRedirectTypeFromError", "getRedirectStatusCodeFromError", "Number", "at"], "mappings": ";;;;;;;;AAAA,SAASA,kBAAkB,QAAQ,yBAAwB;AAC3D,SACEC,YAAY,EAEZC,eAAe,EACfC,mBAAmB,QACd,mBAAkB;;;AAEzB,MAAMC,qBACJ,OAAOC,WAAW,cAEZC,QAAQ,uIACRF,kBAAkB,GACpBG;AAEC,SAASC,iBACdC,GAAW,EACXC,IAAkB,EAClBC,UAAqE;IAArEA,IAAAA,eAAAA,KAAAA,GAAAA,+MAAiCX,qBAAAA,CAAmBY,iBAAiB;IAErE,MAAMC,QAAQ,OAAA,cAA8B,CAA9B,IAAIC,+LAAMX,sBAAAA,GAAV,qBAAA;eAAA;oBAAA;sBAAA;IAA6B;IAC3CU,MAAME,MAAM,4LAAMZ,sBAAAA,GAAoB,MAAGO,OAAK,MAAGD,MAAI,MAAGE,aAAW;IACnE,OAAOE;AACT;AAcO,SAASG,SACd,2BAA2B,GAC3BP,GAAW,EACXC,IAAmB;QAEVN;IAATM,QAAAA,OAAAA,OAAAA,OAASN,CAAAA,sBAAAA,OAAAA,KAAAA,IAAAA,CAAAA,+BAAAA,mBAAoBa,QAAQ,EAAA,KAAA,OAAA,KAAA,IAA5Bb,6BAAgCc,QAAQ,6LAC7CjB,eAAAA,CAAakB,IAAI,4LACjBlB,eAAAA,CAAamB,OAAO;IAExB,MAAMZ,iBAAiBC,KAAKC,wMAAMV,qBAAAA,CAAmBY,iBAAiB;AACxE;AAaO,SAASS,kBACd,2BAA2B,GAC3BZ,GAAW,EACXC,IAAyC;IAAzCA,IAAAA,SAAAA,KAAAA,GAAAA,gMAAqBT,eAAAA,CAAamB,OAAO;IAEzC,MAAMZ,iBAAiBC,KAAKC,wMAAMV,qBAAAA,CAAmBsB,iBAAiB;AACxE;AAUO,SAASC,wBAAwBV,KAAc;IACpD,IAAI,8LAACX,kBAAAA,EAAgBW,QAAQ,OAAO;IAEpC,wEAAwE;IACxE,kBAAkB;IAClB,OAAOA,MAAME,MAAM,CAACS,KAAK,CAAC,KAAKC,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;AACnD;AAEO,SAASC,yBAAyBd,KAAoB;IAC3D,IAAI,8LAACX,kBAAAA,EAAgBW,QAAQ;QAC3B,MAAM,OAAA,cAAiC,CAAjC,IAAIC,MAAM,yBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgC;IACxC;IAEA,OAAOD,MAAME,MAAM,CAACS,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AACtC;AAEO,SAASI,+BAA+Bf,KAAoB;IACjE,IAAI,8LAACX,kBAAAA,EAAgBW,QAAQ;QAC3B,MAAM,OAAA,cAAiC,CAAjC,IAAIC,MAAM,yBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgC;IACxC;IAEA,OAAOe,OAAOhB,MAAME,MAAM,CAACS,KAAK,CAAC,KAAKM,EAAE,CAAC,CAAC;AAC5C", "ignoreList": [0]}}, {"offset": {"line": 1227, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/is-next-router-error.ts"], "sourcesContent": ["import {\n  isHTTPAccessFallbackError,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\nimport { isRedirectError, type RedirectError } from './redirect-error'\n\n/**\n * Returns true if the error is a navigation signal error. These errors are\n * thrown by user code to perform navigation operations and interrupt the React\n * render.\n */\nexport function isNextRouterError(\n  error: unknown\n): error is RedirectError | HTTPAccessFallbackError {\n  return isRedirectError(error) || isHTTPAccessFallbackError(error)\n}\n"], "names": ["isHTTPAccessFallbackError", "isRedirectError", "isNextRouterError", "error"], "mappings": ";;;AAAA,SACEA,yBAAyB,QAEpB,8CAA6C;AACpD,SAASC,eAAe,QAA4B,mBAAkB;;;AAO/D,SAASC,kBACdC,KAAc;IAEd,oMAAOF,kBAAAA,EAAgBE,8OAAUH,4BAAAA,EAA0BG;AAC7D", "ignoreList": [0]}}, {"offset": {"line": 1243, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/match-segments.ts"], "sourcesContent": ["import type { Segment } from '../../server/app-render/types'\n\nexport const matchSegment = (\n  existingSegment: Segment,\n  segment: Segment\n): boolean => {\n  // segment is either Array or string\n  if (typeof existingSegment === 'string') {\n    if (typeof segment === 'string') {\n      // Common case: segment is just a string\n      return existingSegment === segment\n    }\n    return false\n  }\n\n  if (typeof segment === 'string') {\n    return false\n  }\n  return existingSegment[0] === segment[0] && existingSegment[1] === segment[1]\n}\n"], "names": ["matchSegment", "existingSegment", "segment"], "mappings": ";;;AAEO,MAAMA,eAAe,CAC1BC,iBACAC;IAEA,oCAAoC;IACpC,IAAI,OAAOD,oBAAoB,UAAU;QACvC,IAAI,OAAOC,YAAY,UAAU;YAC/B,wCAAwC;YACxC,OAAOD,oBAAoBC;QAC7B;QACA,OAAO;IACT;IAEA,IAAI,OAAOA,YAAY,UAAU;QAC/B,OAAO;IACT;IACA,OAAOD,eAAe,CAAC,EAAE,KAAKC,OAAO,CAAC,EAAE,IAAID,eAAe,CAAC,EAAE,KAAKC,OAAO,CAAC,EAAE;AAC/E,EAAC", "ignoreList": [0]}}, {"offset": {"line": 1266, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/not-found.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n/**\n * This function allows you to render the [not-found.js file](https://nextjs.org/docs/app/api-reference/file-conventions/not-found)\n * within a route segment as well as inject a tag.\n *\n * `notFound()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a `<meta name=\"robots\" content=\"noindex\" />` meta tag and set the status code to 404.\n * - In a Route Handler or Server Action, it will serve a 404 to the caller.\n *\n * Read more: [Next.js Docs: `notFound`](https://nextjs.org/docs/app/api-reference/functions/not-found)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};404`\n\nexport function notFound(): never {\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n\n  throw error\n}\n"], "names": ["HTTP_ERROR_FALLBACK_ERROR_CODE", "DIGEST", "notFound", "error", "Error", "digest"], "mappings": ";;;AAAA,SACEA,8BAA8B,QAEzB,8CAA6C;;AAEpD;;;;;;;;;;;;;CAaC,GAED,MAAMC,SAAU,qOAAED,iCAAAA,GAA+B;AAE1C,SAASE;IACd,4CAA4C;IAC5C,MAAMC,QAAQ,OAAA,cAAiB,CAAjB,IAAIC,MAAMH,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BE,MAAkCE,MAAM,GAAGJ;IAE7C,MAAME;AACR", "ignoreList": [0]}}, {"offset": {"line": 1301, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/parallel-route-default.tsx"], "sourcesContent": ["import { notFound } from './not-found'\n\nexport const PARALLEL_ROUTE_DEFAULT_PATH =\n  'next/dist/client/components/parallel-route-default.js'\n\nexport default function ParallelRouteDefault() {\n  notFound()\n}\n"], "names": ["notFound", "PARALLEL_ROUTE_DEFAULT_PATH", "<PERSON>llel<PERSON><PERSON><PERSON>ault"], "mappings": ";;;;AAAA,SAASA,QAAQ,QAAQ,cAAa;;AAE/B,MAAMC,8BACX,wDAAuD;AAE1C,SAASC;4LACtBF,WAAAA;AACF", "ignoreList": [0]}}, {"offset": {"line": 1317, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/use-action-queue.ts"], "sourcesContent": ["import type { Dispatch } from 'react'\nimport React, { use } from 'react'\nimport { isThenable } from '../../shared/lib/is-thenable'\nimport type { AppRouterActionQueue } from './app-router-instance'\nimport type {\n  AppRouterState,\n  ReducerActions,\n  ReducerState,\n} from './router-reducer/router-reducer-types'\n\n// The app router state lives outside of React, so we can import the dispatch\n// method directly wherever we need it, rather than passing it around via props\n// or context.\nlet dispatch: Dispatch<ReducerActions> | null = null\n\nexport function dispatchAppRouterAction(action: ReducerActions) {\n  if (dispatch === null) {\n    throw new Error(\n      'Internal Next.js error: Router action dispatched before initialization.'\n    )\n  }\n  dispatch(action)\n}\n\nexport function useActionQueue(\n  actionQueue: AppRouterActionQueue\n): AppRouterState {\n  const [state, setState] = React.useState<ReducerState>(actionQueue.state)\n\n  // Because of a known issue that requires to decode Flight streams inside the\n  // render phase, we have to be a bit clever and assign the dispatch method to\n  // a module-level variable upon initialization. The useState hook in this\n  // module only exists to synchronize state that lives outside of React.\n  // Ideally, what we'd do instead is pass the state as a prop to root.render;\n  // this is conceptually how we're modeling the app router state, despite the\n  // weird implementation details.\n  if (process.env.NODE_ENV !== 'production') {\n    const useSyncDevRenderIndicator =\n      require('./react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator')\n        .useSyncDevRenderIndicator as typeof import('./react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator').useSyncDevRenderIndicator\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const syncDevRenderIndicator = useSyncDevRenderIndicator()\n\n    dispatch = (action: ReducerActions) => {\n      syncDevRenderIndicator(() => {\n        actionQueue.dispatch(action, setState)\n      })\n    }\n  } else {\n    dispatch = (action: ReducerActions) =>\n      actionQueue.dispatch(action, setState)\n  }\n\n  return isThenable(state) ? use(state) : state\n}\n"], "names": ["React", "use", "isThenable", "dispatch", "dispatchAppRouterAction", "action", "Error", "useActionQueue", "actionQueue", "state", "setState", "useState", "process", "env", "NODE_ENV", "useSyncDevRenderIndicator", "require", "syncDevRenderIndicator"], "mappings": ";;;;AACA,OAAOA,SAASC,GAAG,QAAQ,QAAO;AAClC,SAASC,UAAU,QAAQ,+BAA8B;;;AAQzD,6EAA6E;AAC7E,+EAA+E;AAC/E,cAAc;AACd,IAAIC,WAA4C;AAEzC,SAASC,wBAAwBC,MAAsB;IAC5D,IAAIF,aAAa,MAAM;QACrB,MAAM,OAAA,cAEL,CAFK,IAAIG,MACR,4EADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACAH,SAASE;AACX;AAEO,SAASE,eACdC,WAAiC;IAEjC,MAAM,CAACC,OAAOC,SAAS,sKAAGV,UAAAA,CAAMW,QAAQ,CAAeH,YAAYC,KAAK;IAExE,6EAA6E;IAC7E,6EAA6E;IAC7E,yEAAyE;IACzE,uEAAuE;IACvE,4EAA4E;IAC5E,4EAA4E;IAC5E,gCAAgC;IAChC,IAAIG,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,MAAMC,4BACJC,QAAQ,6KACLD,yBAAyB;QAC9B,sDAAsD;QACtD,MAAME,yBAAyBF;QAE/BZ,WAAW,CAACE;YACVY,uBAAuB;gBACrBT,YAAYL,QAAQ,CAACE,QAAQK;YAC/B;QACF;IACF,OAAO;;IAGP;IAEA,0LAAOR,aAAAA,EAAWO,gLAASR,MAAAA,EAAIQ,SAASA;AAC1C", "ignoreList": [0]}}, {"offset": {"line": 1368, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/navigation-untracked.ts"], "sourcesContent": ["import { useContext } from 'react'\nimport { PathnameContext } from '../../shared/lib/hooks-client-context.shared-runtime'\n\n/**\n * This checks to see if the current render has any unknown route parameters.\n * It's used to trigger a different render path in the error boundary.\n *\n * @returns true if there are any unknown route parameters, false otherwise\n */\nfunction hasFallbackRouteParams() {\n  if (typeof window === 'undefined') {\n    // AsyncLocalStorage should not be included in the client bundle.\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    const workStore = workAsyncStorage.getStore()\n    if (!workStore) return false\n\n    const { fallbackRouteParams } = workStore\n    if (!fallbackRouteParams || fallbackRouteParams.size === 0) return false\n\n    return true\n  }\n\n  return false\n}\n\n/**\n * This returns a `null` value if there are any unknown route parameters, and\n * otherwise returns the pathname from the context. This is an alternative to\n * `usePathname` that is used in the error boundary to avoid rendering the\n * error boundary when there are unknown route parameters. This doesn't throw\n * when accessed with unknown route parameters.\n *\n * @returns\n *\n * @internal\n */\nexport function useUntrackedPathname(): string | null {\n  // If there are any unknown route parameters we would typically throw\n  // an error, but this internal method allows us to return a null value instead\n  // for components that do not propagate the pathname to the static shell (like\n  // the error boundary).\n  if (hasFallbackRouteParams()) {\n    return null\n  }\n\n  // This shouldn't cause any issues related to conditional rendering because\n  // the environment will be consistent for the render.\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useContext(PathnameContext)\n}\n"], "names": ["useContext", "PathnameContext", "hasFallbackRouteParams", "window", "workAsyncStorage", "require", "workStore", "getStore", "fallbackRouteParams", "size", "useUntrackedPathname"], "mappings": ";;;AAAA,SAASA,UAAU,QAAQ,QAAO;AAClC,SAASC,eAAe,QAAQ,uDAAsD;;;AAEtF;;;;;CAKC,GACD,SAASC;IACP,IAAI,OAAOC,WAAW,aAAa;QACjC,iEAAiE;QACjE,MAAM,EAAEC,gBAAgB,EAAE,GACxBC,QAAQ;QAEV,MAAMC,YAAYF,iBAAiBG,QAAQ;QAC3C,IAAI,CAACD,WAAW,OAAO;QAEvB,MAAM,EAAEE,mBAAmB,EAAE,GAAGF;QAChC,IAAI,CAACE,uBAAuBA,oBAAoBC,IAAI,KAAK,GAAG,OAAO;QAEnE,OAAO;IACT;IAEA,OAAO;AACT;AAaO,SAASC;IACd,qEAAqE;IACrE,8EAA8E;IAC9E,8EAA8E;IAC9E,uBAAuB;IACvB,IAAIR,0BAA0B;QAC5B,OAAO;IACT;IAEA,2EAA2E;IAC3E,qDAAqD;IACrD,sDAAsD;IACtD,8KAAOF,aAAAA,2MAAWC,kBAAAA;AACpB", "ignoreList": [0]}}, {"offset": {"line": 1411, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/nav-failure-handler.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport { createHrefFromUrl } from './router-reducer/create-href-from-url'\n\nexport function handleHardNavError(error: unknown): boolean {\n  if (\n    error &&\n    typeof window !== 'undefined' &&\n    window.next.__pendingUrl &&\n    createHrefFromUrl(new URL(window.location.href)) !==\n      createHrefFromUrl(window.next.__pendingUrl)\n  ) {\n    console.error(\n      `Error occurred during navigation, falling back to hard navigation`,\n      error\n    )\n    window.location.href = window.next.__pendingUrl.toString()\n    return true\n  }\n  return false\n}\n\nexport function useNavFailureHandler() {\n  if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n    // this if is only for DCE of the feature flag not conditional\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      const uncaughtExceptionHandler = (\n        evt: ErrorEvent | PromiseRejectionEvent\n      ) => {\n        const error = 'reason' in evt ? evt.reason : evt.error\n        // if we have an unhandled exception/rejection during\n        // a navigation we fall back to a hard navigation to\n        // attempt recovering to a good state\n        handleHardNavError(error)\n      }\n      window.addEventListener('unhandledrejection', uncaughtExceptionHandler)\n      window.addEventListener('error', uncaughtExceptionHandler)\n      return () => {\n        window.removeEventListener('error', uncaughtExceptionHandler)\n        window.removeEventListener(\n          'unhandledrejection',\n          uncaughtExceptionHandler\n        )\n      }\n    }, [])\n  }\n}\n"], "names": ["useEffect", "createHrefFromUrl", "handleHardNavError", "error", "window", "next", "__pendingUrl", "URL", "location", "href", "console", "toString", "useNavFailureHandler", "process", "env", "__NEXT_APP_NAV_FAIL_HANDLING", "uncaughtExceptionHandler", "evt", "reason", "addEventListener", "removeEventListener"], "mappings": ";;;;AAAA,SAASA,SAAS,QAAQ,QAAO;AACjC,SAASC,iBAAiB,QAAQ,wCAAuC;;;AAElE,SAASC,mBAAmBC,KAAc;IAC/C,IACEA,SACA,OAAOC,WAAW,eAClBA,OAAOC,IAAI,CAACC,YAAY,kOACxBL,oBAAAA,EAAkB,IAAIM,IAAIH,OAAOI,QAAQ,CAACC,IAAI,qOAC5CR,oBAAAA,EAAkBG,OAAOC,IAAI,CAACC,YAAY,GAC5C;QACAI,QAAQP,KAAK,CACV,qEACDA;QAEFC,OAAOI,QAAQ,CAACC,IAAI,GAAGL,OAAOC,IAAI,CAACC,YAAY,CAACK,QAAQ;QACxD,OAAO;IACT;IACA,OAAO;AACT;AAEO,SAASC;IACd,IAAIC,QAAQC,GAAG,CAACC,uBAA8B,KAAF;;IAuB5C;AACF", "ignoreList": [0]}}, {"offset": {"line": 1438, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/error-boundary.tsx"], "sourcesContent": ["'use client'\n\nimport React, { type JSX } from 'react'\nimport { useUntrackedPathname } from './navigation-untracked'\nimport { isNextRouterError } from './is-next-router-error'\nimport { handleHardNavError } from './nav-failure-handler'\n\nconst workAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n      ).workAsyncStorage\n    : undefined\n\nconst styles = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  text: {\n    fontSize: '14px',\n    fontWeight: 400,\n    lineHeight: '28px',\n    margin: '0 8px',\n  },\n} as const\n\nexport type ErrorComponent = React.ComponentType<{\n  error: Error\n  // global-error, there's no `reset` function;\n  // regular error boundary, there's a `reset` function.\n  reset?: () => void\n}>\n\nexport interface ErrorBoundaryProps {\n  children?: React.ReactNode\n  errorComponent: ErrorComponent | undefined\n  errorStyles?: React.ReactNode | undefined\n  errorScripts?: React.ReactNode | undefined\n}\n\ninterface ErrorBoundaryHandlerProps extends ErrorBoundaryProps {\n  pathname: string | null\n  errorComponent: ErrorComponent\n}\n\ninterface ErrorBoundaryHandlerState {\n  error: Error | null\n  previousPathname: string | null\n}\n\n// if we are revalidating we want to re-throw the error so the\n// function crashes so we can maintain our previous cache\n// instead of caching the error page\nfunction HandleISRError({ error }: { error: any }) {\n  if (workAsyncStorage) {\n    const store = workAsyncStorage.getStore()\n    if (store?.isRevalidate || store?.isStaticGeneration) {\n      console.error(error)\n      throw error\n    }\n  }\n\n  return null\n}\n\nexport class ErrorBoundaryHandler extends React.Component<\n  ErrorBoundaryHandlerProps,\n  ErrorBoundaryHandlerState\n> {\n  constructor(props: ErrorBoundaryHandlerProps) {\n    super(props)\n    this.state = { error: null, previousPathname: this.props.pathname }\n  }\n\n  static getDerivedStateFromError(error: Error) {\n    if (isNextRouterError(error)) {\n      // Re-throw if an expected internal Next.js router error occurs\n      // this means it should be handled by a different boundary (such as a NotFound boundary in a parent segment)\n      throw error\n    }\n\n    return { error }\n  }\n\n  static getDerivedStateFromProps(\n    props: ErrorBoundaryHandlerProps,\n    state: ErrorBoundaryHandlerState\n  ): ErrorBoundaryHandlerState | null {\n    const { error } = state\n\n    // if we encounter an error while\n    // a navigation is pending we shouldn't render\n    // the error boundary and instead should fallback\n    // to a hard navigation to attempt recovering\n    if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n      if (error && handleHardNavError(error)) {\n        // clear error so we don't render anything\n        return {\n          error: null,\n          previousPathname: props.pathname,\n        }\n      }\n    }\n\n    /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */\n    if (props.pathname !== state.previousPathname && state.error) {\n      return {\n        error: null,\n        previousPathname: props.pathname,\n      }\n    }\n    return {\n      error: state.error,\n      previousPathname: props.pathname,\n    }\n  }\n\n  reset = () => {\n    this.setState({ error: null })\n  }\n\n  // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific to the `@types/react` version.\n  render(): React.ReactNode {\n    if (this.state.error) {\n      return (\n        <>\n          <HandleISRError error={this.state.error} />\n          {this.props.errorStyles}\n          {this.props.errorScripts}\n          <this.props.errorComponent\n            error={this.state.error}\n            reset={this.reset}\n          />\n        </>\n      )\n    }\n\n    return this.props.children\n  }\n}\n\nexport type GlobalErrorComponent = React.ComponentType<{\n  error: any\n}>\nexport function GlobalError({ error }: { error: any }) {\n  const digest: string | undefined = error?.digest\n  return (\n    <html id=\"__next_error__\">\n      <head></head>\n      <body>\n        <HandleISRError error={error} />\n        <div style={styles.error}>\n          <div>\n            <h2 style={styles.text}>\n              Application error: a {digest ? 'server' : 'client'}-side exception\n              has occurred while loading {window.location.hostname} (see the{' '}\n              {digest ? 'server logs' : 'browser console'} for more\n              information).\n            </h2>\n            {digest ? <p style={styles.text}>{`Digest: ${digest}`}</p> : null}\n          </div>\n        </div>\n      </body>\n    </html>\n  )\n}\n\n// Exported so that the import signature in the loaders can be identical to user\n// supplied custom global error signatures.\nexport default GlobalError\n\n/**\n * Handles errors through `getDerivedStateFromError`.\n * Renders the provided error component and provides a way to `reset` the error boundary state.\n */\n\n/**\n * Renders error boundary with the provided \"errorComponent\" property as the fallback.\n * If no \"errorComponent\" property is provided it renders the children without an error boundary.\n */\nexport function ErrorBoundary({\n  errorComponent,\n  errorStyles,\n  errorScripts,\n  children,\n}: ErrorBoundaryProps & {\n  children: React.ReactNode\n}): JSX.Element {\n  // When we're rendering the missing params shell, this will return null. This\n  // is because we won't be rendering any not found boundaries or error\n  // boundaries for the missing params shell. When this runs on the client\n  // (where these errors can occur), we will get the correct pathname.\n  const pathname = useUntrackedPathname()\n  if (errorComponent) {\n    return (\n      <ErrorBoundaryHandler\n        pathname={pathname}\n        errorComponent={errorComponent}\n        errorStyles={errorStyles}\n        errorScripts={errorScripts}\n      >\n        {children}\n      </ErrorBoundaryHandler>\n    )\n  }\n\n  return <>{children}</>\n}\n"], "names": ["React", "useUntrackedPathname", "isNextRouterError", "handleHardNavError", "workAsyncStorage", "window", "require", "undefined", "styles", "error", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "text", "fontSize", "fontWeight", "lineHeight", "margin", "HandleISRError", "store", "getStore", "isRevalidate", "isStaticGeneration", "console", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Component", "getDerivedStateFromError", "getDerivedStateFromProps", "props", "state", "process", "env", "__NEXT_APP_NAV_FAIL_HANDLING", "previousPathname", "pathname", "render", "errorStyles", "errorScripts", "this", "errorComponent", "reset", "children", "constructor", "setState", "GlobalError", "digest", "html", "id", "head", "body", "div", "style", "h2", "location", "hostname", "p", "Error<PERSON>ou<PERSON><PERSON>"], "mappings": ";;;;;;;AAEA,OAAOA,WAAyB,QAAO;AACvC,SAASC,oBAAoB,QAAQ,yBAAwB;AAC7D,SAASC,iBAAiB,QAAQ,yBAAwB;AAC1D,SAASC,kBAAkB,QAAQ,wBAAuB;AAL1D;;;;;;AAOA,MAAMC,mBACJ,OAAOC,WAAW,cAEZC,QAAQ,qIACRF,gBAAgB,GAClBG;AAEN,MAAMC,SAAS;IACbC,OAAO;QACL,0FAA0F;QAC1FC,YACE;QACFC,QAAQ;QACRC,WAAW;QACXC,SAAS;QACTC,eAAe;QACfC,YAAY;QACZC,gBAAgB;IAClB;IACAC,MAAM;QACJC,UAAU;QACVC,YAAY;QACZC,YAAY;QACZC,QAAQ;IACV;AACF;AA0BA,8DAA8D;AAC9D,yDAAyD;AACzD,oCAAoC;AACpC,SAASC,eAAe,KAAyB;IAAzB,IAAA,EAAEb,KAAK,EAAkB,GAAzB;IACtB,IAAIL,kBAAkB;QACpB,MAAMmB,QAAQnB,iBAAiBoB,QAAQ;QACvC,IAAID,CAAAA,SAAAA,OAAAA,KAAAA,IAAAA,MAAOE,YAAY,KAAA,CAAIF,SAAAA,OAAAA,KAAAA,IAAAA,MAAOG,kBAAkB,GAAE;YACpDC,QAAQlB,KAAK,CAACA;YACd,MAAMA;QACR;IACF;IAEA,OAAO;AACT;AAEO,MAAMmB,6BAA6B5B,6KAAAA,CAAM6B,SAAS;IASvD,OAAOC,yBAAyBrB,KAAY,EAAE;QAC5C,6MAAIP,oBAAAA,EAAkBO,QAAQ;YAC5B,+DAA+D;YAC/D,4GAA4G;YAC5G,MAAMA;QACR;QAEA,OAAO;YAAEA;QAAM;IACjB;IAEA,OAAOsB,yBACLC,KAAgC,EAChCC,KAAgC,EACE;QAClC,MAAM,EAAExB,KAAK,EAAE,GAAGwB;QAElB,iCAAiC;QACjC,8CAA8C;QAC9C,iDAAiD;QACjD,6CAA6C;QAC7C,IAAIC,QAAQC,GAAG,CAACC,uBAA8B,KAAF;;QAQ5C;QAEA;;;;;KAKC,GACD,IAAIJ,MAAMM,QAAQ,KAAKL,MAAMI,gBAAgB,IAAIJ,MAAMxB,KAAK,EAAE;YAC5D,OAAO;gBACLA,OAAO;gBACP4B,kBAAkBL,MAAMM,QAAQ;YAClC;QACF;QACA,OAAO;YACL7B,OAAOwB,MAAMxB,KAAK;YAClB4B,kBAAkBL,MAAMM,QAAQ;QAClC;IACF;IAMA,yIAAyI;IACzIC,SAA0B;QACxB,IAAI,IAAI,CAACN,KAAK,CAACxB,KAAK,EAAE;YACpB,OAAA,WAAA,IACE,sLAAA,EAAA,2KAAA,CAAA,WAAA,EAAA;;sCACE,kLAAA,EAACa,gBAAAA;wBAAeb,OAAO,IAAI,CAACwB,KAAK,CAACxB,KAAK;;oBACtC,IAAI,CAACuB,KAAK,CAACQ,WAAW;oBACtB,IAAI,CAACR,KAAK,CAACS,YAAY;kNACxB,MAAA,EAACC,IAAI,CAACV,KAAK,CAACW,cAAc,EAAA;wBACxBlC,OAAO,IAAI,CAACwB,KAAK,CAACxB,KAAK;wBACvBmC,OAAO,IAAI,CAACA,KAAK;;;;QAIzB;QAEA,OAAO,IAAI,CAACZ,KAAK,CAACa,QAAQ;IAC5B;IA1EAC,YAAYd,KAAgC,CAAE;QAC5C,KAAK,CAACA,QAAAA,IAAAA,CAoDRY,KAAAA,GAAQ;YACN,IAAI,CAACG,QAAQ,CAAC;gBAAEtC,OAAO;YAAK;QAC9B;QArDE,IAAI,CAACwB,KAAK,GAAG;YAAExB,OAAO;YAAM4B,kBAAkB,IAAI,CAACL,KAAK,CAACM,QAAQ;QAAC;IACpE;AAwEF;AAKO,SAASU,YAAY,KAAyB;IAAzB,IAAA,EAAEvC,KAAK,EAAkB,GAAzB;IAC1B,MAAMwC,SAA6BxC,SAAAA,OAAAA,KAAAA,IAAAA,MAAOwC,MAAM;IAChD,OAAA,WAAA,mLACE,OAAA,EAACC,QAAAA;QAAKC,IAAG;;8BACP,kLAAA,EAACC,QAAAA,CAAAA;0MACD,OAAA,EAACC,QAAAA;;kNACC,MAAA,EAAC/B,gBAAAA;wBAAeb,OAAOA;;kNACvB,MAAA,EAAC6C,OAAAA;wBAAIC,OAAO/C,OAAOC,KAAK;kCACtB,WAAA,mLAAA,OAAA,EAAC6C,OAAAA;;8NACC,OAAA,EAACE,MAAAA;oCAAGD,OAAO/C,OAAOS,IAAI;;wCAAE;wCACAgC,SAAS,WAAW;wCAAS;wCACvB5C,OAAOoD,QAAQ,CAACC,QAAQ;wCAAC;wCAAU;wCAC9DT,SAAS,gBAAgB;wCAAkB;;;gCAG7CA,SAAAA,WAAAA,mLAAS,MAAA,EAACU,KAAAA;oCAAEJ,OAAO/C,OAAOS,IAAI;8CAAI,aAAUgC;qCAAgB;;;;;;;;AAMzE;uCAIeD,YAAW;AAWnB,SAASY,cAAc,KAO7B;IAP6B,IAAA,EAC5BjB,cAAc,EACdH,WAAW,EACXC,YAAY,EACZI,QAAQ,EAGT,GAP6B;IAQ5B,6EAA6E;IAC7E,qEAAqE;IACrE,wEAAwE;IACxE,oEAAoE;IACpE,MAAMP,8MAAWrC,uBAAAA;IACjB,IAAI0C,gBAAgB;QAClB,OAAA,WAAA,mLACE,MAAA,EAACf,sBAAAA;YACCU,UAAUA;YACVK,gBAAgBA;YAChBH,aAAaA;YACbC,cAAcA;sBAEbI;;IAGP;IAEA,OAAA,WAAA,mLAAO,MAAA,EAAA,2KAAA,CAAA,WAAA,EAAA;kBAAGA;;AACZ", "ignoreList": [0]}}, {"offset": {"line": 1623, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/app-router-announcer.tsx"], "sourcesContent": ["import { useEffect, useRef, useState } from 'react'\nimport { createPortal } from 'react-dom'\nimport type { FlightRouterState } from '../../server/app-render/types'\n\nconst ANNOUNCER_TYPE = 'next-route-announcer'\nconst ANNOUNCER_ID = '__next-route-announcer__'\n\nfunction getAnnouncerNode() {\n  const existingAnnouncer = document.getElementsByName(ANNOUNCER_TYPE)[0]\n  if (existingAnnouncer?.shadowRoot?.childNodes[0]) {\n    return existingAnnouncer.shadowRoot.childNodes[0] as HTMLElement\n  } else {\n    const container = document.createElement(ANNOUNCER_TYPE)\n    container.style.cssText = 'position:absolute'\n    const announcer = document.createElement('div')\n    announcer.ariaLive = 'assertive'\n    announcer.id = ANNOUNCER_ID\n    announcer.role = 'alert'\n    announcer.style.cssText =\n      'position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal'\n\n    // Use shadow DOM here to avoid any potential CSS bleed\n    const shadow = container.attachShadow({ mode: 'open' })\n    shadow.appendChild(announcer)\n    document.body.appendChild(container)\n    return announcer\n  }\n}\n\nexport function AppRouterAnnouncer({ tree }: { tree: FlightRouterState }) {\n  const [portalNode, setPortalNode] = useState<HTMLElement | null>(null)\n\n  useEffect(() => {\n    const announcer = getAnnouncerNode()\n    setPortalNode(announcer)\n    return () => {\n      const container = document.getElementsByTagName(ANNOUNCER_TYPE)[0]\n      if (container?.isConnected) {\n        document.body.removeChild(container)\n      }\n    }\n  }, [])\n\n  const [routeAnnouncement, setRouteAnnouncement] = useState('')\n  const previousTitle = useRef<string | undefined>(undefined)\n\n  useEffect(() => {\n    let currentTitle = ''\n    if (document.title) {\n      currentTitle = document.title\n    } else {\n      const pageHeader = document.querySelector('h1')\n      if (pageHeader) {\n        currentTitle = pageHeader.innerText || pageHeader.textContent || ''\n      }\n    }\n\n    // Only announce the title change, but not for the first load because screen\n    // readers do that automatically.\n    if (\n      previousTitle.current !== undefined &&\n      previousTitle.current !== currentTitle\n    ) {\n      setRouteAnnouncement(currentTitle)\n    }\n    previousTitle.current = currentTitle\n  }, [tree])\n\n  return portalNode ? createPortal(routeAnnouncement, portalNode) : null\n}\n"], "names": ["useEffect", "useRef", "useState", "createPortal", "ANNOUNCER_TYPE", "ANNOUNCER_ID", "getAnnouncerNode", "existingAnnouncer", "document", "getElementsByName", "shadowRoot", "childNodes", "container", "createElement", "style", "cssText", "announcer", "ariaLive", "id", "role", "shadow", "attachShadow", "mode", "append<PERSON><PERSON><PERSON>", "body", "AppRouterAnnouncer", "tree", "portalNode", "setPortalNode", "getElementsByTagName", "isConnected", "<PERSON><PERSON><PERSON><PERSON>", "routeAnnouncement", "setRouteAnnouncement", "previousTitle", "undefined", "currentTitle", "title", "pageHeader", "querySelector", "innerText", "textContent", "current"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,QAAO;AACnD,SAASC,YAAY,QAAQ,YAAW;;;AAGxC,MAAMC,iBAAiB;AACvB,MAAMC,eAAe;AAErB,SAASC;QAEHC;IADJ,MAAMA,oBAAoBC,SAASC,iBAAiB,CAACL,eAAe,CAAC,EAAE;IACvE,IAAIG,qBAAAA,OAAAA,KAAAA,IAAAA,CAAAA,gCAAAA,kBAAmBG,UAAU,KAAA,OAAA,KAAA,IAA7BH,8BAA+BI,UAAU,CAAC,EAAE,EAAE;QAChD,OAAOJ,kBAAkBG,UAAU,CAACC,UAAU,CAAC,EAAE;IACnD,OAAO;QACL,MAAMC,YAAYJ,SAASK,aAAa,CAACT;QACzCQ,UAAUE,KAAK,CAACC,OAAO,GAAG;QAC1B,MAAMC,YAAYR,SAASK,aAAa,CAAC;QACzCG,UAAUC,QAAQ,GAAG;QACrBD,UAAUE,EAAE,GAAGb;QACfW,UAAUG,IAAI,GAAG;QACjBH,UAAUF,KAAK,CAACC,OAAO,GACrB;QAEF,uDAAuD;QACvD,MAAMK,SAASR,UAAUS,YAAY,CAAC;YAAEC,MAAM;QAAO;QACrDF,OAAOG,WAAW,CAACP;QACnBR,SAASgB,IAAI,CAACD,WAAW,CAACX;QAC1B,OAAOI;IACT;AACF;AAEO,SAASS,mBAAmB,KAAqC;IAArC,IAAA,EAAEC,IAAI,EAA+B,GAArC;IACjC,MAAM,CAACC,YAAYC,cAAc,0KAAG1B,WAAAA,EAA6B;2KAEjEF,YAAAA,EAAU;QACR,MAAMgB,YAAYV;QAClBsB,cAAcZ;QACd,OAAO;YACL,MAAMJ,YAAYJ,SAASqB,oBAAoB,CAACzB,eAAe,CAAC,EAAE;YAClE,IAAIQ,aAAAA,OAAAA,KAAAA,IAAAA,UAAWkB,WAAW,EAAE;gBAC1BtB,SAASgB,IAAI,CAACO,WAAW,CAACnB;YAC5B;QACF;IACF,GAAG,EAAE;IAEL,MAAM,CAACoB,mBAAmBC,qBAAqB,0KAAG/B,WAAAA,EAAS;IAC3D,MAAMgC,iBAAgBjC,+KAAAA,EAA2BkC;2KAEjDnC,YAAAA,EAAU;QACR,IAAIoC,eAAe;QACnB,IAAI5B,SAAS6B,KAAK,EAAE;YAClBD,eAAe5B,SAAS6B,KAAK;QAC/B,OAAO;YACL,MAAMC,aAAa9B,SAAS+B,aAAa,CAAC;YAC1C,IAAID,YAAY;gBACdF,eAAeE,WAAWE,SAAS,IAAIF,WAAWG,WAAW,IAAI;YACnE;QACF;QAEA,4EAA4E;QAC5E,iCAAiC;QACjC,IACEP,cAAcQ,OAAO,KAAKP,aAC1BD,cAAcQ,OAAO,KAAKN,cAC1B;YACAH,qBAAqBG;QACvB;QACAF,cAAcQ,OAAO,GAAGN;IAC1B,GAAG;QAACV;KAAK;IAET,OAAOC,aAAAA,WAAAA,iLAAaxB,eAAAA,EAAa6B,mBAAmBL,cAAc;AACpE", "ignoreList": [0]}}, {"offset": {"line": 1696, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/forbidden.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `forbidden` docs\n/**\n * @experimental\n * This function allows you to render the [forbidden.js file](https://nextjs.org/docs/app/api-reference/file-conventions/forbidden)\n * within a route segment as well as inject a tag.\n *\n * `forbidden()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * Read more: [Next.js Docs: `forbidden`](https://nextjs.org/docs/app/api-reference/functions/forbidden)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};403`\n\nexport function forbidden(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`forbidden()\\` is experimental and only allowed to be enabled when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n"], "names": ["HTTP_ERROR_FALLBACK_ERROR_CODE", "DIGEST", "forbidden", "process", "env", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "Error", "error", "digest"], "mappings": ";;;AAAA,SACEA,8BAA8B,QAEzB,8CAA6C;;AAEpD,6BAA6B;AAC7B;;;;;;;;;;;CAWC,GAED,MAAMC,SAAU,qOAAED,iCAAAA,GAA+B;AAE1C,SAASE;IACd,IAAI,CAACC,QAAQC,GAAG,CAACC,uBAAqC,YAAF;QAClD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,gHADG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,4CAA4C;IAC5C,MAAMC,QAAQ,OAAA,cAAiB,CAAjB,IAAID,MAAML,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BM,MAAkCC,MAAM,GAAGP;IAC7C,MAAMM;AACR", "ignoreList": [0]}}, {"offset": {"line": 1737, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/unauthorized.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `unauthorized` docs\n/**\n * @experimental\n * This function allows you to render the [unauthorized.js file](https://nextjs.org/docs/app/api-reference/file-conventions/unauthorized)\n * within a route segment as well as inject a tag.\n *\n * `unauthorized()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n *\n * Read more: [Next.js Docs: `unauthorized`](https://nextjs.org/docs/app/api-reference/functions/unauthorized)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};401`\n\nexport function unauthorized(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`unauthorized()\\` is experimental and only allowed to be used when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n"], "names": ["HTTP_ERROR_FALLBACK_ERROR_CODE", "DIGEST", "unauthorized", "process", "env", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "Error", "error", "digest"], "mappings": ";;;AAAA,SACEA,8BAA8B,QAEzB,8CAA6C;;AAEpD,gCAAgC;AAChC;;;;;;;;;;;;CAYC,GAED,MAAMC,SAAU,qOAAED,iCAAAA,GAA+B;AAE1C,SAASE;IACd,IAAI,CAACC,QAAQC,GAAG,CAACC,uBAAqC,YAAF;QAClD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,gHADG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,4CAA4C;IAC5C,MAAMC,QAAQ,OAAA,cAAiB,CAAjB,IAAID,MAAML,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BM,MAAkCC,MAAM,GAAGP;IAC7C,MAAMM;AACR", "ignoreList": [0]}}, {"offset": {"line": 1779, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/unstable-rethrow.browser.ts"], "sourcesContent": ["import { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from './is-next-router-error'\n\nexport function unstable_rethrow(error: unknown): void {\n  if (isNextRouterError(error) || isBailoutToCSRError(error)) {\n    throw error\n  }\n\n  if (error instanceof Error && 'cause' in error) {\n    unstable_rethrow(error.cause)\n  }\n}\n"], "names": ["isBailoutToCSRError", "isNextRouterError", "unstable_rethrow", "error", "Error", "cause"], "mappings": ";;;AAAA,SAASA,mBAAmB,QAAQ,+CAA8C;AAClF,SAASC,iBAAiB,QAAQ,yBAAwB;;;AAEnD,SAASC,iBAAiBC,KAAc;IAC7C,6MAAIF,oBAAAA,EAAkBE,sNAAUH,sBAAAA,EAAoBG,QAAQ;QAC1D,MAAMA;IACR;IAEA,IAAIA,iBAAiBC,SAAS,WAAWD,OAAO;QAC9CD,iBAAiBC,MAAME,KAAK;IAC9B;AACF", "ignoreList": [0]}}, {"offset": {"line": 1800, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/unstable-rethrow.server.ts"], "sourcesContent": ["import { isHangingPromiseRejectionError } from '../../server/dynamic-rendering-utils'\nimport { isPostpone } from '../../server/lib/router-utils/is-postpone'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from './is-next-router-error'\nimport { isDynamicPostpone } from '../../server/app-render/dynamic-rendering'\nimport { isDynamicServerError } from './hooks-server-context'\n\nexport function unstable_rethrow(error: unknown): void {\n  if (\n    isNextRouterError(error) ||\n    isBailoutToCSRError(error) ||\n    isDynamicServerError(error) ||\n    isDynamicPostpone(error) ||\n    isPostpone(error) ||\n    isHangingPromiseRejectionError(error)\n  ) {\n    throw error\n  }\n\n  if (error instanceof Error && 'cause' in error) {\n    unstable_rethrow(error.cause)\n  }\n}\n"], "names": ["isHangingPromiseRejectionError", "isPostpone", "isBailoutToCSRError", "isNextRouterError", "isDynamicPostpone", "isDynamicServerError", "unstable_rethrow", "error", "Error", "cause"], "mappings": ";;;AAAA,SAASA,8BAA8B,QAAQ,uCAAsC;AACrF,SAASC,UAAU,QAAQ,4CAA2C;AACtE,SAASC,mBAAmB,QAAQ,+CAA8C;AAClF,SAASC,iBAAiB,QAAQ,yBAAwB;AAC1D,SAASC,iBAAiB,QAAQ,4CAA2C;AAC7E,SAASC,oBAAoB,QAAQ,yBAAwB;;;;;;;AAEtD,SAASC,iBAAiBC,KAAc;IAC7C,6MACEJ,oBAAAA,EAAkBI,WAClBL,iOAAAA,EAAoBK,gNACpBF,uBAAAA,EAAqBE,6MACrBH,oBAAAA,EAAkBG,gNAClBN,aAAAA,EAAWM,qMACXP,iCAAAA,EAA+BO,QAC/B;QACA,MAAMA;IACR;IAEA,IAAIA,iBAAiBC,SAAS,WAAWD,OAAO;QAC9CD,iBAAiBC,MAAME,KAAK;IAC9B;AACF", "ignoreList": [0]}}, {"offset": {"line": 1829, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/unstable-rethrow.ts"], "sourcesContent": ["/**\n * This function should be used to rethrow internal Next.js errors so that they can be handled by the framework.\n * When wrapping an API that uses errors to interrupt control flow, you should use this function before you do any error handling.\n * This function will rethrow the error if it is a Next.js error so it can be handled, otherwise it will do nothing.\n *\n * Read more: [Next.js Docs: `unstable_rethrow`](https://nextjs.org/docs/app/api-reference/functions/unstable_rethrow)\n */\nexport const unstable_rethrow =\n  typeof window === 'undefined'\n    ? (\n        require('./unstable-rethrow.server') as typeof import('./unstable-rethrow.server')\n      ).unstable_rethrow\n    : (\n        require('./unstable-rethrow.browser') as typeof import('./unstable-rethrow.browser')\n      ).unstable_rethrow\n"], "names": ["unstable_rethrow", "window", "require"], "mappings": "AAAA;;;;;;CAMC,GACD;;;AAAO,MAAMA,mBACX,OAAOC,WAAW,cAEZC,QAAQ,iIACRF,gBAAgB,GAEhBE,QAAQ,kIACRF,gBAAgB,CAAA", "ignoreList": [0]}}, {"offset": {"line": 1845, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/navigation.react-server.ts"], "sourcesContent": ["/** @internal */\nclass ReadonlyURLSearchParamsError extends <PERSON>rror {\n  constructor() {\n    super(\n      'Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams'\n    )\n  }\n}\n\nclass ReadonlyURLSearchParams extends URLSearchParams {\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  append() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  delete() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  set() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  sort() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n}\n\nexport { redirect, permanentRedirect } from './redirect'\nexport { RedirectType } from './redirect-error'\nexport { notFound } from './not-found'\nexport { forbidden } from './forbidden'\nexport { unauthorized } from './unauthorized'\nexport { unstable_rethrow } from './unstable-rethrow'\nexport { ReadonlyURLSearchParams }\n"], "names": ["ReadonlyURLSearchParamsError", "Error", "constructor", "ReadonlyURLSearchParams", "URLSearchParams", "append", "delete", "set", "sort", "redirect", "permanentRedirect", "RedirectType", "notFound", "forbidden", "unauthorized", "unstable_rethrow"], "mappings": "AAAA,cAAc;;;AA4Bd,SAASS,QAAQ,EAAEC,iBAAiB,QAAQ,aAAY;AACxD,SAASC,YAAY,QAAQ,mBAAkB;AAC/C,SAASC,QAAQ,QAAQ,cAAa;AACtC,SAASC,SAAS,QAAQ,cAAa;AACvC,SAASC,YAAY,QAAQ,iBAAgB;AAC7C,SAASC,gBAAgB,QAAQ,qBAAoB;AAhCrD,MAAMf,qCAAqCC;IACzCC,aAAc;QACZ,KAAK,CACH;IAEJ;AACF;AAEA,MAAMC,gCAAgCC;IACpC,wKAAwK,GACxKC,SAAS;QACP,MAAM,IAAIL;IACZ;IACA,wKAAwK,GACxKM,SAAS;QACP,MAAM,IAAIN;IACZ;IACA,wKAAwK,GACxKO,MAAM;QACJ,MAAM,IAAIP;IACZ;IACA,wKAAwK,GACxKQ,OAAO;QACL,MAAM,IAAIR;IACZ;AACF", "ignoreList": [0]}}, {"offset": {"line": 1901, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/bailout-to-client-rendering.ts"], "sourcesContent": ["import { BailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { workAsyncStorage } from '../../server/app-render/work-async-storage.external'\n\nexport function bailoutToClientRendering(reason: string): void | never {\n  const workStore = workAsyncStorage.getStore()\n\n  if (workStore?.forceStatic) return\n\n  if (workStore?.isStaticGeneration) throw new BailoutToCSRError(reason)\n}\n"], "names": ["BailoutToCSRError", "workAsyncStorage", "bailoutToClientRendering", "reason", "workStore", "getStore", "forceStatic", "isStaticGeneration"], "mappings": ";;;AAAA,SAASA,iBAAiB,QAAQ,+CAA8C;;AAChF,SAASC,gBAAgB,QAAQ,sDAAqD;;;AAE/E,SAASC,yBAAyBC,MAAc;IACrD,MAAMC,8RAAYH,mBAAAA,CAAiBI,QAAQ;IAE3C,IAAID,aAAAA,OAAAA,KAAAA,IAAAA,UAAWE,WAAW,EAAE;IAE5B,IAAIF,aAAAA,OAAAA,KAAAA,IAAAA,UAAWG,kBAAkB,EAAE,MAAM,OAAA,cAA6B,CAA7B,4MAAIP,oBAAAA,CAAkBG,SAAtB,qBAAA;eAAA;oBAAA;sBAAA;IAA4B;AACvE", "ignoreList": [0]}}, {"offset": {"line": 1924, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/navigation.ts"], "sourcesContent": ["import type { Flight<PERSON>outerState } from '../../server/app-render/types'\nimport type { Params } from '../../server/request/params'\n\nimport { useContext, useMemo } from 'react'\nimport {\n  AppRouterContext,\n  LayoutRouterContext,\n  type AppRouterInstance,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport {\n  SearchParamsContext,\n  PathnameContext,\n  PathParamsContext,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport { getSegmentValue } from './router-reducer/reducers/get-segment-value'\nimport { PAGE_SEGMENT_KEY, DEFAULT_SEGMENT_KEY } from '../../shared/lib/segment'\nimport { ReadonlyURLSearchParams } from './navigation.react-server'\n\nconst useDynamicRouteParams =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/dynamic-rendering') as typeof import('../../server/app-render/dynamic-rendering')\n      ).useDynamicRouteParams\n    : undefined\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you *read* the current URL's search parameters.\n *\n * Learn more about [`URLSearchParams` on MDN](https://developer.mozilla.org/docs/Web/API/URLSearchParams)\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useSearchParams } from 'next/navigation'\n *\n * export default function Page() {\n *   const searchParams = useSearchParams()\n *   searchParams.get('foo') // returns 'bar' when ?foo=bar\n *   // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSearchParams`](https://nextjs.org/docs/app/api-reference/functions/use-search-params)\n */\n// Client components API\nexport function useSearchParams(): ReadonlyURLSearchParams {\n  const searchParams = useContext(SearchParamsContext)\n\n  // In the case where this is `null`, the compat types added in\n  // `next-env.d.ts` will add a new overload that changes the return type to\n  // include `null`.\n  const readonlySearchParams = useMemo(() => {\n    if (!searchParams) {\n      // When the router is not ready in pages, we won't have the search params\n      // available.\n      return null\n    }\n\n    return new ReadonlyURLSearchParams(searchParams)\n  }, [searchParams]) as ReadonlyURLSearchParams\n\n  if (typeof window === 'undefined') {\n    // AsyncLocalStorage should not be included in the client bundle.\n    const { bailoutToClientRendering } =\n      require('./bailout-to-client-rendering') as typeof import('./bailout-to-client-rendering')\n    // TODO-APP: handle dynamic = 'force-static' here and on the client\n    bailoutToClientRendering('useSearchParams()')\n  }\n\n  return readonlySearchParams\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the current URL's pathname.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { usePathname } from 'next/navigation'\n *\n * export default function Page() {\n *  const pathname = usePathname() // returns \"/dashboard\" on /dashboard?foo=bar\n *  // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `usePathname`](https://nextjs.org/docs/app/api-reference/functions/use-pathname)\n */\n// Client components API\nexport function usePathname(): string {\n  useDynamicRouteParams?.('usePathname()')\n\n  // In the case where this is `null`, the compat types added in `next-env.d.ts`\n  // will add a new overload that changes the return type to include `null`.\n  return useContext(PathnameContext) as string\n}\n\n// Client components API\nexport {\n  ServerInsertedHTMLContext,\n  useServerInsertedHTML,\n} from '../../shared/lib/server-inserted-html.shared-runtime'\n\n/**\n *\n * This hook allows you to programmatically change routes inside [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components).\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useRouter } from 'next/navigation'\n *\n * export default function Page() {\n *  const router = useRouter()\n *  // ...\n *  router.push('/dashboard') // Navigate to /dashboard\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useRouter`](https://nextjs.org/docs/app/api-reference/functions/use-router)\n */\n// Client components API\nexport function useRouter(): AppRouterInstance {\n  const router = useContext(AppRouterContext)\n  if (router === null) {\n    throw new Error('invariant expected app router to be mounted')\n  }\n\n  return router\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read a route's dynamic params filled in by the current URL.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useParams } from 'next/navigation'\n *\n * export default function Page() {\n *   // on /dashboard/[team] where pathname is /dashboard/nextjs\n *   const { team } = useParams() // team === \"nextjs\"\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useParams`](https://nextjs.org/docs/app/api-reference/functions/use-params)\n */\n// Client components API\nexport function useParams<T extends Params = Params>(): T {\n  useDynamicRouteParams?.('useParams()')\n\n  return useContext(PathParamsContext) as T\n}\n\n/** Get the canonical parameters from the current level to the leaf node. */\n// Client components API\nfunction getSelectedLayoutSegmentPath(\n  tree: FlightRouterState,\n  parallelRouteKey: string,\n  first = true,\n  segmentPath: string[] = []\n): string[] {\n  let node: FlightRouterState\n  if (first) {\n    // Use the provided parallel route key on the first parallel route\n    node = tree[1][parallelRouteKey]\n  } else {\n    // After first parallel route prefer children, if there's no children pick the first parallel route.\n    const parallelRoutes = tree[1]\n    node = parallelRoutes.children ?? Object.values(parallelRoutes)[0]\n  }\n\n  if (!node) return segmentPath\n  const segment = node[0]\n\n  let segmentValue = getSegmentValue(segment)\n\n  if (!segmentValue || segmentValue.startsWith(PAGE_SEGMENT_KEY)) {\n    return segmentPath\n  }\n\n  segmentPath.push(segmentValue)\n\n  return getSelectedLayoutSegmentPath(\n    node,\n    parallelRouteKey,\n    false,\n    segmentPath\n  )\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segments **below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n *\n * import { useSelectedLayoutSegments } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segments = useSelectedLayoutSegments()\n *\n *   return (\n *     <ul>\n *       {segments.map((segment, index) => (\n *         <li key={index}>{segment}</li>\n *       ))}\n *     </ul>\n *   )\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegments`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segments)\n */\n// Client components API\nexport function useSelectedLayoutSegments(\n  parallelRouteKey: string = 'children'\n): string[] {\n  useDynamicRouteParams?.('useSelectedLayoutSegments()')\n\n  const context = useContext(LayoutRouterContext)\n  // @ts-expect-error This only happens in `pages`. Type is overwritten in navigation.d.ts\n  if (!context) return null\n\n  return getSelectedLayoutSegmentPath(context.parentTree, parallelRouteKey)\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segment **one level below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n * import { useSelectedLayoutSegment } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segment = useSelectedLayoutSegment()\n *\n *   return <p>Active segment: {segment}</p>\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegment`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segment)\n */\n// Client components API\nexport function useSelectedLayoutSegment(\n  parallelRouteKey: string = 'children'\n): string | null {\n  useDynamicRouteParams?.('useSelectedLayoutSegment()')\n\n  const selectedLayoutSegments = useSelectedLayoutSegments(parallelRouteKey)\n\n  if (!selectedLayoutSegments || selectedLayoutSegments.length === 0) {\n    return null\n  }\n\n  const selectedLayoutSegment =\n    parallelRouteKey === 'children'\n      ? selectedLayoutSegments[0]\n      : selectedLayoutSegments[selectedLayoutSegments.length - 1]\n\n  // if the default slot is showing, we return null since it's not technically \"selected\" (it's a fallback)\n  // and returning an internal value like `__DEFAULT__` would be confusing.\n  return selectedLayoutSegment === DEFAULT_SEGMENT_KEY\n    ? null\n    : selectedLayoutSegment\n}\n\n// Shared components APIs\nexport {\n  notFound,\n  forbidden,\n  unauthorized,\n  redirect,\n  permanentRedirect,\n  RedirectType,\n  ReadonlyURLSearchParams,\n  unstable_rethrow,\n} from './navigation.react-server'\n"], "names": ["useContext", "useMemo", "AppRouterContext", "LayoutRouterContext", "SearchParamsContext", "PathnameContext", "PathParamsContext", "getSegmentValue", "PAGE_SEGMENT_KEY", "DEFAULT_SEGMENT_KEY", "ReadonlyURLSearchParams", "useDynamicRouteParams", "window", "require", "undefined", "useSearchParams", "searchParams", "readonlySearchParams", "bailoutToClientRendering", "usePathname", "ServerInsertedHTMLContext", "useServerInsertedHTML", "useRouter", "router", "Error", "useParams", "getSelectedLayoutSegmentPath", "tree", "parallelRouteKey", "first", "segmentPath", "node", "parallelRoutes", "children", "Object", "values", "segment", "segmentValue", "startsWith", "push", "useSelectedLayoutSegments", "context", "parentTree", "useSelectedLayoutSegment", "selectedLayoutSegments", "length", "selectedLayoutSegment", "notFound", "forbidden", "unauthorized", "redirect", "permanentRedirect", "RedirectType", "unstable_rethrow"], "mappings": ";;;;;;;;AAGA,SAASA,UAAU,EAAEC,OAAO,QAAQ,QAAO;AAC3C,SACEC,gBAAgB,EAChBC,mBAAmB,QAEd,qDAAoD;AAC3D,SACEC,mBAAmB,EACnBC,eAAe,EACfC,iBAAiB,QACZ,uDAAsD;AAC7D,SAASC,eAAe,QAAQ,8CAA6C;AAC7E,SAASC,gBAAgB,EAAEC,mBAAmB,QAAQ,2BAA0B;;AAChF,SAASC,uBAAuB,QAAQ,4BAA2B;AAmFnE,wBAAwB;AACxB,SACEU,yBAAyB,EACzBC,qBAAqB,QAChB,uDAAsD;;;;;;;AArF7D,MAAMV,wBACJ,OAAOC,WAAW,cAEZC,QAAQ,2HACRF,qBAAqB,GACvBG;AAuBC,SAASC;IACd,MAAMC,sLAAehB,aAAAA,2MAAWI,sBAAAA;IAEhC,8DAA8D;IAC9D,0EAA0E;IAC1E,kBAAkB;IAClB,MAAMa,8LAAuBhB,UAAAA,EAAQ;QACnC,IAAI,CAACe,cAAc;YACjB,yEAAyE;YACzE,aAAa;YACb,OAAO;QACT;QAEA,OAAO,yNAAIN,0BAAAA,CAAwBM;IACrC,GAAG;QAACA;KAAa;IAEjB,IAAI,OAAOJ,WAAW,aAAa;QACjC,iEAAiE;QACjE,MAAM,EAAEM,wBAAwB,EAAE,GAChCL,QAAQ;QACV,mEAAmE;QACnEK,yBAAyB;IAC3B;IAEA,OAAOD;AACT;AAoBO,SAASE;IACdR,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAwB;IAExB,8EAA8E;IAC9E,0EAA0E;IAC1E,8KAAOX,aAAAA,2MAAWK,kBAAAA;AACpB;;AA2BO,SAASiB;IACd,MAAMC,UAASvB,mLAAAA,yMAAWE,mBAAAA;IAC1B,IAAIqB,WAAW,MAAM;QACnB,MAAM,OAAA,cAAwD,CAAxD,IAAIC,MAAM,gDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAuD;IAC/D;IAEA,OAAOD;AACT;AAoBO,SAASE;IACdd,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAwB;IAExB,8KAAOX,aAAAA,2MAAWM,oBAAAA;AACpB;AAEA,0EAA0E,GAC1E,wBAAwB;AACxB,SAASoB,6BACPC,IAAuB,EACvBC,gBAAwB,EACxBC,KAAY,EACZC,WAA0B;IAD1BD,IAAAA,UAAAA,KAAAA,GAAAA,QAAQ;IACRC,IAAAA,gBAAAA,KAAAA,GAAAA,cAAwB,EAAE;IAE1B,IAAIC;IACJ,IAAIF,OAAO;QACT,kEAAkE;QAClEE,OAAOJ,IAAI,CAAC,EAAE,CAACC,iBAAiB;IAClC,OAAO;QACL,oGAAoG;QACpG,MAAMI,iBAAiBL,IAAI,CAAC,EAAE;YACvBK;QAAPD,OAAOC,CAAAA,2BAAAA,eAAeC,QAAQ,KAAA,OAAvBD,2BAA2BE,OAAOC,MAAM,CAACH,eAAe,CAAC,EAAE;IACpE;IAEA,IAAI,CAACD,MAAM,OAAOD;IAClB,MAAMM,UAAUL,IAAI,CAAC,EAAE;IAEvB,IAAIM,mPAAe9B,kBAAAA,EAAgB6B;IAEnC,IAAI,CAACC,gBAAgBA,aAAaC,UAAU,yKAAC9B,mBAAAA,GAAmB;QAC9D,OAAOsB;IACT;IAEAA,YAAYS,IAAI,CAACF;IAEjB,OAAOX,6BACLK,MACAH,kBACA,OACAE;AAEJ;AA4BO,SAASU,0BACdZ,gBAAqC;IAArCA,IAAAA,qBAAAA,KAAAA,GAAAA,mBAA2B;IAE3BjB,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAwB;IAExB,MAAM8B,WAAUzC,mLAAAA,yMAAWG,sBAAAA;IAC3B,wFAAwF;IACxF,IAAI,CAACsC,SAAS,OAAO;IAErB,OAAOf,6BAA6Be,QAAQC,UAAU,EAAEd;AAC1D;AAqBO,SAASe,yBACdf,gBAAqC;IAArCA,IAAAA,qBAAAA,KAAAA,GAAAA,mBAA2B;IAE3BjB,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAwB;IAExB,MAAMiC,yBAAyBJ,0BAA0BZ;IAEzD,IAAI,CAACgB,0BAA0BA,uBAAuBC,MAAM,KAAK,GAAG;QAClE,OAAO;IACT;IAEA,MAAMC,wBACJlB,qBAAqB,aACjBgB,sBAAsB,CAAC,EAAE,GACzBA,sBAAsB,CAACA,uBAAuBC,MAAM,GAAG,EAAE;IAE/D,yGAAyG;IACzG,yEAAyE;IACzE,OAAOC,kMAA0BrC,sBAAAA,GAC7B,OACAqC;AACN", "ignoreList": [0]}}, {"offset": {"line": 2059, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/redirect-boundary.tsx"], "sourcesContent": ["'use client'\nimport React, { useEffect } from 'react'\nimport type { AppRouterInstance } from '../../shared/lib/app-router-context.shared-runtime'\nimport { useRouter } from './navigation'\nimport { getRedirectTypeFromError, getURLFromRedirectError } from './redirect'\nimport { RedirectType, isRedirectError } from './redirect-error'\n\ninterface RedirectBoundaryProps {\n  router: AppRouterInstance\n  children: React.ReactNode\n}\n\nfunction HandleRedirect({\n  redirect,\n  reset,\n  redirectType,\n}: {\n  redirect: string\n  redirectType: RedirectType\n  reset: () => void\n}) {\n  const router = useRouter()\n\n  useEffect(() => {\n    React.startTransition(() => {\n      if (redirectType === RedirectType.push) {\n        router.push(redirect, {})\n      } else {\n        router.replace(redirect, {})\n      }\n      reset()\n    })\n  }, [redirect, redirectType, reset, router])\n\n  return null\n}\n\nexport class RedirectErrorBoundary extends React.Component<\n  RedirectBoundaryProps,\n  { redirect: string | null; redirectType: RedirectType | null }\n> {\n  constructor(props: RedirectBoundaryProps) {\n    super(props)\n    this.state = { redirect: null, redirectType: null }\n  }\n\n  static getDerivedStateFromError(error: any) {\n    if (isRedirectError(error)) {\n      const url = getURLFromRedirectError(error)\n      const redirectType = getRedirectTypeFromError(error)\n      return { redirect: url, redirectType }\n    }\n    // Re-throw if error is not for redirect\n    throw error\n  }\n\n  // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific to the `@types/react` version.\n  render(): React.ReactNode {\n    const { redirect, redirectType } = this.state\n    if (redirect !== null && redirectType !== null) {\n      return (\n        <HandleRedirect\n          redirect={redirect}\n          redirectType={redirectType}\n          reset={() => this.setState({ redirect: null })}\n        />\n      )\n    }\n\n    return this.props.children\n  }\n}\n\nexport function RedirectBoundary({ children }: { children: React.ReactNode }) {\n  const router = useRouter()\n  return (\n    <RedirectErrorBoundary router={router}>{children}</RedirectErrorBoundary>\n  )\n}\n"], "names": ["React", "useEffect", "useRouter", "getRedirectTypeFromError", "getURLFromRedirectError", "RedirectType", "isRedirectError", "HandleRedirect", "redirect", "reset", "redirectType", "router", "startTransition", "push", "replace", "RedirectErrorBoundary", "Component", "getDerivedStateFromError", "error", "url", "render", "state", "setState", "props", "children", "constructor", "RedirectBoundary"], "mappings": ";;;;;AACA,OAAOA,SAASC,SAAS,QAAQ,QAAO;AAExC,SAASC,SAAS,QAAQ,eAAc;;AACxC,SAASC,wBAAwB,EAAEC,uBAAuB,QAAQ,aAAY;AAC9E,SAASC,YAAY,EAAEC,eAAe,QAAQ,mBAAkB;AALhE;;;;;;AAYA,SAASC,eAAe,KAQvB;IARuB,IAAA,EACtBC,QAAQ,EACRC,KAAK,EACLC,YAAY,EAKb,GARuB;IAStB,MAAMC,8MAAST,aAAAA;2KAEfD,YAAAA,EAAU;2KACRD,UAAAA,CAAMY,eAAe,CAAC;YACpB,IAAIF,0MAAiBL,eAAAA,CAAaQ,IAAI,EAAE;gBACtCF,OAAOE,IAAI,CAACL,UAAU,CAAC;YACzB,OAAO;gBACLG,OAAOG,OAAO,CAACN,UAAU,CAAC;YAC5B;YACAC;QACF;IACF,GAAG;QAACD;QAAUE;QAAcD;QAAOE;KAAO;IAE1C,OAAO;AACT;AAEO,MAAMI,iMAA8Bf,UAAAA,CAAMgB,SAAS;IASxD,OAAOC,yBAAyBC,KAAU,EAAE;QAC1C,iMAAIZ,kBAAAA,EAAgBY,QAAQ;YAC1B,MAAMC,MAAMf,8MAAAA,EAAwBc;YACpC,MAAMR,mMAAeP,2BAAAA,EAAyBe;YAC9C,OAAO;gBAAEV,UAAUW;gBAAKT;YAAa;QACvC;QACA,wCAAwC;QACxC,MAAMQ;IACR;IAEA,yIAAyI;IACzIE,SAA0B;QACxB,MAAM,EAAEZ,QAAQ,EAAEE,YAAY,EAAE,GAAG,IAAI,CAACW,KAAK;QAC7C,IAAIb,aAAa,QAAQE,iBAAiB,MAAM;YAC9C,OAAA,WAAA,mLACE,MAAA,EAACH,gBAAAA;gBACCC,UAAUA;gBACVE,cAAcA;gBACdD,OAAO,IAAM,IAAI,CAACa,QAAQ,CAAC;wBAAEd,UAAU;oBAAK;;QAGlD;QAEA,OAAO,IAAI,CAACe,KAAK,CAACC,QAAQ;IAC5B;IA7BAC,YAAYF,KAA4B,CAAE;QACxC,KAAK,CAACA;QACN,IAAI,CAACF,KAAK,GAAG;YAAEb,UAAU;YAAME,cAAc;QAAK;IACpD;AA2BF;AAEO,SAASgB,iBAAiB,KAA2C;IAA3C,IAAA,EAAEF,QAAQ,EAAiC,GAA3C;IAC/B,MAAMb,+MAAST,YAAAA;IACf,OAAA,WAAA,mLACE,MAAA,EAACa,uBAAAA;QAAsBJ,QAAQA;kBAASa;;AAE5C", "ignoreList": [0]}}, {"offset": {"line": 2144, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/unresolved-thenable.ts"], "sourcesContent": ["/**\n * Create a \"Thenable\" that does not resolve. This is used to suspend indefinitely when data is not available yet.\n */\nexport const unresolvedThenable = {\n  then: () => {},\n} as PromiseLike<void>\n"], "names": ["unresolvedThenable", "then"], "mappings": "AAAA;;CAEC,GACD;;;AAAO,MAAMA,qBAAqB;IAChCC,MAAM,KAAO;AACf,EAAsB", "ignoreList": [0]}}, {"offset": {"line": 2158, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/promise-queue.ts"], "sourcesContent": ["/*\n    This is a simple promise queue that allows you to limit the number of concurrent promises\n    that are running at any given time. It's used to limit the number of concurrent\n    prefetch requests that are being made to the server but could be used for other\n    things as well.\n*/\nexport class PromiseQueue {\n  #maxConcurrency: number\n  #runningCount: number\n  #queue: Array<{\n    promiseFn: Promise<any>\n    task: () => void\n  }>\n\n  constructor(maxConcurrency = 5) {\n    this.#maxConcurrency = maxConcurrency\n    this.#runningCount = 0\n    this.#queue = []\n  }\n\n  enqueue<T>(promiseFn: () => Promise<T>): Promise<T> {\n    let taskResolve: (value: T | PromiseLike<T>) => void\n    let taskReject: (reason?: any) => void\n\n    const taskPromise = new Promise((resolve, reject) => {\n      taskResolve = resolve\n      taskReject = reject\n    }) as Promise<T>\n\n    const task = async () => {\n      try {\n        this.#runningCount++\n        const result = await promiseFn()\n        taskResolve(result)\n      } catch (error) {\n        taskReject(error)\n      } finally {\n        this.#runningCount--\n        this.#processNext()\n      }\n    }\n\n    const enqueueResult = { promiseFn: taskPromise, task }\n    // wonder if we should take a LIFO approach here\n    this.#queue.push(enqueueResult)\n    this.#processNext()\n\n    return taskPromise\n  }\n\n  bump(promiseFn: Promise<any>) {\n    const index = this.#queue.findIndex((item) => item.promiseFn === promiseFn)\n\n    if (index > -1) {\n      const bumpedItem = this.#queue.splice(index, 1)[0]\n      this.#queue.unshift(bumpedItem)\n      this.#processNext(true)\n    }\n  }\n\n  #processNext(forced = false) {\n    if (\n      (this.#runningCount < this.#maxConcurrency || forced) &&\n      this.#queue.length > 0\n    ) {\n      this.#queue.shift()?.task()\n    }\n  }\n}\n"], "names": ["PromiseQueue", "enqueue", "promiseFn", "taskResolve", "taskReject", "taskPromise", "Promise", "resolve", "reject", "task", "result", "error", "enqueueResult", "push", "bump", "index", "findIndex", "item", "bumpedItem", "splice", "unshift", "constructor", "maxConcurrency", "forced", "length", "shift"], "mappings": "AAAA;;;;;AAKA;;;AAAA;;;;IAEE,kBAAA,WAAA,GAAA,CAAA,GAAA,iLAAA,CAAA,IAAA,EAAA,oBACA,gBAAA,WAAA,GAAA,CAAA,GAAA,iLAAA,CAAA,IAAA,EAAA,kBACA,SAAA,WAAA,GAAA,CAAA,GAAA,iLAAA,CAAA,IAAA,EAAA,WAmDA,eAAA,WAAA,GAAA,CAAA,GAAA,iLAAA,CAAA,IAAA,EAAA;AAtDK,MAAMA;IAcXC,QAAWC,SAA2B,EAAc;QAClD,IAAIC;QACJ,IAAIC;QAEJ,MAAMC,cAAc,IAAIC,QAAQ,CAACC,SAASC;YACxCL,cAAcI;YACdH,aAAaI;QACf;QAEA,MAAMC,OAAO;YACX,IAAI;gBACF,2LAAA,EAAA,IAAI,EAAC,cAAA,CAAA,cAAA;gBACL,MAAMC,SAAS,MAAMR;gBACrBC,YAAYO;YACd,EAAE,OAAOC,OAAO;gBACdP,WAAWO;YACb,SAAU;gBACR,2LAAA,EAAA,IAAI,EAAC,cAAA,CAAA,cAAA;uMACL,IAAA,EAAA,IAAI,EAAC,aAAA,CAAA,aAAA;YACP;QACF;QAEA,MAAMC,gBAAgB;YAAEV,WAAWG;YAAaI;QAAK;QACrD,gDAAgD;+LAChD,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,CAAOI,IAAI,CAACD;+LACjB,IAAA,EAAA,IAAI,EAAC,aAAA,CAAA,aAAA;QAEL,OAAOP;IACT;IAEAS,KAAKZ,SAAuB,EAAE;QAC5B,MAAMa,+LAAQ,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,CAAOC,SAAS,CAAC,CAACC,OAASA,KAAKf,SAAS,KAAKA;QAEjE,IAAIa,QAAQ,CAAC,GAAG;YACd,MAAMG,oMAAa,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,CAAOC,MAAM,CAACJ,OAAO,EAAE,CAAC,EAAE;mMAClD,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,CAAOK,OAAO,CAACF;aACpB,0LAAA,EAAA,IAAI,EAAC,aAAA,CAAA,aAAA,CAAa;QACpB;IACF;IA5CAG,YAAYC,iBAAiB,CAAC,CAAE;QA8ChC,OAAA,cAAA,CAAA,IAAA,EAAA,cAAA;mBAAA;;QArDA,OAAA,cAAA,CAAA,IAAA,EAAA,iBAAA;;mBAAA,KAAA;;QACA,OAAA,cAAA,CAAA,IAAA,EAAA,eAAA;;mBAAA,KAAA;;QACA,OAAA,cAAA,CAAA,IAAA,EAAA,QAAA;;mBAAA,KAAA;;+LAME,IAAA,EAAA,IAAI,EAAC,gBAAA,CAAA,gBAAA,GAAkBA;SACvB,0LAAA,EAAA,IAAI,EAAC,cAAA,CAAA,cAAA,GAAgB;+LACrB,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,GAAS,EAAE;IAClB;AAkDF;AARE,SAAA,YAAaC,MAAc;IAAdA,IAAAA,WAAAA,KAAAA,GAAAA,SAAS;IACpB,IACG,wLAAA,IAAA,EAAA,IAAI,EAAC,cAAA,CAAA,cAAA,GAAgB,2LAAA,EAAA,IAAI,EAAC,gBAAA,CAAA,gBAAA,IAAmBA,MAAK,4LACnD,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,CAAOC,MAAM,GAAG,GACrB;YACA;SAAA,sOAAA,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,CAAOC,KAAK,EAAA,KAAA,OAAA,KAAA,IAAjB,6CAAqBhB,IAAI;IAC3B;AACF", "ignoreList": [0]}}, {"offset": {"line": 2242, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/segment-cache.ts"], "sourcesContent": ["/**\n * Entry point to the Segment Cache implementation.\n *\n * All code related to the Segment Cache lives `segment-cache-impl` directory.\n * Callers access it through this indirection.\n *\n * This is to ensure the code is dead code eliminated from the bundle if the\n * flag is disabled.\n *\n * TODO: This is super tedious. Since experimental flags are an essential part\n * of our workflow, we should establish a better pattern for dead code\n * elimination. Ideally it would be done at the bundler level, like how React's\n * build process works. In the React repo, you don't even need to add any extra\n * configuration per experiment — if the code is not reachable, it gets stripped\n * from the build automatically by Rollup. Or, shorter term, we could stub out\n * experimental modules at build time by updating the build config, i.e. a more\n * automated version of what I'm doing manually in this file.\n */\n\nexport type { NavigationResult } from './segment-cache-impl/navigation'\nexport type { PrefetchTask } from './segment-cache-impl/scheduler'\n\nconst notEnabled: any = () => {\n  throw new Error(\n    'Segment Cache experiment is not enabled. This is a bug in Next.js.'\n  )\n}\n\nexport const prefetch: typeof import('./segment-cache-impl/prefetch').prefetch =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/prefetch').prefetch(...args)\n      }\n    : notEnabled\n\nexport const navigate: typeof import('./segment-cache-impl/navigation').navigate =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/navigation').navigate(...args)\n      }\n    : notEnabled\n\nexport const revalidateEntireCache: typeof import('./segment-cache-impl/cache').revalidateEntireCache =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/cache').revalidateEntireCache(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const getCurrentCacheVersion: typeof import('./segment-cache-impl/cache').getCurrentCacheVersion =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/cache').getCurrentCacheVersion(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const schedulePrefetchTask: typeof import('./segment-cache-impl/scheduler').schedulePrefetchTask =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/scheduler').schedulePrefetchTask(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const cancelPrefetchTask: typeof import('./segment-cache-impl/scheduler').cancelPrefetchTask =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/scheduler').cancelPrefetchTask(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const reschedulePrefetchTask: typeof import('./segment-cache-impl/scheduler').reschedulePrefetchTask =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/scheduler').reschedulePrefetchTask(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const createCacheKey: typeof import('./segment-cache-impl/cache-key').createCacheKey =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/cache-key').createCacheKey(...args)\n      }\n    : notEnabled\n\n/**\n * Below are public constants. They're small enough that we don't need to\n * DCE them.\n */\n\nexport const enum NavigationResultTag {\n  MPA,\n  Success,\n  NoOp,\n  Async,\n}\n\n/**\n * The priority of the prefetch task. Higher numbers are higher priority.\n */\nexport const enum PrefetchPriority {\n  /**\n   * Assigned to any visible link that was hovered/touched at some point. This\n   * is not removed on mouse exit, because a link that was momentarily\n   * hovered is more likely to to be interacted with than one that was not.\n   */\n  Intent = 2,\n  /**\n   * The default priority for prefetch tasks.\n   */\n  Default = 1,\n  /**\n   * Assigned to tasks when they spawn non-blocking background work, like\n   * revalidating a partially cached entry to see if more data is available.\n   */\n  Background = 0,\n}\n"], "names": ["notEnabled", "Error", "prefetch", "process", "env", "__NEXT_CLIENT_SEGMENT_CACHE", "args", "require", "navigate", "revalidateEntireCache", "getCurrentCacheVersion", "schedulePrefetchTask", "cancelPrefetchTask", "reschedulePrefetchTask", "createCacheKey", "NavigationResultTag", "PrefetchPriority"], "mappings": "AAAA;;;;;;;;;;;;;;;;;CAiBC;;;;;;;;;;;;AAKD,MAAMA,aAAkB;IACtB,MAAM,OAAA,cAEL,CAFK,IAAIC,MACR,uEADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEO,MAAMC,WACXC,QAAQC,GAAG,CAACC,2BAA2B,GACnC,mCAGAL,WAAU;AAET,MAAMQ,WACXL,QAAQC,GAAG,CAACC,2BAA2B,GACnC,mCAGAL,WAAU;AAET,MAAMS,wBACXN,QAAQC,GAAG,CAACC,2BAA2B,GACnC,mCAKAL,WAAU;AAET,MAAMU,yBACXP,QAAQC,GAAG,CAACC,2BAA2B,GACnC,mCAKAL,WAAU;AAET,MAAMW,uBACXR,QAAQC,GAAG,CAACC,2BAA2B,GACnC,mCAKAL,WAAU;AAET,MAAMY,qBACXT,QAAQC,GAAG,CAACC,2BAA2B,GACnC,mCAKAL,WAAU;AAET,MAAMa,yBACXV,QAAQC,GAAG,CAACC,2BAA2B,GACnC,mCAKAL,WAAU;AAET,MAAMc,iBACXX,QAAQC,GAAG,CAACC,2BAA2B,GACnC,mCAGAL,WAAU;AAOT,IAAWe,sBAAAA,WAAAA,GAAAA,SAAAA,mBAAAA;;;;;WAAAA;MAKjB;AAKM,IAAWC,mBAAAA,WAAAA,GAAAA,SAAAA,gBAAAA;IAChB;;;;GAIC,GAAA,gBAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,EAAA,GAAA;IAED;;GAEC,GAAA,gBAAA,CAAA,gBAAA,CAAA,UAAA,GAAA,EAAA,GAAA;IAED;;;GAGC,GAAA,gBAAA,CAAA,gBAAA,CAAA,aAAA,GAAA,EAAA,GAAA;WAdeA;MAgBjB", "ignoreList": [0]}}, {"offset": {"line": 2314, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/links.ts"], "sourcesContent": ["import type { FlightRouterState } from '../../server/app-render/types'\nimport type { AppRouterInstance } from '../../shared/lib/app-router-context.shared-runtime'\nimport { getCurrentAppRouterState } from './app-router-instance'\nimport { createPrefetchURL } from './app-router'\nimport { PrefetchKind } from './router-reducer/router-reducer-types'\nimport { getCurrentCacheVersion } from './segment-cache'\nimport { createCacheKey } from './segment-cache'\nimport {\n  type PrefetchTask,\n  PrefetchPriority,\n  schedulePrefetchTask as scheduleSegmentPrefetchTask,\n  cancelPrefetchTask,\n  reschedulePrefetchTask,\n} from './segment-cache'\nimport { startTransition } from 'react'\n\ntype LinkElement = HTMLAnchorElement | SVGAElement\n\ntype Element = LinkElement | HTMLFormElement\n\n// Properties that are shared between Link and Form instances. We use the same\n// shape for both to prevent a polymorphic de-opt in the VM.\ntype LinkOrFormInstanceShared = {\n  router: AppRouterInstance\n  kind: PrefetchKind.AUTO | PrefetchKind.FULL\n\n  isVisible: boolean\n  wasHoveredOrTouched: boolean\n\n  // The most recently initiated prefetch task. It may or may not have\n  // already completed.  The same prefetch task object can be reused across\n  // multiple prefetches of the same link.\n  prefetchTask: PrefetchTask | null\n\n  // The cache version at the time the task was initiated. This is used to\n  // determine if the cache was invalidated since the task was initiated.\n  cacheVersion: number\n}\n\nexport type FormInstance = LinkOrFormInstanceShared & {\n  prefetchHref: string\n  setOptimisticLinkStatus: null\n}\n\ntype PrefetchableLinkInstance = LinkOrFormInstanceShared & {\n  prefetchHref: string\n  setOptimisticLinkStatus: (status: { pending: boolean }) => void\n}\n\ntype NonPrefetchableLinkInstance = LinkOrFormInstanceShared & {\n  prefetchHref: null\n  setOptimisticLinkStatus: (status: { pending: boolean }) => void\n}\n\ntype PrefetchableInstance = PrefetchableLinkInstance | FormInstance\n\nexport type LinkInstance =\n  | PrefetchableLinkInstance\n  | NonPrefetchableLinkInstance\n\n// Tracks the most recently navigated link instance. When null, indicates\n// the current navigation was not initiated by a link click.\nlet linkForMostRecentNavigation: LinkInstance | null = null\n\n// Status object indicating link is pending\nexport const PENDING_LINK_STATUS = { pending: true }\n\n// Status object indicating link is idle\nexport const IDLE_LINK_STATUS = { pending: false }\n\n// Updates the loading state when navigating between links\n// - Resets the previous link's loading state\n// - Sets the new link's loading state\n// - Updates tracking of current navigation\nexport function setLinkForCurrentNavigation(link: LinkInstance | null) {\n  startTransition(() => {\n    linkForMostRecentNavigation?.setOptimisticLinkStatus(IDLE_LINK_STATUS)\n    link?.setOptimisticLinkStatus(PENDING_LINK_STATUS)\n    linkForMostRecentNavigation = link\n  })\n}\n\n// Unmounts the current link instance from navigation tracking\nexport function unmountLinkForCurrentNavigation(link: LinkInstance) {\n  if (linkForMostRecentNavigation === link) {\n    linkForMostRecentNavigation = null\n  }\n}\n\n// Use a WeakMap to associate a Link instance with its DOM element. This is\n// used by the IntersectionObserver to track the link's visibility.\nconst prefetchable:\n  | WeakMap<Element, PrefetchableInstance>\n  | Map<Element, PrefetchableInstance> =\n  typeof WeakMap === 'function' ? new WeakMap() : new Map()\n\n// A Set of the currently visible links. We re-prefetch visible links after a\n// cache invalidation, or when the current URL changes. It's a separate data\n// structure from the WeakMap above because only the visible links need to\n// be enumerated.\nconst prefetchableAndVisible: Set<PrefetchableInstance> = new Set()\n\n// A single IntersectionObserver instance shared by all <Link> components.\nconst observer: IntersectionObserver | null =\n  typeof IntersectionObserver === 'function'\n    ? new IntersectionObserver(handleIntersect, {\n        rootMargin: '200px',\n      })\n    : null\n\nfunction observeVisibility(element: Element, instance: PrefetchableInstance) {\n  const existingInstance = prefetchable.get(element)\n  if (existingInstance !== undefined) {\n    // This shouldn't happen because each <Link> component should have its own\n    // anchor tag instance, but it's defensive coding to avoid a memory leak in\n    // case there's a logical error somewhere else.\n    unmountPrefetchableInstance(element)\n  }\n  // Only track prefetchable links that have a valid prefetch URL\n  prefetchable.set(element, instance)\n  if (observer !== null) {\n    observer.observe(element)\n  }\n}\n\nfunction coercePrefetchableUrl(href: string): URL | null {\n  try {\n    return createPrefetchURL(href)\n  } catch {\n    // createPrefetchURL sometimes throws an error if an invalid URL is\n    // provided, though I'm not sure if it's actually necessary.\n    // TODO: Consider removing the throw from the inner function, or change it\n    // to reportError. Or maybe the error isn't even necessary for automatic\n    // prefetches, just navigations.\n    const reportErrorFn =\n      typeof reportError === 'function' ? reportError : console.error\n    reportErrorFn(\n      `Cannot prefetch '${href}' because it cannot be converted to a URL.`\n    )\n    return null\n  }\n}\n\nexport function mountLinkInstance(\n  element: LinkElement,\n  href: string,\n  router: AppRouterInstance,\n  kind: PrefetchKind.AUTO | PrefetchKind.FULL,\n  prefetchEnabled: boolean,\n  setOptimisticLinkStatus: (status: { pending: boolean }) => void\n): LinkInstance {\n  if (prefetchEnabled) {\n    const prefetchURL = coercePrefetchableUrl(href)\n    if (prefetchURL !== null) {\n      const instance: PrefetchableLinkInstance = {\n        router,\n        kind,\n        isVisible: false,\n        wasHoveredOrTouched: false,\n        prefetchTask: null,\n        cacheVersion: -1,\n        prefetchHref: prefetchURL.href,\n        setOptimisticLinkStatus,\n      }\n      // We only observe the link's visibility if it's prefetchable. For\n      // example, this excludes links to external URLs.\n      observeVisibility(element, instance)\n      return instance\n    }\n  }\n  // If the link is not prefetchable, we still create an instance so we can\n  // track its optimistic state (i.e. useLinkStatus).\n  const instance: NonPrefetchableLinkInstance = {\n    router,\n    kind,\n    isVisible: false,\n    wasHoveredOrTouched: false,\n    prefetchTask: null,\n    cacheVersion: -1,\n    prefetchHref: null,\n    setOptimisticLinkStatus,\n  }\n  return instance\n}\n\nexport function mountFormInstance(\n  element: HTMLFormElement,\n  href: string,\n  router: AppRouterInstance,\n  kind: PrefetchKind.AUTO | PrefetchKind.FULL\n): void {\n  const prefetchURL = coercePrefetchableUrl(href)\n  if (prefetchURL === null) {\n    // This href is not prefetchable, so we don't track it.\n    // TODO: We currently observe/unobserve a form every time its href changes.\n    // For Links, this isn't a big deal because the href doesn't usually change,\n    // but for forms it's extremely common. We should optimize this.\n    return\n  }\n  const instance: FormInstance = {\n    router,\n    kind,\n    isVisible: false,\n    wasHoveredOrTouched: false,\n    prefetchTask: null,\n    cacheVersion: -1,\n    prefetchHref: prefetchURL.href,\n    setOptimisticLinkStatus: null,\n  }\n  observeVisibility(element, instance)\n}\n\nexport function unmountPrefetchableInstance(element: Element) {\n  const instance = prefetchable.get(element)\n  if (instance !== undefined) {\n    prefetchable.delete(element)\n    prefetchableAndVisible.delete(instance)\n    const prefetchTask = instance.prefetchTask\n    if (prefetchTask !== null) {\n      cancelPrefetchTask(prefetchTask)\n    }\n  }\n  if (observer !== null) {\n    observer.unobserve(element)\n  }\n}\n\nfunction handleIntersect(entries: Array<IntersectionObserverEntry>) {\n  for (const entry of entries) {\n    // Some extremely old browsers or polyfills don't reliably support\n    // isIntersecting so we check intersectionRatio instead. (Do we care? Not\n    // really. But whatever this is fine.)\n    const isVisible = entry.intersectionRatio > 0\n    onLinkVisibilityChanged(entry.target as HTMLAnchorElement, isVisible)\n  }\n}\n\nexport function onLinkVisibilityChanged(element: Element, isVisible: boolean) {\n  if (process.env.NODE_ENV !== 'production') {\n    // Prefetching on viewport is disabled in development for performance\n    // reasons, because it requires compiling the target page.\n    // TODO: Investigate re-enabling this.\n    return\n  }\n\n  const instance = prefetchable.get(element)\n  if (instance === undefined) {\n    return\n  }\n\n  instance.isVisible = isVisible\n  if (isVisible) {\n    prefetchableAndVisible.add(instance)\n  } else {\n    prefetchableAndVisible.delete(instance)\n  }\n  rescheduleLinkPrefetch(instance)\n}\n\nexport function onNavigationIntent(\n  element: HTMLAnchorElement | SVGAElement,\n  unstable_upgradeToDynamicPrefetch: boolean\n) {\n  const instance = prefetchable.get(element)\n  if (instance === undefined) {\n    return\n  }\n  // Prefetch the link on hover/touchstart.\n  if (instance !== undefined) {\n    instance.wasHoveredOrTouched = true\n    if (\n      process.env.__NEXT_DYNAMIC_ON_HOVER &&\n      unstable_upgradeToDynamicPrefetch\n    ) {\n      // Switch to a full, dynamic prefetch\n      instance.kind = PrefetchKind.FULL\n    }\n    rescheduleLinkPrefetch(instance)\n  }\n}\n\nfunction rescheduleLinkPrefetch(instance: PrefetchableInstance) {\n  const existingPrefetchTask = instance.prefetchTask\n\n  if (!instance.isVisible) {\n    // Cancel any in-progress prefetch task. (If it already finished then this\n    // is a no-op.)\n    if (existingPrefetchTask !== null) {\n      cancelPrefetchTask(existingPrefetchTask)\n    }\n    // We don't need to reset the prefetchTask to null upon cancellation; an\n    // old task object can be rescheduled with reschedulePrefetchTask. This is a\n    // micro-optimization but also makes the code simpler (don't need to\n    // worry about whether an old task object is stale).\n    return\n  }\n\n  if (!process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n    // The old prefetch implementation does not have different priority levels.\n    // Just schedule a new prefetch task.\n    prefetchWithOldCacheImplementation(instance)\n    return\n  }\n\n  // In the Segment Cache implementation, we assign a higher priority level to\n  // links that were at one point hovered or touched. Since the queue is last-\n  // in-first-out, the highest priority Link is whichever one was hovered last.\n  //\n  // We also increase the relative priority of links whenever they re-enter the\n  // viewport, as if they were being scheduled for the first time.\n  const priority = instance.wasHoveredOrTouched\n    ? PrefetchPriority.Intent\n    : PrefetchPriority.Default\n  const appRouterState = getCurrentAppRouterState()\n  if (appRouterState !== null) {\n    const treeAtTimeOfPrefetch = appRouterState.tree\n    if (existingPrefetchTask === null) {\n      // Initiate a prefetch task.\n      const nextUrl = appRouterState.nextUrl\n      const cacheKey = createCacheKey(instance.prefetchHref, nextUrl)\n      instance.prefetchTask = scheduleSegmentPrefetchTask(\n        cacheKey,\n        treeAtTimeOfPrefetch,\n        instance.kind === PrefetchKind.FULL,\n        priority\n      )\n    } else {\n      // We already have an old task object that we can reschedule. This is\n      // effectively the same as canceling the old task and creating a new one.\n      reschedulePrefetchTask(\n        existingPrefetchTask,\n        treeAtTimeOfPrefetch,\n        instance.kind === PrefetchKind.FULL,\n        priority\n      )\n    }\n\n    // Keep track of the cache version at the time the prefetch was requested.\n    // This is used to check if the prefetch is stale.\n    instance.cacheVersion = getCurrentCacheVersion()\n  }\n}\n\nexport function pingVisibleLinks(\n  nextUrl: string | null,\n  tree: FlightRouterState\n) {\n  // For each currently visible link, cancel the existing prefetch task (if it\n  // exists) and schedule a new one. This is effectively the same as if all the\n  // visible links left and then re-entered the viewport.\n  //\n  // This is called when the Next-Url or the base tree changes, since those\n  // may affect the result of a prefetch task. It's also called after a\n  // cache invalidation.\n  const currentCacheVersion = getCurrentCacheVersion()\n  for (const instance of prefetchableAndVisible) {\n    const task = instance.prefetchTask\n    if (\n      task !== null &&\n      instance.cacheVersion === currentCacheVersion &&\n      task.key.nextUrl === nextUrl &&\n      task.treeAtTimeOfPrefetch === tree\n    ) {\n      // The cache has not been invalidated, and none of the inputs have\n      // changed. Bail out.\n      continue\n    }\n    // Something changed. Cancel the existing prefetch task and schedule a\n    // new one.\n    if (task !== null) {\n      cancelPrefetchTask(task)\n    }\n    const cacheKey = createCacheKey(instance.prefetchHref, nextUrl)\n    const priority = instance.wasHoveredOrTouched\n      ? PrefetchPriority.Intent\n      : PrefetchPriority.Default\n    instance.prefetchTask = scheduleSegmentPrefetchTask(\n      cacheKey,\n      tree,\n      instance.kind === PrefetchKind.FULL,\n      priority\n    )\n    instance.cacheVersion = getCurrentCacheVersion()\n  }\n}\n\nfunction prefetchWithOldCacheImplementation(instance: PrefetchableInstance) {\n  // This is the path used when the Segment Cache is not enabled.\n  if (typeof window === 'undefined') {\n    return\n  }\n\n  const doPrefetch = async () => {\n    // note that `appRouter.prefetch()` is currently sync,\n    // so we have to wrap this call in an async function to be able to catch() errors below.\n    return instance.router.prefetch(instance.prefetchHref, {\n      kind: instance.kind,\n    })\n  }\n\n  // Prefetch the page if asked (only in the client)\n  // We need to handle a prefetch error here since we may be\n  // loading with priority which can reject but we don't\n  // want to force navigation since this is only a prefetch\n  doPrefetch().catch((err) => {\n    if (process.env.NODE_ENV !== 'production') {\n      // rethrow to show invalid URL errors\n      throw err\n    }\n  })\n}\n"], "names": ["getCurrentAppRouterState", "createPrefetchURL", "PrefetchKind", "getCurrentCacheVersion", "createCacheKey", "PrefetchPriority", "schedulePrefetchTask", "scheduleSegmentPrefetchTask", "cancelPrefetchTask", "reschedulePrefetchTask", "startTransition", "linkForMostRecentNavigation", "PENDING_LINK_STATUS", "pending", "IDLE_LINK_STATUS", "setLinkForCurrentNavigation", "link", "setOptimisticLinkStatus", "unmountLinkForCurrentNavigation", "prefetchable", "WeakMap", "Map", "prefetchableAndVisible", "Set", "observer", "IntersectionObserver", "handleIntersect", "rootMargin", "observeVisibility", "element", "instance", "existingInstance", "get", "undefined", "unmountPrefetchableInstance", "set", "observe", "coercePrefetchableUrl", "href", "reportErrorFn", "reportError", "console", "error", "mountLinkInstance", "router", "kind", "prefetchEnabled", "prefetchURL", "isVisible", "wasHoveredOrTouched", "prefetchTask", "cacheVersion", "prefetchHref", "mountFormInstance", "delete", "unobserve", "entries", "entry", "intersectionRatio", "onLinkVisibilityChanged", "target", "process", "env", "NODE_ENV", "add", "rescheduleLinkPrefetch", "onNavigationIntent", "unstable_upgradeToDynamicPrefetch", "__NEXT_DYNAMIC_ON_HOVER", "FULL", "existingPrefetchTask", "__NEXT_CLIENT_SEGMENT_CACHE", "prefetchWithOldCacheImplementation", "priority", "Intent", "<PERSON><PERSON><PERSON>", "appRouterState", "treeAtTimeOfPrefetch", "tree", "nextUrl", "cache<PERSON>ey", "pingVisibleLinks", "currentCacheVersion", "task", "key", "window", "doPrefetch", "prefetch", "catch", "err"], "mappings": ";;;;;;;;;;;;AAEA,SAASA,wBAAwB,QAAQ,wBAAuB;AAChE,SAASC,iBAAiB,QAAQ,eAAc;AAChD,SAASC,YAAY,QAAQ,wCAAuC;AACpE,SAASC,sBAAsB,QAAQ,kBAAiB;AASxD,SAASO,eAAe,QAAQ,QAAO;;;;;;;;AA8CvC,yEAAyE;AACzE,4DAA4D;AAC5D,IAAIC,8BAAmD;AAGhD,MAAMC,sBAAsB;IAAEC,SAAS;AAAK,EAAC;AAG7C,MAAMC,mBAAmB;IAAED,SAAS;AAAM,EAAC;AAM3C,SAASE,4BAA4BC,IAAyB;2KACnEN,kBAAAA,EAAgB;QACdC,+BAAAA,OAAAA,KAAAA,IAAAA,4BAA6BM,uBAAuB,CAACH;QACrDE,QAAAA,OAAAA,KAAAA,IAAAA,KAAMC,uBAAuB,CAACL;QAC9BD,8BAA8BK;IAChC;AACF;AAGO,SAASE,gCAAgCF,IAAkB;IAChE,IAAIL,gCAAgCK,MAAM;QACxCL,8BAA8B;IAChC;AACF;AAEA,2EAA2E;AAC3E,mEAAmE;AACnE,MAAMQ,eAGJ,OAAOC,YAAY,aAAa,IAAIA,YAAY,IAAIC;AAEtD,6EAA6E;AAC7E,4EAA4E;AAC5E,0EAA0E;AAC1E,iBAAiB;AACjB,MAAMC,yBAAoD,IAAIC;AAE9D,0EAA0E;AAC1E,MAAMC,WACJ,OAAOC,yBAAyB,aAC5B,IAAIA,qBAAqBC,iBAAiB;IACxCC,YAAY;AACd,KACA;AAEN,SAASC,kBAAkBC,OAAgB,EAAEC,QAA8B;IACzE,MAAMC,mBAAmBZ,aAAaa,GAAG,CAACH;IAC1C,IAAIE,qBAAqBE,WAAW;QAClC,0EAA0E;QAC1E,2EAA2E;QAC3E,+CAA+C;QAC/CC,4BAA4BL;IAC9B;IACA,+DAA+D;IAC/DV,aAAagB,GAAG,CAACN,SAASC;IAC1B,IAAIN,aAAa,MAAM;QACrBA,SAASY,OAAO,CAACP;IACnB;AACF;AAEA,SAASQ,sBAAsBC,IAAY;IACzC,IAAI;QACF,OAAOrC,6MAAAA,EAAkBqC;IAC3B,EAAE,OAAA,GAAM;QACN,mEAAmE;QACnE,4DAA4D;QAC5D,0EAA0E;QAC1E,wEAAwE;QACxE,gCAAgC;QAChC,MAAMC,gBACJ,OAAOC,gBAAgB,aAAaA,cAAcC,QAAQC,KAAK;QACjEH,cACG,sBAAmBD,OAAK;QAE3B,OAAO;IACT;AACF;AAEO,SAASK,kBACdd,OAAoB,EACpBS,IAAY,EACZM,MAAyB,EACzBC,IAA2C,EAC3CC,eAAwB,EACxB7B,uBAA+D;IAE/D,IAAI6B,iBAAiB;QACnB,MAAMC,cAAcV,sBAAsBC;QAC1C,IAAIS,gBAAgB,MAAM;YACxB,MAAMjB,WAAqC;gBACzCc;gBACAC;gBACAG,WAAW;gBACXC,qBAAqB;gBACrBC,cAAc;gBACdC,cAAc,CAAC;gBACfC,cAAcL,YAAYT,IAAI;gBAC9BrB;YACF;YACA,kEAAkE;YAClE,iDAAiD;YACjDW,kBAAkBC,SAASC;YAC3B,OAAOA;QACT;IACF;IACA,yEAAyE;IACzE,mDAAmD;IACnD,MAAMA,WAAwC;QAC5Cc;QACAC;QACAG,WAAW;QACXC,qBAAqB;QACrBC,cAAc;QACdC,cAAc,CAAC;QACfC,cAAc;QACdnC;IACF;IACA,OAAOa;AACT;AAEO,SAASuB,kBACdxB,OAAwB,EACxBS,IAAY,EACZM,MAAyB,EACzBC,IAA2C;IAE3C,MAAME,cAAcV,sBAAsBC;IAC1C,IAAIS,gBAAgB,MAAM;QACxB,uDAAuD;QACvD,2EAA2E;QAC3E,4EAA4E;QAC5E,gEAAgE;QAChE;IACF;IACA,MAAMjB,WAAyB;QAC7Bc;QACAC;QACAG,WAAW;QACXC,qBAAqB;QACrBC,cAAc;QACdC,cAAc,CAAC;QACfC,cAAcL,YAAYT,IAAI;QAC9BrB,yBAAyB;IAC3B;IACAW,kBAAkBC,SAASC;AAC7B;AAEO,SAASI,4BAA4BL,OAAgB;IAC1D,MAAMC,WAAWX,aAAaa,GAAG,CAACH;IAClC,IAAIC,aAAaG,WAAW;QAC1Bd,aAAamC,MAAM,CAACzB;QACpBP,uBAAuBgC,MAAM,CAACxB;QAC9B,MAAMoB,eAAepB,SAASoB,YAAY;QAC1C,IAAIA,iBAAiB,MAAM;wMACzB1C,qBAAAA,EAAmB0C;QACrB;IACF;IACA,IAAI1B,aAAa,MAAM;QACrBA,SAAS+B,SAAS,CAAC1B;IACrB;AACF;AAEA,SAASH,gBAAgB8B,OAAyC;IAChE,KAAK,MAAMC,SAASD,QAAS;QAC3B,kEAAkE;QAClE,yEAAyE;QACzE,sCAAsC;QACtC,MAAMR,YAAYS,MAAMC,iBAAiB,GAAG;QAC5CC,wBAAwBF,MAAMG,MAAM,EAAuBZ;IAC7D;AACF;AAEO,SAASW,wBAAwB9B,OAAgB,EAAEmB,SAAkB;IAC1E,IAAIa,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,qEAAqE;QACrE,0DAA0D;QAC1D,sCAAsC;QACtC;IACF;;IAEA,MAAMjC,WAAWX,aAAaa,GAAG,CAACH;AAYpC;AAEO,SAASqC,mBACdrC,OAAwC,EACxCsC,iCAA0C;IAE1C,MAAMrC,WAAWX,aAAaa,GAAG,CAACH;IAClC,IAAIC,aAAaG,WAAW;QAC1B;IACF;IACA,yCAAyC;IACzC,IAAIH,aAAaG,WAAW;QAC1BH,SAASmB,mBAAmB,GAAG;QAC/B,IACEY,QAAQC,GAAG,CAACM,uBAAuB,AAEnC,IADAD;;QAIF;QACAF,uBAAuBnC;IACzB;AACF;AAEA,SAASmC,uBAAuBnC,QAA8B;IAC5D,MAAMwC,uBAAuBxC,SAASoB,YAAY;IAElD,IAAI,CAACpB,SAASkB,SAAS,EAAE;QACvB,0EAA0E;QAC1E,eAAe;QACf,IAAIsB,yBAAyB,MAAM;gBACjC9D,6MAAAA,EAAmB8D;QACrB;QACA,wEAAwE;QACxE,4EAA4E;QAC5E,oEAAoE;QACpE,oDAAoD;QACpD;IACF;IAEA,IAAI,CAACT,QAAQC,GAAG,CAACS,uBAA6B,IAAF;QAC1C,2EAA2E;QAC3E,qCAAqC;QACrCC,mCAAmC1C;QACnC;IACF;;IAEA,4EAA4E;IAC5E,4EAA4E;IAC5E,6EAA6E;IAC7E,EAAE;IACF,6EAA6E;IAC7E,gEAAgE;IAChE,MAAM2C,WAAW3C,SAASmB,mBAAmB,GACzC5C,iBAAiBqE,MAAM,GACvBrE,iBAAiBsE,OAAO;IAC5B,MAAMC,iBAAiB5E;AA4BzB;AAEO,SAASiF,iBACdF,OAAsB,EACtBD,IAAuB;IAEvB,4EAA4E;IAC5E,6EAA6E;IAC7E,uDAAuD;IACvD,EAAE;IACF,yEAAyE;IACzE,qEAAqE;IACrE,sBAAsB;IACtB,MAAMI,uBAAsB/E,oNAAAA;IAC5B,KAAK,MAAM2B,YAAYR,uBAAwB;QAC7C,MAAM6D,OAAOrD,SAASoB,YAAY;QAClC,IACEiC,SAAS,QACTrD,SAASqB,YAAY,KAAK+B,uBAC1BC,KAAKC,GAAG,CAACL,OAAO,KAAKA,WACrBI,KAAKN,oBAAoB,KAAKC,MAC9B;YAGA;QACF;QACA,sEAAsE;QACtE,WAAW;QACX,IAAIK,SAAS,MAAM;wMACjB3E,qBAAAA,EAAmB2E;QACrB;QACA,MAAMH,uMAAW5E,iBAAAA,EAAe0B,SAASsB,YAAY,EAAE2B;QACvD,MAAMN,WAAW3C,SAASmB,mBAAmB,2LACzC5C,mBAAAA,CAAiBqE,MAAM,2LACvBrE,mBAAAA,CAAiBsE,OAAO;QAC5B7C,SAASoB,YAAY,+LAAG3C,uBAAAA,EACtByE,UACAF,MACAhD,SAASe,IAAI,2NAAK3C,gBAAAA,CAAamE,IAAI,EACnCI;QAEF3C,SAASqB,YAAY,+LAAGhD,yBAAAA;IAC1B;AACF;AAEA,SAASqE,mCAAmC1C,QAA8B;IACxE,+DAA+D;IAC/D,IAAI,OAAOuD,WAAW,aAAa;QACjC;IACF;IAEA,MAAMC,aAAa;QACjB,sDAAsD;QACtD,wFAAwF;QACxF,OAAOxD,SAASc,MAAM,CAAC2C,QAAQ,CAACzD,SAASsB,YAAY,EAAE;YACrDP,MAAMf,SAASe,IAAI;QACrB;IACF;IAEA,kDAAkD;IAClD,0DAA0D;IAC1D,sDAAsD;IACtD,yDAAyD;IACzDyC,aAAaE,KAAK,CAAC,CAACC;QAClB,IAAI5B,QAAQC,GAAG,CAACC,QAAQ,gCAAK,cAAc;YACzC,qCAAqC;YACrC,MAAM0B;QACR;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 2585, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/app-router-instance.ts"], "sourcesContent": ["import {\n  type AppRouterState,\n  type ReducerActions,\n  type ReducerState,\n  ACTION_REFRESH,\n  ACTION_SERVER_ACTION,\n  ACTION_NAVIGATE,\n  ACTION_RESTORE,\n  type NavigateAction,\n  ACTION_HMR_REFRESH,\n  PrefetchKind,\n  ACTION_PREFETCH,\n} from './router-reducer/router-reducer-types'\nimport { reducer } from './router-reducer/router-reducer'\nimport { startTransition } from 'react'\nimport { isThenable } from '../../shared/lib/is-thenable'\nimport { prefetch as prefetchWithSegmentCache } from './segment-cache'\nimport { dispatchAppRouterAction } from './use-action-queue'\nimport { addBasePath } from '../add-base-path'\nimport { createPrefetchURL, isExternalURL } from './app-router'\nimport { prefetchReducer } from './router-reducer/reducers/prefetch-reducer'\nimport type {\n  AppRouterInstance,\n  NavigateOptions,\n  PrefetchOptions,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport { setLinkForCurrentNavigation, type LinkInstance } from './links'\nimport type { FlightRouterState } from '../../server/app-render/types'\nimport type { ClientInstrumentationHooks } from '../app-index'\n\nexport type DispatchStatePromise = React.Dispatch<ReducerState>\n\nexport type AppRouterActionQueue = {\n  state: AppRouterState\n  dispatch: (payload: ReducerActions, setState: DispatchStatePromise) => void\n  action: (state: AppRouterState, action: ReducerActions) => ReducerState\n\n  onRouterTransitionStart:\n    | ((url: string, type: 'push' | 'replace' | 'traverse') => void)\n    | null\n\n  pending: ActionQueueNode | null\n  needsRefresh?: boolean\n  last: ActionQueueNode | null\n}\n\nexport type ActionQueueNode = {\n  payload: ReducerActions\n  next: ActionQueueNode | null\n  resolve: (value: ReducerState) => void\n  reject: (err: Error) => void\n  discarded?: boolean\n}\n\nfunction runRemainingActions(\n  actionQueue: AppRouterActionQueue,\n  setState: DispatchStatePromise\n) {\n  if (actionQueue.pending !== null) {\n    actionQueue.pending = actionQueue.pending.next\n    if (actionQueue.pending !== null) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      runAction({\n        actionQueue,\n        action: actionQueue.pending,\n        setState,\n      })\n    } else {\n      // No more actions are pending, check if a refresh is needed\n      if (actionQueue.needsRefresh) {\n        actionQueue.needsRefresh = false\n        actionQueue.dispatch(\n          {\n            type: ACTION_REFRESH,\n            origin: window.location.origin,\n          },\n          setState\n        )\n      }\n    }\n  }\n}\n\nasync function runAction({\n  actionQueue,\n  action,\n  setState,\n}: {\n  actionQueue: AppRouterActionQueue\n  action: ActionQueueNode\n  setState: DispatchStatePromise\n}) {\n  const prevState = actionQueue.state\n\n  actionQueue.pending = action\n\n  const payload = action.payload\n  const actionResult = actionQueue.action(prevState, payload)\n\n  function handleResult(nextState: AppRouterState) {\n    // if we discarded this action, the state should also be discarded\n    if (action.discarded) {\n      return\n    }\n\n    actionQueue.state = nextState\n\n    runRemainingActions(actionQueue, setState)\n    action.resolve(nextState)\n  }\n\n  // if the action is a promise, set up a callback to resolve it\n  if (isThenable(actionResult)) {\n    actionResult.then(handleResult, (err) => {\n      runRemainingActions(actionQueue, setState)\n      action.reject(err)\n    })\n  } else {\n    handleResult(actionResult)\n  }\n}\n\nfunction dispatchAction(\n  actionQueue: AppRouterActionQueue,\n  payload: ReducerActions,\n  setState: DispatchStatePromise\n) {\n  let resolvers: {\n    resolve: (value: ReducerState) => void\n    reject: (reason: any) => void\n  } = { resolve: setState, reject: () => {} }\n\n  // most of the action types are async with the exception of restore\n  // it's important that restore is handled quickly since it's fired on the popstate event\n  // and we don't want to add any delay on a back/forward nav\n  // this only creates a promise for the async actions\n  if (payload.type !== ACTION_RESTORE) {\n    // Create the promise and assign the resolvers to the object.\n    const deferredPromise = new Promise<AppRouterState>((resolve, reject) => {\n      resolvers = { resolve, reject }\n    })\n\n    startTransition(() => {\n      // we immediately notify React of the pending promise -- the resolver is attached to the action node\n      // and will be called when the associated action promise resolves\n      setState(deferredPromise)\n    })\n  }\n\n  const newAction: ActionQueueNode = {\n    payload,\n    next: null,\n    resolve: resolvers.resolve,\n    reject: resolvers.reject,\n  }\n\n  // Check if the queue is empty\n  if (actionQueue.pending === null) {\n    // The queue is empty, so add the action and start it immediately\n    // Mark this action as the last in the queue\n    actionQueue.last = newAction\n\n    runAction({\n      actionQueue,\n      action: newAction,\n      setState,\n    })\n  } else if (\n    payload.type === ACTION_NAVIGATE ||\n    payload.type === ACTION_RESTORE\n  ) {\n    // Navigations (including back/forward) take priority over any pending actions.\n    // Mark the pending action as discarded (so the state is never applied) and start the navigation action immediately.\n    actionQueue.pending.discarded = true\n\n    // The rest of the current queue should still execute after this navigation.\n    // (Note that it can't contain any earlier navigations, because we always put those into `actionQueue.pending` by calling `runAction`)\n    newAction.next = actionQueue.pending.next\n\n    // if the pending action was a server action, mark the queue as needing a refresh once events are processed\n    if (actionQueue.pending.payload.type === ACTION_SERVER_ACTION) {\n      actionQueue.needsRefresh = true\n    }\n\n    runAction({\n      actionQueue,\n      action: newAction,\n      setState,\n    })\n  } else {\n    // The queue is not empty, so add the action to the end of the queue\n    // It will be started by runRemainingActions after the previous action finishes\n    if (actionQueue.last !== null) {\n      actionQueue.last.next = newAction\n    }\n    actionQueue.last = newAction\n  }\n}\n\nlet globalActionQueue: AppRouterActionQueue | null = null\n\nexport function createMutableActionQueue(\n  initialState: AppRouterState,\n  instrumentationHooks: ClientInstrumentationHooks | null\n): AppRouterActionQueue {\n  const actionQueue: AppRouterActionQueue = {\n    state: initialState,\n    dispatch: (payload: ReducerActions, setState: DispatchStatePromise) =>\n      dispatchAction(actionQueue, payload, setState),\n    action: async (state: AppRouterState, action: ReducerActions) => {\n      const result = reducer(state, action)\n      return result\n    },\n    pending: null,\n    last: null,\n    onRouterTransitionStart:\n      instrumentationHooks !== null &&\n      typeof instrumentationHooks.onRouterTransitionStart === 'function'\n        ? // This profiling hook will be called at the start of every navigation.\n          instrumentationHooks.onRouterTransitionStart\n        : null,\n  }\n\n  if (typeof window !== 'undefined') {\n    // The action queue is lazily created on hydration, but after that point\n    // it doesn't change. So we can store it in a global rather than pass\n    // it around everywhere via props/context.\n    if (globalActionQueue !== null) {\n      throw new Error(\n        'Internal Next.js Error: createMutableActionQueue was called more ' +\n          'than once'\n      )\n    }\n    globalActionQueue = actionQueue\n  }\n\n  return actionQueue\n}\n\nexport function getCurrentAppRouterState(): AppRouterState | null {\n  return globalActionQueue !== null ? globalActionQueue.state : null\n}\n\nfunction getAppRouterActionQueue(): AppRouterActionQueue {\n  if (globalActionQueue === null) {\n    throw new Error(\n      'Internal Next.js error: Router action dispatched before initialization.'\n    )\n  }\n  return globalActionQueue\n}\n\nfunction getProfilingHookForOnNavigationStart() {\n  if (globalActionQueue !== null) {\n    return globalActionQueue.onRouterTransitionStart\n  }\n  return null\n}\n\nexport function dispatchNavigateAction(\n  href: string,\n  navigateType: NavigateAction['navigateType'],\n  shouldScroll: boolean,\n  linkInstanceRef: LinkInstance | null\n): void {\n  // TODO: This stuff could just go into the reducer. Leaving as-is for now\n  // since we're about to rewrite all the router reducer stuff anyway.\n  const url = new URL(addBasePath(href), location.href)\n  if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n    window.next.__pendingUrl = url\n  }\n\n  setLinkForCurrentNavigation(linkInstanceRef)\n\n  const onRouterTransitionStart = getProfilingHookForOnNavigationStart()\n  if (onRouterTransitionStart !== null) {\n    onRouterTransitionStart(href, navigateType)\n  }\n\n  dispatchAppRouterAction({\n    type: ACTION_NAVIGATE,\n    url,\n    isExternalUrl: isExternalURL(url),\n    locationSearch: location.search,\n    shouldScroll,\n    navigateType,\n    allowAliasing: true,\n  })\n}\n\nexport function dispatchTraverseAction(\n  href: string,\n  tree: FlightRouterState | undefined\n) {\n  const onRouterTransitionStart = getProfilingHookForOnNavigationStart()\n  if (onRouterTransitionStart !== null) {\n    onRouterTransitionStart(href, 'traverse')\n  }\n  dispatchAppRouterAction({\n    type: ACTION_RESTORE,\n    url: new URL(href),\n    tree,\n  })\n}\n\n/**\n * The app router that is exposed through `useRouter`. These are public API\n * methods. Internal Next.js code should call the lower level methods directly\n * (although there's lots of existing code that doesn't do that).\n */\nexport const publicAppRouterInstance: AppRouterInstance = {\n  back: () => window.history.back(),\n  forward: () => window.history.forward(),\n  prefetch: process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? // Unlike the old implementation, the Segment Cache doesn't store its\n      // data in the router reducer state; it writes into a global mutable\n      // cache. So we don't need to dispatch an action.\n      (href: string, options?: PrefetchOptions) => {\n        const actionQueue = getAppRouterActionQueue()\n        prefetchWithSegmentCache(\n          href,\n          actionQueue.state.nextUrl,\n          actionQueue.state.tree,\n          options?.kind === PrefetchKind.FULL\n        )\n      }\n    : (href: string, options?: PrefetchOptions) => {\n        // Use the old prefetch implementation.\n        const actionQueue = getAppRouterActionQueue()\n        const url = createPrefetchURL(href)\n        if (url !== null) {\n          // The prefetch reducer doesn't actually update any state or\n          // trigger a rerender. It just writes to a mutable cache. So we\n          // shouldn't bother calling setState/dispatch; we can just re-run\n          // the reducer directly using the current state.\n          // TODO: Refactor this away from a \"reducer\" so it's\n          // less confusing.\n          prefetchReducer(actionQueue.state, {\n            type: ACTION_PREFETCH,\n            url,\n            kind: options?.kind ?? PrefetchKind.FULL,\n          })\n        }\n      },\n  replace: (href: string, options?: NavigateOptions) => {\n    startTransition(() => {\n      dispatchNavigateAction(href, 'replace', options?.scroll ?? true, null)\n    })\n  },\n  push: (href: string, options?: NavigateOptions) => {\n    startTransition(() => {\n      dispatchNavigateAction(href, 'push', options?.scroll ?? true, null)\n    })\n  },\n  refresh: () => {\n    startTransition(() => {\n      dispatchAppRouterAction({\n        type: ACTION_REFRESH,\n        origin: window.location.origin,\n      })\n    })\n  },\n  hmrRefresh: () => {\n    if (process.env.NODE_ENV !== 'development') {\n      throw new Error(\n        'hmrRefresh can only be used in development mode. Please use refresh instead.'\n      )\n    } else {\n      startTransition(() => {\n        dispatchAppRouterAction({\n          type: ACTION_HMR_REFRESH,\n          origin: window.location.origin,\n        })\n      })\n    }\n  },\n}\n\n// Exists for debugging purposes. Don't use in application code.\nif (typeof window !== 'undefined' && window.next) {\n  window.next.router = publicAppRouterInstance\n}\n"], "names": ["ACTION_REFRESH", "ACTION_SERVER_ACTION", "ACTION_NAVIGATE", "ACTION_RESTORE", "ACTION_HMR_REFRESH", "PrefetchKind", "ACTION_PREFETCH", "reducer", "startTransition", "isThenable", "prefetch", "prefetchWithSegmentCache", "dispatchAppRouterAction", "addBasePath", "createPrefetchURL", "isExternalURL", "prefetchReducer", "setLinkForCurrentNavigation", "runRemainingActions", "actionQueue", "setState", "pending", "next", "runAction", "action", "needsRefresh", "dispatch", "type", "origin", "window", "location", "prevState", "state", "payload", "actionResult", "handleResult", "nextState", "discarded", "resolve", "then", "err", "reject", "dispatchAction", "resolvers", "deferred<PERSON><PERSON><PERSON>", "Promise", "newAction", "last", "globalActionQueue", "createMutableActionQueue", "initialState", "<PERSON><PERSON><PERSON><PERSON>", "result", "onRouterTransitionStart", "Error", "getCurrentAppRouterState", "getAppRouterActionQueue", "getProfilingHookForOnNavigationStart", "dispatchNavigateAction", "href", "navigateType", "shouldScroll", "linkInstanceRef", "url", "URL", "process", "env", "__NEXT_APP_NAV_FAIL_HANDLING", "__pendingUrl", "isExternalUrl", "locationSearch", "search", "allowAliasing", "dispatchTraverseAction", "tree", "publicAppRouterInstance", "back", "history", "forward", "__NEXT_CLIENT_SEGMENT_CACHE", "options", "nextUrl", "kind", "FULL", "replace", "scroll", "push", "refresh", "hmrRefresh", "NODE_ENV", "router"], "mappings": ";;;;;;;AAAA,SAIEA,cAAc,EACdC,oBAAoB,EACpBC,eAAe,EACfC,cAAc,EAEdC,kBAAkB,EAClBC,YAAY,EACZC,eAAe,QACV,wCAAuC;AAC9C,SAASC,OAAO,QAAQ,kCAAiC;AACzD,SAASC,eAAe,QAAQ,QAAO;AACvC,SAASC,UAAU,QAAQ,+BAA8B;AACzD,SAASC,YAAYC,wBAAwB,QAAQ,kBAAiB;AACtE,SAASC,uBAAuB,QAAQ,qBAAoB;AAC5D,SAASC,WAAW,QAAQ,mBAAkB;AAC9C,SAASC,iBAAiB,EAAEC,aAAa,QAAQ,eAAc;AAC/D,SAASC,eAAe,QAAQ,6CAA4C;AAM5E,SAASC,2BAA2B,QAA2B,UAAS;;;;;;;;;;;AA4BxE,SAASC,oBACPC,WAAiC,EACjCC,QAA8B;IAE9B,IAAID,YAAYE,OAAO,KAAK,MAAM;QAChCF,YAAYE,OAAO,GAAGF,YAAYE,OAAO,CAACC,IAAI;QAC9C,IAAIH,YAAYE,OAAO,KAAK,MAAM;YAChC,mEAAmE;YACnEE,UAAU;gBACRJ;gBACAK,QAAQL,YAAYE,OAAO;gBAC3BD;YACF;QACF,OAAO;YACL,4DAA4D;YAC5D,IAAID,YAAYM,YAAY,EAAE;gBAC5BN,YAAYM,YAAY,GAAG;gBAC3BN,YAAYO,QAAQ,CAClB;oBACEC,6NAAM3B,iBAAAA;oBACN4B,QAAQC,OAAOC,QAAQ,CAACF,MAAM;gBAChC,GACAR;YAEJ;QACF;IACF;AACF;AAEA,eAAeG,UAAU,KAQxB;IARwB,IAAA,EACvBJ,WAAW,EACXK,MAAM,EACNJ,QAAQ,EAKT,GARwB;IASvB,MAAMW,YAAYZ,YAAYa,KAAK;IAEnCb,YAAYE,OAAO,GAAGG;IAEtB,MAAMS,UAAUT,OAAOS,OAAO;IAC9B,MAAMC,eAAef,YAAYK,MAAM,CAACO,WAAWE;IAEnD,SAASE,aAAaC,SAAyB;QAC7C,kEAAkE;QAClE,IAAIZ,OAAOa,SAAS,EAAE;YACpB;QACF;QAEAlB,YAAYa,KAAK,GAAGI;QAEpBlB,oBAAoBC,aAAaC;QACjCI,OAAOc,OAAO,CAACF;IACjB;IAEA,8DAA8D;IAC9D,uLAAI3B,aAAAA,EAAWyB,eAAe;QAC5BA,aAAaK,IAAI,CAACJ,cAAc,CAACK;YAC/BtB,oBAAoBC,aAAaC;YACjCI,OAAOiB,MAAM,CAACD;QAChB;IACF,OAAO;QACLL,aAAaD;IACf;AACF;AAEA,SAASQ,eACPvB,WAAiC,EACjCc,OAAuB,EACvBb,QAA8B;IAE9B,IAAIuB,YAGA;QAAEL,SAASlB;QAAUqB,QAAQ,KAAO;IAAE;IAE1C,mEAAmE;IACnE,wFAAwF;IACxF,2DAA2D;IAC3D,oDAAoD;IACpD,IAAIR,QAAQN,IAAI,4NAAKxB,iBAAAA,EAAgB;QACnC,6DAA6D;QAC7D,MAAMyC,kBAAkB,IAAIC,QAAwB,CAACP,SAASG;YAC5DE,YAAY;gBAAEL;gBAASG;YAAO;QAChC;SAEAjC,wLAAAA,EAAgB;YACd,oGAAoG;YACpG,iEAAiE;YACjEY,SAASwB;QACX;IACF;IAEA,MAAME,YAA6B;QACjCb;QACAX,MAAM;QACNgB,SAASK,UAAUL,OAAO;QAC1BG,QAAQE,UAAUF,MAAM;IAC1B;IAEA,8BAA8B;IAC9B,IAAItB,YAAYE,OAAO,KAAK,MAAM;QAChC,iEAAiE;QACjE,4CAA4C;QAC5CF,YAAY4B,IAAI,GAAGD;QAEnBvB,UAAU;YACRJ;YACAK,QAAQsB;YACR1B;QACF;IACF,OAAO,IACLa,QAAQN,IAAI,KAAKzB,yOAAAA,IACjB+B,QAAQN,IAAI,4NAAKxB,iBAAAA,EACjB;QACA,+EAA+E;QAC/E,oHAAoH;QACpHgB,YAAYE,OAAO,CAACgB,SAAS,GAAG;QAEhC,4EAA4E;QAC5E,sIAAsI;QACtIS,UAAUxB,IAAI,GAAGH,YAAYE,OAAO,CAACC,IAAI;QAEzC,2GAA2G;QAC3G,IAAIH,YAAYE,OAAO,CAACY,OAAO,CAACN,IAAI,4NAAK1B,uBAAAA,EAAsB;YAC7DkB,YAAYM,YAAY,GAAG;QAC7B;QAEAF,UAAU;YACRJ;YACAK,QAAQsB;YACR1B;QACF;IACF,OAAO;QACL,oEAAoE;QACpE,+EAA+E;QAC/E,IAAID,YAAY4B,IAAI,KAAK,MAAM;YAC7B5B,YAAY4B,IAAI,CAACzB,IAAI,GAAGwB;QAC1B;QACA3B,YAAY4B,IAAI,GAAGD;IACrB;AACF;AAEA,IAAIE,oBAAiD;AAE9C,SAASC,yBACdC,YAA4B,EAC5BC,oBAAuD;IAEvD,MAAMhC,cAAoC;QACxCa,OAAOkB;QACPxB,UAAU,CAACO,SAAyBb,WAClCsB,eAAevB,aAAac,SAASb;QACvCI,QAAQ,OAAOQ,OAAuBR;YACpC,MAAM4B,UAAS7C,2NAAAA,EAAQyB,OAAOR;YAC9B,OAAO4B;QACT;QACA/B,SAAS;QACT0B,MAAM;QACNM,yBACEF,yBAAyB,QACzB,OAAOA,qBAAqBE,uBAAuB,KAAK,aAEpDF,qBAAqBE,uBAAuB,GAC5C;IACR;IAEA,IAAI,OAAOxB,WAAW,aAAa;QACjC,wEAAwE;QACxE,qEAAqE;QACrE,0CAA0C;QAC1C,IAAImB,sBAAsB,MAAM;YAC9B,MAAM,OAAA,cAGL,CAHK,IAAIM,MACR,sEACE,cAFE,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QACAN,oBAAoB7B;IACtB;IAEA,OAAOA;AACT;AAEO,SAASoC;IACd,OAAOP,sBAAsB,OAAOA,kBAAkBhB,KAAK,GAAG;AAChE;AAEA,SAASwB;IACP,IAAIR,sBAAsB,MAAM;QAC9B,MAAM,OAAA,cAEL,CAFK,IAAIM,MACR,4EADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACA,OAAON;AACT;AAEA,SAASS;IACP,IAAIT,sBAAsB,MAAM;QAC9B,OAAOA,kBAAkBK,uBAAuB;IAClD;IACA,OAAO;AACT;AAEO,SAASK,uBACdC,IAAY,EACZC,YAA4C,EAC5CC,YAAqB,EACrBC,eAAoC;IAEpC,yEAAyE;IACzE,oEAAoE;IACpE,MAAMC,MAAM,IAAIC,IAAInD,+LAAAA,EAAY8C,OAAO7B,SAAS6B,IAAI;IACpD,IAAIM,QAAQC,GAAG,CAACC,uBAA8B,KAAF;;IAE5C;qLAEAlD,8BAAAA,EAA4B6C;IAE5B,MAAMT,0BAA0BI;IAChC,IAAIJ,4BAA4B,MAAM;QACpCA,wBAAwBM,MAAMC;IAChC;sMAEAhD,0BAAAA,EAAwB;QACtBe,6NAAMzB,kBAAAA;QACN6D;QACAM,wMAAetD,gBAAAA,EAAcgD;QAC7BO,gBAAgBxC,SAASyC,MAAM;QAC/BV;QACAD;QACAY,eAAe;IACjB;AACF;AAEO,SAASC,uBACdd,IAAY,EACZe,IAAmC;IAEnC,MAAMrB,0BAA0BI;IAChC,IAAIJ,4BAA4B,MAAM;QACpCA,wBAAwBM,MAAM;IAChC;KACA/C,2NAAAA,EAAwB;QACtBe,6NAAMxB,iBAAAA;QACN4D,KAAK,IAAIC,IAAIL;QACbe;IACF;AACF;AAOO,MAAMC,0BAA6C;IACxDC,MAAM,IAAM/C,OAAOgD,OAAO,CAACD,IAAI;IAC/BE,SAAS,IAAMjD,OAAOgD,OAAO,CAACC,OAAO;IACrCpE,UAAUuD,QAAQC,GAAG,CAACa,2BAA2B,GAE7C,mCAWA,CAACpB,MAAcqB,0BAXqD;QAYlE,uCAAuC;QACvC,MAAM7D,cAAcqC;QACpB,MAAMO,OAAMjD,4MAAAA,EAAkB6C;QAC9B,IAAII,QAAQ,MAAM;gBAURiB;YATR,4DAA4D;YAC5D,+DAA+D;YAC/D,iEAAiE;YACjE,gDAAgD;YAChD,oDAAoD;YACpD,kBAAkB;4OAClBhE,kBAAAA,EAAgBG,YAAYa,KAAK,EAAE;gBACjCL,6NAAMrB,kBAAAA;gBACNyD;gBACAmB,MAAMF,CAAAA,gBAAAA,WAAAA,OAAAA,KAAAA,IAAAA,QAASE,IAAI,KAAA,OAAbF,uOAAiB3E,eAAAA,CAAa8E,IAAI;YAC1C;QACF;IACF;IACJC,SAAS,CAACzB,MAAcqB;YACtBxE,qLAAAA,EAAgB;gBAC0BwE;YAAxCtB,uBAAuBC,MAAM,WAAWqB,CAAAA,kBAAAA,WAAAA,OAAAA,KAAAA,IAAAA,QAASK,MAAM,KAAA,OAAfL,kBAAmB,MAAM;QACnE;IACF;IACAM,MAAM,CAAC3B,MAAcqB;+KACnBxE,kBAAAA,EAAgB;gBACuBwE;YAArCtB,uBAAuBC,MAAM,QAAQqB,CAAAA,kBAAAA,WAAAA,OAAAA,KAAAA,IAAAA,QAASK,MAAM,KAAA,OAAfL,kBAAmB,MAAM;QAChE;IACF;IACAO,SAAS;+KACP/E,kBAAAA,EAAgB;YACdI,4NAAAA,EAAwB;gBACtBe,6NAAM3B,iBAAAA;gBACN4B,QAAQC,OAAOC,QAAQ,CAACF,MAAM;YAChC;QACF;IACF;IACA4D,YAAY;QACV,IAAIvB,QAAQC,GAAG,CAACuB,QAAQ,KAAK,UAAe;;QAI5C,OAAO;mLACLjF,kBAAAA,EAAgB;kNACdI,0BAAAA,EAAwB;oBACtBe,6NAAMvB,qBAAAA;oBACNwB,QAAQC,OAAOC,QAAQ,CAACF,MAAM;gBAChC;YACF;QACF;IACF;AACF,EAAC;AAED,gEAAgE;AAChE,IAAI,OAAOC,WAAW,eAAeA,OAAOP,IAAI,EAAE;IAChDO,OAAOP,IAAI,CAACoE,MAAM,GAAGf;AACvB", "ignoreList": [0]}}, {"offset": {"line": 2868, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/dev-root-http-access-fallback-boundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { HTTPAccessFallbackBoundary } from './http-access-fallback/error-boundary'\n\n// TODO: error on using forbidden and unauthorized in root layout\nexport function bailOnRootNotFound() {\n  throw new Error('notFound() is not allowed to use in root layout')\n}\n\nfunction NotAllowedRootHTTPFallbackError() {\n  bailOnRootNotFound()\n  return null\n}\n\nexport function DevRootHTTPAccessFallbackBoundary({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return (\n    <HTTPAccessFallbackBoundary notFound={<NotAllowedRootHTTPFallbackError />}>\n      {children}\n    </HTTPAccessFallbackBoundary>\n  )\n}\n"], "names": ["React", "HTTPAccessFallbackBoundary", "bailOnRootNotFound", "Error", "NotAllowedRootHTTPFallbackError", "DevRootHTTPAccessFallbackBoundary", "children", "notFound"], "mappings": ";;;;;AAEA,OAAOA,WAAW,QAAO;AACzB,SAASC,0BAA0B,QAAQ,wCAAuC;AAHlF;;;;AAMO,SAASC;IACd,MAAM,OAAA,cAA4D,CAA5D,IAAIC,MAAM,oDAAV,qBAAA;eAAA;oBAAA;sBAAA;IAA2D;AACnE;AAEA,SAASC;IACPF;IACA,OAAO;AACT;AAEO,SAASG,kCAAkC,KAIjD;IAJiD,IAAA,EAChDC,QAAQ,EAGT,GAJiD;IAKhD,OAAA,WAAA,mLACE,MAAA,yNAACL,6BAAAA,EAAAA;QAA2BM,UAAAA,WAAAA,mLAAU,MAAA,EAACH,iCAAAA,CAAAA;kBACpCE;;AAGP", "ignoreList": [0]}}, {"offset": {"line": 2903, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/is-hydration-error.ts"], "sourcesContent": ["import isError from '../../lib/is-error'\n\nconst hydrationErrorRegex =\n  /hydration failed|while hydrating|content does not match|did not match|HTML didn't match|text didn't match/i\n\nconst reactUnifiedMismatchWarning = `Hydration failed because the server rendered HTML didn't match the client. As a result this tree will be regenerated on the client. This can happen if a SSR-ed Client Component used:`\n\nconst reactHydrationStartMessages = [\n  reactUnifiedMismatchWarning,\n  `Hydration failed because the server rendered text didn't match the client. As a result this tree will be regenerated on the client. This can happen if a SSR-ed Client Component used:`,\n  `A tree hydrated but some attributes of the server rendered HTML didn't match the client properties. This won't be patched up. This can happen if a SSR-ed Client Component used:`,\n]\n\nexport const REACT_HYDRATION_ERROR_LINK =\n  'https://react.dev/link/hydration-mismatch'\nexport const NEXTJS_HYDRATION_ERROR_LINK =\n  'https://nextjs.org/docs/messages/react-hydration-error'\n\nexport const getDefaultHydrationErrorMessage = () => {\n  return reactUnifiedMismatchWarning\n}\n\nexport function isHydrationError(error: unknown): boolean {\n  return isError(error) && hydrationErrorRegex.test(error.message)\n}\n\nexport function isReactHydrationErrorMessage(msg: string): boolean {\n  return reactHydrationStartMessages.some((prefix) => msg.startsWith(prefix))\n}\n\nconst hydrationWarningRegexes = [\n  /^In HTML, (.+?) cannot be a child of <(.+?)>\\.(.*)\\nThis will cause a hydration error\\.(.*)/,\n  /^In HTML, (.+?) cannot be a descendant of <(.+?)>\\.\\nThis will cause a hydration error\\.(.*)/,\n  /^In HTML, text nodes cannot be a child of <(.+?)>\\.\\nThis will cause a hydration error\\./,\n  /^In HTML, whitespace text nodes cannot be a child of <(.+?)>\\. Make sure you don't have any extra whitespace between tags on each line of your source code\\.\\nThis will cause a hydration error\\./,\n  /^Expected server HTML to contain a matching <(.+?)> in <(.+?)>\\.(.*)/,\n  /^Did not expect server HTML to contain a <(.+?)> in <(.+?)>\\.(.*)/,\n  /^Expected server HTML to contain a matching text node for \"(.+?)\" in <(.+?)>\\.(.*)/,\n  /^Did not expect server HTML to contain the text node \"(.+?)\" in <(.+?)>\\.(.*)/,\n  /^Text content did not match\\. Server: \"(.+?)\" Client: \"(.+?)\"(.*)/,\n]\n\nexport function testReactHydrationWarning(msg: string): boolean {\n  if (typeof msg !== 'string' || !msg) return false\n  // React 18 has the `Warning: ` prefix.\n  // React 19 does not.\n  if (msg.startsWith('Warning: ')) {\n    msg = msg.slice('Warning: '.length)\n  }\n  return hydrationWarningRegexes.some((regex) => regex.test(msg))\n}\n\nexport function getHydrationErrorStackInfo(rawMessage: string): {\n  message: string | null\n  stack?: string\n  diff?: string\n} {\n  rawMessage = rawMessage.replace(/^Error: /, '')\n  rawMessage = rawMessage.replace('Warning: ', '')\n  const isReactHydrationWarning = testReactHydrationWarning(rawMessage)\n\n  if (!isReactHydrationErrorMessage(rawMessage) && !isReactHydrationWarning) {\n    return {\n      message: null,\n      stack: rawMessage,\n      diff: '',\n    }\n  }\n\n  if (isReactHydrationWarning) {\n    const [message, diffLog] = rawMessage.split('\\n\\n')\n    return {\n      message: message.trim(),\n      stack: '',\n      diff: (diffLog || '').trim(),\n    }\n  }\n\n  const firstLineBreak = rawMessage.indexOf('\\n')\n  rawMessage = rawMessage.slice(firstLineBreak + 1).trim()\n\n  const [message, trailing] = rawMessage.split(`${REACT_HYDRATION_ERROR_LINK}`)\n  const trimmedMessage = message.trim()\n  // React built-in hydration diff starts with a newline, checking if length is > 1\n  if (trailing && trailing.length > 1) {\n    const stacks: string[] = []\n    const diffs: string[] = []\n    trailing.split('\\n').forEach((line) => {\n      if (line.trim() === '') return\n      if (line.trim().startsWith('at ')) {\n        stacks.push(line)\n      } else {\n        diffs.push(line)\n      }\n    })\n\n    return {\n      message: trimmedMessage,\n      diff: diffs.join('\\n'),\n      stack: stacks.join('\\n'),\n    }\n  } else {\n    return {\n      message: trimmedMessage,\n      stack: trailing, // without hydration diff\n    }\n  }\n}\n"], "names": ["isError", "hydrationErrorRegex", "reactUnifiedMismatchWarning", "reactHydrationStartMessages", "REACT_HYDRATION_ERROR_LINK", "NEXTJS_HYDRATION_ERROR_LINK", "getDefaultHydrationErrorMessage", "isHydrationError", "error", "test", "message", "isReactHydrationErrorMessage", "msg", "some", "prefix", "startsWith", "hydrationWarningRegexes", "testReactHydrationWarning", "slice", "length", "regex", "getHydrationErrorStackInfo", "rawMessage", "replace", "isReactHydrationWarning", "stack", "diff", "diffLog", "split", "trim", "firstLineBreak", "indexOf", "trailing", "trimmedMessage", "stacks", "diffs", "for<PERSON>ach", "line", "push", "join"], "mappings": ";;;;;;;;;AAAA,OAAOA,aAAa,qBAAoB;;AAExC,MAAMC,sBACJ;AAEF,MAAMC,8BAA+B;AAErC,MAAMC,8BAA8B;IAClCD;IACC;IACA;CACF;AAEM,MAAME,6BACX,4CAA2C;AACtC,MAAMC,8BACX,yDAAwD;AAEnD,MAAMC,kCAAkC;IAC7C,OAAOJ;AACT,EAAC;AAEM,SAASK,iBAAiBC,KAAc;IAC7C,6KAAOR,UAAAA,EAAQQ,UAAUP,oBAAoBQ,IAAI,CAACD,MAAME,OAAO;AACjE;AAEO,SAASC,6BAA6BC,GAAW;IACtD,OAAOT,4BAA4BU,IAAI,CAAC,CAACC,SAAWF,IAAIG,UAAU,CAACD;AACrE;AAEA,MAAME,0BAA0B;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAASC,0BAA0BL,GAAW;IACnD,IAAI,OAAOA,QAAQ,YAAY,CAACA,KAAK,OAAO;IAC5C,uCAAuC;IACvC,qBAAqB;IACrB,IAAIA,IAAIG,UAAU,CAAC,cAAc;QAC/BH,MAAMA,IAAIM,KAAK,CAAC,YAAYC,MAAM;IACpC;IACA,OAAOH,wBAAwBH,IAAI,CAAC,CAACO,QAAUA,MAAMX,IAAI,CAACG;AAC5D;AAEO,SAASS,2BAA2BC,UAAkB;IAK3DA,aAAaA,WAAWC,OAAO,CAAC,YAAY;IAC5CD,aAAaA,WAAWC,OAAO,CAAC,aAAa;IAC7C,MAAMC,0BAA0BP,0BAA0BK;IAE1D,IAAI,CAACX,6BAA6BW,eAAe,CAACE,yBAAyB;QACzE,OAAO;YACLd,SAAS;YACTe,OAAOH;YACPI,MAAM;QACR;IACF;IAEA,IAAIF,yBAAyB;QAC3B,MAAM,CAACd,SAASiB,QAAQ,GAAGL,WAAWM,KAAK,CAAC;QAC5C,OAAO;YACLlB,SAASA,QAAQmB,IAAI;YACrBJ,OAAO;YACPC,MAAOC,CAAAA,WAAW,EAAC,EAAGE,IAAI;QAC5B;IACF;IAEA,MAAMC,iBAAiBR,WAAWS,OAAO,CAAC;IAC1CT,aAAaA,WAAWJ,KAAK,CAACY,iBAAiB,GAAGD,IAAI;IAEtD,MAAM,CAACnB,SAASsB,SAAS,GAAGV,WAAWM,KAAK,CAAE,KAAExB;IAChD,MAAM6B,iBAAiBvB,QAAQmB,IAAI;IACnC,iFAAiF;IACjF,IAAIG,YAAYA,SAASb,MAAM,GAAG,GAAG;QACnC,MAAMe,SAAmB,EAAE;QAC3B,MAAMC,QAAkB,EAAE;QAC1BH,SAASJ,KAAK,CAAC,MAAMQ,OAAO,CAAC,CAACC;YAC5B,IAAIA,KAAKR,IAAI,OAAO,IAAI;YACxB,IAAIQ,KAAKR,IAAI,GAAGd,UAAU,CAAC,QAAQ;gBACjCmB,OAAOI,IAAI,CAACD;YACd,OAAO;gBACLF,MAAMG,IAAI,CAACD;YACb;QACF;QAEA,OAAO;YACL3B,SAASuB;YACTP,MAAMS,MAAMI,IAAI,CAAC;YACjBd,OAAOS,OAAOK,IAAI,CAAC;QACrB;IACF,OAAO;QACL,OAAO;YACL7B,SAASuB;YACTR,OAAOO;QACT;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 3005, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/errors/runtime-error-handler.ts"], "sourcesContent": ["export const RuntimeErrorHandler = {\n  hadRuntimeError: false,\n}\n"], "names": ["RuntimeError<PERSON>andler", "hadRuntimeError"], "mappings": ";;;AAAO,MAAMA,sBAAsB;IACjCC,iBAAiB;AACnB,EAAC", "ignoreList": [0]}}, {"offset": {"line": 3017, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/errors/hydration-error-info.ts"], "sourcesContent": ["import {\n  getHydrationErrorStackInfo,\n  testReactHydrationWarning,\n} from '../is-hydration-error'\n\nexport type HydrationErrorState = {\n  // Hydration warning template format: <message> <serverContent> <clientContent>\n  warning?: [string, string, string]\n  serverContent?: string\n  clientContent?: string\n  // React 19 hydration diff format: <notes> <link> <component diff?>\n  notes?: string\n  reactOutputComponentDiff?: string\n}\n\ntype NullableText = string | null | undefined\n\nexport const hydrationErrorState: HydrationErrorState = {}\n\n// https://github.com/facebook/react/blob/main/packages/react-dom/src/__tests__/ReactDOMHydrationDiff-test.js used as a reference\nconst htmlTagsWarnings = new Set([\n  'Warning: In HTML, %s cannot be a child of <%s>.%s\\nThis will cause a hydration error.%s',\n  'Warning: In HTML, %s cannot be a descendant of <%s>.\\nThis will cause a hydration error.%s',\n  'Warning: In HTML, text nodes cannot be a child of <%s>.\\nThis will cause a hydration error.',\n  \"Warning: In HTML, whitespace text nodes cannot be a child of <%s>. Make sure you don't have any extra whitespace between tags on each line of your source code.\\nThis will cause a hydration error.\",\n  'Warning: Expected server HTML to contain a matching <%s> in <%s>.%s',\n  'Warning: Did not expect server HTML to contain a <%s> in <%s>.%s',\n])\nconst textAndTagsMismatchWarnings = new Set([\n  'Warning: Expected server HTML to contain a matching text node for \"%s\" in <%s>.%s',\n  'Warning: Did not expect server HTML to contain the text node \"%s\" in <%s>.%s',\n])\n\nexport const getHydrationWarningType = (\n  message: NullableText\n): 'tag' | 'text' | 'text-in-tag' => {\n  if (typeof message !== 'string') {\n    // TODO: Doesn't make sense to treat no message as a hydration error message.\n    // We should bail out somewhere earlier.\n    return 'text'\n  }\n\n  const normalizedMessage = message.startsWith('Warning: ')\n    ? message\n    : `Warning: ${message}`\n\n  if (isHtmlTagsWarning(normalizedMessage)) return 'tag'\n  if (isTextInTagsMismatchWarning(normalizedMessage)) return 'text-in-tag'\n\n  return 'text'\n}\n\nconst isHtmlTagsWarning = (message: string) => htmlTagsWarnings.has(message)\n\nconst isTextInTagsMismatchWarning = (msg: string) =>\n  textAndTagsMismatchWarnings.has(msg)\n\nexport const getReactHydrationDiffSegments = (msg: NullableText) => {\n  if (msg) {\n    const { message, diff } = getHydrationErrorStackInfo(msg)\n    if (message) return [message, diff]\n  }\n  return undefined\n}\n\n/**\n * Patch console.error to capture hydration errors.\n * If any of the knownHydrationWarnings are logged, store the message and component stack.\n * When the hydration runtime error is thrown, the message and component stack are added to the error.\n * This results in a more helpful error message in the error overlay.\n */\n\nexport function storeHydrationErrorStateFromConsoleArgs(...args: any[]) {\n  let [msg, firstContent, secondContent, ...rest] = args\n  if (testReactHydrationWarning(msg)) {\n    // Some hydration warnings has 4 arguments, some has 3, fallback to the last argument\n    // when the 3rd argument is not the component stack but an empty string\n    const isReact18 = msg.startsWith('Warning: ')\n\n    // For some warnings, there's only 1 argument for template.\n    // The second argument is the diff or component stack.\n    if (args.length === 3) {\n      secondContent = ''\n    }\n\n    const warning: [string, string, string] = [\n      // remove the last %s from the message\n      msg,\n      firstContent,\n      secondContent,\n    ]\n\n    const lastArg = (rest[rest.length - 1] || '').trim()\n    if (!isReact18) {\n      hydrationErrorState.reactOutputComponentDiff = lastArg\n    } else {\n      hydrationErrorState.reactOutputComponentDiff =\n        generateHydrationDiffReact18(msg, firstContent, secondContent, lastArg)\n    }\n\n    hydrationErrorState.warning = warning\n    hydrationErrorState.serverContent = firstContent\n    hydrationErrorState.clientContent = secondContent\n  }\n}\n\n/*\n * Some hydration errors in React 18 does not have the diff in the error message.\n * Instead it has the error stack trace which is component stack that we can leverage.\n * Will parse the diff from the error stack trace\n *  e.g.\n *  Warning: Expected server HTML to contain a matching <div> in <p>.\n *    at div\n *    at p\n *    at div\n *    at div\n *    at Page\n *  output:\n *    <Page>\n *      <div>\n *        <p>\n *  >       <div>\n *\n */\nfunction generateHydrationDiffReact18(\n  message: string,\n  firstContent: string,\n  secondContent: string,\n  lastArg: string\n) {\n  const componentStack = lastArg\n  let firstIndex = -1\n  let secondIndex = -1\n  const hydrationWarningType = getHydrationWarningType(message)\n\n  // at div\\n at Foo\\n at Bar (....)\\n -> [div, Foo]\n  const components = componentStack\n    .split('\\n')\n    // .reverse()\n    .map((line: string, index: number) => {\n      // `<space>at <component> (<location>)` -> `at <component> (<location>)`\n      line = line.trim()\n      // extract `<space>at <component>` to `<<component>>`\n      // e.g. `  at Foo` -> `<Foo>`\n      const [, component, location] = /at (\\w+)( \\((.*)\\))?/.exec(line) || []\n      // If there's no location then it's user-land stack frame\n      if (!location) {\n        if (component === firstContent && firstIndex === -1) {\n          firstIndex = index\n        } else if (component === secondContent && secondIndex === -1) {\n          secondIndex = index\n        }\n      }\n      return location ? '' : component\n    })\n    .filter(Boolean)\n    .reverse()\n\n  let diff = ''\n  for (let i = 0; i < components.length; i++) {\n    const component = components[i]\n    const matchFirstContent =\n      hydrationWarningType === 'tag' && i === components.length - firstIndex - 1\n    const matchSecondContent =\n      hydrationWarningType === 'tag' &&\n      i === components.length - secondIndex - 1\n    if (matchFirstContent || matchSecondContent) {\n      const spaces = ' '.repeat(Math.max(i * 2 - 2, 0) + 2)\n      diff += `> ${spaces}<${component}>\\n`\n    } else {\n      const spaces = ' '.repeat(i * 2 + 2)\n      diff += `${spaces}<${component}>\\n`\n    }\n  }\n  if (hydrationWarningType === 'text') {\n    const spaces = ' '.repeat(components.length * 2)\n    diff += `+ ${spaces}\"${firstContent}\"\\n`\n    diff += `- ${spaces}\"${secondContent}\"\\n`\n  } else if (hydrationWarningType === 'text-in-tag') {\n    const spaces = ' '.repeat(components.length * 2)\n    diff += `> ${spaces}<${secondContent}>\\n`\n    diff += `>   ${spaces}\"${firstContent}\"\\n`\n  }\n  return diff\n}\n"], "names": ["getHydrationErrorStackInfo", "testReactHydrationWarning", "hydrationErrorState", "htmlTagsWarnings", "Set", "textAndTagsMismatchWarnings", "getHydrationWarningType", "message", "normalizedMessage", "startsWith", "isHtmlTagsWarning", "isTextInTagsMismatchWarning", "has", "msg", "getReactHydrationDiffSegments", "diff", "undefined", "storeHydrationErrorStateFromConsoleArgs", "args", "firstContent", "second<PERSON><PERSON>nt", "rest", "isReact18", "length", "warning", "lastArg", "trim", "reactOutputComponentDiff", "generateHydrationDiffReact18", "serverContent", "clientContent", "componentStack", "firstIndex", "secondIndex", "hydrationWarningType", "components", "split", "map", "line", "index", "component", "location", "exec", "filter", "Boolean", "reverse", "i", "match<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "matchSecondContent", "spaces", "repeat", "Math", "max"], "mappings": ";;;;;;AAAA,SACEA,0BAA0B,EAC1BC,yBAAyB,QACpB,wBAAuB;;AAcvB,MAAMC,sBAA2C,CAAC,EAAC;AAE1D,iIAAiI;AACjI,MAAMC,mBAAmB,IAAIC,IAAI;IAC/B;IACA;IACA;IACA;IACA;IACA;CACD;AACD,MAAMC,8BAA8B,IAAID,IAAI;IAC1C;IACA;CACD;AAEM,MAAME,0BAA0B,CACrCC;IAEA,IAAI,OAAOA,YAAY,UAAU;QAC/B,6EAA6E;QAC7E,wCAAwC;QACxC,OAAO;IACT;IAEA,MAAMC,oBAAoBD,QAAQE,UAAU,CAAC,eACzCF,UACC,cAAWA;IAEhB,IAAIG,kBAAkBF,oBAAoB,OAAO;IACjD,IAAIG,4BAA4BH,oBAAoB,OAAO;IAE3D,OAAO;AACT,EAAC;AAED,MAAME,oBAAoB,CAACH,UAAoBJ,iBAAiBS,GAAG,CAACL;AAEpE,MAAMI,8BAA8B,CAACE,MACnCR,4BAA4BO,GAAG,CAACC;AAE3B,MAAMC,gCAAgC,CAACD;IAC5C,IAAIA,KAAK;QACP,MAAM,EAAEN,OAAO,EAAEQ,IAAI,EAAE,uMAAGf,6BAAAA,EAA2Ba;QACrD,IAAIN,SAAS,OAAO;YAACA;YAASQ;SAAK;IACrC;IACA,OAAOC;AACT,EAAC;AASM,SAASC;IAAwC,IAAA,IAAA,OAAA,UAAA,MAAA,EAAGC,OAAH,IAAA,MAAA,OAAA,OAAA,GAAA,OAAA,MAAA,OAAA;QAAGA,IAAAA,CAAH,KAAA,GAAA,SAAA,CAAA,KAAc;;IACpE,IAAI,CAACL,KAAKM,cAAcC,eAAe,GAAGC,KAAK,GAAGH;IAClD,IAAIjB,gOAAAA,EAA0BY,MAAM;QAClC,qFAAqF;QACrF,uEAAuE;QACvE,MAAMS,YAAYT,IAAIJ,UAAU,CAAC;QAEjC,2DAA2D;QAC3D,sDAAsD;QACtD,IAAIS,KAAKK,MAAM,KAAK,GAAG;YACrBH,gBAAgB;QAClB;QAEA,MAAMI,UAAoC;YACxC,sCAAsC;YACtCX;YACAM;YACAC;SACD;QAED,MAAMK,UAAWJ,CAAAA,IAAI,CAACA,KAAKE,MAAM,GAAG,EAAE,IAAI,EAAC,EAAGG,IAAI;QAClD,IAAI,CAACJ,WAAW;YACdpB,oBAAoByB,wBAAwB,GAAGF;QACjD,OAAO;YACLvB,oBAAoByB,wBAAwB,GAC1CC,6BAA6Bf,KAAKM,cAAcC,eAAeK;QACnE;QAEAvB,oBAAoBsB,OAAO,GAAGA;QAC9BtB,oBAAoB2B,aAAa,GAAGV;QACpCjB,oBAAoB4B,aAAa,GAAGV;IACtC;AACF;AAEA;;;;;;;;;;;;;;;;;CAiBC,GACD,SAASQ,6BACPrB,OAAe,EACfY,YAAoB,EACpBC,aAAqB,EACrBK,OAAe;IAEf,MAAMM,iBAAiBN;IACvB,IAAIO,aAAa,CAAC;IAClB,IAAIC,cAAc,CAAC;IACnB,MAAMC,uBAAuB5B,wBAAwBC;IAErD,kDAAkD;IAClD,MAAM4B,aAAaJ,eAChBK,KAAK,CAAC,MACP,aAAa;KACZC,GAAG,CAAC,CAACC,MAAcC;QAClB,wEAAwE;QACxED,OAAOA,KAAKZ,IAAI;QAChB,qDAAqD;QACrD,6BAA6B;QAC7B,MAAM,GAAGc,WAAWC,SAAS,GAAG,uBAAuBC,IAAI,CAACJ,SAAS,EAAE;QACvE,yDAAyD;QACzD,IAAI,CAACG,UAAU;YACb,IAAID,cAAcrB,gBAAgBa,eAAe,CAAC,GAAG;gBACnDA,aAAaO;YACf,OAAO,IAAIC,cAAcpB,iBAAiBa,gBAAgB,CAAC,GAAG;gBAC5DA,cAAcM;YAChB;QACF;QACA,OAAOE,WAAW,KAAKD;IACzB,GACCG,MAAM,CAACC,SACPC,OAAO;IAEV,IAAI9B,OAAO;IACX,IAAK,IAAI+B,IAAI,GAAGA,IAAIX,WAAWZ,MAAM,EAAEuB,IAAK;QAC1C,MAAMN,YAAYL,UAAU,CAACW,EAAE;QAC/B,MAAMC,oBACJb,yBAAyB,SAASY,MAAMX,WAAWZ,MAAM,GAAGS,aAAa;QAC3E,MAAMgB,qBACJd,yBAAyB,SACzBY,MAAMX,WAAWZ,MAAM,GAAGU,cAAc;QAC1C,IAAIc,qBAAqBC,oBAAoB;YAC3C,MAAMC,SAAS,IAAIC,MAAM,CAACC,KAAKC,GAAG,CAACN,IAAI,IAAI,GAAG,KAAK;YACnD/B,QAAS,OAAIkC,SAAO,MAAGT,YAAU;QACnC,OAAO;YACL,MAAMS,SAAS,IAAIC,MAAM,CAACJ,IAAI,IAAI;YAClC/B,QAAWkC,SAAO,MAAGT,YAAU;QACjC;IACF;IACA,IAAIN,yBAAyB,QAAQ;QACnC,MAAMe,SAAS,IAAIC,MAAM,CAACf,WAAWZ,MAAM,GAAG;QAC9CR,QAAS,OAAIkC,SAAO,MAAG9B,eAAa;QACpCJ,QAAS,OAAIkC,SAAO,MAAG7B,gBAAc;IACvC,OAAO,IAAIc,yBAAyB,eAAe;QACjD,MAAMe,SAAS,IAAIC,MAAM,CAACf,WAAWZ,MAAM,GAAG;QAC9CR,QAAS,OAAIkC,SAAO,MAAG7B,gBAAc;QACrCL,QAAS,SAAMkC,SAAO,MAAG9B,eAAa;IACxC;IACA,OAAOJ;AACT", "ignoreList": [0]}}, {"offset": {"line": 3163, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/errors/console-error.ts"], "sourcesContent": ["// To distinguish from React error.digest, we use a different symbol here to determine if the error is from console.error or unhandled promise rejection.\nconst digestSym = Symbol.for('next.console.error.digest')\nconst consoleTypeSym = Symbol.for('next.console.error.type')\n\n// Represent non Error shape unhandled promise rejections or console.error errors.\n// Those errors will be captured and displayed in Error Overlay.\nexport type ConsoleError = Error & {\n  [digestSym]: 'NEXT_CONSOLE_ERROR'\n  [consoleTypeSym]: 'string' | 'error'\n  environmentName: string\n}\n\nexport function createConsoleError(\n  message: string | Error,\n  environmentName?: string | null\n): ConsoleError {\n  const error = (\n    typeof message === 'string' ? new Error(message) : message\n  ) as ConsoleError\n  error[digestSym] = 'NEXT_CONSOLE_ERROR'\n  error[consoleTypeSym] = typeof message === 'string' ? 'string' : 'error'\n\n  if (environmentName && !error.environmentName) {\n    error.environmentName = environmentName\n  }\n\n  return error\n}\n\nexport const isConsoleError = (error: any): error is ConsoleError => {\n  return error && error[digestSym] === 'NEXT_CONSOLE_ERROR'\n}\n\nexport const getConsoleErrorType = (error: ConsoleError) => {\n  return error[consoleTypeSym]\n}\n"], "names": ["digestSym", "Symbol", "for", "consoleTypeSym", "createConsoleError", "message", "environmentName", "error", "Error", "isConsoleError", "getConsoleErrorType"], "mappings": "AAAA,yJAAyJ;;;;;;AACzJ,MAAMA,YAAYC,OAAOC,GAAG,CAAC;AAC7B,MAAMC,iBAAiBF,OAAOC,GAAG,CAAC;AAU3B,SAASE,mBACdC,OAAuB,EACvBC,eAA+B;IAE/B,MAAMC,QACJ,OAAOF,YAAY,WAAW,OAAA,cAAkB,CAAlB,IAAIG,MAAMH,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB,KAAIA;IAErDE,KAAK,CAACP,UAAU,GAAG;IACnBO,KAAK,CAACJ,eAAe,GAAG,OAAOE,YAAY,WAAW,WAAW;IAEjE,IAAIC,mBAAmB,CAACC,MAAMD,eAAe,EAAE;QAC7CC,MAAMD,eAAe,GAAGA;IAC1B;IAEA,OAAOC;AACT;AAEO,MAAME,iBAAiB,CAACF;IAC7B,OAAOA,SAASA,KAAK,CAACP,UAAU,KAAK;AACvC,EAAC;AAEM,MAAMU,sBAAsB,CAACH;IAClC,OAAOA,KAAK,CAACJ,eAAe;AAC9B,EAAC", "ignoreList": [0]}}, {"offset": {"line": 3196, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/errors/attach-hydration-error-state.ts"], "sourcesContent": ["import {\n  getDefaultHydrationErrorMessage,\n  isHydrationError,\n  testReactHydrationWarning,\n} from '../is-hydration-error'\nimport {\n  hydrationErrorState,\n  getReactHydrationDiffSegments,\n  type HydrationErrorState,\n} from './hydration-error-info'\n\nexport function attachHydrationErrorState(error: Error) {\n  let parsedHydrationErrorState: typeof hydrationErrorState = {}\n  const isHydrationWarning = testReactHydrationWarning(error.message)\n  const isHydrationRuntimeError = isHydrationError(error)\n\n  // If it's not hydration warnings or errors, skip\n  if (!(isHydrationRuntimeError || isHydrationWarning)) {\n    return\n  }\n\n  const reactHydrationDiffSegments = getReactHydrationDiffSegments(\n    error.message\n  )\n  // If the reactHydrationDiffSegments exists\n  // and the diff (reactHydrationDiffSegments[1]) exists\n  // e.g. the hydration diff log error.\n  if (reactHydrationDiffSegments) {\n    const diff = reactHydrationDiffSegments[1]\n    parsedHydrationErrorState = {\n      ...((error as any).details as HydrationErrorState),\n      ...hydrationErrorState,\n      // If diff is present in error, we don't need to pick up the console logged warning.\n      // - if hydration error has diff, and is not hydration diff log, then it's a normal hydration error.\n      // - if hydration error no diff, then leverage the one from the hydration diff log.\n\n      warning: (diff && !isHydrationWarning\n        ? null\n        : hydrationErrorState.warning) || [\n        getDefaultHydrationErrorMessage(),\n        '',\n        '',\n      ],\n      // When it's hydration diff log, do not show notes section.\n      // This condition is only for the 1st squashed error.\n      notes: isHydrationWarning ? '' : reactHydrationDiffSegments[0],\n      reactOutputComponentDiff: diff,\n    }\n    // Cache the `reactOutputComponentDiff` into hydrationErrorState.\n    // This is only required for now when we still squashed the hydration diff log into hydration error.\n    // Once the all error is logged to dev overlay in order, this will go away.\n    if (!hydrationErrorState.reactOutputComponentDiff && diff) {\n      hydrationErrorState.reactOutputComponentDiff = diff\n    }\n    // If it's hydration runtime error that doesn't contain the diff, combine the diff from the cached hydration diff.\n    if (\n      !diff &&\n      isHydrationRuntimeError &&\n      hydrationErrorState.reactOutputComponentDiff\n    ) {\n      parsedHydrationErrorState.reactOutputComponentDiff =\n        hydrationErrorState.reactOutputComponentDiff\n    }\n  } else {\n    // Normal runtime error, where it doesn't contain the hydration diff.\n\n    // If there's any extra information in the error message to display,\n    // append it to the error message details property\n    if (hydrationErrorState.warning) {\n      // The patched console.error found hydration errors logged by React\n      // Append the logged warning to the error message\n      parsedHydrationErrorState = {\n        ...(error as any).details,\n        // It contains the warning, component stack, server and client tag names\n        ...hydrationErrorState,\n      }\n    }\n    // Consume the cached hydration diff.\n    // This is only required for now when we still squashed the hydration diff log into hydration error.\n    // Once the all error is logged to dev overlay in order, this will go away.\n    if (hydrationErrorState.reactOutputComponentDiff) {\n      parsedHydrationErrorState.reactOutputComponentDiff =\n        hydrationErrorState.reactOutputComponentDiff\n    }\n  }\n  // If it's a hydration error, store the hydration error state into the error object\n  ;(error as any).details = parsedHydrationErrorState\n}\n"], "names": ["getDefaultHydrationErrorMessage", "isHydrationError", "testReactHydrationWarning", "hydrationErrorState", "getReactHydrationDiffSegments", "attachHydrationErrorState", "error", "parsedHydrationErrorState", "isHydrationWarning", "message", "isHydrationRuntimeError", "reactHydrationDiffSegments", "diff", "details", "warning", "notes", "reactOutputComponentDiff"], "mappings": ";;;AAAA,SACEA,+BAA+B,EAC/BC,gBAAgB,EAChBC,yBAAyB,QACpB,wBAAuB;AAC9B,SACEC,mBAAmB,EACnBC,6BAA6B,QAExB,yBAAwB;;;AAExB,SAASC,0BAA0BC,KAAY;IACpD,IAAIC,4BAAwD,CAAC;IAC7D,MAAMC,yNAAqBN,4BAAAA,EAA0BI,MAAMG,OAAO;IAClE,MAAMC,8BAA0BT,mNAAAA,EAAiBK;IAEjD,iDAAiD;IACjD,IAAI,CAAEI,CAAAA,2BAA2BF,kBAAiB,GAAI;QACpD;IACF;IAEA,MAAMG,6OAA6BP,gCAAAA,EACjCE,MAAMG,OAAO;IAEf,2CAA2C;IAC3C,sDAAsD;IACtD,qCAAqC;IACrC,IAAIE,4BAA4B;QAC9B,MAAMC,OAAOD,0BAA0B,CAAC,EAAE;QAC1CJ,4BAA4B;YAC1B,GAAKD,MAAcO,OAAO;YAC1B,+MAAGV,sBAAmB;YACtB,oFAAoF;YACpF,oGAAoG;YACpG,mFAAmF;YAEnFW,SAAUF,CAAAA,QAAQ,CAACJ,qBACf,mNACAL,sBAAAA,CAAoBW,OAAM,KAAM;iBAClCd,qOAAAA;gBACA;gBACA;aACD;YACD,2DAA2D;YAC3D,qDAAqD;YACrDe,OAAOP,qBAAqB,KAAKG,0BAA0B,CAAC,EAAE;YAC9DK,0BAA0BJ;QAC5B;QACA,iEAAiE;QACjE,oGAAoG;QACpG,2EAA2E;QAC3E,IAAI,4MAACT,uBAAAA,CAAoBa,wBAAwB,IAAIJ,MAAM;wNACzDT,sBAAAA,CAAoBa,wBAAwB,GAAGJ;QACjD;QACA,kHAAkH;QAClH,IACE,CAACA,QACDF,uOACAP,sBAAAA,CAAoBa,wBAAwB,EAC5C;YACAT,0BAA0BS,wBAAwB,+MAChDb,sBAAAA,CAAoBa,wBAAwB;QAChD;IACF,OAAO;QACL,qEAAqE;QAErE,oEAAoE;QACpE,kDAAkD;QAClD,gNAAIb,sBAAAA,CAAoBW,OAAO,EAAE;YAC/B,mEAAmE;YACnE,iDAAiD;YACjDP,4BAA4B;gBAC1B,GAAID,MAAcO,OAAO;gBACzB,wEAAwE;gBACxE,+MAAGV,sBAAmB;YACxB;QACF;QACA,qCAAqC;QACrC,oGAAoG;QACpG,2EAA2E;QAC3E,gNAAIA,sBAAAA,CAAoBa,wBAAwB,EAAE;YAChDT,0BAA0BS,wBAAwB,+MAChDb,sBAAAA,CAAoBa,wBAAwB;QAChD;IACF;IACA,mFAAmF;;IACjFV,MAAcO,OAAO,GAAGN;AAC5B", "ignoreList": [0]}}, {"offset": {"line": 3273, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/errors/enqueue-client-error.ts"], "sourcesContent": ["// Dedupe the two consecutive errors: If the previous one is same as current one, ignore the current one.\nexport function enqueueConsecutiveDedupedError(\n  queue: Array<Error>,\n  error: Error\n) {\n  const previousError = queue[queue.length - 1]\n  // Compare the error stack to dedupe the consecutive errors\n  if (previousError && previousError.stack === error.stack) {\n    return\n  }\n  queue.push(error)\n}\n"], "names": ["enqueueConsecutiveDedupedError", "queue", "error", "previousError", "length", "stack", "push"], "mappings": "AAAA,yGAAyG;;;;AAClG,SAASA,+BACdC,KAAmB,EACnBC,KAAY;IAEZ,MAAMC,gBAAgBF,KAAK,CAACA,MAAMG,MAAM,GAAG,EAAE;IAC7C,2DAA2D;IAC3D,IAAID,iBAAiBA,cAAcE,KAAK,KAAKH,MAAMG,KAAK,EAAE;QACxD;IACF;IACAJ,MAAMK,IAAI,CAACJ;AACb", "ignoreList": [0]}}, {"offset": {"line": 3291, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/errors/stitched-error.ts"], "sourcesContent": ["import React from 'react'\nimport isError from '../../../lib/is-error'\nimport { copyNextErrorCode } from '../../../lib/error-telemetry-utils'\n\nconst REACT_ERROR_STACK_BOTTOM_FRAME = 'react-stack-bottom-frame'\nconst REACT_ERROR_STACK_BOTTOM_FRAME_REGEX = new RegExp(\n  `(at ${REACT_ERROR_STACK_BOTTOM_FRAME} )|(${REACT_ERROR_STACK_BOTTOM_FRAME}\\\\@)`\n)\n\nexport function getReactStitchedError<T = unknown>(err: T): Error | T {\n  const isErrorInstance = isError(err)\n  const originStack = isErrorInstance ? err.stack || '' : ''\n  const originMessage = isErrorInstance ? err.message : ''\n  const stackLines = originStack.split('\\n')\n  const indexOfSplit = stackLines.findIndex((line) =>\n    REACT_ERROR_STACK_BOTTOM_FRAME_REGEX.test(line)\n  )\n  const isOriginalReactError = indexOfSplit >= 0 // has the react-stack-bottom-frame\n  let newStack = isOriginalReactError\n    ? stackLines.slice(0, indexOfSplit).join('\\n')\n    : originStack\n\n  const newError = new Error(originMessage)\n  // Copy all enumerable properties, e.g. digest\n  Object.assign(newError, err)\n  copyNextErrorCode(err, newError)\n  newError.stack = newStack\n\n  // Avoid duplicate overriding stack frames\n  appendOwnerStack(newError)\n\n  return newError\n}\n\nfunction appendOwnerStack(error: Error) {\n  if (!React.captureOwnerStack) {\n    return\n  }\n  let stack = error.stack || ''\n  // This module is only bundled in development mode so this is safe.\n  const ownerStack = React.captureOwnerStack()\n  // Avoid duplicate overriding stack frames\n  if (ownerStack && stack.endsWith(ownerStack) === false) {\n    stack += ownerStack\n    // Override stack\n    error.stack = stack\n  }\n}\n"], "names": ["React", "isError", "copyNextErrorCode", "REACT_ERROR_STACK_BOTTOM_FRAME", "REACT_ERROR_STACK_BOTTOM_FRAME_REGEX", "RegExp", "getReactStitchedError", "err", "isErrorInstance", "originStack", "stack", "originMessage", "message", "stackLines", "split", "indexOfSplit", "findIndex", "line", "test", "isOriginalReactError", "newStack", "slice", "join", "newError", "Error", "Object", "assign", "appendOwnerStack", "error", "captureOwnerStack", "ownerStack", "endsWith"], "mappings": ";;;AAAA,OAAOA,WAAW,QAAO;AACzB,OAAOC,aAAa,wBAAuB;AAC3C,SAASC,iBAAiB,QAAQ,qCAAoC;;;;AAEtE,MAAMC,iCAAiC;AACvC,MAAMC,uCAAuC,IAAIC,OAC9C,SAAMF,iCAA+B,SAAMA,iCAA+B;AAGtE,SAASG,sBAAmCC,GAAM;IACvD,MAAMC,wLAAkBP,UAAAA,EAAQM;IAChC,MAAME,cAAcD,kBAAkBD,IAAIG,KAAK,IAAI,KAAK;IACxD,MAAMC,gBAAgBH,kBAAkBD,IAAIK,OAAO,GAAG;IACtD,MAAMC,aAAaJ,YAAYK,KAAK,CAAC;IACrC,MAAMC,eAAeF,WAAWG,SAAS,CAAC,CAACC,OACzCb,qCAAqCc,IAAI,CAACD;IAE5C,MAAME,uBAAuBJ,gBAAgB,EAAE,mCAAmC;;IAClF,IAAIK,WAAWD,uBACXN,WAAWQ,KAAK,CAAC,GAAGN,cAAcO,IAAI,CAAC,QACvCb;IAEJ,MAAMc,WAAW,OAAA,cAAwB,CAAxB,IAAIC,MAAMb,gBAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAuB;IACxC,8CAA8C;IAC9Cc,OAAOC,MAAM,CAACH,UAAUhB;0LACxBL,oBAAAA,EAAkBK,KAAKgB;IACvBA,SAASb,KAAK,GAAGU;IAEjB,0CAA0C;IAC1CO,iBAAiBJ;IAEjB,OAAOA;AACT;AAEA,SAASI,iBAAiBC,KAAY;IACpC,IAAI,oKAAC5B,UAAAA,CAAM6B,iBAAiB,EAAE;QAC5B;IACF;IACA,IAAInB,QAAQkB,MAAMlB,KAAK,IAAI;IAC3B,mEAAmE;IACnE,MAAMoB,gLAAa9B,UAAAA,CAAM6B,iBAAiB;IAC1C,0CAA0C;IAC1C,IAAIC,cAAcpB,MAAMqB,QAAQ,CAACD,gBAAgB,OAAO;QACtDpB,SAASoB;QACT,iBAAiB;QACjBF,MAAMlB,KAAK,GAAGA;IAChB;AACF", "ignoreList": [0]}}, {"offset": {"line": 3344, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/errors/use-error-handler.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport { attachHydrationErrorState } from './attach-hydration-error-state'\nimport { isNextRouterError } from '../is-next-router-error'\nimport { storeHydrationErrorStateFromConsoleArgs } from './hydration-error-info'\nimport { formatConsoleArgs, parseConsoleArgs } from '../../lib/console'\nimport isError from '../../../lib/is-error'\nimport { createConsoleError } from './console-error'\nimport { enqueueConsecutiveDedupedError } from './enqueue-client-error'\nimport { getReactStitchedError } from '../errors/stitched-error'\n\nconst queueMicroTask =\n  globalThis.queueMicrotask || ((cb: () => void) => Promise.resolve().then(cb))\n\nexport type ErrorHandler = (error: Error) => void\n\nconst errorQueue: Array<Error> = []\nconst errorHandlers: Array<ErrorHandler> = []\nconst rejectionQueue: Array<Error> = []\nconst rejectionHandlers: Array<ErrorHandler> = []\n\nexport function handleConsoleError(\n  originError: unknown,\n  consoleErrorArgs: any[]\n) {\n  let error: Error\n  const { environmentName } = parseConsoleArgs(consoleErrorArgs)\n  if (isError(originError)) {\n    error = createConsoleError(originError, environmentName)\n  } else {\n    error = createConsoleError(\n      formatConsoleArgs(consoleErrorArgs),\n      environmentName\n    )\n  }\n  error = getReactStitchedError(error)\n\n  storeHydrationErrorStateFromConsoleArgs(...consoleErrorArgs)\n  attachHydrationErrorState(error)\n\n  enqueueConsecutiveDedupedError(errorQueue, error)\n  for (const handler of errorHandlers) {\n    // Delayed the error being passed to React Dev Overlay,\n    // avoid the state being synchronously updated in the component.\n    queueMicroTask(() => {\n      handler(error)\n    })\n  }\n}\n\nexport function handleClientError(originError: unknown) {\n  let error: Error\n  if (isError(originError)) {\n    error = originError\n  } else {\n    // If it's not an error, format the args into an error\n    const formattedErrorMessage = originError + ''\n    error = new Error(formattedErrorMessage)\n  }\n  error = getReactStitchedError(error)\n\n  attachHydrationErrorState(error)\n\n  enqueueConsecutiveDedupedError(errorQueue, error)\n  for (const handler of errorHandlers) {\n    // Delayed the error being passed to React Dev Overlay,\n    // avoid the state being synchronously updated in the component.\n    queueMicroTask(() => {\n      handler(error)\n    })\n  }\n}\n\nexport function useErrorHandler(\n  handleOnUnhandledError: ErrorHandler,\n  handleOnUnhandledRejection: ErrorHandler\n) {\n  useEffect(() => {\n    // Handle queued errors.\n    errorQueue.forEach(handleOnUnhandledError)\n    rejectionQueue.forEach(handleOnUnhandledRejection)\n\n    // Listen to new errors.\n    errorHandlers.push(handleOnUnhandledError)\n    rejectionHandlers.push(handleOnUnhandledRejection)\n\n    return () => {\n      // Remove listeners.\n      errorHandlers.splice(errorHandlers.indexOf(handleOnUnhandledError), 1)\n      rejectionHandlers.splice(\n        rejectionHandlers.indexOf(handleOnUnhandledRejection),\n        1\n      )\n\n      // Reset error queues.\n      errorQueue.splice(0, errorQueue.length)\n      rejectionQueue.splice(0, rejectionQueue.length)\n    }\n  }, [handleOnUnhandledError, handleOnUnhandledRejection])\n}\n\nfunction onUnhandledError(event: WindowEventMap['error']): void | boolean {\n  if (isNextRouterError(event.error)) {\n    event.preventDefault()\n    return false\n  }\n  // When there's an error property present, we log the error to error overlay.\n  // Otherwise we don't do anything as it's not logging in the console either.\n  if (event.error) {\n    handleClientError(event.error)\n  }\n}\n\nfunction onUnhandledRejection(ev: WindowEventMap['unhandledrejection']): void {\n  const reason = ev?.reason\n  if (isNextRouterError(reason)) {\n    ev.preventDefault()\n    return\n  }\n\n  let error = reason\n  if (error && !isError(error)) {\n    error = new Error(error + '')\n  }\n\n  rejectionQueue.push(error)\n  for (const handler of rejectionHandlers) {\n    handler(error)\n  }\n}\n\nexport function handleGlobalErrors() {\n  if (typeof window !== 'undefined') {\n    try {\n      // Increase the number of stack frames on the client\n      Error.stackTraceLimit = 50\n    } catch {}\n\n    window.addEventListener('error', onUnhandledError)\n    window.addEventListener('unhandledrejection', onUnhandledRejection)\n  }\n}\n"], "names": ["useEffect", "attachHydrationErrorState", "isNextRouterError", "storeHydrationErrorStateFromConsoleArgs", "formatConsoleArgs", "parseConsoleArgs", "isError", "createConsoleError", "enqueueConsecutiveDedupedError", "getReactStitchedError", "queueMicroTask", "globalThis", "queueMicrotask", "cb", "Promise", "resolve", "then", "errorQueue", "errorHandlers", "rejectionQueue", "rejectionHandlers", "handleConsoleError", "originError", "consoleErrorArgs", "error", "environmentName", "handler", "handleClientError", "formattedErrorMessage", "Error", "useErrorHandler", "handleOnUnhandledError", "handleOnUnhandledRejection", "for<PERSON>ach", "push", "splice", "indexOf", "length", "onUnhandledError", "event", "preventDefault", "onUnhandledRejection", "ev", "reason", "handleGlobalErrors", "window", "stackTraceLimit", "addEventListener"], "mappings": ";;;;;;AAAA,SAASA,SAAS,QAAQ,QAAO;AACjC,SAASC,yBAAyB,QAAQ,iCAAgC;AAC1E,SAASC,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,uCAAuC,QAAQ,yBAAwB;AAChF,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,oBAAmB;AACvE,OAAOC,aAAa,wBAAuB;AAC3C,SAASC,kBAAkB,QAAQ,kBAAiB;AACpD,SAASC,8BAA8B,QAAQ,yBAAwB;AACvE,SAASC,qBAAqB,QAAQ,2BAA0B;;;;;;;;;;AAEhE,MAAMC,iBACJC,WAAWC,cAAc,IAAK,CAAA,CAACC,KAAmBC,QAAQC,OAAO,GAAGC,IAAI,CAACH,GAAE;AAI7E,MAAMI,aAA2B,EAAE;AACnC,MAAMC,gBAAqC,EAAE;AAC7C,MAAMC,iBAA+B,EAAE;AACvC,MAAMC,oBAAyC,EAAE;AAE1C,SAASC,mBACdC,WAAoB,EACpBC,gBAAuB;IAEvB,IAAIC;IACJ,MAAM,EAAEC,eAAe,EAAE,+KAAGpB,mBAAAA,EAAiBkB;IAC7C,0KAAIjB,UAAAA,EAAQgB,cAAc;QACxBE,8MAAQjB,qBAAAA,EAAmBe,aAAaG;IAC1C,OAAO;QACLD,QAAQjB,2NAAAA,8KACNH,oBAAAA,EAAkBmB,mBAClBE;IAEJ;IACAD,+MAAQf,wBAAAA,EAAsBe;QAE9BrB,sPAAAA,KAA2CoB;+NAC3CtB,4BAAAA,EAA0BuB;QAE1BhB,6OAAAA,EAA+BS,YAAYO;IAC3C,KAAK,MAAME,WAAWR,cAAe;QACnC,uDAAuD;QACvD,gEAAgE;QAChER,eAAe;YACbgB,QAAQF;QACV;IACF;AACF;AAEO,SAASG,kBAAkBL,WAAoB;IACpD,IAAIE;IACJ,0KAAIlB,UAAAA,EAAQgB,cAAc;QACxBE,QAAQF;IACV,OAAO;QACL,sDAAsD;QACtD,MAAMM,wBAAwBN,cAAc;QAC5CE,QAAQ,OAAA,cAAgC,CAAhC,IAAIK,MAAMD,wBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA+B;IACzC;IACAJ,+MAAQf,wBAAAA,EAAsBe;KAE9BvB,sPAAAA,EAA0BuB;oNAE1BhB,iCAAAA,EAA+BS,YAAYO;IAC3C,KAAK,MAAME,WAAWR,cAAe;QACnC,uDAAuD;QACvD,gEAAgE;QAChER,eAAe;YACbgB,QAAQF;QACV;IACF;AACF;AAEO,SAASM,gBACdC,sBAAoC,EACpCC,0BAAwC;2KAExChC,YAAAA,EAAU;QACR,wBAAwB;QACxBiB,WAAWgB,OAAO,CAACF;QACnBZ,eAAec,OAAO,CAACD;QAEvB,wBAAwB;QACxBd,cAAcgB,IAAI,CAACH;QACnBX,kBAAkBc,IAAI,CAACF;QAEvB,OAAO;YACL,oBAAoB;YACpBd,cAAciB,MAAM,CAACjB,cAAckB,OAAO,CAACL,yBAAyB;YACpEX,kBAAkBe,MAAM,CACtBf,kBAAkBgB,OAAO,CAACJ,6BAC1B;YAGF,sBAAsB;YACtBf,WAAWkB,MAAM,CAAC,GAAGlB,WAAWoB,MAAM;YACtClB,eAAegB,MAAM,CAAC,GAAGhB,eAAekB,MAAM;QAChD;IACF,GAAG;QAACN;QAAwBC;KAA2B;AACzD;AAEA,SAASM,iBAAiBC,KAA8B;IACtD,KAAIrC,4NAAAA,EAAkBqC,MAAMf,KAAK,GAAG;QAClCe,MAAMC,cAAc;QACpB,OAAO;IACT;IACA,6EAA6E;IAC7E,4EAA4E;IAC5E,IAAID,MAAMf,KAAK,EAAE;QACfG,kBAAkBY,MAAMf,KAAK;IAC/B;AACF;AAEA,SAASiB,qBAAqBC,EAAwC;IACpE,MAAMC,SAASD,MAAAA,OAAAA,KAAAA,IAAAA,GAAIC,MAAM;IACzB,6MAAIzC,oBAAAA,EAAkByC,SAAS;QAC7BD,GAAGF,cAAc;QACjB;IACF;IAEA,IAAIhB,QAAQmB;IACZ,IAAInB,SAAS,uKAAClB,UAAAA,EAAQkB,QAAQ;QAC5BA,QAAQ,OAAA,cAAqB,CAArB,IAAIK,MAAML,QAAQ,KAAlB,qBAAA;mBAAA;wBAAA;0BAAA;QAAoB;IAC9B;IAEAL,eAAee,IAAI,CAACV;IACpB,KAAK,MAAME,WAAWN,kBAAmB;QACvCM,QAAQF;IACV;AACF;AAEO,SAASoB;IACd,IAAI,OAAOC,WAAW,aAAa;QACjC,IAAI;YACF,oDAAoD;YACpDhB,MAAMiB,eAAe,GAAG;QAC1B,EAAE,OAAA,GAAM,CAAC;QAETD,OAAOE,gBAAgB,CAAC,SAAST;QACjCO,OAAOE,gBAAgB,CAAC,sBAAsBN;IAChD;AACF", "ignoreList": [0]}}, {"offset": {"line": 3484, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/app-router.tsx"], "sourcesContent": ["'use client'\n\nimport React, {\n  use,\n  useEffect,\n  useMemo,\n  startTransition,\n  useInsertionEffect,\n  useDeferredValue,\n} from 'react'\nimport {\n  AppRouterContext,\n  LayoutRouterContext,\n  GlobalLayoutRouterContext,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport type { CacheNode } from '../../shared/lib/app-router-context.shared-runtime'\nimport { ACTION_RESTORE } from './router-reducer/router-reducer-types'\nimport type { AppRouterState } from './router-reducer/router-reducer-types'\nimport { createHrefFromUrl } from './router-reducer/create-href-from-url'\nimport {\n  SearchParamsContext,\n  PathnameContext,\n  PathParamsContext,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport { dispatchAppRouterAction, useActionQueue } from './use-action-queue'\nimport {\n  default as DefaultGlobalError,\n  ErrorBoundary,\n  type GlobalErrorComponent,\n} from './error-boundary'\nimport { isBot } from '../../shared/lib/router/utils/is-bot'\nimport { addBasePath } from '../add-base-path'\nimport { AppRouterAnnouncer } from './app-router-announcer'\nimport { RedirectBoundary } from './redirect-boundary'\nimport { findHeadInCache } from './router-reducer/reducers/find-head-in-cache'\nimport { unresolvedThenable } from './unresolved-thenable'\nimport { removeBasePath } from '../remove-base-path'\nimport { hasBasePath } from '../has-base-path'\nimport { getSelectedParams } from './router-reducer/compute-changed-path'\nimport type { FlightRouterState } from '../../server/app-render/types'\nimport { useNavFailureHandler } from './nav-failure-handler'\nimport {\n  dispatchTraverseAction,\n  publicAppRouterInstance,\n  type AppRouterActionQueue,\n} from './app-router-instance'\nimport { getRedirectTypeFromError, getURLFromRedirectError } from './redirect'\nimport { isRedirectError, RedirectType } from './redirect-error'\nimport { pingVisibleLinks } from './links'\n\nconst globalMutable: {\n  pendingMpaPath?: string\n} = {}\n\nexport function isExternalURL(url: URL) {\n  return url.origin !== window.location.origin\n}\n\n/**\n * Given a link href, constructs the URL that should be prefetched. Returns null\n * in cases where prefetching should be disabled, like external URLs, or\n * during development.\n * @param href The href passed to <Link>, router.prefetch(), or similar\n * @returns A URL object to prefetch, or null if prefetching should be disabled\n */\nexport function createPrefetchURL(href: string): URL | null {\n  // Don't prefetch for bots as they don't navigate.\n  if (isBot(window.navigator.userAgent)) {\n    return null\n  }\n\n  let url: URL\n  try {\n    url = new URL(addBasePath(href), window.location.href)\n  } catch (_) {\n    // TODO: Does this need to throw or can we just console.error instead? Does\n    // anyone rely on this throwing? (Seems unlikely.)\n    throw new Error(\n      `Cannot prefetch '${href}' because it cannot be converted to a URL.`\n    )\n  }\n\n  // Don't prefetch during development (improves compilation performance)\n  if (process.env.NODE_ENV === 'development') {\n    return null\n  }\n\n  // External urls can't be prefetched in the same way.\n  if (isExternalURL(url)) {\n    return null\n  }\n\n  return url\n}\n\nfunction HistoryUpdater({\n  appRouterState,\n}: {\n  appRouterState: AppRouterState\n}) {\n  useInsertionEffect(() => {\n    if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n      // clear pending URL as navigation is no longer\n      // in flight\n      window.next.__pendingUrl = undefined\n    }\n\n    const { tree, pushRef, canonicalUrl } = appRouterState\n    const historyState = {\n      ...(pushRef.preserveCustomHistoryState ? window.history.state : {}),\n      // Identifier is shortened intentionally.\n      // __NA is used to identify if the history entry can be handled by the app-router.\n      // __N is used to identify if the history entry can be handled by the old router.\n      __NA: true,\n      __PRIVATE_NEXTJS_INTERNALS_TREE: tree,\n    }\n    if (\n      pushRef.pendingPush &&\n      // Skip pushing an additional history entry if the canonicalUrl is the same as the current url.\n      // This mirrors the browser behavior for normal navigation.\n      createHrefFromUrl(new URL(window.location.href)) !== canonicalUrl\n    ) {\n      // This intentionally mutates React state, pushRef is overwritten to ensure additional push/replace calls do not trigger an additional history entry.\n      pushRef.pendingPush = false\n      window.history.pushState(historyState, '', canonicalUrl)\n    } else {\n      window.history.replaceState(historyState, '', canonicalUrl)\n    }\n  }, [appRouterState])\n\n  useEffect(() => {\n    // The Next-Url and the base tree may affect the result of a prefetch\n    // task. Re-prefetch all visible links with the updated values. In most\n    // cases, this will not result in any new network requests, only if\n    // the prefetch result actually varies on one of these inputs.\n    if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n      pingVisibleLinks(appRouterState.nextUrl, appRouterState.tree)\n    }\n  }, [appRouterState.nextUrl, appRouterState.tree])\n\n  return null\n}\n\nexport function createEmptyCacheNode(): CacheNode {\n  return {\n    lazyData: null,\n    rsc: null,\n    prefetchRsc: null,\n    head: null,\n    prefetchHead: null,\n    parallelRoutes: new Map(),\n    loading: null,\n    navigatedAt: -1,\n  }\n}\n\nfunction copyNextJsInternalHistoryState(data: any) {\n  if (data == null) data = {}\n  const currentState = window.history.state\n  const __NA = currentState?.__NA\n  if (__NA) {\n    data.__NA = __NA\n  }\n  const __PRIVATE_NEXTJS_INTERNALS_TREE =\n    currentState?.__PRIVATE_NEXTJS_INTERNALS_TREE\n  if (__PRIVATE_NEXTJS_INTERNALS_TREE) {\n    data.__PRIVATE_NEXTJS_INTERNALS_TREE = __PRIVATE_NEXTJS_INTERNALS_TREE\n  }\n\n  return data\n}\n\nfunction Head({\n  headCacheNode,\n}: {\n  headCacheNode: CacheNode | null\n}): React.ReactNode {\n  // If this segment has a `prefetchHead`, it's the statically prefetched data.\n  // We should use that on initial render instead of `head`. Then we'll switch\n  // to `head` when the dynamic response streams in.\n  const head = headCacheNode !== null ? headCacheNode.head : null\n  const prefetchHead =\n    headCacheNode !== null ? headCacheNode.prefetchHead : null\n\n  // If no prefetch data is available, then we go straight to rendering `head`.\n  const resolvedPrefetchRsc = prefetchHead !== null ? prefetchHead : head\n\n  // We use `useDeferredValue` to handle switching between the prefetched and\n  // final values. The second argument is returned on initial render, then it\n  // re-renders with the first argument.\n  return useDeferredValue(head, resolvedPrefetchRsc)\n}\n\n/**\n * The global router that wraps the application components.\n */\nfunction Router({\n  actionQueue,\n  assetPrefix,\n  globalError,\n}: {\n  actionQueue: AppRouterActionQueue\n  assetPrefix: string\n  globalError: [GlobalErrorComponent, React.ReactNode]\n}) {\n  const state = useActionQueue(actionQueue)\n  const { canonicalUrl } = state\n  // Add memoized pathname/query for useSearchParams and usePathname.\n  const { searchParams, pathname } = useMemo(() => {\n    const url = new URL(\n      canonicalUrl,\n      typeof window === 'undefined' ? 'http://n' : window.location.href\n    )\n\n    return {\n      // This is turned into a readonly class in `useSearchParams`\n      searchParams: url.searchParams,\n      pathname: hasBasePath(url.pathname)\n        ? removeBasePath(url.pathname)\n        : url.pathname,\n    }\n  }, [canonicalUrl])\n\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const { cache, prefetchCache, tree } = state\n\n    // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      // Add `window.nd` for debugging purposes.\n      // This is not meant for use in applications as concurrent rendering will affect the cache/tree/router.\n      // @ts-ignore this is for debugging\n      window.nd = {\n        router: publicAppRouterInstance,\n        cache,\n        prefetchCache,\n        tree,\n      }\n    }, [cache, prefetchCache, tree])\n  }\n\n  useEffect(() => {\n    // If the app is restored from bfcache, it's possible that\n    // pushRef.mpaNavigation is true, which would mean that any re-render of this component\n    // would trigger the mpa navigation logic again from the lines below.\n    // This will restore the router to the initial state in the event that the app is restored from bfcache.\n    function handlePageShow(event: PageTransitionEvent) {\n      if (\n        !event.persisted ||\n        !window.history.state?.__PRIVATE_NEXTJS_INTERNALS_TREE\n      ) {\n        return\n      }\n\n      // Clear the pendingMpaPath value so that a subsequent MPA navigation to the same URL can be triggered.\n      // This is necessary because if the browser restored from bfcache, the pendingMpaPath would still be set to the value\n      // of the last MPA navigation.\n      globalMutable.pendingMpaPath = undefined\n\n      dispatchAppRouterAction({\n        type: ACTION_RESTORE,\n        url: new URL(window.location.href),\n        tree: window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE,\n      })\n    }\n\n    window.addEventListener('pageshow', handlePageShow)\n\n    return () => {\n      window.removeEventListener('pageshow', handlePageShow)\n    }\n  }, [])\n\n  useEffect(() => {\n    // Ensure that any redirect errors that bubble up outside of the RedirectBoundary\n    // are caught and handled by the router.\n    function handleUnhandledRedirect(\n      event: ErrorEvent | PromiseRejectionEvent\n    ) {\n      const error = 'reason' in event ? event.reason : event.error\n      if (isRedirectError(error)) {\n        event.preventDefault()\n        const url = getURLFromRedirectError(error)\n        const redirectType = getRedirectTypeFromError(error)\n        // TODO: This should access the router methods directly, rather than\n        // go through the public interface.\n        if (redirectType === RedirectType.push) {\n          publicAppRouterInstance.push(url, {})\n        } else {\n          publicAppRouterInstance.replace(url, {})\n        }\n      }\n    }\n    window.addEventListener('error', handleUnhandledRedirect)\n    window.addEventListener('unhandledrejection', handleUnhandledRedirect)\n\n    return () => {\n      window.removeEventListener('error', handleUnhandledRedirect)\n      window.removeEventListener('unhandledrejection', handleUnhandledRedirect)\n    }\n  }, [])\n\n  // When mpaNavigation flag is set do a hard navigation to the new url.\n  // Infinitely suspend because we don't actually want to rerender any child\n  // components with the new URL and any entangled state updates shouldn't\n  // commit either (eg: useTransition isPending should stay true until the page\n  // unloads).\n  //\n  // This is a side effect in render. Don't try this at home, kids. It's\n  // probably safe because we know this is a singleton component and it's never\n  // in <Offscreen>. At least I hope so. (It will run twice in dev strict mode,\n  // but that's... fine?)\n  const { pushRef } = state\n  if (pushRef.mpaNavigation) {\n    // if there's a re-render, we don't want to trigger another redirect if one is already in flight to the same URL\n    if (globalMutable.pendingMpaPath !== canonicalUrl) {\n      const location = window.location\n      if (pushRef.pendingPush) {\n        location.assign(canonicalUrl)\n      } else {\n        location.replace(canonicalUrl)\n      }\n\n      globalMutable.pendingMpaPath = canonicalUrl\n    }\n    // TODO-APP: Should we listen to navigateerror here to catch failed\n    // navigations somehow? And should we call window.stop() if a SPA navigation\n    // should interrupt an MPA one?\n    use(unresolvedThenable)\n  }\n\n  useEffect(() => {\n    const originalPushState = window.history.pushState.bind(window.history)\n    const originalReplaceState = window.history.replaceState.bind(\n      window.history\n    )\n\n    // Ensure the canonical URL in the Next.js Router is updated when the URL is changed so that `usePathname` and `useSearchParams` hold the pushed values.\n    const applyUrlFromHistoryPushReplace = (\n      url: string | URL | null | undefined\n    ) => {\n      const href = window.location.href\n      const tree: FlightRouterState | undefined =\n        window.history.state?.__PRIVATE_NEXTJS_INTERNALS_TREE\n\n      startTransition(() => {\n        dispatchAppRouterAction({\n          type: ACTION_RESTORE,\n          url: new URL(url ?? href, href),\n          tree,\n        })\n      })\n    }\n\n    /**\n     * Patch pushState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */\n    window.history.pushState = function pushState(\n      data: any,\n      _unused: string,\n      url?: string | URL | null\n    ): void {\n      // Avoid a loop when Next.js internals trigger pushState/replaceState\n      if (data?.__NA || data?._N) {\n        return originalPushState(data, _unused, url)\n      }\n\n      data = copyNextJsInternalHistoryState(data)\n\n      if (url) {\n        applyUrlFromHistoryPushReplace(url)\n      }\n\n      return originalPushState(data, _unused, url)\n    }\n\n    /**\n     * Patch replaceState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */\n    window.history.replaceState = function replaceState(\n      data: any,\n      _unused: string,\n      url?: string | URL | null\n    ): void {\n      // Avoid a loop when Next.js internals trigger pushState/replaceState\n      if (data?.__NA || data?._N) {\n        return originalReplaceState(data, _unused, url)\n      }\n      data = copyNextJsInternalHistoryState(data)\n\n      if (url) {\n        applyUrlFromHistoryPushReplace(url)\n      }\n      return originalReplaceState(data, _unused, url)\n    }\n\n    /**\n     * Handle popstate event, this is used to handle back/forward in the browser.\n     * By default dispatches ACTION_RESTORE, however if the history entry was not pushed/replaced by app-router it will reload the page.\n     * That case can happen when the old router injected the history entry.\n     */\n    const onPopState = (event: PopStateEvent) => {\n      if (!event.state) {\n        // TODO-APP: this case only happens when pushState/replaceState was called outside of Next.js. It should probably reload the page in this case.\n        return\n      }\n\n      // This case happens when the history entry was pushed by the `pages` router.\n      if (!event.state.__NA) {\n        window.location.reload()\n        return\n      }\n\n      // TODO-APP: Ideally the back button should not use startTransition as it should apply the updates synchronously\n      // Without startTransition works if the cache is there for this path\n      startTransition(() => {\n        dispatchTraverseAction(\n          window.location.href,\n          event.state.__PRIVATE_NEXTJS_INTERNALS_TREE\n        )\n      })\n    }\n\n    // Register popstate event to call onPopstate.\n    window.addEventListener('popstate', onPopState)\n    return () => {\n      window.history.pushState = originalPushState\n      window.history.replaceState = originalReplaceState\n      window.removeEventListener('popstate', onPopState)\n    }\n  }, [])\n\n  const { cache, tree, nextUrl, focusAndScrollRef } = state\n\n  const matchingHead = useMemo(() => {\n    return findHeadInCache(cache, tree[1])\n  }, [cache, tree])\n\n  // Add memoized pathParams for useParams.\n  const pathParams = useMemo(() => {\n    return getSelectedParams(tree)\n  }, [tree])\n\n  const layoutRouterContext = useMemo(() => {\n    return {\n      parentTree: tree,\n      parentCacheNode: cache,\n      parentSegmentPath: null,\n      // Root node always has `url`\n      // Provided in AppTreeContext to ensure it can be overwritten in layout-router\n      url: canonicalUrl,\n    }\n  }, [tree, cache, canonicalUrl])\n\n  const globalLayoutRouterContext = useMemo(() => {\n    return {\n      tree,\n      focusAndScrollRef,\n      nextUrl,\n    }\n  }, [tree, focusAndScrollRef, nextUrl])\n\n  let head\n  if (matchingHead !== null) {\n    // The head is wrapped in an extra component so we can use\n    // `useDeferredValue` to swap between the prefetched and final versions of\n    // the head. (This is what LayoutRouter does for segment data, too.)\n    //\n    // The `key` is used to remount the component whenever the head moves to\n    // a different segment.\n    const [headCacheNode, headKey] = matchingHead\n    head = <Head key={headKey} headCacheNode={headCacheNode} />\n  } else {\n    head = null\n  }\n\n  let content = (\n    <RedirectBoundary>\n      {head}\n      {cache.rsc}\n      <AppRouterAnnouncer tree={tree} />\n    </RedirectBoundary>\n  )\n\n  if (process.env.NODE_ENV !== 'production') {\n    // In development, we apply few error boundaries and hot-reloader:\n    // - DevRootHTTPAccessFallbackBoundary: avoid using navigation API like notFound() in root layout\n    // - HotReloader:\n    //  - hot-reload the app when the code changes\n    //  - render dev overlay\n    //  - catch runtime errors and display global-error when necessary\n    if (typeof window !== 'undefined') {\n      const { DevRootHTTPAccessFallbackBoundary } =\n        require('./dev-root-http-access-fallback-boundary') as typeof import('./dev-root-http-access-fallback-boundary')\n      content = (\n        <DevRootHTTPAccessFallbackBoundary>\n          {content}\n        </DevRootHTTPAccessFallbackBoundary>\n      )\n    }\n    const HotReloader: typeof import('./react-dev-overlay/app/hot-reloader-client').default =\n      require('./react-dev-overlay/app/hot-reloader-client').default\n\n    content = (\n      <HotReloader assetPrefix={assetPrefix} globalError={globalError}>\n        {content}\n      </HotReloader>\n    )\n  } else {\n    // In production, we only apply the user-customized global error boundary.\n    content = (\n      <ErrorBoundary\n        errorComponent={globalError[0]}\n        errorStyles={globalError[1]}\n      >\n        {content}\n      </ErrorBoundary>\n    )\n  }\n\n  return (\n    <>\n      <HistoryUpdater appRouterState={state} />\n      <RuntimeStyles />\n      <PathParamsContext.Provider value={pathParams}>\n        <PathnameContext.Provider value={pathname}>\n          <SearchParamsContext.Provider value={searchParams}>\n            <GlobalLayoutRouterContext.Provider\n              value={globalLayoutRouterContext}\n            >\n              {/* TODO: We should be able to remove this context. useRouter\n                  should import from app-router-instance instead. It's only\n                  necessary because useRouter is shared between Pages and\n                  App Router. We should fork that module, then remove this\n                  context provider. */}\n              <AppRouterContext.Provider value={publicAppRouterInstance}>\n                <LayoutRouterContext.Provider value={layoutRouterContext}>\n                  {content}\n                </LayoutRouterContext.Provider>\n              </AppRouterContext.Provider>\n            </GlobalLayoutRouterContext.Provider>\n          </SearchParamsContext.Provider>\n        </PathnameContext.Provider>\n      </PathParamsContext.Provider>\n    </>\n  )\n}\n\nexport default function AppRouter({\n  actionQueue,\n  globalErrorComponentAndStyles: [globalErrorComponent, globalErrorStyles],\n  assetPrefix,\n}: {\n  actionQueue: AppRouterActionQueue\n  globalErrorComponentAndStyles: [GlobalErrorComponent, React.ReactNode]\n  assetPrefix: string\n}) {\n  useNavFailureHandler()\n\n  return (\n    <ErrorBoundary\n      // At the very top level, use the default GlobalError component as the final fallback.\n      // When the app router itself fails, which means the framework itself fails, we show the default error.\n      errorComponent={DefaultGlobalError}\n    >\n      <Router\n        actionQueue={actionQueue}\n        assetPrefix={assetPrefix}\n        globalError={[globalErrorComponent, globalErrorStyles]}\n      />\n    </ErrorBoundary>\n  )\n}\n\nconst runtimeStyles = new Set<string>()\nlet runtimeStyleChanged = new Set<() => void>()\n\nglobalThis._N_E_STYLE_LOAD = function (href: string) {\n  let len = runtimeStyles.size\n  runtimeStyles.add(href)\n  if (runtimeStyles.size !== len) {\n    runtimeStyleChanged.forEach((cb) => cb())\n  }\n  // TODO figure out how to get a promise here\n  // But maybe it's not necessary as react would block rendering until it's loaded\n  return Promise.resolve()\n}\n\nfunction RuntimeStyles() {\n  const [, forceUpdate] = React.useState(0)\n  const renderedStylesSize = runtimeStyles.size\n  useEffect(() => {\n    const changed = () => forceUpdate((c) => c + 1)\n    runtimeStyleChanged.add(changed)\n    if (renderedStylesSize !== runtimeStyles.size) {\n      changed()\n    }\n    return () => {\n      runtimeStyleChanged.delete(changed)\n    }\n  }, [renderedStylesSize, forceUpdate])\n\n  const dplId = process.env.NEXT_DEPLOYMENT_ID\n    ? `?dpl=${process.env.NEXT_DEPLOYMENT_ID}`\n    : ''\n  return [...runtimeStyles].map((href, i) => (\n    <link\n      key={i}\n      rel=\"stylesheet\"\n      href={`${href}${dplId}`}\n      // @ts-ignore\n      precedence=\"next\"\n      // TODO figure out crossOrigin and nonce\n      // crossOrigin={TODO}\n      // nonce={TODO}\n    />\n  ))\n}\n"], "names": ["React", "use", "useEffect", "useMemo", "startTransition", "useInsertionEffect", "useDeferredValue", "AppRouterContext", "LayoutRouterContext", "GlobalLayoutRouterContext", "ACTION_RESTORE", "createHrefFromUrl", "SearchParamsContext", "PathnameContext", "PathParamsContext", "dispatchAppRouterAction", "useActionQueue", "default", "DefaultGlobalError", "Error<PERSON>ou<PERSON><PERSON>", "isBot", "addBasePath", "AppRouterAnnouncer", "RedirectBoundary", "findHeadInCache", "unresolvedThenable", "removeBasePath", "has<PERSON>ase<PERSON><PERSON>", "getSelectedParams", "useNavFailureHandler", "dispatchTraverseAction", "publicAppRouterInstance", "getRedirectTypeFromError", "getURLFromRedirectError", "isRedirectError", "RedirectType", "pingVisibleLinks", "globalMutable", "isExternalURL", "url", "origin", "window", "location", "createPrefetchURL", "href", "navigator", "userAgent", "URL", "_", "Error", "process", "env", "NODE_ENV", "HistoryUpdater", "appRouterState", "__NEXT_APP_NAV_FAIL_HANDLING", "next", "__pendingUrl", "undefined", "tree", "pushRef", "canonicalUrl", "historyState", "preserveCustomHistoryState", "history", "state", "__NA", "__PRIVATE_NEXTJS_INTERNALS_TREE", "pendingPush", "pushState", "replaceState", "__NEXT_CLIENT_SEGMENT_CACHE", "nextUrl", "createEmptyCacheNode", "lazyData", "rsc", "prefetchRsc", "head", "prefetchHead", "parallelRoutes", "Map", "loading", "navigatedAt", "copyNextJsInternalHistoryState", "data", "currentState", "Head", "headCacheNode", "resolvedPrefetchRsc", "Router", "actionQueue", "assetPrefix", "globalError", "searchParams", "pathname", "cache", "prefetchCache", "nd", "router", "handlePageShow", "event", "persisted", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "addEventListener", "removeEventListener", "handleUnhandledRedirect", "error", "reason", "preventDefault", "redirectType", "push", "replace", "mpaNavigation", "assign", "originalPushState", "bind", "originalReplaceState", "applyUrlFromHistoryPushReplace", "_unused", "_N", "onPopState", "reload", "focusAndScrollRef", "matchingHead", "pathParams", "layoutRouterContext", "parentTree", "parentCacheNode", "parentSegmentPath", "globalLayoutRouterContext", "head<PERSON><PERSON>", "content", "DevRootHTTPAccessFallbackBoundary", "require", "HotReloader", "errorComponent", "errorStyles", "RuntimeStyles", "Provider", "value", "AppRouter", "globalErrorComponentAndStyles", "globalErrorComponent", "globalErrorStyles", "runtimeStyles", "Set", "runtimeStyleChanged", "globalThis", "_N_E_STYLE_LOAD", "len", "size", "add", "for<PERSON>ach", "cb", "Promise", "resolve", "forceUpdate", "useState", "renderedStylesSize", "changed", "c", "delete", "dplId", "NEXT_DEPLOYMENT_ID", "map", "i", "link", "rel", "precedence"], "mappings": ";;;;;;;AAEA,OAAOA,SACLC,GAAG,EACHC,SAAS,EACTC,OAAO,EACPC,eAAe,EACfC,kBAAkB,EAClBC,gBAAgB,QACX,QAAO;AACd,SACEC,gBAAgB,EAChBC,mBAAmB,EACnBC,yBAAyB,QACpB,qDAAoD;AAE3D,SAASC,cAAc,QAAQ,wCAAuC;AAEtE,SAASC,iBAAiB,QAAQ,wCAAuC;AACzE,SACEC,mBAAmB,EACnBC,eAAe,EACfC,iBAAiB,QACZ,uDAAsD;AAC7D,SAASC,uBAAuB,EAAEC,cAAc,QAAQ,qBAAoB;AAC5E,SACEC,WAAWC,kBAAkB,EAC7BC,aAAa,QAER,mBAAkB;;AACzB,SAASC,KAAK,QAAQ,uCAAsC;AAC5D,SAASC,WAAW,QAAQ,mBAAkB;AAC9C,SAASC,kBAAkB,QAAQ,yBAAwB;AAC3D,SAASC,gBAAgB,QAAQ,sBAAqB;AACtD,SAASC,eAAe,QAAQ,+CAA8C;AAC9E,SAASC,kBAAkB,QAAQ,wBAAuB;AAC1D,SAASC,cAAc,QAAQ,sBAAqB;AACpD,SAASC,WAAW,QAAQ,mBAAkB;AAC9C,SAASC,iBAAiB,QAAQ,wCAAuC;AAEzE,SAASC,oBAAoB,QAAQ,wBAAuB;AAC5D,SACEC,sBAAsB,EACtBC,uBAAuB,QAElB,wBAAuB;AAC9B,SAASC,wBAAwB,EAAEC,uBAAuB,QAAQ,aAAY;AAC9E,SAASC,eAAe,EAAEC,YAAY,QAAQ,mBAAkB;AAChE,SAASC,gBAAgB,QAAQ,UAAS;AAhD1C;;;;;;;;;;;;;;;;;;;;;;;AAkDA,MAAMC,gBAEF,CAAC;AAEE,SAASC,cAAcC,GAAQ;IACpC,OAAOA,IAAIC,MAAM,KAAKC,OAAOC,QAAQ,CAACF,MAAM;AAC9C;AASO,SAASG,kBAAkBC,IAAY;IAC5C,kDAAkD;IAClD,qNAAIxB,QAAAA,EAAMqB,OAAOI,SAAS,CAACC,SAAS,GAAG;QACrC,OAAO;IACT;IAEA,IAAIP;IACJ,IAAI;QACFA,MAAM,IAAIQ,qLAAI1B,cAAAA,EAAYuB,OAAOH,OAAOC,QAAQ,CAACE,IAAI;IACvD,EAAE,OAAOI,GAAG;QACV,2EAA2E;QAC3E,kDAAkD;QAClD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,sBAAmBL,OAAK,+CADrB,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,uEAAuE;IACvE,IAAIM,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;QAC1C,OAAO;IACT;;AAQF;AAEA,SAASC,eAAe,KAIvB;IAJuB,IAAA,EACtBC,cAAc,EAGf,GAJuB;IAKtBjD,4LAAAA,EAAmB;QACjB,IAAI6C,QAAQC,GAAG,CAACI,uBAA8B,KAAF;;QAI5C;QAEA,MAAM,EAAEI,IAAI,EAAEC,OAAO,EAAEC,YAAY,EAAE,GAAGP;QACxC,MAAMQ,eAAe;YACnB,GAAIF,QAAQG,0BAA0B,GAAGtB,OAAOuB,OAAO,CAACC,KAAK,GAAG,CAAC,CAAC;YAClE,yCAAyC;YACzC,kFAAkF;YAClF,iFAAiF;YACjFC,MAAM;YACNC,iCAAiCR;QACnC;QACA,IACEC,QAAQQ,WAAW,IACnB,+FAA+F;QAC/F,2DAA2D;qOAC3DzD,qBAAAA,EAAkB,IAAIoC,IAAIN,OAAOC,QAAQ,CAACE,IAAI,OAAOiB,cACrD;YACA,qJAAqJ;YACrJD,QAAQQ,WAAW,GAAG;YACtB3B,OAAOuB,OAAO,CAACK,SAAS,CAACP,cAAc,IAAID;QAC7C,OAAO;YACLpB,OAAOuB,OAAO,CAACM,YAAY,CAACR,cAAc,IAAID;QAChD;IACF,GAAG;QAACP;KAAe;2KAEnBpD,YAAAA,EAAU;QACR,qEAAqE;QACrE,uEAAuE;QACvE,mEAAmE;QACnE,8DAA8D;QAC9D,IAAIgD,QAAQC,GAAG,CAACoB,uBAA6B,IAAF;;QAE3C;IACF,GAAG;QAACjB,eAAekB,OAAO;QAAElB,eAAeK,IAAI;KAAC;IAEhD,OAAO;AACT;AAEO,SAASc;IACd,OAAO;QACLC,UAAU;QACVC,KAAK;QACLC,aAAa;QACbC,MAAM;QACNC,cAAc;QACdC,gBAAgB,IAAIC;QACpBC,SAAS;QACTC,aAAa,CAAC;IAChB;AACF;AAEA,SAASC,+BAA+BC,IAAS;IAC/C,IAAIA,QAAQ,MAAMA,OAAO,CAAC;IAC1B,MAAMC,eAAe5C,OAAOuB,OAAO,CAACC,KAAK;IACzC,MAAMC,OAAOmB,gBAAAA,OAAAA,KAAAA,IAAAA,aAAcnB,IAAI;IAC/B,IAAIA,MAAM;QACRkB,KAAKlB,IAAI,GAAGA;IACd;IACA,MAAMC,kCACJkB,gBAAAA,OAAAA,KAAAA,IAAAA,aAAclB,+BAA+B;IAC/C,IAAIA,iCAAiC;QACnCiB,KAAKjB,+BAA+B,GAAGA;IACzC;IAEA,OAAOiB;AACT;AAEA,SAASE,KAAK,KAIb;IAJa,IAAA,EACZC,aAAa,EAGd,GAJa;IAKZ,6EAA6E;IAC7E,4EAA4E;IAC5E,kDAAkD;IAClD,MAAMV,OAAOU,kBAAkB,OAAOA,cAAcV,IAAI,GAAG;IAC3D,MAAMC,eACJS,kBAAkB,OAAOA,cAAcT,YAAY,GAAG;IAExD,6EAA6E;IAC7E,MAAMU,sBAAsBV,iBAAiB,OAAOA,eAAeD;IAEnE,2EAA2E;IAC3E,2EAA2E;IAC3E,sCAAsC;IACtC,8KAAOvE,mBAAAA,EAAiBuE,MAAMW;AAChC;AAEA;;CAEC,GACD,SAASC,OAAO,KAQf;IARe,IAAA,EACdC,WAAW,EACXC,WAAW,EACXC,WAAW,EAKZ,GARe;IASd,MAAM3B,yMAAQjD,kBAAAA,EAAe0E;IAC7B,MAAM,EAAE7B,YAAY,EAAE,GAAGI;IACzB,mEAAmE;IACnE,MAAM,EAAE4B,YAAY,EAAEC,QAAQ,EAAE,0KAAG3F,UAAAA,EAAQ;QACzC,MAAMoC,MAAM,IAAIQ,IACdc,cACA,OAAOpB,WAAW,cAAc,aAAaA,OAAOC,QAAQ,CAACE,IAAI;QAGnE,OAAO;YACL,4DAA4D;YAC5DiD,cAActD,IAAIsD,YAAY;YAC9BC,2LAAUnE,cAAAA,EAAYY,IAAIuD,QAAQ,IAC9BpE,qMAAAA,EAAea,IAAIuD,QAAQ,IAC3BvD,IAAIuD,QAAQ;QAClB;IACF,GAAG;QAACjC;KAAa;IAEjB,IAAIX,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,sDAAsD;QACtD,MAAM,EAAE2C,KAAK,EAAEC,aAAa,EAAErC,IAAI,EAAE,GAAGM;QAEvC,4FAA4F;QAC5F,sDAAsD;+KACtD/D,YAAAA,EAAU;YACR,0CAA0C;YAC1C,uGAAuG;YACvG,mCAAmC;YACnCuC,OAAOwD,EAAE,GAAG;gBACVC,yMAAQnE,0BAAAA;gBACRgE;gBACAC;gBACArC;YACF;QACF,GAAG;YAACoC;YAAOC;YAAerC;SAAK;IACjC;2KAEAzD,YAAAA,EAAU;QACR,0DAA0D;QAC1D,uFAAuF;QACvF,qEAAqE;QACrE,wGAAwG;QACxG,SAASiG,eAAeC,KAA0B;gBAG7C3D;YAFH,IACE,CAAC2D,MAAMC,SAAS,IAChB,CAAA,CAAA,CAAC5D,wBAAAA,OAAOuB,OAAO,CAACC,KAAK,KAAA,OAAA,KAAA,IAApBxB,sBAAsB0B,+BAA+B,GACtD;gBACA;YACF;YAEA,uGAAuG;YACvG,qHAAqH;YACrH,8BAA8B;YAC9B9B,cAAciE,cAAc,GAAG5C;8MAE/B3C,0BAAAA,EAAwB;gBACtBwF,6NAAM7F,iBAAAA;gBACN6B,KAAK,IAAIQ,IAAIN,OAAOC,QAAQ,CAACE,IAAI;gBACjCe,MAAMlB,OAAOuB,OAAO,CAACC,KAAK,CAACE,+BAA+B;YAC5D;QACF;QAEA1B,OAAO+D,gBAAgB,CAAC,YAAYL;QAEpC,OAAO;YACL1D,OAAOgE,mBAAmB,CAAC,YAAYN;QACzC;IACF,GAAG,EAAE;2KAELjG,YAAAA,EAAU;QACR,iFAAiF;QACjF,wCAAwC;QACxC,SAASwG,wBACPN,KAAyC;YAEzC,MAAMO,QAAQ,YAAYP,QAAQA,MAAMQ,MAAM,GAAGR,MAAMO,KAAK;YAC5D,iMAAIzE,kBAAAA,EAAgByE,QAAQ;gBAC1BP,MAAMS,cAAc;gBACpB,MAAMtE,yLAAMN,2BAAAA,EAAwB0E;gBACpC,MAAMG,mMAAe9E,2BAAAA,EAAyB2E;gBAC9C,oEAAoE;gBACpE,mCAAmC;gBACnC,IAAIG,0MAAiB3E,eAAAA,CAAa4E,IAAI,EAAE;oBACtChF,2NAAAA,CAAwBgF,IAAI,CAACxE,KAAK,CAAC;gBACrC,OAAO;qNACLR,0BAAAA,CAAwBiF,OAAO,CAACzE,KAAK,CAAC;gBACxC;YACF;QACF;QACAE,OAAO+D,gBAAgB,CAAC,SAASE;QACjCjE,OAAO+D,gBAAgB,CAAC,sBAAsBE;QAE9C,OAAO;YACLjE,OAAOgE,mBAAmB,CAAC,SAASC;YACpCjE,OAAOgE,mBAAmB,CAAC,sBAAsBC;QACnD;IACF,GAAG,EAAE;IAEL,sEAAsE;IACtE,0EAA0E;IAC1E,wEAAwE;IACxE,6EAA6E;IAC7E,YAAY;IACZ,EAAE;IACF,sEAAsE;IACtE,6EAA6E;IAC7E,6EAA6E;IAC7E,uBAAuB;IACvB,MAAM,EAAE9C,OAAO,EAAE,GAAGK;IACpB,IAAIL,QAAQqD,aAAa,EAAE;QACzB,gHAAgH;QAChH,IAAI5E,cAAciE,cAAc,KAAKzC,cAAc;YACjD,MAAMnB,WAAWD,OAAOC,QAAQ;YAChC,IAAIkB,QAAQQ,WAAW,EAAE;gBACvB1B,SAASwE,MAAM,CAACrD;YAClB,OAAO;gBACLnB,SAASsE,OAAO,CAACnD;YACnB;YAEAxB,cAAciE,cAAc,GAAGzC;QACjC;QACA,mEAAmE;QACnE,4EAA4E;QAC5E,+BAA+B;+KAC/B5D,MAAAA,gMAAIwB,qBAAAA;IACN;2KAEAvB,YAAAA,EAAU;QACR,MAAMiH,oBAAoB1E,OAAOuB,OAAO,CAACK,SAAS,CAAC+C,IAAI,CAAC3E,OAAOuB,OAAO;QACtE,MAAMqD,uBAAuB5E,OAAOuB,OAAO,CAACM,YAAY,CAAC8C,IAAI,CAC3D3E,OAAOuB,OAAO;QAGhB,wJAAwJ;QACxJ,MAAMsD,iCAAiC,CACrC/E;gBAIEE;YAFF,MAAMG,OAAOH,OAAOC,QAAQ,CAACE,IAAI;YACjC,MAAMe,OAAAA,CACJlB,wBAAAA,OAAOuB,OAAO,CAACC,KAAK,KAAA,OAAA,KAAA,IAApBxB,sBAAsB0B,+BAA+B;gBAEvD/D,qLAAAA,EAAgB;kNACdW,0BAAAA,EAAwB;oBACtBwF,6NAAM7F,iBAAAA;oBACN6B,KAAK,IAAIQ,IAAIR,OAAAA,OAAAA,MAAOK,MAAMA;oBAC1Be;gBACF;YACF;QACF;QAEA;;;;KAIC,GACDlB,OAAOuB,OAAO,CAACK,SAAS,GAAG,SAASA,UAClCe,IAAS,EACTmC,OAAe,EACfhF,GAAyB;YAEzB,qEAAqE;YACrE,IAAI6C,CAAAA,QAAAA,OAAAA,KAAAA,IAAAA,KAAMlB,IAAI,KAAA,CAAIkB,QAAAA,OAAAA,KAAAA,IAAAA,KAAMoC,EAAE,GAAE;gBAC1B,OAAOL,kBAAkB/B,MAAMmC,SAAShF;YAC1C;YAEA6C,OAAOD,+BAA+BC;YAEtC,IAAI7C,KAAK;gBACP+E,+BAA+B/E;YACjC;YAEA,OAAO4E,kBAAkB/B,MAAMmC,SAAShF;QAC1C;QAEA;;;;KAIC,GACDE,OAAOuB,OAAO,CAACM,YAAY,GAAG,SAASA,aACrCc,IAAS,EACTmC,OAAe,EACfhF,GAAyB;YAEzB,qEAAqE;YACrE,IAAI6C,CAAAA,QAAAA,OAAAA,KAAAA,IAAAA,KAAMlB,IAAI,KAAA,CAAIkB,QAAAA,OAAAA,KAAAA,IAAAA,KAAMoC,EAAE,GAAE;gBAC1B,OAAOH,qBAAqBjC,MAAMmC,SAAShF;YAC7C;YACA6C,OAAOD,+BAA+BC;YAEtC,IAAI7C,KAAK;gBACP+E,+BAA+B/E;YACjC;YACA,OAAO8E,qBAAqBjC,MAAMmC,SAAShF;QAC7C;QAEA;;;;KAIC,GACD,MAAMkF,aAAa,CAACrB;YAClB,IAAI,CAACA,MAAMnC,KAAK,EAAE;gBAChB,+IAA+I;gBAC/I;YACF;YAEA,6EAA6E;YAC7E,IAAI,CAACmC,MAAMnC,KAAK,CAACC,IAAI,EAAE;gBACrBzB,OAAOC,QAAQ,CAACgF,MAAM;gBACtB;YACF;YAEA,gHAAgH;YAChH,oEAAoE;mLACpEtH,kBAAAA,EAAgB;gBACd0B,8NAAAA,EACEW,OAAOC,QAAQ,CAACE,IAAI,EACpBwD,MAAMnC,KAAK,CAACE,+BAA+B;YAE/C;QACF;QAEA,8CAA8C;QAC9C1B,OAAO+D,gBAAgB,CAAC,YAAYiB;QACpC,OAAO;YACLhF,OAAOuB,OAAO,CAACK,SAAS,GAAG8C;YAC3B1E,OAAOuB,OAAO,CAACM,YAAY,GAAG+C;YAC9B5E,OAAOgE,mBAAmB,CAAC,YAAYgB;QACzC;IACF,GAAG,EAAE;IAEL,MAAM,EAAE1B,KAAK,EAAEpC,IAAI,EAAEa,OAAO,EAAEmD,iBAAiB,EAAE,GAAG1D;IAEpD,MAAM2D,sLAAezH,UAAAA,EAAQ;QAC3B,+OAAOqB,kBAAAA,EAAgBuE,OAAOpC,IAAI,CAAC,EAAE;IACvC,GAAG;QAACoC;QAAOpC;KAAK;IAEhB,yCAAyC;IACzC,MAAMkE,oLAAa1H,UAAAA,EAAQ;QACzB,kOAAOyB,oBAAAA,EAAkB+B;IAC3B,GAAG;QAACA;KAAK;IAET,MAAMmE,0BAAsB3H,6KAAAA,EAAQ;QAClC,OAAO;YACL4H,YAAYpE;YACZqE,iBAAiBjC;YACjBkC,mBAAmB;YACnB,6BAA6B;YAC7B,8EAA8E;YAC9E1F,KAAKsB;QACP;IACF,GAAG;QAACF;QAAMoC;QAAOlC;KAAa;IAE9B,MAAMqE,mMAA4B/H,UAAAA,EAAQ;QACxC,OAAO;YACLwD;YACAgE;YACAnD;QACF;IACF,GAAG;QAACb;QAAMgE;QAAmBnD;KAAQ;IAErC,IAAIK;IACJ,IAAI+C,iBAAiB,MAAM;QACzB,0DAA0D;QAC1D,0EAA0E;QAC1E,oEAAoE;QACpE,EAAE;QACF,wEAAwE;QACxE,uBAAuB;QACvB,MAAM,CAACrC,eAAe4C,QAAQ,GAAGP;QACjC/C,OAAAA,WAAAA,mLAAO,MAAA,EAACS,MAAAA;YAAmBC,eAAeA;WAAxB4C;IACpB,OAAO;QACLtD,OAAO;IACT;IAEA,IAAIuD,UAAAA,WAAAA,mLACF,OAAA,8LAAC7G,mBAAAA,EAAAA;;YACEsD;YACAkB,MAAMpB,GAAG;0MACV,MAAA,oMAACrD,qBAAAA,EAAAA;gBAAmBqC,MAAMA;;;;IAI9B,IAAIT,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,kEAAkE;QAClE,iGAAiG;QACjG,iBAAiB;QACjB,8CAA8C;QAC9C,wBAAwB;QACxB,kEAAkE;QAClE,IAAI,OAAOX,WAAW,aAAa;YACjC,MAAM,EAAE4F,iCAAiC,EAAE,GACzCC,QAAQ;YACVF,UAAAA,WAAAA,GACE,sLAAA,EAACC,mCAAAA;0BACED;;QAGP;QACA,MAAMG,cACJD,QAAQ,mJAA+CrH,OAAO;QAEhEmH,UAAAA,WAAAA,OACE,kLAAA,EAACG,aAAAA;YAAY5C,aAAaA;YAAaC,aAAaA;sBACjDwC;;IAGP,OAAO;;IAUP;IAEA,OAAA,WAAA,IACE,sLAAA,EAAA,2KAAA,CAAA,WAAA,EAAA;;2BACE,qLAAA,EAAC/E,gBAAAA;gBAAeC,gBAAgBW;;0MAChC,MAAA,EAACyE,eAAAA,CAAAA;0BACD,sLAAA,2MAAC5H,oBAAAA,CAAkB6H,QAAQ,EAAA;gBAACC,OAAOf;0BACjC,WAAA,mLAAA,MAAA,2MAAChH,kBAAAA,CAAgB8H,QAAQ,EAAA;oBAACC,OAAO9C;8BAC/B,WAAA,mLAAA,MAAA,2MAAClF,sBAAAA,CAAoB+H,QAAQ,EAAA;wBAACC,OAAO/C;kCACnC,WAAA,mLAAA,MAAA,yMAACpF,4BAAAA,CAA0BkI,QAAQ,EAAA;4BACjCC,OAAOV;sCAOP,WAAA,OAAA,kLAAA,yMAAC3H,mBAAAA,CAAiBoI,QAAQ,EAAA;gCAACC,wMAAO7G,0BAAAA;0CAChC,WAAA,IAAA,qLAAA,yMAACvB,sBAAAA,CAAoBmI,QAAQ,EAAA;oCAACC,OAAOd;8CAClCM;;;;;;;;;AASnB;AAEe,SAASS,UAAU,KAQjC;IARiC,IAAA,EAChCnD,WAAW,EACXoD,+BAA+B,CAACC,sBAAsBC,kBAAkB,EACxErD,WAAW,EAKZ,GARiC;yMAShC9D,uBAAAA;IAEA,OAAA,WAAA,mLACE,MAAA,2LAACV,gBAAAA,EAAAA;QACC,sFAAsF;QACtF,uGAAuG;QACvGqH,yMAAgBtH,UAAAA;kBAEhB,WAAA,mLAAA,MAAA,EAACuE,QAAAA;YACCC,aAAaA;YACbC,aAAaA;YACbC,aAAa;gBAACmD;gBAAsBC;aAAkB;;;AAI9D;AAEA,MAAMC,gBAAgB,IAAIC;AAC1B,IAAIC,sBAAsB,IAAID;AAE9BE,WAAWC,eAAe,GAAG,SAAUzG,IAAY;IACjD,IAAI0G,MAAML,cAAcM,IAAI;IAC5BN,cAAcO,GAAG,CAAC5G;IAClB,IAAIqG,cAAcM,IAAI,KAAKD,KAAK;QAC9BH,oBAAoBM,OAAO,CAAC,CAACC,KAAOA;IACtC;IACA,4CAA4C;IAC5C,gFAAgF;IAChF,OAAOC,QAAQC,OAAO;AACxB;AAEA,SAASlB;IACP,MAAM,GAAGmB,YAAY,sKAAG7J,UAAAA,CAAM8J,QAAQ,CAAC;IACvC,MAAMC,qBAAqBd,cAAcM,IAAI;2KAC7CrJ,YAAAA,EAAU;QACR,MAAM8J,UAAU,IAAMH,YAAY,CAACI,IAAMA,IAAI;QAC7Cd,oBAAoBK,GAAG,CAACQ;QACxB,IAAID,uBAAuBd,cAAcM,IAAI,EAAE;YAC7CS;QACF;QACA,OAAO;YACLb,oBAAoBe,MAAM,CAACF;QAC7B;IACF,GAAG;QAACD;QAAoBF;KAAY;IAEpC,MAAMM,QAAQjH,QAAQC,GAAG,CAACiH,kBAAkB,GACxC,AAAC,UAAOlH,QAAQC,GAAG,CAACiH,kBAAkB,IACtC;IACJ,OAAO;WAAInB;KAAc,CAACoB,GAAG,CAAC,CAACzH,MAAM0H,IAAAA,WAAAA,mLACnC,MAAA,EAACC,QAAAA;YAECC,KAAI;YACJ5H,MAAO,KAAEA,OAAOuH;YAChB,aAAa;YACbM,YAAW;WAJNH;AAUX", "ignoreList": [0]}}, {"offset": {"line": 4004, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/esm/client/components/error-boundary.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/error-boundary.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 4011, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/esm/client/components/error-boundary.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/error-boundary.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 4019, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/error-boundary.tsx"], "sourcesContent": ["'use client'\n\nimport React, { type JSX } from 'react'\nimport { useUntrackedPathname } from './navigation-untracked'\nimport { isNextRouterError } from './is-next-router-error'\nimport { handleHardNavError } from './nav-failure-handler'\n\nconst workAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n      ).workAsyncStorage\n    : undefined\n\nconst styles = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  text: {\n    fontSize: '14px',\n    fontWeight: 400,\n    lineHeight: '28px',\n    margin: '0 8px',\n  },\n} as const\n\nexport type ErrorComponent = React.ComponentType<{\n  error: Error\n  // global-error, there's no `reset` function;\n  // regular error boundary, there's a `reset` function.\n  reset?: () => void\n}>\n\nexport interface ErrorBoundaryProps {\n  children?: React.ReactNode\n  errorComponent: ErrorComponent | undefined\n  errorStyles?: React.ReactNode | undefined\n  errorScripts?: React.ReactNode | undefined\n}\n\ninterface ErrorBoundaryHandlerProps extends ErrorBoundaryProps {\n  pathname: string | null\n  errorComponent: ErrorComponent\n}\n\ninterface ErrorBoundaryHandlerState {\n  error: Error | null\n  previousPathname: string | null\n}\n\n// if we are revalidating we want to re-throw the error so the\n// function crashes so we can maintain our previous cache\n// instead of caching the error page\nfunction HandleISRError({ error }: { error: any }) {\n  if (workAsyncStorage) {\n    const store = workAsyncStorage.getStore()\n    if (store?.isRevalidate || store?.isStaticGeneration) {\n      console.error(error)\n      throw error\n    }\n  }\n\n  return null\n}\n\nexport class ErrorBoundaryHandler extends React.Component<\n  ErrorBoundaryHandlerProps,\n  ErrorBoundaryHandlerState\n> {\n  constructor(props: ErrorBoundaryHandlerProps) {\n    super(props)\n    this.state = { error: null, previousPathname: this.props.pathname }\n  }\n\n  static getDerivedStateFromError(error: Error) {\n    if (isNextRouterError(error)) {\n      // Re-throw if an expected internal Next.js router error occurs\n      // this means it should be handled by a different boundary (such as a NotFound boundary in a parent segment)\n      throw error\n    }\n\n    return { error }\n  }\n\n  static getDerivedStateFromProps(\n    props: ErrorBoundaryHandlerProps,\n    state: ErrorBoundaryHandlerState\n  ): ErrorBoundaryHandlerState | null {\n    const { error } = state\n\n    // if we encounter an error while\n    // a navigation is pending we shouldn't render\n    // the error boundary and instead should fallback\n    // to a hard navigation to attempt recovering\n    if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n      if (error && handleHardNavError(error)) {\n        // clear error so we don't render anything\n        return {\n          error: null,\n          previousPathname: props.pathname,\n        }\n      }\n    }\n\n    /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */\n    if (props.pathname !== state.previousPathname && state.error) {\n      return {\n        error: null,\n        previousPathname: props.pathname,\n      }\n    }\n    return {\n      error: state.error,\n      previousPathname: props.pathname,\n    }\n  }\n\n  reset = () => {\n    this.setState({ error: null })\n  }\n\n  // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific to the `@types/react` version.\n  render(): React.ReactNode {\n    if (this.state.error) {\n      return (\n        <>\n          <HandleISRError error={this.state.error} />\n          {this.props.errorStyles}\n          {this.props.errorScripts}\n          <this.props.errorComponent\n            error={this.state.error}\n            reset={this.reset}\n          />\n        </>\n      )\n    }\n\n    return this.props.children\n  }\n}\n\nexport type GlobalErrorComponent = React.ComponentType<{\n  error: any\n}>\nexport function GlobalError({ error }: { error: any }) {\n  const digest: string | undefined = error?.digest\n  return (\n    <html id=\"__next_error__\">\n      <head></head>\n      <body>\n        <HandleISRError error={error} />\n        <div style={styles.error}>\n          <div>\n            <h2 style={styles.text}>\n              Application error: a {digest ? 'server' : 'client'}-side exception\n              has occurred while loading {window.location.hostname} (see the{' '}\n              {digest ? 'server logs' : 'browser console'} for more\n              information).\n            </h2>\n            {digest ? <p style={styles.text}>{`Digest: ${digest}`}</p> : null}\n          </div>\n        </div>\n      </body>\n    </html>\n  )\n}\n\n// Exported so that the import signature in the loaders can be identical to user\n// supplied custom global error signatures.\nexport default GlobalError\n\n/**\n * Handles errors through `getDerivedStateFromError`.\n * Renders the provided error component and provides a way to `reset` the error boundary state.\n */\n\n/**\n * Renders error boundary with the provided \"errorComponent\" property as the fallback.\n * If no \"errorComponent\" property is provided it renders the children without an error boundary.\n */\nexport function ErrorBoundary({\n  errorComponent,\n  errorStyles,\n  errorScripts,\n  children,\n}: ErrorBoundaryProps & {\n  children: React.ReactNode\n}): JSX.Element {\n  // When we're rendering the missing params shell, this will return null. This\n  // is because we won't be rendering any not found boundaries or error\n  // boundaries for the missing params shell. When this runs on the client\n  // (where these errors can occur), we will get the correct pathname.\n  const pathname = useUntrackedPathname()\n  if (errorComponent) {\n    return (\n      <ErrorBoundaryHandler\n        pathname={pathname}\n        errorComponent={errorComponent}\n        errorStyles={errorStyles}\n        errorScripts={errorScripts}\n      >\n        {children}\n      </ErrorBoundaryHandler>\n    )\n  }\n\n  return <>{children}</>\n}\n"], "names": ["React", "useUntrackedPathname", "isNextRouterError", "handleHardNavError", "workAsyncStorage", "window", "require", "undefined", "styles", "error", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "text", "fontSize", "fontWeight", "lineHeight", "margin", "HandleISRError", "store", "getStore", "isRevalidate", "isStaticGeneration", "console", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Component", "getDerivedStateFromError", "getDerivedStateFromProps", "props", "state", "process", "env", "__NEXT_APP_NAV_FAIL_HANDLING", "previousPathname", "pathname", "render", "errorStyles", "errorScripts", "this", "errorComponent", "reset", "children", "constructor", "setState", "GlobalError", "digest", "html", "id", "head", "body", "div", "style", "h2", "location", "hostname", "p", "Error<PERSON>ou<PERSON><PERSON>"], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 4033, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/esm/client/components/layout-router.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/layout-router.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 4040, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/esm/client/components/layout-router.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/layout-router.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 4048, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/layout-router.tsx"], "sourcesContent": ["'use client'\n\nimport type {\n  <PERSON>ache<PERSON>ode,\n  LazyCacheNode,\n  LoadingModuleData,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../server/app-render/types'\nimport type { ErrorComponent } from './error-boundary'\nimport {\n  ACTION_SERVER_PATCH,\n  type FocusAndScrollRef,\n} from './router-reducer/router-reducer-types'\n\nimport React, {\n  useContext,\n  use,\n  startTransition,\n  Suspense,\n  useDeferredValue,\n  type JSX,\n} from 'react'\nimport ReactDOM from 'react-dom'\nimport {\n  LayoutRouterContext,\n  GlobalLayoutRouterContext,\n  TemplateContext,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport { fetchServerResponse } from './router-reducer/fetch-server-response'\nimport { unresolvedThenable } from './unresolved-thenable'\nimport { ErrorBoundary } from './error-boundary'\nimport { matchSegment } from './match-segments'\nimport { handleSmoothScroll } from '../../shared/lib/router/utils/handle-smooth-scroll'\nimport { RedirectBoundary } from './redirect-boundary'\nimport { HTTPAccessFallbackBoundary } from './http-access-fallback/error-boundary'\nimport { createRouterCacheKey } from './router-reducer/create-router-cache-key'\nimport { hasInterceptionRouteInCurrentTree } from './router-reducer/reducers/has-interception-route-in-current-tree'\nimport { dispatchAppRouterAction } from './use-action-queue'\n\n/**\n * Add refetch marker to router state at the point of the current layout segment.\n * This ensures the response returned is not further down than the current layout segment.\n */\nfunction walkAddRefetch(\n  segmentPathToWalk: FlightSegmentPath | undefined,\n  treeToRecreate: FlightRouterState\n): FlightRouterState {\n  if (segmentPathToWalk) {\n    const [segment, parallelRouteKey] = segmentPathToWalk\n    const isLast = segmentPathToWalk.length === 2\n\n    if (matchSegment(treeToRecreate[0], segment)) {\n      if (treeToRecreate[1].hasOwnProperty(parallelRouteKey)) {\n        if (isLast) {\n          const subTree = walkAddRefetch(\n            undefined,\n            treeToRecreate[1][parallelRouteKey]\n          )\n          return [\n            treeToRecreate[0],\n            {\n              ...treeToRecreate[1],\n              [parallelRouteKey]: [\n                subTree[0],\n                subTree[1],\n                subTree[2],\n                'refetch',\n              ],\n            },\n          ]\n        }\n\n        return [\n          treeToRecreate[0],\n          {\n            ...treeToRecreate[1],\n            [parallelRouteKey]: walkAddRefetch(\n              segmentPathToWalk.slice(2),\n              treeToRecreate[1][parallelRouteKey]\n            ),\n          },\n        ]\n      }\n    }\n  }\n\n  return treeToRecreate\n}\n\nconst __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE = (\n  ReactDOM as any\n).__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE\n\n// TODO-APP: Replace with new React API for finding dom nodes without a `ref` when available\n/**\n * Wraps ReactDOM.findDOMNode with additional logic to hide React Strict Mode warning\n */\nfunction findDOMNode(\n  instance: React.ReactInstance | null | undefined\n): Element | Text | null {\n  // Tree-shake for server bundle\n  if (typeof window === 'undefined') return null\n\n  // __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode is null during module init.\n  // We need to lazily reference it.\n  const internal_reactDOMfindDOMNode =\n    __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode\n  return internal_reactDOMfindDOMNode(instance)\n}\n\nconst rectProperties = [\n  'bottom',\n  'height',\n  'left',\n  'right',\n  'top',\n  'width',\n  'x',\n  'y',\n] as const\n/**\n * Check if a HTMLElement is hidden or fixed/sticky position\n */\nfunction shouldSkipElement(element: HTMLElement) {\n  // we ignore fixed or sticky positioned elements since they'll likely pass the \"in-viewport\" check\n  // and will result in a situation we bail on scroll because of something like a fixed nav,\n  // even though the actual page content is offscreen\n  if (['sticky', 'fixed'].includes(getComputedStyle(element).position)) {\n    if (process.env.NODE_ENV === 'development') {\n      console.warn(\n        'Skipping auto-scroll behavior due to `position: sticky` or `position: fixed` on element:',\n        element\n      )\n    }\n    return true\n  }\n\n  // Uses `getBoundingClientRect` to check if the element is hidden instead of `offsetParent`\n  // because `offsetParent` doesn't consider document/body\n  const rect = element.getBoundingClientRect()\n  return rectProperties.every((item) => rect[item] === 0)\n}\n\n/**\n * Check if the top corner of the HTMLElement is in the viewport.\n */\nfunction topOfElementInViewport(element: HTMLElement, viewportHeight: number) {\n  const rect = element.getBoundingClientRect()\n  return rect.top >= 0 && rect.top <= viewportHeight\n}\n\n/**\n * Find the DOM node for a hash fragment.\n * If `top` the page has to scroll to the top of the page. This mirrors the browser's behavior.\n * If the hash fragment is an id, the page has to scroll to the element with that id.\n * If the hash fragment is a name, the page has to scroll to the first element with that name.\n */\nfunction getHashFragmentDomNode(hashFragment: string) {\n  // If the hash fragment is `top` the page has to scroll to the top of the page.\n  if (hashFragment === 'top') {\n    return document.body\n  }\n\n  // If the hash fragment is an id, the page has to scroll to the element with that id.\n  return (\n    document.getElementById(hashFragment) ??\n    // If the hash fragment is a name, the page has to scroll to the first element with that name.\n    document.getElementsByName(hashFragment)[0]\n  )\n}\ninterface ScrollAndFocusHandlerProps {\n  focusAndScrollRef: FocusAndScrollRef\n  children: React.ReactNode\n  segmentPath: FlightSegmentPath\n}\nclass InnerScrollAndFocusHandler extends React.Component<ScrollAndFocusHandlerProps> {\n  handlePotentialScroll = () => {\n    // Handle scroll and focus, it's only applied once in the first useEffect that triggers that changed.\n    const { focusAndScrollRef, segmentPath } = this.props\n\n    if (focusAndScrollRef.apply) {\n      // segmentPaths is an array of segment paths that should be scrolled to\n      // if the current segment path is not in the array, the scroll is not applied\n      // unless the array is empty, in which case the scroll is always applied\n      if (\n        focusAndScrollRef.segmentPaths.length !== 0 &&\n        !focusAndScrollRef.segmentPaths.some((scrollRefSegmentPath) =>\n          segmentPath.every((segment, index) =>\n            matchSegment(segment, scrollRefSegmentPath[index])\n          )\n        )\n      ) {\n        return\n      }\n\n      let domNode:\n        | ReturnType<typeof getHashFragmentDomNode>\n        | ReturnType<typeof findDOMNode> = null\n      const hashFragment = focusAndScrollRef.hashFragment\n\n      if (hashFragment) {\n        domNode = getHashFragmentDomNode(hashFragment)\n      }\n\n      // `findDOMNode` is tricky because it returns just the first child if the component is a fragment.\n      // This already caused a bug where the first child was a <link/> in head.\n      if (!domNode) {\n        domNode = findDOMNode(this)\n      }\n\n      // If there is no DOM node this layout-router level is skipped. It'll be handled higher-up in the tree.\n      if (!(domNode instanceof Element)) {\n        return\n      }\n\n      // Verify if the element is a HTMLElement and if we want to consider it for scroll behavior.\n      // If the element is skipped, try to select the next sibling and try again.\n      while (!(domNode instanceof HTMLElement) || shouldSkipElement(domNode)) {\n        if (process.env.NODE_ENV !== 'production') {\n          if (domNode.parentElement?.localName === 'head') {\n            // TODO: We enter this state when metadata was rendered as part of the page or via Next.js.\n            // This is always a bug in Next.js and caused by React hoisting metadata.\n            // We need to replace `findDOMNode` in favor of Fragment Refs (when available) so that we can skip over metadata.\n          }\n        }\n\n        // No siblings found that match the criteria are found, so handle scroll higher up in the tree instead.\n        if (domNode.nextElementSibling === null) {\n          return\n        }\n        domNode = domNode.nextElementSibling\n      }\n\n      // State is mutated to ensure that the focus and scroll is applied only once.\n      focusAndScrollRef.apply = false\n      focusAndScrollRef.hashFragment = null\n      focusAndScrollRef.segmentPaths = []\n\n      handleSmoothScroll(\n        () => {\n          // In case of hash scroll, we only need to scroll the element into view\n          if (hashFragment) {\n            ;(domNode as HTMLElement).scrollIntoView()\n\n            return\n          }\n          // Store the current viewport height because reading `clientHeight` causes a reflow,\n          // and it won't change during this function.\n          const htmlElement = document.documentElement\n          const viewportHeight = htmlElement.clientHeight\n\n          // If the element's top edge is already in the viewport, exit early.\n          if (topOfElementInViewport(domNode as HTMLElement, viewportHeight)) {\n            return\n          }\n\n          // Otherwise, try scrolling go the top of the document to be backward compatible with pages\n          // scrollIntoView() called on `<html/>` element scrolls horizontally on chrome and firefox (that shouldn't happen)\n          // We could use it to scroll horizontally following RTL but that also seems to be broken - it will always scroll left\n          // scrollLeft = 0 also seems to ignore RTL and manually checking for RTL is too much hassle so we will scroll just vertically\n          htmlElement.scrollTop = 0\n\n          // Scroll to domNode if domNode is not in viewport when scrolled to top of document\n          if (!topOfElementInViewport(domNode as HTMLElement, viewportHeight)) {\n            // Scroll into view doesn't scroll horizontally by default when not needed\n            ;(domNode as HTMLElement).scrollIntoView()\n          }\n        },\n        {\n          // We will force layout by querying domNode position\n          dontForceLayout: true,\n          onlyHashChange: focusAndScrollRef.onlyHashChange,\n        }\n      )\n\n      // Mutate after scrolling so that it can be read by `handleSmoothScroll`\n      focusAndScrollRef.onlyHashChange = false\n\n      // Set focus on the element\n      domNode.focus()\n    }\n  }\n\n  componentDidMount() {\n    this.handlePotentialScroll()\n  }\n\n  componentDidUpdate() {\n    // Because this property is overwritten in handlePotentialScroll it's fine to always run it when true as it'll be set to false for subsequent renders.\n    if (this.props.focusAndScrollRef.apply) {\n      this.handlePotentialScroll()\n    }\n  }\n\n  render() {\n    return this.props.children\n  }\n}\n\nfunction ScrollAndFocusHandler({\n  segmentPath,\n  children,\n}: {\n  segmentPath: FlightSegmentPath\n  children: React.ReactNode\n}) {\n  const context = useContext(GlobalLayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant global layout router not mounted')\n  }\n\n  return (\n    <InnerScrollAndFocusHandler\n      segmentPath={segmentPath}\n      focusAndScrollRef={context.focusAndScrollRef}\n    >\n      {children}\n    </InnerScrollAndFocusHandler>\n  )\n}\n\n/**\n * InnerLayoutRouter handles rendering the provided segment based on the cache.\n */\nfunction InnerLayoutRouter({\n  tree,\n  segmentPath,\n  cacheNode,\n  url,\n}: {\n  tree: FlightRouterState\n  segmentPath: FlightSegmentPath\n  cacheNode: CacheNode\n  url: string\n}) {\n  const context = useContext(GlobalLayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant global layout router not mounted')\n  }\n\n  const { tree: fullTree } = context\n\n  // `rsc` represents the renderable node for this segment.\n\n  // If this segment has a `prefetchRsc`, it's the statically prefetched data.\n  // We should use that on initial render instead of `rsc`. Then we'll switch\n  // to `rsc` when the dynamic response streams in.\n  //\n  // If no prefetch data is available, then we go straight to rendering `rsc`.\n  const resolvedPrefetchRsc =\n    cacheNode.prefetchRsc !== null ? cacheNode.prefetchRsc : cacheNode.rsc\n\n  // We use `useDeferredValue` to handle switching between the prefetched and\n  // final values. The second argument is returned on initial render, then it\n  // re-renders with the first argument.\n  const rsc: any = useDeferredValue(cacheNode.rsc, resolvedPrefetchRsc)\n\n  // `rsc` is either a React node or a promise for a React node, except we\n  // special case `null` to represent that this segment's data is missing. If\n  // it's a promise, we need to unwrap it so we can determine whether or not the\n  // data is missing.\n  const resolvedRsc: React.ReactNode =\n    typeof rsc === 'object' && rsc !== null && typeof rsc.then === 'function'\n      ? use(rsc)\n      : rsc\n\n  if (!resolvedRsc) {\n    // The data for this segment is not available, and there's no pending\n    // navigation that will be able to fulfill it. We need to fetch more from\n    // the server and patch the cache.\n\n    // Check if there's already a pending request.\n    let lazyData = cacheNode.lazyData\n    if (lazyData === null) {\n      /**\n       * Router state with refetch marker added\n       */\n      // TODO-APP: remove ''\n      const refetchTree = walkAddRefetch(['', ...segmentPath], fullTree)\n      const includeNextUrl = hasInterceptionRouteInCurrentTree(fullTree)\n      const navigatedAt = Date.now()\n      cacheNode.lazyData = lazyData = fetchServerResponse(\n        new URL(url, location.origin),\n        {\n          flightRouterState: refetchTree,\n          nextUrl: includeNextUrl ? context.nextUrl : null,\n        }\n      ).then((serverResponse) => {\n        startTransition(() => {\n          dispatchAppRouterAction({\n            type: ACTION_SERVER_PATCH,\n            previousTree: fullTree,\n            serverResponse,\n            navigatedAt,\n          })\n        })\n\n        return serverResponse\n      })\n\n      // Suspend while waiting for lazyData to resolve\n      use(lazyData)\n    }\n    // Suspend infinitely as `changeByServerResponse` will cause a different part of the tree to be rendered.\n    // A falsey `resolvedRsc` indicates missing data -- we should not commit that branch, and we need to wait for the data to arrive.\n    use(unresolvedThenable) as never\n  }\n\n  // If we get to this point, then we know we have something we can render.\n  const subtree = (\n    // The layout router context narrows down tree and childNodes at each level.\n    <LayoutRouterContext.Provider\n      value={{\n        parentTree: tree,\n        parentCacheNode: cacheNode,\n        parentSegmentPath: segmentPath,\n\n        // TODO-APP: overriding of url for parallel routes\n        url: url,\n      }}\n    >\n      {resolvedRsc}\n    </LayoutRouterContext.Provider>\n  )\n  // Ensure root layout is not wrapped in a div as the root layout renders `<html>`\n  return subtree\n}\n\n/**\n * Renders suspense boundary with the provided \"loading\" property as the fallback.\n * If no loading property is provided it renders the children without a suspense boundary.\n */\nfunction LoadingBoundary({\n  loading,\n  children,\n}: {\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n  children: React.ReactNode\n}): JSX.Element {\n  // If loading is a promise, unwrap it. This happens in cases where we haven't\n  // yet received the loading data from the server — which includes whether or\n  // not this layout has a loading component at all.\n  //\n  // It's OK to suspend here instead of inside the fallback because this\n  // promise will resolve simultaneously with the data for the segment itself.\n  // So it will never suspend for longer than it would have if we didn't use\n  // a Suspense fallback at all.\n  let loadingModuleData\n  if (\n    typeof loading === 'object' &&\n    loading !== null &&\n    typeof (loading as any).then === 'function'\n  ) {\n    const promiseForLoading = loading as Promise<LoadingModuleData>\n    loadingModuleData = use(promiseForLoading)\n  } else {\n    loadingModuleData = loading as LoadingModuleData\n  }\n\n  if (loadingModuleData) {\n    const loadingRsc = loadingModuleData[0]\n    const loadingStyles = loadingModuleData[1]\n    const loadingScripts = loadingModuleData[2]\n    return (\n      <Suspense\n        fallback={\n          <>\n            {loadingStyles}\n            {loadingScripts}\n            {loadingRsc}\n          </>\n        }\n      >\n        {children}\n      </Suspense>\n    )\n  }\n\n  return <>{children}</>\n}\n\n/**\n * OuterLayoutRouter handles the current segment as well as <Offscreen> rendering of other segments.\n * It can be rendered next to each other with a different `parallelRouterKey`, allowing for Parallel routes.\n */\nexport default function OuterLayoutRouter({\n  parallelRouterKey,\n  error,\n  errorStyles,\n  errorScripts,\n  templateStyles,\n  templateScripts,\n  template,\n  notFound,\n  forbidden,\n  unauthorized,\n}: {\n  parallelRouterKey: string\n  error: ErrorComponent | undefined\n  errorStyles: React.ReactNode | undefined\n  errorScripts: React.ReactNode | undefined\n  templateStyles: React.ReactNode | undefined\n  templateScripts: React.ReactNode | undefined\n  template: React.ReactNode\n  notFound: React.ReactNode | undefined\n  forbidden: React.ReactNode | undefined\n  unauthorized: React.ReactNode | undefined\n}) {\n  const context = useContext(LayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant expected layout router to be mounted')\n  }\n\n  const { parentTree, parentCacheNode, parentSegmentPath, url } = context\n\n  // Get the CacheNode for this segment by reading it from the parent segment's\n  // child map.\n  const parentParallelRoutes = parentCacheNode.parallelRoutes\n  let segmentMap = parentParallelRoutes.get(parallelRouterKey)\n  // If the parallel router cache node does not exist yet, create it.\n  // This writes to the cache when there is no item in the cache yet. It never *overwrites* existing cache items which is why it's safe in concurrent mode.\n  if (!segmentMap) {\n    segmentMap = new Map()\n    parentParallelRoutes.set(parallelRouterKey, segmentMap)\n  }\n\n  // Get the active segment in the tree\n  // The reason arrays are used in the data format is that these are transferred from the server to the browser so it's optimized to save bytes.\n  const parentTreeSegment = parentTree[0]\n  const tree = parentTree[1][parallelRouterKey]\n  const treeSegment = tree[0]\n\n  const segmentPath =\n    parentSegmentPath === null\n      ? // TODO: The root segment value is currently omitted from the segment\n        // path. This has led to a bunch of special cases scattered throughout\n        // the code. We should clean this up.\n        [parallelRouterKey]\n      : parentSegmentPath.concat([parentTreeSegment, parallelRouterKey])\n\n  // The \"state\" key of a segment is the one passed to React — it represents the\n  // identity of the UI tree. Whenever the state key changes, the tree is\n  // recreated and the state is reset. In the App Router model, search params do\n  // not cause state to be lost, so two segments with the same segment path but\n  // different search params should have the same state key.\n  //\n  // The \"cache\" key of a segment, however, *does* include the search params, if\n  // it's possible that the segment accessed the search params on the server.\n  // (This only applies to page segments; layout segments cannot access search\n  // params on the server.)\n  const cacheKey = createRouterCacheKey(treeSegment)\n  const stateKey = createRouterCacheKey(treeSegment, true) // no search params\n\n  // Read segment path from the parallel router cache node.\n  let cacheNode = segmentMap.get(cacheKey)\n  if (cacheNode === undefined) {\n    // When data is not available during rendering client-side we need to fetch\n    // it from the server.\n    const newLazyCacheNode: LazyCacheNode = {\n      lazyData: null,\n      rsc: null,\n      prefetchRsc: null,\n      head: null,\n      prefetchHead: null,\n      parallelRoutes: new Map(),\n      loading: null,\n      navigatedAt: -1,\n    }\n\n    // Flight data fetch kicked off during render and put into the cache.\n    cacheNode = newLazyCacheNode\n    segmentMap.set(cacheKey, newLazyCacheNode)\n  }\n\n  /*\n    - Error boundary\n      - Only renders error boundary if error component is provided.\n      - Rendered for each segment to ensure they have their own error state.\n    - Loading boundary\n      - Only renders suspense boundary if loading components is provided.\n      - Rendered for each segment to ensure they have their own loading state.\n      - Passed to the router during rendering to ensure it can be immediately rendered when suspending on a Flight fetch.\n  */\n\n  // TODO: The loading module data for a segment is stored on the parent, then\n  // applied to each of that parent segment's parallel route slots. In the\n  // simple case where there's only one parallel route (the `children` slot),\n  // this is no different from if the loading module data where stored on the\n  // child directly. But I'm not sure this actually makes sense when there are\n  // multiple parallel routes. It's not a huge issue because you always have\n  // the option to define a narrower loading boundary for a particular slot. But\n  // this sort of smells like an implementation accident to me.\n  const loadingModuleData = parentCacheNode.loading\n\n  return (\n    <TemplateContext.Provider\n      key={stateKey}\n      value={\n        <ScrollAndFocusHandler segmentPath={segmentPath}>\n          <ErrorBoundary\n            errorComponent={error}\n            errorStyles={errorStyles}\n            errorScripts={errorScripts}\n          >\n            <LoadingBoundary loading={loadingModuleData}>\n              <HTTPAccessFallbackBoundary\n                notFound={notFound}\n                forbidden={forbidden}\n                unauthorized={unauthorized}\n              >\n                <RedirectBoundary>\n                  <InnerLayoutRouter\n                    url={url}\n                    tree={tree}\n                    cacheNode={cacheNode}\n                    segmentPath={segmentPath}\n                  />\n                </RedirectBoundary>\n              </HTTPAccessFallbackBoundary>\n            </LoadingBoundary>\n          </ErrorBoundary>\n        </ScrollAndFocusHandler>\n      }\n    >\n      {templateStyles}\n      {templateScripts}\n      {template}\n    </TemplateContext.Provider>\n  )\n}\n"], "names": ["ACTION_SERVER_PATCH", "React", "useContext", "use", "startTransition", "Suspense", "useDeferredValue", "ReactDOM", "LayoutRouterContext", "GlobalLayoutRouterContext", "TemplateContext", "fetchServerResponse", "unresolvedThenable", "Error<PERSON>ou<PERSON><PERSON>", "matchSegment", "handleSmoothScroll", "RedirectBoundary", "HTTPAccessFallbackBoundary", "createRouterCache<PERSON>ey", "hasInterceptionRouteInCurrentTree", "dispatchAppRouterAction", "walkAddRefetch", "segmentPathToWalk", "treeToRecreate", "segment", "parallelRouteKey", "isLast", "length", "hasOwnProperty", "subTree", "undefined", "slice", "__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE", "findDOMNode", "instance", "window", "internal_reactDOMfindDOMNode", "rectProperties", "shouldSkipElement", "element", "includes", "getComputedStyle", "position", "process", "env", "NODE_ENV", "console", "warn", "rect", "getBoundingClientRect", "every", "item", "topOfElementInViewport", "viewportHeight", "top", "getHashFragmentDomNode", "hashFragment", "document", "body", "getElementById", "getElementsByName", "InnerScrollAndFocusHandler", "Component", "componentDidMount", "handlePotentialScroll", "componentDidUpdate", "props", "focusAndScrollRef", "apply", "render", "children", "segmentPath", "segmentPaths", "some", "scrollRefSegmentPath", "index", "domNode", "Element", "HTMLElement", "parentElement", "localName", "nextElement<PERSON><PERSON>ling", "scrollIntoView", "htmlElement", "documentElement", "clientHeight", "scrollTop", "dontForceLayout", "onlyHashChange", "focus", "ScrollAndFocusHandler", "context", "Error", "InnerLayoutRouter", "tree", "cacheNode", "url", "fullTree", "resolvedPrefetchRsc", "prefetchRsc", "rsc", "resolvedRsc", "then", "lazyData", "refetchTree", "includeNextUrl", "navigatedAt", "Date", "now", "URL", "location", "origin", "flightRouterState", "nextUrl", "serverResponse", "type", "previousTree", "subtree", "Provider", "value", "parentTree", "parentCacheNode", "parentSegmentPath", "LoadingBoundary", "loading", "loadingModuleData", "promiseForLoading", "loadingRsc", "loadingStyles", "loadingScripts", "fallback", "OuterLayoutRouter", "parallel<PERSON><PERSON>er<PERSON>ey", "error", "errorStyles", "errorScripts", "templateStyles", "templateScripts", "template", "notFound", "forbidden", "unauthorized", "parentParallelRoutes", "parallelRoutes", "segmentMap", "get", "Map", "set", "parentTreeSegment", "treeSegment", "concat", "cache<PERSON>ey", "stateKey", "newLazyCacheNode", "head", "prefetchHead", "errorComponent"], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 4057, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 4064, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 4072, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/render-from-template-context.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useContext, type JSX } from 'react'\nimport { TemplateContext } from '../../shared/lib/app-router-context.shared-runtime'\n\nexport default function RenderFromTemplateContext(): JSX.Element {\n  const children = useContext(TemplateContext)\n  return <>{children}</>\n}\n"], "names": ["React", "useContext", "TemplateContext", "RenderFromTemplateContext", "children"], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 4081, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/esm/client/components/client-page.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/client-page.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 4088, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/esm/client/components/client-page.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/client-page.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 4096, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/client-page.tsx"], "sourcesContent": ["'use client'\n\nimport type { ParsedUrlQuery } from 'querystring'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport type { Params } from '../../server/request/params'\n\n/**\n * When the Page is a client component we send the params and searchParams to this client wrapper\n * where they are turned into dynamically tracked values before being passed to the actual Page component.\n *\n * additionally we may send promises representing the params and searchParams. We don't ever use these passed\n * values but it can be necessary for the sender to send a Promise that doesn't resolve in certain situations.\n * It is up to the caller to decide if the promises are needed.\n */\nexport function ClientPageRoot({\n  Component,\n  searchParams,\n  params,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  promises,\n}: {\n  Component: React.ComponentType<any>\n  searchParams: ParsedUrlQuery\n  params: Params\n  promises?: Array<Promise<any>>\n}) {\n  if (typeof window === 'undefined') {\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    let clientSearchParams: Promise<ParsedUrlQuery>\n    let clientParams: Promise<Params>\n    // We are going to instrument the searchParams prop with tracking for the\n    // appropriate context. We wrap differently in prerendering vs rendering\n    const store = workAsyncStorage.getStore()\n    if (!store) {\n      throw new InvariantError(\n        'Expected workStore to exist when handling searchParams in a client Page.'\n      )\n    }\n\n    const { createSearchParamsFromClient } =\n      require('../../server/request/search-params') as typeof import('../../server/request/search-params')\n    clientSearchParams = createSearchParamsFromClient(searchParams, store)\n\n    const { createParamsFromClient } =\n      require('../../server/request/params') as typeof import('../../server/request/params')\n    clientParams = createParamsFromClient(params, store)\n\n    return <Component params={clientParams} searchParams={clientSearchParams} />\n  } else {\n    const { createRenderSearchParamsFromClient } =\n      require('../request/search-params.browser') as typeof import('../request/search-params.browser')\n    const clientSearchParams = createRenderSearchParamsFromClient(searchParams)\n    const { createRenderParamsFromClient } =\n      require('../request/params.browser') as typeof import('../request/params.browser')\n    const clientParams = createRenderParamsFromClient(params)\n\n    return <Component params={clientParams} searchParams={clientSearchParams} />\n  }\n}\n"], "names": ["InvariantError", "ClientPageRoot", "Component", "searchParams", "params", "promises", "window", "workAsyncStorage", "require", "clientSearchParams", "clientParams", "store", "getStore", "createSearchParamsFromClient", "createParamsFromClient", "createRenderSearchParamsFromClient", "createRenderParamsFromClient"], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 4105, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/esm/client/components/client-segment.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/client-segment.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 4112, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/esm/client/components/client-segment.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/client-segment.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 4120, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/client-segment.tsx"], "sourcesContent": ["'use client'\n\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport type { Params } from '../../server/request/params'\n\n/**\n * When the Page is a client component we send the params to this client wrapper\n * where they are turned into dynamically tracked values before being passed to the actual Segment component.\n *\n * additionally we may send a promise representing params. We don't ever use this passed\n * value but it can be necessary for the sender to send a Promise that doesn't resolve in certain situations\n * such as when dynamicIO is enabled. It is up to the caller to decide if the promises are needed.\n */\nexport function ClientSegmentRoot({\n  Component,\n  slots,\n  params,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  promise,\n}: {\n  Component: React.ComponentType<any>\n  slots: { [key: string]: React.ReactNode }\n  params: Params\n  promise?: Promise<any>\n}) {\n  if (typeof window === 'undefined') {\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    let clientParams: Promise<Params>\n    // We are going to instrument the searchParams prop with tracking for the\n    // appropriate context. We wrap differently in prerendering vs rendering\n    const store = workAsyncStorage.getStore()\n    if (!store) {\n      throw new InvariantError(\n        'Expected workStore to exist when handling params in a client segment such as a Layout or Template.'\n      )\n    }\n\n    const { createParamsFromClient } =\n      require('../../server/request/params') as typeof import('../../server/request/params')\n    clientParams = createParamsFromClient(params, store)\n\n    return <Component {...slots} params={clientParams} />\n  } else {\n    const { createRenderParamsFromClient } =\n      require('../request/params.browser') as typeof import('../request/params.browser')\n    const clientParams = createRenderParamsFromClient(params)\n    return <Component {...slots} params={clientParams} />\n  }\n}\n"], "names": ["InvariantError", "ClientSegmentRoot", "Component", "slots", "params", "promise", "window", "workAsyncStorage", "require", "clientParams", "store", "getStore", "createParamsFromClient", "createRenderParamsFromClient"], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 4129, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 4136, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 4144, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/metadata/async-metadata.tsx"], "sourcesContent": ["'use client'\n\nimport { Suspense, use } from 'react'\nimport type { StreamingMetadataResolvedState } from './types'\n\nexport const AsyncMetadata =\n  typeof window === 'undefined'\n    ? (\n        require('./server-inserted-metadata') as typeof import('./server-inserted-metadata')\n      ).ServerInsertMetadata\n    : (\n        require('./browser-resolved-metadata') as typeof import('./browser-resolved-metadata')\n      ).BrowserResolvedMetadata\n\nfunction MetadataOutlet({\n  promise,\n}: {\n  promise: Promise<StreamingMetadataResolvedState>\n}) {\n  const { error, digest } = use(promise)\n  if (error) {\n    if (digest) {\n      // The error will lose its original digest after passing from server layer to client layer；\n      // We recover the digest property here to override the React created one if original digest exists.\n      ;(error as any).digest = digest\n    }\n    throw error\n  }\n  return null\n}\n\nexport function AsyncMetadataOutlet({\n  promise,\n}: {\n  promise: Promise<StreamingMetadataResolvedState>\n}) {\n  return (\n    <Suspense fallback={null}>\n      <MetadataOutlet promise={promise} />\n    </Suspense>\n  )\n}\n"], "names": ["Suspense", "use", "AsyncMetadata", "window", "require", "ServerInsertMetadata", "BrowserResolvedMetadata", "MetadataOutlet", "promise", "error", "digest", "AsyncMetadataOutlet", "fallback"], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 4153, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 4160, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 4168, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/metadata/metadata-boundary.tsx"], "sourcesContent": ["'use client'\n\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../../lib/metadata/metadata-constants'\n\n// We use a namespace object to allow us to recover the name of the function\n// at runtime even when production bundling/minification is used.\nconst NameSpace = {\n  [METADATA_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: React.ReactNode\n  }) {\n    return children\n  },\n  [VIEWPORT_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: React.ReactNode\n  }) {\n    return children\n  },\n  [OUTLET_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: React.ReactNode\n  }) {\n    return children\n  },\n}\n\nexport const MetadataBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[METADATA_BOUNDARY_NAME.slice(0) as typeof METADATA_BOUNDARY_NAME]\n\nexport const ViewportBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[VIEWPORT_BOUNDARY_NAME.slice(0) as typeof VIEWPORT_BOUNDARY_NAME]\n\nexport const OutletBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[OUTLET_BOUNDARY_NAME.slice(0) as typeof OUTLET_BOUNDARY_NAME]\n"], "names": ["METADATA_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "NameSpace", "children", "MetadataBoundary", "slice", "ViewportBoundary", "OutletBoundary"], "mappings": "", "ignoreList": [0]}}]}