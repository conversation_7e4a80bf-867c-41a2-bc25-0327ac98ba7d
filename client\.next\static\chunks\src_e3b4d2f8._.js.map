{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/app/react-scan.tsx"], "sourcesContent": ["'use client';\r\nimport { useEffect, useState } from 'react';\r\nimport { scan } from 'react-scan';\r\n\r\nexport function ReactScan() {\r\n  const [isMounted, setIsMounted] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsMounted(true);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (isMounted) {\r\n      scan();\r\n    }\r\n  }, [isMounted]);\r\n  return null\r\n}"], "names": [], "mappings": ";;;AACA;AACA;;AAFA;;;AAIO,SAAS;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,aAAa;QACf;8BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,WAAW;gBACb,CAAA,GAAA,kJAAA,CAAA,OAAI,AAAD;YACL;QACF;8BAAG;QAAC;KAAU;IACd,OAAO;AACT;GAbgB;KAAA", "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/providers/Providers.tsx"], "sourcesContent": ["\"use client\"\r\n// Client providers:\r\nimport * as React from \"react\"\r\n// import { ThemeProvider as NextThemesProvider } from \"next-themes\"\r\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query'\r\n\r\nconst queryClient = new QueryClient()\r\n\r\nexport function Providers({\r\n  children,\r\n}: {\r\n  children: React.ReactNode\r\n}) {\r\n  return (\r\n    <>\r\n      {/* <NextThemesProvider\r\n        attribute=\"class\"\r\n        defaultTheme=\"system\"\r\n        enableSystem\r\n        disableTransitionOnChange\r\n      > */}\r\n        <QueryClientProvider client={queryClient}>\r\n\r\n        <>{children}</>\r\n        </QueryClientProvider>\r\n      {/* </NextThemesProvider> */}\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA,oEAAoE;AACpE;AAAA;AAJA;;;AAMA,MAAM,cAAc,IAAI,gLAAA,CAAA,cAAW;AAE5B,SAAS,UAAU,EACxB,QAAQ,EAGT;IACC,qBACE;kBAOI,cAAA,6LAAC,yLAAA,CAAA,sBAAmB;YAAC,QAAQ;sBAE7B,cAAA;0BAAG;;;;;;;;AAKX;KApBgB", "debugId": null}}]}