import { <PERSON>sia } from "elysia";
import { cors } from '@elysiajs/cors';
import { swagger } from '@elysiajs/swagger';
import betterAuthView from '@/app/utils/auth-view';
import { onError } from '@/app/lifecycles/onError';
import { afterHandle } from '@/app/lifecycles/afterHandle';
import appModule from '@/app';
import { authCache } from "./app/middlewares/auth.middleware";
import telemetry from "./telemetry/telemetry";
import { onRequest } from './app/lifecycles/requests';

const app = new Elysia()
  .use(telemetry())
  .use(
    cors({
      credentials: true, // Permite cookies
      origin: true,
    }),
  )
  .use(swagger())
  .state({
    start: 0,
    requestId: ''
  })
  .on('request', onRequest)
  .onError(onError)
  .onAfterHandle(afterHandle)
  .onAfterResponse((c) => {
    const cache = authCache.get(c.store.requestId);
    if (cache) {
      authCache.delete(c.store.requestId);
    }
  })
  .get("/", {
    message: "Hello Vexo API",
  })
  // .get('/ip', ({ server, request }) => {
  //   return server?.requestIP(request)
  // })
  .get("/api/get-headers", (c) => c.headers)
  .all("/api/auth/*", betterAuthView)
  .use(appModule)


const PORT = process.env.PORT || 3000;

app.listen(PORT, async () => {
  console.log(`🦊 Elysia is running at http://${app.server?.hostname}:${app.server?.port}`);
});
