{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/theme-provider-dashboard.tsx"], "sourcesContent": ["\"use client\"\r\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\"\r\nimport type { ThemeProviderProps } from \"next-themes\"\r\n\r\nexport function DashboardThemeProvider({ children, ...props }: ThemeProviderProps) {\r\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAIO,SAAS,uBAAuB,EAAE,QAAQ,EAAE,GAAG,OAA2B;IAC/E,qBAAO,6LAAC,mJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC;KAFgB", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\nimport { DateTime } from \"luxon\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));\r\n\r\n/**\r\n * Normaliza una fecha ISO a un objeto Date de JavaScript establecido a medianoche en la zona horaria local\r\n * @param dateStr Fecha en formato ISO string\r\n * @returns Objeto Date normalizado\r\n */\r\nexport function normalizeDate(dateStr: string): Date {\r\n  return DateTime.fromISO(dateStr)\r\n    .setZone('local')\r\n    .startOf('day')\r\n    .toJSDate();\r\n}\r\n\r\n/**\r\n * Convierte un array de fechas ISO a objetos Date normalizados\r\n * @param dateStrings Array de fechas en formato ISO string\r\n * @returns Array de objetos Date normalizados\r\n */\r\nexport function normalizeDates(dateStrings: string[]): Date[] {\r\n  if (!dateStrings || !Array.isArray(dateStrings)) return [];\r\n  return dateStrings.map(normalizeDate);\r\n}\r\n\r\n/**\r\n * Convierte un ISO a un string con formato \"dd de MMM de yyyy\"\r\n * @param date Fecha en formato ISO string\r\n * @returns String formateado\r\n */\r\nexport function formatISODate(date: string): string {\r\n  return DateTime.fromISO(date)\r\n    .setZone('local')\r\n    .toFormat('d \\'de\\' MMM \\'de\\' yyyy', { locale: 'es' });\r\n}\r\n\r\n/**\r\n * Verifica si una fecha está en un array de fechas deshabilitadas\r\n * @param date Fecha a verificar\r\n * @param disabledDates Array de fechas deshabilitadas\r\n * @returns true si la fecha está deshabilitada, false en caso contrario\r\n */\r\nexport function isDateDisabled(date: Date, disabledDates: Date[]): boolean {\r\n  const luxonDate = DateTime.fromJSDate(date)\r\n    .setZone('local')\r\n    .startOf('day');\r\n\r\n  return disabledDates.some(disabledDate => {\r\n    const luxonDisabledDate = DateTime.fromJSDate(disabledDate);\r\n    return luxonDate.hasSame(luxonDisabledDate, 'day');\r\n  });\r\n}\r\n\r\n/**\r\n * Formatea una fecha a un string en formato: \"dd de MMM de yyyy\"\r\n * @param date Fecha a formatear\r\n * @returns String formateado\r\n */\r\nexport function formatDate(date: string) {\r\n  return DateTime.fromISO(date)\r\n    .setZone('local')\r\n    .toFormat('d \\'de\\' MMMM, yyyy', { locale: 'es' });\r\n}\r\n\r\n/** \r\n * Formatea un número a un string en formato de moneda local, por default MXN\r\n * @param value Número a formatear\r\n * @returns String formateado\r\n */\r\nexport function formatCurrency(value: number, currency: string = 'MXN') {\r\n  return new Intl.NumberFormat('es-MX', { style: 'currency', currency }).format(value);\r\n\r\n}"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AAAA;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,QAAQ,CAAC,KAAe,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AAOzE,SAAS,cAAc,OAAe;IAC3C,OAAO,kLAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,SACrB,OAAO,CAAC,SACR,OAAO,CAAC,OACR,QAAQ;AACb;AAOO,SAAS,eAAe,WAAqB;IAClD,IAAI,CAAC,eAAe,CAAC,MAAM,OAAO,CAAC,cAAc,OAAO,EAAE;IAC1D,OAAO,YAAY,GAAG,CAAC;AACzB;AAOO,SAAS,cAAc,IAAY;IACxC,OAAO,kLAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,MACrB,OAAO,CAAC,SACR,QAAQ,CAAC,4BAA4B;QAAE,QAAQ;IAAK;AACzD;AAQO,SAAS,eAAe,IAAU,EAAE,aAAqB;IAC9D,MAAM,YAAY,kLAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,MACnC,OAAO,CAAC,SACR,OAAO,CAAC;IAEX,OAAO,cAAc,IAAI,CAAC,CAAA;QACxB,MAAM,oBAAoB,kLAAA,CAAA,WAAQ,CAAC,UAAU,CAAC;QAC9C,OAAO,UAAU,OAAO,CAAC,mBAAmB;IAC9C;AACF;AAOO,SAAS,WAAW,IAAY;IACrC,OAAO,kLAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,MACrB,OAAO,CAAC,SACR,QAAQ,CAAC,uBAAuB;QAAE,QAAQ;IAAK;AACpD;AAOO,SAAS,eAAe,KAAa,EAAE,WAAmB,KAAK;IACpE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QAAE,OAAO;QAAY;IAAS,GAAG,MAAM,CAAC;AAEhF", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/constants/env.ts"], "sourcesContent": ["import { z } from 'zod';\r\n\r\nexport const envSchema = z.object({\r\n  NEXT_PUBLIC_API_URL: z.string().url(),\r\n  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),\r\n  IS_DEV: z.string().optional().transform((val) => val === 'true'),\r\n});\r\n\r\nexport type Env = z.infer<typeof envSchema>;\r\n\r\n// create the same object but with process.env\r\nconst env = envSchema.parse({\r\n  NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,\r\n  NODE_ENV: process.env.NODE_ENV,\r\n  IS_DEV: process.env.IS_DEV,\r\n});\r\n\r\nexport default env;\r\n"], "names": [], "mappings": ";;;;AAYuB;AAZvB;;AAEO,MAAM,YAAY,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,qBAAqB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;IACnC,UAAU,uIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAe;QAAc;KAAO,EAAE,OAAO,CAAC;IAChE,QAAQ,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,SAAS,CAAC,CAAC,MAAQ,QAAQ;AAC3D;AAIA,8CAA8C;AAC9C,MAAM,MAAM,UAAU,KAAK,CAAC;IAC1B,mBAAmB;IACnB,QAAQ;IACR,QAAQ,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,MAAM;AAC5B;uCAEe", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/constants/index.ts"], "sourcesContent": ["export const BEARER_COOKIE_NAME = \"bearer_token\";\r\nexport const PENDING_INVITATION_COOKIE = 'pending_invitation';\r\nexport const PENDING_INVITATION_EMAIL_COOKIE = 'pending_invitation_email';\r\nexport const PENDING_INVITATION_ORG_ID_COOKIE = 'pending_invitation_org_id';"], "names": [], "mappings": ";;;;;;AAAO,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAClC,MAAM,kCAAkC;AACxC,MAAM,mCAAmC", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/auth-client.ts"], "sourcesContent": ["import { createAuthClient } from \"better-auth/react\";\r\nimport { adminClient, /* multiSessionClient, */ organizationClient } from \"better-auth/client/plugins\";\r\nimport { getCookie } from 'cookies-next/client';\r\n\r\nimport env from \"@/constants/env\";\r\nimport { BEARER_COOKIE_NAME } from './constants';\r\n\r\nexport const authClient = createAuthClient({\r\n  baseURL: env.NEXT_PUBLIC_API_URL,\r\n\r\n  plugins: [\r\n    adminClient(),\r\n    organizationClient(),\r\n    // multiSessionClient()\r\n  ],\r\n  credentials: 'include',\r\n  fetchOptions: {\r\n    onError: (ctx) => {\r\n      console.log('Error:', ctx.error);\r\n      console.log('Response:', ctx.response.url);\r\n    },\r\n    headers: {\r\n      'x-dashboard-call': 'true'\r\n    },\r\n    auth: {\r\n      type: 'Bearer',\r\n      token: () => {\r\n        const token = getCookie(BEARER_COOKIE_NAME);\r\n        if (token) {\r\n          return token; // No truncar el token\r\n        }\r\n      }\r\n    }\r\n  },\r\n});"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AAEA;AACA;;;;;;AAEO,MAAM,aAAa,CAAA,GAAA,sKAAA,CAAA,mBAAgB,AAAD,EAAE;IACzC,SAAS,0HAAA,CAAA,UAAG,CAAC,mBAAmB;IAEhC,SAAS;QACP,CAAA,GAAA,wLAAA,CAAA,cAAW,AAAD;QACV,CAAA,GAAA,wLAAA,CAAA,qBAAkB,AAAD;KAElB;IACD,aAAa;IACb,cAAc;QACZ,SAAS,CAAC;YACR,QAAQ,GAAG,CAAC,UAAU,IAAI,KAAK;YAC/B,QAAQ,GAAG,CAAC,aAAa,IAAI,QAAQ,CAAC,GAAG;QAC3C;QACA,SAAS;YACP,oBAAoB;QACtB;QACA,MAAM;YACJ,MAAM;YACN,OAAO;gBACL,MAAM,QAAQ,CAAA,GAAA,4JAAA,CAAA,YAAS,AAAD,EAAE,4HAAA,CAAA,qBAAkB;gBAC1C,IAAI,OAAO;oBACT,OAAO,OAAO,sBAAsB;gBACtC;YACF;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n  VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: ButtonProps) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,8cACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OACS;IACZ,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 556, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/context/user-context.tsx"], "sourcesContent": ["'use client';\r\nimport { createContext, useContext, useEffect, useState } from 'react';\r\n\r\n\r\ninterface UserContextType {\r\n  user: User;\r\n  orgId: string;\r\n  setUser: React.Dispatch<React.SetStateAction<User>>;\r\n  session: any;\r\n  setSession: React.Dispatch<React.SetStateAction<any>>;\r\n}\r\n\r\nexport const UserContext = createContext<UserContextType | undefined>(undefined);\r\n\r\nexport function UserProvider({ children, session: sessionData }: { children: React.ReactNode; session: any }) {\r\n  const [session, setSession] = useState(sessionData.session);\r\n  const [user, setUser] = useState(sessionData?.user);\r\n  const [orgId, setOrgId] = useState(sessionData.session.activeOrganizationId);\r\n\r\n  useEffect(() => {\r\n    // set orgId from session\r\n    setOrgId(sessionData.session.activeOrganizationId);\r\n    // setSession(sessionData.session);\r\n    setSession((prevSession: any) => ({ ...prevSession, activeOrganizationId: sessionData.session.activeOrganizationId }));\r\n  }, [sessionData.session.activeOrganizationId]);\r\n\r\n  return (\r\n    <UserContext.Provider value={{ user, setUser, orgId, session, setSession }}>\r\n      {children}\r\n    </UserContext.Provider>\r\n  );\r\n}\r\n\r\n\r\nexport function useUser() {\r\n  const context = useContext(UserContext);\r\n  if (!context) {\r\n    throw new Error('useUser must be used within a UserProvider');\r\n  }\r\n  return context;\r\n}"], "names": [], "mappings": ";;;;;;AACA;;;AADA;;AAYO,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAE/D,SAAS,aAAa,EAAE,QAAQ,EAAE,SAAS,WAAW,EAA+C;;IAC1G,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,OAAO;IAC1D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;IAC9C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,OAAO,CAAC,oBAAoB;IAE3E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,yBAAyB;YACzB,SAAS,YAAY,OAAO,CAAC,oBAAoB;YACjD,mCAAmC;YACnC;0CAAW,CAAC,cAAqB,CAAC;wBAAE,GAAG,WAAW;wBAAE,sBAAsB,YAAY,OAAO,CAAC,oBAAoB;oBAAC,CAAC;;QACtH;iCAAG;QAAC,YAAY,OAAO,CAAC,oBAAoB;KAAC;IAE7C,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAM;YAAS;YAAO;YAAS;QAAW;kBACtE;;;;;;AAGP;GAjBgB;KAAA;AAoBT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 625, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        success:\r\n          // \"border-transparent bg-success text-success-foreground [a&]:hover:bg-success/90\",\r\n          \"border-transparent bg-green-600 text-green-foreground [a&]:hover:bg-green-700 text-white\",\r\n        warning:\r\n          \"border-transparent bg-warning text-warning-foreground [a&]:hover:bg-warning/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,SACE,oFAAoF;YACpF;YACF,SACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 680, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';\r\nimport env from \"../constants/env\";\r\nimport { BEARER_COOKIE_NAME } from '@/constants';\r\nimport { getCookie } from 'cookies-next/client';\r\n// import { sendLogToLogflare } from '@/lib/log-requests';\r\n\r\nfunction checkIfIsClient() {\r\n    return typeof window !== 'undefined';\r\n}\r\n\r\ntype ApiResponse<T> =\r\n    {\r\n        success: true;\r\n        data: T; status:\r\n        number;\r\n        statusText: string;\r\n        headers: AxiosResponse['headers'];\r\n        config: InternalAxiosRequestConfig;\r\n        request: AxiosRequestConfig;\r\n        error: undefined\r\n    }\r\n    |\r\n    {\r\n        success: false;\r\n        data: undefined;\r\n        status: number;\r\n        statusText: string;\r\n        headers: AxiosResponse['headers'];\r\n        config: AxiosRequestConfig;\r\n        request: AxiosRequestConfig;\r\n        error: any\r\n    };\r\n\r\ntype ErrorHandler = (error: any) => void;\r\n\r\nconst baseURL = env.NEXT_PUBLIC_API_URL;\r\n\r\nclass ApiService {\r\n    private axiosInstance: AxiosInstance;\r\n    baseURL: string = '';\r\n\r\n    /** \r\n     * @param version - The version of the API to use. Defaults to 'v1'.\r\n     */\r\n    constructor(\r\n        {\r\n            version,\r\n            prefix\r\n        }:\r\n            {\r\n                version?: 'v1' | 'v2',\r\n                prefix?: 'api' | 'dash-utils'\r\n            } =\r\n            {\r\n                version: 'v1',\r\n                prefix: 'api'\r\n            }) {\r\n\r\n\r\n        // Version is only available for api prefix so if prefix is dash-utils, version is not used\r\n        this.baseURL = `${baseURL}/${prefix}${prefix === 'api' ? `/${version}` : ''}`;\r\n\r\n        this.axiosInstance = axios.create({\r\n            baseURL: this.baseURL,\r\n            withCredentials: true\r\n        });\r\n    }\r\n\r\n    private async setHeaders() {\r\n        const isClient = checkIfIsClient();\r\n        if (isClient) {\r\n            return {};\r\n\r\n        };\r\n        const headers = (await import('next/headers')).headers;\r\n        const rawHeaders = await headers();\r\n        const headersObj: Record<string, string> = {};\r\n        rawHeaders.forEach((value, key) => {\r\n            headersObj[key] = value;\r\n        });\r\n        return headersObj;\r\n    }\r\n\r\n    private async request<T>(\r\n        method: 'get' | 'post' | 'patch' | 'put' | 'delete',\r\n        path: string,\r\n        config?: AxiosRequestConfig,\r\n        onError?: ErrorHandler\r\n    ): Promise<ApiResponse<T>> {\r\n        try {\r\n            const isClient = checkIfIsClient();\r\n            const headers = await this.setHeaders();\r\n            let token = ''\r\n\r\n            if (isClient) {\r\n                token = getCookie(BEARER_COOKIE_NAME) as string;\r\n            } else {\r\n                const getCookieServerByName = (await import('@/actions/cookies')).getCookieServerByName;\r\n                token = await getCookieServerByName({ name: BEARER_COOKIE_NAME }) as string;\r\n                if (path.includes('/files/download')) {\r\n                    console.log('Token on request for /api/v1/files/download: ', token);\r\n                }\r\n            }\r\n\r\n            const requestHeaders = {\r\n                    cookie: isClient ? undefined : headers.cookie,\r\n                    'x-dashboard-call': 'true',\r\n                    Authorization: `Bearer ${token}`,\r\n                    ...config?.headers\r\n                }\r\n\r\n            if (path.includes('/files/download')) {\r\n                console.log('============================================================')\r\n                console.log('============================================================')\r\n\r\n                console.log('Headers: ', headers);\r\n                console.log('Request on /api/v1/files/download: ', requestHeaders);\r\n                console.log('============================================================')\r\n                console.log('============================================================')\r\n\r\n            }\r\n\r\n            const response = await this.axiosInstance({\r\n                method,\r\n                url: path,\r\n                ...config,\r\n                headers: requestHeaders,\r\n            });\r\n            // Simplificamos el objeto de respuesta para evitar problemas de serialización\r\n            return {\r\n                success: true,\r\n                data: response.data,\r\n                status: response.status,\r\n                statusText: response.statusText,\r\n                headers: response.headers,\r\n                config: response.config,\r\n                request: response.request,\r\n                error: undefined\r\n            }\r\n        } catch (error: any) {\r\n            console.error('Error in request:', error?.config?.url);\r\n\r\n            if (onError) onError(error);\r\n            await this.handleError(error);\r\n            return {\r\n                success: false,\r\n                data: undefined,\r\n                status: error.response?.status || 500,\r\n                statusText: error.response?.statusText || 'Unknown Error',\r\n                headers: error.response?.headers || {},\r\n                config: error.config || {},\r\n                request: error.request || {},\r\n                error: error?.response?.data || error.message,\r\n            };\r\n        }\r\n    }\r\n\r\n    public async get<T>(path: string, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('get', path, config, onError);\r\n    }\r\n\r\n    public async post<T>(path: string, data?: any, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('post', path, { ...config, data }, onError);\r\n    }\r\n\r\n    public async patch<T>(path: string, data?: any, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('patch', path, { ...config, data }, onError);\r\n    }\r\n\r\n    public async put<T>(path: string, data?: any, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('put', path, { ...config, data }, onError);\r\n    }\r\n\r\n    public async delete<T>(path: string, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('delete', path, config, onError);\r\n    }\r\n\r\n    private async handleError(error: any) {\r\n        console.error('API Error:', {\r\n            message: error.message,\r\n            status: error.response?.status,\r\n            data: error.response?.data\r\n        });\r\n    }\r\n}\r\n\r\nexport const apiService = new ApiService();\r\n\r\nexport const dashUtilsService = new ApiService({ prefix: 'dash-utils' });\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AACA,0DAA0D;AAE1D,SAAS;IACL,OAAO,aAAkB;AAC7B;AA2BA,MAAM,UAAU,0HAAA,CAAA,UAAG,CAAC,mBAAmB;AAEvC,MAAM;IACM,cAA6B;IACrC,UAAkB,GAAG;IAErB;;KAEC,GACD,YACI,EACI,OAAO,EACP,MAAM,EAKL,GACD;QACI,SAAS;QACT,QAAQ;IACZ,CAAC,CAAE;QAGP,2FAA2F;QAC3F,IAAI,CAAC,OAAO,GAAG,GAAG,QAAQ,CAAC,EAAE,SAAS,WAAW,QAAQ,CAAC,CAAC,EAAE,SAAS,GAAG,IAAI;QAE7E,IAAI,CAAC,aAAa,GAAG,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YAC9B,SAAS,IAAI,CAAC,OAAO;YACrB,iBAAiB;QACrB;IACJ;IAEA,MAAc,aAAa;QACvB,MAAM,WAAW;QACjB,wCAAc;YACV,OAAO,CAAC;QAEZ;;QACA,MAAM;QACN,MAAM;QACN,MAAM;IAKV;IAEA,MAAc,QACV,MAAmD,EACnD,IAAY,EACZ,MAA2B,EAC3B,OAAsB,EACC;QACvB,IAAI;YACA,MAAM,WAAW;YACjB,MAAM,UAAU,MAAM,IAAI,CAAC,UAAU;YACrC,IAAI,QAAQ;YAEZ,wCAAc;gBACV,QAAQ,CAAA,GAAA,4JAAA,CAAA,YAAS,AAAD,EAAE,4HAAA,CAAA,qBAAkB;YACxC,OAAO;;YAMP;YAEA,MAAM,iBAAiB;gBACf,QAAQ,uCAAW;gBACnB,oBAAoB;gBACpB,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,GAAG,QAAQ,OAAO;YACtB;YAEJ,IAAI,KAAK,QAAQ,CAAC,oBAAoB;gBAClC,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC;gBAEZ,QAAQ,GAAG,CAAC,aAAa;gBACzB,QAAQ,GAAG,CAAC,uCAAuC;gBACnD,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC;YAEhB;YAEA,MAAM,WAAW,MAAM,IAAI,CAAC,aAAa,CAAC;gBACtC;gBACA,KAAK;gBACL,GAAG,MAAM;gBACT,SAAS;YACb;YACA,8EAA8E;YAC9E,OAAO;gBACH,SAAS;gBACT,MAAM,SAAS,IAAI;gBACnB,QAAQ,SAAS,MAAM;gBACvB,YAAY,SAAS,UAAU;gBAC/B,SAAS,SAAS,OAAO;gBACzB,QAAQ,SAAS,MAAM;gBACvB,SAAS,SAAS,OAAO;gBACzB,OAAO;YACX;QACJ,EAAE,OAAO,OAAY;YACjB,QAAQ,KAAK,CAAC,qBAAqB,OAAO,QAAQ;YAElD,IAAI,SAAS,QAAQ;YACrB,MAAM,IAAI,CAAC,WAAW,CAAC;YACvB,OAAO;gBACH,SAAS;gBACT,MAAM;gBACN,QAAQ,MAAM,QAAQ,EAAE,UAAU;gBAClC,YAAY,MAAM,QAAQ,EAAE,cAAc;gBAC1C,SAAS,MAAM,QAAQ,EAAE,WAAW,CAAC;gBACrC,QAAQ,MAAM,MAAM,IAAI,CAAC;gBACzB,SAAS,MAAM,OAAO,IAAI,CAAC;gBAC3B,OAAO,OAAO,UAAU,QAAQ,MAAM,OAAO;YACjD;QACJ;IACJ;IAEA,MAAa,IAAO,IAAY,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QAC5G,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,MAAM,QAAQ;IAC7C;IAEA,MAAa,KAAQ,IAAY,EAAE,IAAU,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QACzH,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,MAAM;YAAE,GAAG,MAAM;YAAE;QAAK,GAAG;IAC3D;IAEA,MAAa,MAAS,IAAY,EAAE,IAAU,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QAC1H,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,MAAM;YAAE,GAAG,MAAM;YAAE;QAAK,GAAG;IAC5D;IAEA,MAAa,IAAO,IAAY,EAAE,IAAU,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QACxH,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,MAAM;YAAE,GAAG,MAAM;YAAE;QAAK,GAAG;IAC1D;IAEA,MAAa,OAAU,IAAY,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QAC/G,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,MAAM,QAAQ;IAChD;IAEA,MAAc,YAAY,KAAU,EAAE;QAClC,QAAQ,KAAK,CAAC,cAAc;YACxB,SAAS,MAAM,OAAO;YACtB,QAAQ,MAAM,QAAQ,EAAE;YACxB,MAAM,MAAM,QAAQ,EAAE;QAC1B;IACJ;AACJ;AAEO,MAAM,aAAa,IAAI;AAEvB,MAAM,mBAAmB,IAAI,WAAW;IAAE,QAAQ;AAAa", "debugId": null}}, {"offset": {"line": 825, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/lib/api/user-roles.api.ts"], "sourcesContent": ["import { apiService } from '@/services/api';\n\nexport interface UserRoleResponse {\n  success: boolean;\n  message: string;\n  userType: 'client' | 'host';\n  availableUserTypes: string[];\n  isHostVerified: boolean;\n  newRoleAdded?: string;\n}\n\nexport const userRolesApi = {\n  // Cambiar rol actual del usuario\n  switchRole: async (userType: 'client' | 'host'): Promise<UserRoleResponse> => {\n    const result = await apiService.patch<UserRoleResponse>('/user/switch-role', {\n      userType\n    });\n    \n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error || 'Error switching role');\n    }\n  },\n\n  // Solicitar rol adicional\n  requestAdditionalRole: async (userType: 'client' | 'host'): Promise<UserRoleResponse> => {\n    const result = await apiService.post<UserRoleResponse>('/user/request-additional-role', {\n      userType\n    });\n    \n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error || 'Error requesting additional role');\n    }\n  },\n};\n"], "names": [], "mappings": ";;;AAAA;;AAWO,MAAM,eAAe;IAC1B,iCAAiC;IACjC,YAAY,OAAO;QACjB,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,KAAK,CAAmB,qBAAqB;YAC3E;QACF;QAEA,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;QAClC;IACF;IAEA,0BAA0B;IAC1B,uBAAuB,OAAO;QAC5B,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,IAAI,CAAmB,iCAAiC;YACtF;QACF;QAEA,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;QAClC;IACF;AACF", "debugId": null}}, {"offset": {"line": 863, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/layout/role-switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { useRouter } from \"next/navigation\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { ChevronDown, User, UserCheck } from \"lucide-react\"\nimport { userRolesApi } from '@/lib/api/user-roles.api'\nimport toast from 'react-hot-toast'\nimport { useUser } from '@/context/user-context'\n\nexport function RoleSwitch() {\n  const { user, setUser } = useUser()\n  const router = useRouter()\n  const [isLoading, setIsLoading] = useState(false)\n\n  console.log('availableUserTypes', user.availableUserTypes)\n\n  // Solo mostrar si el usuario tiene más de un rol disponible\n  if (!user.availableUserTypes || user.availableUserTypes.length <= 1) {\n    return null\n  }\n\n  const handleRoleSwitch = async (newRole: 'client' | 'host') => {\n    if (newRole === user.userType) return\n\n    setIsLoading(true)\n    try {\n      const response = await userRolesApi.switchRole(newRole)\n\n      // Actualizar el contexto del usuario\n      toast.promise(\n        async () => {\n          await userRolesApi.switchRole(newRole)\n          setUser((prevUser) => ({\n            ...prevUser,\n            userType: newRole,\n          }))\n          router.push('/dashboard')\n        },\n        {\n          loading: 'Cambiando de rol...',\n          success: response.message,\n          error: 'Error al cambiar de rol',\n        }\n      )\n\n\n    } catch (error) {\n      console.error('Error switching role:', error)\n      toast.error('Error al cambiar de rol')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const getRoleLabel = (role: string) => {\n    return role === 'host' ? 'Anfitrión' : 'Cliente'\n  }\n\n  const getRoleIcon = (role: string) => {\n    return role === 'host' ? UserCheck : User\n  }\n\n  console.log('user.userType', user.userType)\n  const currentRoleLabel = getRoleLabel(user.userType || 'client')\n  const CurrentRoleIcon = getRoleIcon(user.userType || 'client')\n\n  return (\n    <div className=\"px-4 py-2 border-t border-white/10\">\n      <div className=\"text-xs text-gray-400 mb-2\">Modo actual</div>\n      <DropdownMenu>\n        <DropdownMenuTrigger asChild>\n          <Button\n            variant=\"ghost\"\n            className=\"w-full justify-between p-2 h-auto text-left hover:bg-white/5\"\n            disabled={isLoading}\n          >\n            <div className=\"flex items-center\">\n              <CurrentRoleIcon className=\"h-4 w-4 mr-2 text-gray-400\" />\n              <span className=\"text-sm text-white\">{currentRoleLabel}</span>\n              {user.userType === 'host' && user.isHostVerified && (\n                <Badge variant=\"secondary\" className=\"ml-2 text-xs\">\n                  Verificado\n                </Badge>\n              )}\n            </div>\n            <ChevronDown className=\"h-4 w-4 text-gray-400\" />\n          </Button>\n        </DropdownMenuTrigger>\n        <DropdownMenuContent side=\"top\" align=\"start\" className=\"w-48 mb-2\">\n          {user.availableUserTypes.map((role) => {\n            const RoleIcon = getRoleIcon(role)\n            const isCurrentRole = role === user.userType\n\n            return (\n              <DropdownMenuItem\n                key={role}\n                onClick={() => handleRoleSwitch(role as 'client' | 'host')}\n                disabled={isCurrentRole || isLoading}\n                className={isCurrentRole ? 'bg-accent' : ''}\n              >\n                <RoleIcon className=\"mr-2 h-4 w-4\" />\n                <span>{getRoleLabel(role)}</span>\n                {isCurrentRole && (\n                  <Badge variant=\"outline\" className=\"ml-auto text-xs\">\n                    Actual\n                  </Badge>\n                )}\n                {role === 'host' && user.isHostVerified && (\n                  <Badge variant=\"secondary\" className=\"ml-auto text-xs\">\n                    Verificado\n                  </Badge>\n                )}\n              </DropdownMenuItem>\n            )\n          })}\n        </DropdownMenuContent>\n      </DropdownMenu>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAMA;AACA;AAAA;AAAA;AACA;AACA;AACA;;;AAfA;;;;;;;;;;AAiBO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,QAAQ,GAAG,CAAC,sBAAsB,KAAK,kBAAkB;IAEzD,4DAA4D;IAC5D,IAAI,CAAC,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC,MAAM,IAAI,GAAG;QACnE,OAAO;IACT;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,YAAY,KAAK,QAAQ,EAAE;QAE/B,aAAa;QACb,IAAI;YACF,MAAM,WAAW,MAAM,4IAAA,CAAA,eAAY,CAAC,UAAU,CAAC;YAE/C,qCAAqC;YACrC,0JAAA,CAAA,UAAK,CAAC,OAAO,CACX;gBACE,MAAM,4IAAA,CAAA,eAAY,CAAC,UAAU,CAAC;gBAC9B,QAAQ,CAAC,WAAa,CAAC;wBACrB,GAAG,QAAQ;wBACX,UAAU;oBACZ,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,GACA;gBACE,SAAS;gBACT,SAAS,SAAS,OAAO;gBACzB,OAAO;YACT;QAIJ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAO,SAAS,SAAS,cAAc;IACzC;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,SAAS,SAAS,mNAAA,CAAA,YAAS,GAAG,qMAAA,CAAA,OAAI;IAC3C;IAEA,QAAQ,GAAG,CAAC,iBAAiB,KAAK,QAAQ;IAC1C,MAAM,mBAAmB,aAAa,KAAK,QAAQ,IAAI;IACvD,MAAM,kBAAkB,YAAY,KAAK,QAAQ,IAAI;IAErD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BAA6B;;;;;;0BAC5C,6LAAC,+IAAA,CAAA,eAAY;;kCACX,6LAAC,+IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,UAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAgB,WAAU;;;;;;sDAC3B,6LAAC;4CAAK,WAAU;sDAAsB;;;;;;wCACrC,KAAK,QAAQ,KAAK,UAAU,KAAK,cAAc,kBAC9C,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAAe;;;;;;;;;;;;8CAKxD,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG3B,6LAAC,+IAAA,CAAA,sBAAmB;wBAAC,MAAK;wBAAM,OAAM;wBAAQ,WAAU;kCACrD,KAAK,kBAAkB,CAAC,GAAG,CAAC,CAAC;4BAC5B,MAAM,WAAW,YAAY;4BAC7B,MAAM,gBAAgB,SAAS,KAAK,QAAQ;4BAE5C,qBACE,6LAAC,+IAAA,CAAA,mBAAgB;gCAEf,SAAS,IAAM,iBAAiB;gCAChC,UAAU,iBAAiB;gCAC3B,WAAW,gBAAgB,cAAc;;kDAEzC,6LAAC;wCAAS,WAAU;;;;;;kDACpB,6LAAC;kDAAM,aAAa;;;;;;oCACnB,+BACC,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;kDAAkB;;;;;;oCAItD,SAAS,UAAU,KAAK,cAAc,kBACrC,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAAkB;;;;;;;+BAbpD;;;;;wBAmBX;;;;;;;;;;;;;;;;;;AAKV;GA9GgB;;QACY,qIAAA,CAAA,UAAO;QAClB,qIAAA,CAAA,YAAS;;;KAFV", "debugId": null}}, {"offset": {"line": 1092, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/layout/user-profile-sidebar.tsx"], "sourcesContent": ["\"use client\"\n\n// import { useState } from \"react\"\nimport Image from \"next/image\"\nimport Link from \"next/link\"\nimport { useRouter } from \"next/navigation\"\nimport { authClient } from \"@/auth-client\"\nimport { Button } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport {\n  User,\n  Settings,\n  // CreditCard,\n  Bell,\n  LogOut,\n  ChevronUp,\n  Star,\n} from \"lucide-react\"\nimport { useUser } from '@/context/user-context'\nimport { deleteCookie } from 'cookies-next/client'\nimport { BEARER_COOKIE_NAME } from '@/constants'\nimport { RoleSwitch } from './role-switch'\n\nexport function UserProfileSidebar() {\n  // const { data: session } = authClient.useSession()\n  const { user } = useUser()\n  const router = useRouter()\n\n  const handleLogout = async () => {\n    await authClient.signOut();\n    deleteCookie(BEARER_COOKIE_NAME);\n    router.push(\"/\")\n  }\n\n  // Determinar la ruta del perfil según el tipo de usuario\n  const getProfilePath = () => {\n    if (user.role === \"admin\") {\n      return \"/dashboard/admin/profile\"\n    } else if (user.userType === \"host\") {\n      return \"/dashboard/host/profile\"\n    } else if (user.userType === \"client\") {\n      return \"/dashboard/client/profile\"\n    }\n    return \"/dashboard/profile\"\n  }\n\n  return (\n    <div className=\"border-t border-white/10\">\n      {/* Role Switch Component */}\n      <RoleSwitch />\n\n      {/* User Profile Dropdown */}\n      <div className=\"p-4\">\n        <DropdownMenu>\n        <DropdownMenuTrigger asChild>\n          <Button variant=\"ghost\" className=\"w-full justify-start p-2 h-auto text-left hover:bg-white/5\">\n            <div className=\"flex items-center w-full\">\n              <Image\n                src={user.image || \"/placeholder.svg\"}\n                alt={user.name || \"Usuario\"}\n                width={40}\n                height={40}\n                className=\"rounded-full mr-3\"\n              />\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-white truncate\">{user.name || \"Usuario\"}</p>\n                <p className=\"text-xs text-gray-400 truncate\">{user.email || \"\"}</p>\n              </div>\n              <ChevronUp className=\"h-4 w-4 text-gray-400\" />\n            </div>\n          </Button>\n        </DropdownMenuTrigger>\n        <DropdownMenuContent side=\"top\" align=\"start\" className=\"w-56 mb-2\">\n          {/* Opcional: mostrar plan premium solo si es relevante */}\n          <DropdownMenuItem>\n            <Star className=\"mr-2 h-4 w-4\" />\n            <span>Upgrade to Pro</span>\n          </DropdownMenuItem>\n          <DropdownMenuSeparator />\n          <DropdownMenuItem asChild>\n            <Link href={getProfilePath()}>\n              <User className=\"mr-2 h-4 w-4\" />\n              <span>Mi Perfil</span>\n            </Link>\n          </DropdownMenuItem>\n          <DropdownMenuItem asChild>\n            <Link href={`${getProfilePath()}/settings`}>\n              <Settings className=\"mr-2 h-4 w-4\" />\n              <span>Configuración</span>\n            </Link>\n          </DropdownMenuItem>\n          <DropdownMenuItem>\n            <Bell className=\"mr-2 h-4 w-4\" />\n            <span>Notificaciones</span>\n          </DropdownMenuItem>\n          <DropdownMenuSeparator />\n          <DropdownMenuItem onClick={handleLogout} className=\"text-red-600\">\n            <LogOut className=\"mr-2 h-4 w-4\" />\n            <span>Cerrar sesión</span>\n          </DropdownMenuItem>\n        </DropdownMenuContent>\n      </DropdownMenu>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;AACA;;;AA3BA;;;;;;;;;;;;AA6BO,SAAS;;IACd,oDAAoD;IACpD,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,MAAM,wHAAA,CAAA,aAAU,CAAC,OAAO;QACxB,CAAA,GAAA,4JAAA,CAAA,eAAY,AAAD,EAAE,4HAAA,CAAA,qBAAkB;QAC/B,OAAO,IAAI,CAAC;IACd;IAEA,yDAAyD;IACzD,MAAM,iBAAiB;QACrB,IAAI,KAAK,IAAI,KAAK,SAAS;YACzB,OAAO;QACT,OAAO,IAAI,KAAK,QAAQ,KAAK,QAAQ;YACnC,OAAO;QACT,OAAO,IAAI,KAAK,QAAQ,KAAK,UAAU;YACrC,OAAO;QACT;QACA,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,iJAAA,CAAA,aAAU;;;;;0BAGX,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,+IAAA,CAAA,eAAY;;sCACb,6LAAC,+IAAA,CAAA,sBAAmB;4BAAC,OAAO;sCAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,WAAU;0CAChC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAK,KAAK,KAAK,IAAI;4CACnB,KAAK,KAAK,IAAI,IAAI;4CAClB,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;sDAEZ,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAA2C,KAAK,IAAI,IAAI;;;;;;8DACrE,6LAAC;oDAAE,WAAU;8DAAkC,KAAK,KAAK,IAAI;;;;;;;;;;;;sDAE/D,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAI3B,6LAAC,+IAAA,CAAA,sBAAmB;4BAAC,MAAK;4BAAM,OAAM;4BAAQ,WAAU;;8CAEtD,6LAAC,+IAAA,CAAA,mBAAgB;;sDACf,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC,+IAAA,CAAA,wBAAqB;;;;;8CACtB,6LAAC,+IAAA,CAAA,mBAAgB;oCAAC,OAAO;8CACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM;;0DACV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;8CAGV,6LAAC,+IAAA,CAAA,mBAAgB;oCAAC,OAAO;8CACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM,GAAG,iBAAiB,SAAS,CAAC;;0DACxC,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;8CAGV,6LAAC,+IAAA,CAAA,mBAAgB;;sDACf,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC,+IAAA,CAAA,wBAAqB;;;;;8CACtB,6LAAC,+IAAA,CAAA,mBAAgB;oCAAC,SAAS;oCAAc,WAAU;;sDACjD,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GAlFgB;;QAEG,qIAAA,CAAA,UAAO;QACT,qIAAA,CAAA,YAAS;;;KAHV", "debugId": null}}, {"offset": {"line": 1412, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/layout/admin-sidebar.tsx"], "sourcesContent": ["\"use client\"\r\nimport Link from \"next/link\"\r\nimport Image from \"next/image\"\r\nimport { usePathname } from \"next/navigation\"\r\nimport {\r\n  LayoutDashboard,\r\n  Users,\r\n  UserCircle,\r\n  Car,\r\n  CalendarClock,\r\n  CreditCard,\r\n  // BarChart,\r\n  // LifeBuoy,\r\n  Settings,\r\n  Shield,\r\n} from \"lucide-react\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { UserProfileSidebar } from \"./user-profile-sidebar\"\r\n\r\nconst menuItems = [\r\n  {\r\n    title: \"PRINCIPAL\",\r\n    items: [\r\n      {\r\n        name: \"Dashboard\",\r\n        href: \"/dashboard/admin\",\r\n        icon: LayoutDashboard,\r\n      },\r\n      {\r\n        name: \"Anfitriones\",\r\n        href: \"/dashboard/admin/hosts\",\r\n        icon: Users,\r\n      },\r\n      {\r\n        name: \"Clientes\",\r\n        href: \"/dashboard/admin/clients\",\r\n        icon: UserCircle,\r\n      },\r\n      {\r\n        name: \"Vehícu<PERSON>\",\r\n        href: \"/dashboard/admin/vehicles\",\r\n        icon: Car,\r\n      },\r\n      {\r\n        name: \"Reservas\",\r\n        href: \"/dashboard/admin/reservations\",\r\n        icon: CalendarClock,\r\n      },\r\n      {\r\n        name: \"Gestionar Estados\",\r\n        href: \"/dashboard/admin/states\",\r\n        icon: Shield,\r\n      },\r\n      {\r\n        name: \"Pagos\",\r\n        href: \"/dashboard/admin/payouts\",\r\n        icon: CreditCard,\r\n        badge: undefined,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"GESTIÓN\",\r\n    items: [\r\n      {\r\n        name: \"Verificaciones\",\r\n        href: \"/dashboard/admin/verifications\",\r\n        icon: Shield,\r\n      },\r\n      // {\r\n      //   name: \"Reportes\",\r\n      //   href: \"/dashboard/admin/reports\",\r\n      //   icon: BarChart,\r\n      // },\r\n      // {\r\n      //   name: \"Soporte\",\r\n      //   href: \"/dashboard/admin/support\",\r\n      //   icon: LifeBuoy,\r\n      // },\r\n    ],\r\n  },\r\n  {\r\n    title: \"CONFIGURACIÓN\",\r\n    items: [\r\n      {\r\n        name: \"Configuración\",\r\n        href: \"/dashboard/admin/settings\",\r\n        icon: Settings,\r\n      },\r\n    ],\r\n  },\r\n]\r\n\r\n\r\nexport function AdminSidebar() {\r\n  const pathname = usePathname()\r\n\r\n  return (\r\n    <div className=\"w-full bg-[#0f2a5c] dark:bg-gray-900 text-white flex flex-col h-screen\">\r\n      {/* Header */}\r\n      <div className=\"p-4 flex items-center border-b border-white/10\">\r\n        <div className=\"flex items-center\">\r\n          <div className=\"bg-white rounded-md p-1 mr-2\">\r\n            <Image src=\"/placeholder.svg?height=24&width=24\" alt=\"Autoop Logo\" width={24} height={24} />\r\n          </div>\r\n          <span className=\"text-xl font-bold\">Autoop</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Navigation */}\r\n      <div className=\"flex-1 overflow-y-auto py-4\">\r\n        {menuItems.map((section, i) => (\r\n          <div key={i} className=\"px-4 py-2\">\r\n            <div className=\"text-xs font-semibold text-gray-400 mb-2\">{section.title}</div>\r\n            <ul className=\"space-y-1\">\r\n              {section.items.map((item, j) => (\r\n                <li key={j}>\r\n                  <Link\r\n                    href={item.href}\r\n                    className={cn(\r\n                      \"flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors\",\r\n                      pathname === item.href\r\n                        ? \"bg-white/10 text-white\"\r\n                        : \"text-gray-300 hover:bg-white/5 hover:text-white\",\r\n                    )}\r\n                  >\r\n                    <item.icon className=\"h-5 w-5 mr-3\" />\r\n                    <span>{item.name}</span>\r\n                    {item.badge && (\r\n                      <span className=\"ml-auto bg-primary text-white text-xs font-semibold px-2 py-0.5 rounded-full\">\r\n                        {item.badge}\r\n                      </span>\r\n                    )}\r\n                  </Link>\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      <UserProfileSidebar />\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;;;AAjBA;;;;;;;AAmBA,MAAM,YAAY;IAChB;QACE,OAAO;QACP,OAAO;YACL;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,+NAAA,CAAA,kBAAe;YACvB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,uMAAA,CAAA,QAAK;YACb;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,qNAAA,CAAA,aAAU;YAClB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,mMAAA,CAAA,MAAG;YACX;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,2NAAA,CAAA,gBAAa;YACrB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,yMAAA,CAAA,SAAM;YACd;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,qNAAA,CAAA,aAAU;gBAChB,OAAO;YACT;SACD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,yMAAA,CAAA,SAAM;YACd;SAWD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,6MAAA,CAAA,WAAQ;YAChB;SACD;IACH;CACD;AAGM,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCAAC,KAAI;gCAAsC,KAAI;gCAAc,OAAO;gCAAI,QAAQ;;;;;;;;;;;sCAExF,6LAAC;4BAAK,WAAU;sCAAoB;;;;;;;;;;;;;;;;;0BAKxC,6LAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,SAAS,kBACvB,6LAAC;wBAAY,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;0CAA4C,QAAQ,KAAK;;;;;;0CACxE,6LAAC;gCAAG,WAAU;0CACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,kBACxB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gFACA,aAAa,KAAK,IAAI,GAClB,2BACA;;8DAGN,6LAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;8DACrB,6LAAC;8DAAM,KAAK,IAAI;;;;;;gDACf,KAAK,KAAK,kBACT,6LAAC;oDAAK,WAAU;8DACb,KAAK,KAAK;;;;;;;;;;;;uCAdV;;;;;;;;;;;uBAJL;;;;;;;;;;0BA6Bd,6LAAC,6JAAA,CAAA,qBAAkB;;;;;;;;;;;AAGzB;GAlDgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 1646, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/layout/host-sidebar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport Link from \"next/link\"\r\nimport Image from \"next/image\"\r\nimport { usePathname } from \"next/navigation\"\r\nimport {\r\n  LayoutDashboard,\r\n  Car,\r\n  CalendarClock,\r\n  CreditCard,\r\n  BarChart,\r\n  Settings,\r\n  // MessageSquare,\r\n} from \"lucide-react\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { UserProfileSidebar } from \"./user-profile-sidebar\"\r\n\r\nconst menuItems = [\r\n  {\r\n    title: \"PRINCIPAL\",\r\n    items: [\r\n      {\r\n        name: \"Dashboard\",\r\n        href: \"/dashboard/host\",\r\n        icon: LayoutDashboard,\r\n      },\r\n      {\r\n        name: \"Mis Vehículos\",\r\n        href: \"/dashboard/host/vehicles\",\r\n        icon: Car,\r\n      },\r\n      {\r\n        name: \"Reser<PERSON>\",\r\n        href: \"/dashboard/host/reservations\",\r\n        icon: CalendarClock,\r\n        badge: undefined,\r\n      },\r\n      {\r\n        name: \"<PERSON>ana<PERSON><PERSON>\",\r\n        href: \"/dashboard/host/earnings\",\r\n        icon: CreditCard,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"GESTIÓN\",\r\n    items: [\r\n      {\r\n        name: \"Reportes\",\r\n        href: \"/dashboard/host/reports\",\r\n        icon: Bar<PERSON><PERSON>,\r\n      },\r\n      // {\r\n      //   name: \"<PERSON><PERSON><PERSON><PERSON>\",\r\n      //   href: \"/dashboard/host/messages\",\r\n      //   icon: MessageSquare,\r\n      // },\r\n    ],\r\n  },\r\n  {\r\n    title: \"CONFIGURACIÓN\",\r\n    items: [\r\n      {\r\n        name: \"Configuración\",\r\n        href: \"/dashboard/host/settings\",\r\n        icon: Settings,\r\n      },\r\n    ],\r\n  },\r\n]\r\n\r\nexport function HostSidebar() {\r\n  const pathname = usePathname()\r\n\r\n  return (\r\n    <div className=\"w-full bg-[#0f2a5c] dark:bg-gray-900 text-white flex flex-col h-screen border-none\">\r\n      {/* Header */}\r\n      <div className=\"p-4 flex items-center border-b border-white/10\">\r\n        <div className=\"flex items-center\">\r\n          <div className=\"bg-white rounded-md p-1 mr-2\">\r\n            <Image src=\"/placeholder.svg?height=24&width=24\" alt=\"Autoop Logo\" width={24} height={24} />\r\n          </div>\r\n          <span className=\"text-xl font-bold\">Autoop</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Navigation */}\r\n      <div className=\"flex-1 overflow-y-auto py-4\">\r\n        {menuItems.map((section, i) => (\r\n          <div key={i} className=\"px-4 py-2\">\r\n            <div className=\"text-xs font-semibold text-gray-400 mb-2\">{section.title}</div>\r\n            <ul className=\"space-y-1\">\r\n              {section.items.map((item, j) => (\r\n                <li key={j}>\r\n                  <Link\r\n                    href={item.href}\r\n                    className={cn(\r\n                      \"flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors\",\r\n                      pathname === item.href\r\n                        ? \"bg-white/10 text-white\"\r\n                        : \"text-gray-300 hover:bg-white/5 hover:text-white\",\r\n                    )}\r\n                  >\r\n                    <item.icon className=\"h-5 w-5 mr-3\" />\r\n                    <span>{item.name}</span>\r\n                    {item.badge && (\r\n                      <span className=\"ml-auto bg-primary text-white text-xs font-semibold px-2 py-0.5 rounded-full\">\r\n                        {item.badge}\r\n                      </span>\r\n                    )}\r\n                  </Link>\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      <UserProfileSidebar />\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;;;AAfA;;;;;;;AAiBA,MAAM,YAAY;IAChB;QACE,OAAO;QACP,OAAO;YACL;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,+NAAA,CAAA,kBAAe;YACvB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,mMAAA,CAAA,MAAG;YACX;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,2NAAA,CAAA,gBAAa;gBACnB,OAAO;YACT;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,qNAAA,CAAA,aAAU;YAClB;SACD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,gPAAA,CAAA,WAAQ;YAChB;SAMD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,6MAAA,CAAA,WAAQ;YAChB;SACD;IACH;CACD;AAEM,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCAAC,KAAI;gCAAsC,KAAI;gCAAc,OAAO;gCAAI,QAAQ;;;;;;;;;;;sCAExF,6LAAC;4BAAK,WAAU;sCAAoB;;;;;;;;;;;;;;;;;0BAKxC,6LAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,SAAS,kBACvB,6LAAC;wBAAY,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;0CAA4C,QAAQ,KAAK;;;;;;0CACxE,6LAAC;gCAAG,WAAU;0CACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,kBACxB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gFACA,aAAa,KAAK,IAAI,GAClB,2BACA;;8DAGN,6LAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;8DACrB,6LAAC;8DAAM,KAAK,IAAI;;;;;;gDACf,KAAK,KAAK,kBACT,6LAAC;oDAAK,WAAU;8DACb,KAAK,KAAK;;;;;;;;;;;;uCAdV;;;;;;;;;;;uBAJL;;;;;;;;;;0BA6Bd,6LAAC,6JAAA,CAAA,qBAAkB;;;;;;;;;;;AAGzB;GAlDgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 1863, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/layout/client-sidebar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport Link from \"next/link\"\r\nimport Image from \"next/image\"\r\nimport { usePathname } from \"next/navigation\"\r\nimport {\r\n  LayoutDashboard,\r\n  Search,\r\n  CalendarClock,\r\n  CreditCard,\r\n  Heart,\r\n  // MessageSquare,\r\n  Settings,\r\n  History,\r\n} from \"lucide-react\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { UserProfileSidebar } from \"./user-profile-sidebar\"\r\n\r\nconst menuItems = [\r\n  {\r\n    title: \"PRINCIPAL\",\r\n    items: [\r\n      {\r\n        name: \"Dashboard\",\r\n        href: \"/dashboard/client\",\r\n        icon: LayoutDashboard,\r\n      },\r\n      {\r\n        name: \"Buscar Vehículos\",\r\n        href: \"/dashboard/client/search\",\r\n        icon: Search,\r\n      },\r\n      {\r\n        name: \"Mis Reservas\",\r\n        href: \"/dashboard/client/reservations\",\r\n        icon: CalendarClock,\r\n        badge: undefined,\r\n      },\r\n      {\r\n        name: \"Historial\",\r\n        href: \"/dashboard/client/history\",\r\n        icon: History,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"GESTIÓN\",\r\n    items: [\r\n      {\r\n        name: \"Pagos\",\r\n        href: \"/dashboard/client/payments\",\r\n        icon: CreditCard,\r\n      },\r\n      {\r\n        name: \"Favori<PERSON>\",\r\n        href: \"/dashboard/client/favorites\",\r\n        icon: Heart,\r\n      },\r\n      // {\r\n      //   name: \"<PERSON><PERSON><PERSON><PERSON>\",\r\n      //   href: \"/dashboard/client/messages\",\r\n      //   icon: MessageSquare,\r\n      // },\r\n    ],\r\n  },\r\n  {\r\n    title: \"CONFIGURACIÓN\",\r\n    items: [\r\n      {\r\n        name: \"Configuración\",\r\n        href: \"/dashboard/client/settings\",\r\n        icon: Settings,\r\n      },\r\n    ],\r\n  },\r\n]\r\n\r\n\r\nexport function ClientSidebar() {\r\n  const pathname = usePathname()\r\n\r\n  return (\r\n    <div className=\"w-full bg-[#0f2a5c] dark:bg-gray-900 text-white flex flex-col h-screen\">\r\n      {/* Header */}\r\n      <div className=\"p-4 flex items-center border-b border-white/10\">\r\n        <div className=\"flex items-center\">\r\n          <div className=\"bg-white rounded-md p-1 mr-2\">\r\n            <Image src=\"/placeholder.svg?height=24&width=24\" alt=\"Autoop Logo\" width={24} height={24} />\r\n          </div>\r\n          <span className=\"text-xl font-bold\">Autoop</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Navigation */}\r\n      <div className=\"flex-1 overflow-y-auto py-4\">\r\n        {menuItems.map((section, i) => (\r\n          <div key={i} className=\"px-4 py-2\">\r\n            <div className=\"text-xs font-semibold text-gray-400 mb-2\">{section.title}</div>\r\n            <ul className=\"space-y-1\">\r\n              {section.items.map((item, j) => (\r\n                <li key={j}>\r\n                  <Link\r\n                    href={item.href}\r\n                    className={cn(\r\n                      \"flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors\",\r\n                      pathname === item.href\r\n                        ? \"bg-white/10 text-white\"\r\n                        : \"text-gray-300 hover:bg-white/5 hover:text-white\",\r\n                    )}\r\n                  >\r\n                    <item.icon className=\"h-5 w-5 mr-3\" />\r\n                    <span>{item.name}</span>\r\n                    {item.badge && (\r\n                      <span className=\"ml-auto bg-primary text-white text-xs font-semibold px-2 py-0.5 rounded-full\">\r\n                        {item.badge}\r\n                      </span>\r\n                    )}\r\n                  </Link>\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* User Profile - Usando el componente reutilizable */}\r\n      <UserProfileSidebar />\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;;;AAhBA;;;;;;;AAkBA,MAAM,YAAY;IAChB;QACE,OAAO;QACP,OAAO;YACL;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,+NAAA,CAAA,kBAAe;YACvB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,yMAAA,CAAA,SAAM;YACd;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,2NAAA,CAAA,gBAAa;gBACnB,OAAO;YACT;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,2MAAA,CAAA,UAAO;YACf;SACD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,qNAAA,CAAA,aAAU;YAClB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,uMAAA,CAAA,QAAK;YACb;SAMD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,6MAAA,CAAA,WAAQ;YAChB;SACD;IACH;CACD;AAGM,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCAAC,KAAI;gCAAsC,KAAI;gCAAc,OAAO;gCAAI,QAAQ;;;;;;;;;;;sCAExF,6LAAC;4BAAK,WAAU;sCAAoB;;;;;;;;;;;;;;;;;0BAKxC,6LAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,SAAS,kBACvB,6LAAC;wBAAY,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;0CAA4C,QAAQ,KAAK;;;;;;0CACxE,6LAAC;gCAAG,WAAU;0CACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,kBACxB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gFACA,aAAa,KAAK,IAAI,GAClB,2BACA;;8DAGN,6LAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;8DACrB,6LAAC;8DAAM,KAAK,IAAI;;;;;;gDACf,KAAK,KAAK,kBACT,6LAAC;oDAAK,WAAU;8DACb,KAAK,KAAK;;;;;;;;;;;;uCAdV;;;;;;;;;;;uBAJL;;;;;;;;;;0BA8Bd,6LAAC,6JAAA,CAAA,qBAAkB;;;;;;;;;;;AAGzB;GAnDgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 2086, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 2118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Sheet = SheetPrimitive.Root\r\n\r\nconst SheetTrigger = SheetPrimitive.Trigger\r\n\r\nconst SheetClose = SheetPrimitive.Close\r\n\r\nconst SheetPortal = SheetPrimitive.Portal\r\n\r\nconst SheetOverlay = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Overlay\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  />\r\n))\r\nSheetOverlay.displayName = SheetPrimitive.Overlay.displayName\r\n\r\nconst sheetVariants = cva(\r\n  \"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\r\n  {\r\n    variants: {\r\n      side: {\r\n        top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\r\n        bottom:\r\n          \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\r\n        left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\r\n        right:\r\n          \"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      side: \"right\",\r\n    },\r\n  }\r\n)\r\n\r\ninterface SheetContentProps\r\n  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,\r\n    VariantProps<typeof sheetVariants> {}\r\n\r\nconst SheetContent = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Content>,\r\n  SheetContentProps\r\n>(({ side = \"right\", className, children, ...props }, ref) => (\r\n  <SheetPortal>\r\n    <SheetOverlay />\r\n    <SheetPrimitive.Content\r\n      ref={ref}\r\n      className={cn(sheetVariants({ side }), className)}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SheetPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </SheetPrimitive.Close>\r\n    </SheetPrimitive.Content>\r\n  </SheetPortal>\r\n))\r\nSheetContent.displayName = SheetPrimitive.Content.displayName\r\n\r\nconst SheetHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-2 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nSheetHeader.displayName = \"SheetHeader\"\r\n\r\nconst SheetFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nSheetFooter.displayName = \"SheetFooter\"\r\n\r\nconst SheetTitle = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\"text-lg font-semibold text-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSheetTitle.displayName = SheetPrimitive.Title.displayName\r\n\r\nconst SheetDescription = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSheetDescription.displayName = SheetPrimitive.Description.displayName\r\n\r\nexport {\r\n  Sheet,\r\n  SheetPortal,\r\n  SheetOverlay,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AASA,MAAM,QAAQ,qKAAA,CAAA,OAAmB;AAEjC,MAAM,eAAe,qKAAA,CAAA,UAAsB;AAE3C,MAAM,aAAa,qKAAA,CAAA,QAAoB;AAEvC,MAAM,cAAc,qKAAA,CAAA,SAAqB;AAEzC,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,UAAsB;QACrB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;KAVH;AAaN,aAAa,WAAW,GAAG,qKAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,oMACA;IACE,UAAU;QACR,MAAM;YACJ,KAAK;YACL,QACE;YACF,MAAM;YACN,OACE;QACJ;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAOF,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGlC,CAAC,EAAE,OAAO,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpD,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;oBAAE;gBAAK,IAAI;gBACtC,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,aAAa,WAAW,GAAG,qKAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,YAAY,WAAW,GAAG;AAE1B,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACtD,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG,qKAAA,CAAA,QAAoB,CAAC,WAAW;AAEzD,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,cAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,qKAAA,CAAA,cAA0B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2286, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/layout/top-bar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useState } from \"react\"\r\n// import { useRouter } from \"next/navigation\"\r\nimport { Search, Bell, Menu, Sun, Moon } from \"lucide-react\"\r\nimport { useTheme } from \"next-themes\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\"\r\nimport { <PERSON><PERSON>, <PERSON>etContent, <PERSON>et<PERSON>itle, SheetTrigger } from \"@/components/ui/sheet\"\r\nimport { AdminSidebar } from \"./admin-sidebar\"\r\nimport { HostSidebar } from \"./host-sidebar\"\r\nimport { ClientSidebar } from \"./client-sidebar\"\r\nimport { useUser } from '@/context/user-context'\r\n\r\nexport function TopBar() {\r\n  const [sidebarOpen, setSidebarOpen] = useState(false)\r\n  const { setTheme } = useTheme()\r\n  const { user } = useUser()\r\n\r\n\r\n  const getSidebarComponent = () => {\r\n\r\n    // check first if it's admin, then check usertype\r\n    if (user.role === \"admin\") {\r\n      return <AdminSidebar />\r\n    } else if (user.userType === \"host\") {\r\n      return <HostSidebar />\r\n    } else if (user.userType === \"client\") {\r\n      return <ClientSidebar />\r\n    }\r\n  }\r\n\r\n  const getSearchPlaceholder = () => {\r\n\r\n    if (user.role === \"admin\") {\r\n      return \"Buscar usuarios, vehículos...\"\r\n    } else if (user.userType === \"host\") {\r\n      return \"Buscar en mis vehículos...\"\r\n    } else if (user.userType === \"client\") {\r\n      return \"Buscar vehículos...\"\r\n    }\r\n  }\r\n\r\n  return (\r\n    <header className=\"bg-background border-b border-border px-4 py-3 flex items-center justify-between\">\r\n      <div className=\"flex items-center gap-4\">\r\n        {/* Mobile Sidebar - solo visible en mobile */}\r\n        <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>\r\n          <SheetTitle className=\"sr-only\">Menú</SheetTitle>\r\n          <SheetTrigger asChild>\r\n            <Button variant=\"ghost\" size=\"icon\" className=\"md:hidden\">\r\n              <Menu className=\"h-6 w-6\" />\r\n              <span className=\"sr-only\">Toggle Menu</span>\r\n            </Button>\r\n          </SheetTrigger>\r\n          <SheetContent side=\"left\" className=\"p-0 w-72 border-none text-white\">\r\n            {getSidebarComponent()}\r\n          </SheetContent>\r\n        </Sheet>\r\n\r\n        {/* Search */}\r\n        <div className=\"relative hidden md:flex items-center\">\r\n          <Search className=\"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\" />\r\n          <Input\r\n            type=\"search\"\r\n            placeholder={getSearchPlaceholder()}\r\n            className=\"pl-8 w-[300px] bg-background\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex items-center gap-3\">\r\n        {/* Theme Toggle */}\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <Button variant=\"ghost\" size=\"icon\">\r\n              <Sun className=\"h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\r\n              <Moon className=\"absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\r\n              <span className=\"sr-only\">Toggle theme</span>\r\n            </Button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent align=\"end\">\r\n            <DropdownMenuItem onClick={() => setTheme(\"light\")}>Light</DropdownMenuItem>\r\n            <DropdownMenuItem onClick={() => setTheme(\"dark\")}>Dark</DropdownMenuItem>\r\n            <DropdownMenuItem onClick={() => setTheme(\"system\")}>System</DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n\r\n        {/* Notifications */}\r\n        <Button variant=\"ghost\" size=\"icon\" className=\"relative\">\r\n          <Bell className=\"h-5 w-5\" />\r\n          <span className=\"absolute top-1 right-1 h-2 w-2 rounded-full bg-primary\" />\r\n          <span className=\"sr-only\">Notifications</span>\r\n        </Button>\r\n\r\n      </div>\r\n    </header>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA,8CAA8C;AAC9C;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;AACA;AACA;;;AAlBA;;;;;;;;;;;;AAoBO,SAAS;;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAC5B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAGvB,MAAM,sBAAsB;QAE1B,iDAAiD;QACjD,IAAI,KAAK,IAAI,KAAK,SAAS;YACzB,qBAAO,6LAAC,mJAAA,CAAA,eAAY;;;;;QACtB,OAAO,IAAI,KAAK,QAAQ,KAAK,QAAQ;YACnC,qBAAO,6LAAC,kJAAA,CAAA,cAAW;;;;;QACrB,OAAO,IAAI,KAAK,QAAQ,KAAK,UAAU;YACrC,qBAAO,6LAAC,oJAAA,CAAA,gBAAa;;;;;QACvB;IACF;IAEA,MAAM,uBAAuB;QAE3B,IAAI,KAAK,IAAI,KAAK,SAAS;YACzB,OAAO;QACT,OAAO,IAAI,KAAK,QAAQ,KAAK,QAAQ;YACnC,OAAO;QACT,OAAO,IAAI,KAAK,QAAQ,KAAK,UAAU;YACrC,OAAO;QACT;IACF;IAEA,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,oIAAA,CAAA,QAAK;wBAAC,MAAM;wBAAa,cAAc;;0CACtC,6LAAC,oIAAA,CAAA,aAAU;gCAAC,WAAU;0CAAU;;;;;;0CAChC,6LAAC,oIAAA,CAAA,eAAY;gCAAC,OAAO;0CACnB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,WAAU;;sDAC5C,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAG9B,6LAAC,oIAAA,CAAA,eAAY;gCAAC,MAAK;gCAAO,WAAU;0CACjC;;;;;;;;;;;;kCAKL,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,aAAa;gCACb,WAAU;;;;;;;;;;;;;;;;;;0BAKhB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,+IAAA,CAAA,eAAY;;0CACX,6LAAC,+IAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;;sDAC3B,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAG9B,6LAAC,+IAAA,CAAA,sBAAmB;gCAAC,OAAM;;kDACzB,6LAAC,+IAAA,CAAA,mBAAgB;wCAAC,SAAS,IAAM,SAAS;kDAAU;;;;;;kDACpD,6LAAC,+IAAA,CAAA,mBAAgB;wCAAC,SAAS,IAAM,SAAS;kDAAS;;;;;;kDACnD,6LAAC,+IAAA,CAAA,mBAAgB;wCAAC,SAAS,IAAM,SAAS;kDAAW;;;;;;;;;;;;;;;;;;kCAKzD,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAO,WAAU;;0CAC5C,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;gCAAK,WAAU;;;;;;0CAChB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMpC;GApFgB;;QAEO,mJAAA,CAAA,WAAQ;QACZ,qIAAA,CAAA,UAAO;;;KAHV", "debugId": null}}, {"offset": {"line": 2600, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/app/%28dashboard%29/dashboard/layout-2.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport type React from \"react\"\r\nimport { useEffect } from \"react\"\r\nimport { useRouter, usePathname } from \"next/navigation\"\r\nimport { AdminSidebar } from \"@/components/layout/admin-sidebar\"\r\nimport { HostSidebar } from \"@/components/layout/host-sidebar\"\r\nimport { ClientSidebar } from \"@/components/layout/client-sidebar\"\r\nimport { TopBar } from \"@/components/layout/top-bar\"\r\nimport { useUser } from '@/context/user-context'\r\n\r\n\r\nexport default function DashboardLayout2({\r\n  children,\r\n}: {\r\n  children: React.ReactNode\r\n}) {\r\n  const router = useRouter()\r\n  const pathname = usePathname()\r\n  const { user } = useUser()\r\n\r\n  // Verificar si la ruta es válida para el rol del usuario\r\n  useEffect(() => {\r\n    const userType = user.userType;\r\n\r\n    if (user.role === \"admin\") {\r\n      if (!pathname.includes('admin')) {\r\n        router.push('/dashboard/admin')\r\n      }\r\n    } else if (userType === \"host\") {\r\n      if (!pathname.includes('host')) {\r\n        router.push('/dashboard/host')\r\n      }\r\n    } else if (userType === \"client\") {\r\n      if (!pathname.includes('client')) {\r\n        router.push('/dashboard/client')\r\n      }\r\n    } else {\r\n      router.push('/dashboard')\r\n    }\r\n\r\n  }, [pathname, user.role, user.userType, router])\r\n\r\n  const getSidebarComponent = () => {\r\n\r\n    if (user.role === \"admin\") {\r\n      return <AdminSidebar />\r\n    } else if (user.userType === \"host\") {\r\n      return <HostSidebar />\r\n    } else if (user.userType === \"client\") {\r\n      return <ClientSidebar />\r\n    }\r\n\r\n  }\r\n\r\n\r\n  return (\r\n    <>\r\n      <div className=\"flex h-screen bg-background overflow-hidden\">\r\n        {/* Sidebar - solo visible en desktop */}\r\n        <aside className=\"hidden md:block \">\r\n          {getSidebarComponent()}\r\n        </aside>\r\n\r\n        <div className=\"flex flex-col flex-1 min-w-0\">\r\n          <TopBar />\r\n\r\n          <main className=\"flex-1 overflow-y-auto p-6 bg-background\">{children}</main>\r\n        </div>\r\n      </div>\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;AAYe,SAAS,iBAAiB,EACvC,QAAQ,EAGT;;IACC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAEvB,yDAAyD;IACzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,WAAW,KAAK,QAAQ;YAE9B,IAAI,KAAK,IAAI,KAAK,SAAS;gBACzB,IAAI,CAAC,SAAS,QAAQ,CAAC,UAAU;oBAC/B,OAAO,IAAI,CAAC;gBACd;YACF,OAAO,IAAI,aAAa,QAAQ;gBAC9B,IAAI,CAAC,SAAS,QAAQ,CAAC,SAAS;oBAC9B,OAAO,IAAI,CAAC;gBACd;YACF,OAAO,IAAI,aAAa,UAAU;gBAChC,IAAI,CAAC,SAAS,QAAQ,CAAC,WAAW;oBAChC,OAAO,IAAI,CAAC;gBACd;YACF,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAEF;qCAAG;QAAC;QAAU,KAAK,IAAI;QAAE,KAAK,QAAQ;QAAE;KAAO;IAE/C,MAAM,sBAAsB;QAE1B,IAAI,KAAK,IAAI,KAAK,SAAS;YACzB,qBAAO,6LAAC,mJAAA,CAAA,eAAY;;;;;QACtB,OAAO,IAAI,KAAK,QAAQ,KAAK,QAAQ;YACnC,qBAAO,6LAAC,kJAAA,CAAA,cAAW;;;;;QACrB,OAAO,IAAI,KAAK,QAAQ,KAAK,UAAU;YACrC,qBAAO,6LAAC,oJAAA,CAAA,gBAAa;;;;;QACvB;IAEF;IAGA,qBACE;kBACE,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAM,WAAU;8BACd;;;;;;8BAGH,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6IAAA,CAAA,SAAM;;;;;sCAEP,6LAAC;4BAAK,WAAU;sCAA4C;;;;;;;;;;;;;;;;;;;AAKtE;GA5DwB;;QAKP,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QACX,qIAAA,CAAA,UAAO;;;KAPF", "debugId": null}}, {"offset": {"line": 2734, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  showCloseButton = true,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\r\n  showCloseButton?: boolean\r\n}) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        {showCloseButton && (\r\n          <DialogPrimitive.Close\r\n            data-slot=\"dialog-close\"\r\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\r\n          >\r\n            <XIcon />\r\n            <span className=\"sr-only\">Close</span>\r\n          </DialogPrimitive.Close>\r\n        )}\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  )\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 2932, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/radio-group.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as RadioGroupPrimitive from \"@radix-ui/react-radio-group\"\r\nimport { Circle } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst RadioGroup = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Root\r\n      className={cn(\"grid gap-2\", className)}\r\n      {...props}\r\n      ref={ref}\r\n    />\r\n  )\r\n})\r\nRadioGroup.displayName = RadioGroupPrimitive.Root.displayName\r\n\r\nconst RadioGroupItem = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Item\r\n      ref={ref}\r\n      className={cn(\r\n        \"aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <RadioGroupPrimitive.Indicator className=\"flex items-center justify-center\">\r\n        <Circle className=\"h-2.5 w-2.5 fill-current text-current\" />\r\n      </RadioGroupPrimitive.Indicator>\r\n    </RadioGroupPrimitive.Item>\r\n  )\r\n})\r\nRadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName\r\n\r\nexport { RadioGroup, RadioGroupItem }\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC,6KAAA,CAAA,OAAwB;QACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;QAC3B,GAAG,KAAK;QACT,KAAK;;;;;;AAGX;;AACA,WAAW,WAAW,GAAG,6KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC,6KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4OACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,6KAAA,CAAA,YAA6B;YAAC,WAAU;sBACvC,cAAA,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI1B;;AACA,eAAe,WAAW,GAAG,6KAAA,CAAA,OAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3002, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium  group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50 !select-text cursor-text\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qOACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 3036, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/UserTypeModal.tsx"], "sourcesContent": ["'use client';\r\nimport { useState } from 'react';\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from \"@/components/ui/dialog\";\r\nimport { RadioGroup, RadioGroupItem } from \"@/components/ui/radio-group\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { toast } from \"sonner\";\r\nimport { apiService } from '@/services/api';\r\nimport { useUser } from '@/context/user-context';\r\n\r\n\r\nexport function UserTypeModal() {\r\n  const [userType, setUserType] = useState<'client' | 'host'>('client');\r\n  const { user } = useUser();\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  const handleSetUserType = async (userType: 'client' | 'host') => {\r\n    try {\r\n      const response = await apiService.post('/user/set-user-type', { userType, userId: user?.id });\r\n      window.location.reload();\r\n      return response.data;\r\n    } catch (error) {\r\n      toast.error(\"Error al guardar el tipo de usuario\");\r\n      console.error(error);\r\n    }\r\n  };\r\n\r\n\r\n  const handleSubmit = async () => {\r\n    setLoading(true);\r\n    try {\r\n      // await onSubmit(userType);\r\n      await handleSetUserType(userType);\r\n      // No cerramos el modal aquí, se cerrará cuando el estado del usuario se actualice\r\n    } catch (error) {\r\n      toast.error(\"Error al guardar el tipo de usuario\");\r\n      console.error(error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog open={!user?.userType} onOpenChange={() => { }}>\r\n      <DialogContent className=\"sm:max-w-[425px]\" onInteractOutside={(e) => e.preventDefault()}>\r\n        <DialogHeader>\r\n          <DialogTitle>¿Cómo deseas usar nuestra plataforma?</DialogTitle>\r\n          <DialogDescription>\r\n            Selecciona tu rol principal en la plataforma. Podrás cambiar esto más adelante.\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n        <div className=\"py-4\">\r\n          <RadioGroup value={userType} onValueChange={(value) => setUserType(value as 'client' | 'host')}>\r\n            <div className=\"flex items-center space-x-2 mb-3\">\r\n              <RadioGroupItem value=\"client\" id=\"client\" />\r\n              <Label htmlFor=\"client\">Cliente (Quiero rentar autos)</Label>\r\n            </div>\r\n            <div className=\"flex items-center space-x-2\">\r\n              <RadioGroupItem value=\"host\" id=\"host\" />\r\n              <Label htmlFor=\"host\">Anfitrión (Quiero ofrecer mis autos)</Label>\r\n            </div>\r\n          </RadioGroup>\r\n        </div>\r\n        <DialogFooter>\r\n          <Button onClick={handleSubmit} disabled={loading}>\r\n            {loading ? \"Guardando...\" : \"Continuar\"}\r\n          </Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;;AAWO,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAC5D,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM,yHAAA,CAAA,aAAU,CAAC,IAAI,CAAC,uBAAuB;gBAAE;gBAAU,QAAQ,MAAM;YAAG;YAC3F,OAAO,QAAQ,CAAC,MAAM;YACtB,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC;QAChB;IACF;IAGA,MAAM,eAAe;QACnB,WAAW;QACX,IAAI;YACF,4BAA4B;YAC5B,MAAM,kBAAkB;QACxB,kFAAkF;QACpF,EAAE,OAAO,OAAO;YACd,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM,CAAC,MAAM;QAAU,cAAc,KAAQ;kBACnD,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;YAAmB,mBAAmB,CAAC,IAAM,EAAE,cAAc;;8BACpF,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;sCAAC;;;;;;sCACb,6LAAC,qIAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAIrB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6IAAA,CAAA,aAAU;wBAAC,OAAO;wBAAU,eAAe,CAAC,QAAU,YAAY;;0CACjE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6IAAA,CAAA,iBAAc;wCAAC,OAAM;wCAAS,IAAG;;;;;;kDAClC,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAS;;;;;;;;;;;;0CAE1B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6IAAA,CAAA,iBAAc;wCAAC,OAAM;wCAAO,IAAG;;;;;;kDAChC,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAO;;;;;;;;;;;;;;;;;;;;;;;8BAI5B,6LAAC,qIAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAc,UAAU;kCACtC,UAAU,iBAAiB;;;;;;;;;;;;;;;;;;;;;;AAMxC;GA5DgB;;QAEG,qIAAA,CAAA,UAAO;;;KAFV", "debugId": null}}]}