"use client"

import Image from "next/image"
import { useRouter } from "next/navigation"
import { useQueryClient } from "@tanstack/react-query"
import toast from "react-hot-toast"
import { Badge } from "@/components/ui/badge"
// import { Button } from "@/components/ui/button"
import { DataTable } from "@/components/data-table/data-table"
import { createColumns } from "@/components/data-table/columns"
import { DropdownMenuItem } from "@/components/ui/dropdown-menu"
import { Eye, Edit, Trash2 } from "lucide-react"
import { Vehicle, vehiclesApi } from "@/lib/api/vehicles.api"

// Definiciones de estado
const statusLabels: Record<string, string> = {
  active: "Activo",
  rented: "Rentado",
  rejected: "Rechazado",
  inactive: "Inactivo",
  pending: "Pendiente",
}

const statusColors: Record<string, string> = {
  active: "bg-green-100 text-green-800",
  rented: "bg-blue-100 text-blue-800",
  rejected: "bg-red-100 text-red-800",
  inactive: "bg-gray-100 text-gray-800",
  pending: "bg-purple-100 text-purple-800",
}

interface AdminVehicleTableProps {
  vehicles: Vehicle[]
  rowCount: number
  isLoading: boolean
  pageSize: number
  pageIndex: number
  onPageChange: (page: number) => void
}

export function AdminVehicleTable({
  vehicles,
  rowCount,
  isLoading,
  pageSize,
  pageIndex,
  onPageChange
}: AdminVehicleTableProps) {
  const router = useRouter()
  const queryClient = useQueryClient()

  const handleStatusChange = async (id: string, status: string) => {
    try {
      await vehiclesApi.admin.updateStatus(id, status)
      toast.success(`El vehículo ahora está ${status === 'active' ? 'activo' : status}`)
      queryClient.invalidateQueries({ queryKey: ['admin-vehicles'] })
    } catch (error) {
      console.log('Error updating status:', error)
      toast.error("No se pudo actualizar el estado del vehículo")
    }
  }

  const columns = createColumns<Vehicle>(
    [
      {
        accessorKey: "vehicle",
        header: "Vehículo",
        cell: ({ row }) => {
          const vehicle = row.original
          return (
            <div className="flex items-center space-x-3">
              <Image
                src={Array.isArray(vehicle.images) && vehicle.images.length > 0
                  ? vehicle.images[0]
                  : "/placeholder.svg?height=60&width=80"}
                alt={`${vehicle.make} ${vehicle.model}`}
                width={80}
                height={60}
                className="rounded-md object-cover"
              />
              <div>
                <div className="font-medium">
                  {vehicle.make} {vehicle.model} {vehicle.year}
                </div>
                <div className="text-sm text-muted-foreground">
                  {vehicle.color} • {vehicle.plate}
                </div>
                <div className="text-xs text-muted-foreground">VIN: {vehicle.vin}</div>
              </div>
            </div>
          )
        },
      },
      {
        accessorKey: "host",
        header: "Anfitrión",
        cell: ({ row }) => <div className="font-medium">{row.original.host.name}</div>,
      },
      {
        accessorKey: "status",
        header: "Estado",
        cell: ({ row }) => {
          const status = row.original.status
          return (
            <Badge className={statusColors[status as keyof typeof statusColors] || statusColors.pending}>
              {statusLabels[status as keyof typeof statusLabels] || "Desconocido"}
            </Badge>
          )
        },
      },
      {
        accessorKey: "price",
        header: "Precio/Día",
        cell: ({ row }) => <div>${row.original.price}</div>,
      },
      {
        accessorKey: "reviews",
        header: "Rentas",
        cell: ({ row }) => <div>{row.original.reviews}</div>,
      },
      {
        accessorKey: "rating",
        header: "Calificación",
        cell: ({ row }) => <div>{row.original.rating.toFixed(1)}</div>,
      },
    ],
    {
      onView: (vehicle) => router.push(`/dashboard/admin/vehicles/${vehicle.id}`),
      onEdit: (vehicle) => router.push(`/dashboard/admin/vehicles/${vehicle.id}/edit`),
      onDelete: (vehicle) => {
        if (confirm(`¿Estás seguro de que deseas desactivar ${vehicle.make} ${vehicle.model}?`)) {
          handleStatusChange(vehicle.id, "inactive")
        }
      },
      extraActions: (vehicle) => [
        <DropdownMenuItem key="view" onClick={() => router.push(`/dashboard/admin/vehicles/${vehicle.id}`)}>
          <Eye className="mr-2 h-4 w-4" />
          Ver detalles
        </DropdownMenuItem>,
        <DropdownMenuItem key="edit" onClick={() => router.push(`/dashboard/admin/vehicles/${vehicle.id}/edit`)}>
          <Edit className="mr-2 h-4 w-4" />
          Editar
        </DropdownMenuItem>,
        <DropdownMenuItem
          key="deactivate"
          className="text-red-600"
          onClick={() => {
            if (confirm(`¿Estás seguro de que deseas desactivar ${vehicle.make} ${vehicle.model}?`)) {
              handleStatusChange(vehicle.id, "inactive")
            }
          }}
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Desactivar
        </DropdownMenuItem>
      ]
    }
  )

  return (
    <DataTable
      columns={columns}
      data={vehicles}
      rowCount={rowCount}
      pageSize={pageSize}
      pageIndex={pageIndex}
      onPageChange={onPageChange}
      isLoading={isLoading}
    />
  )
}