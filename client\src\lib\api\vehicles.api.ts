import { apiService } from '@/services/api';
import { cache } from 'react';

export interface VehicleFormData {
  make: string;
  model: string;
  year: number;
  color: string;
  vin: string;
  plate: string;
  state_code: string;
  country_code: string;
  price: number;
  description: string;

  // Nuevos campos estructurados
  engineSize: number;
  transmission: string;
  trim: string;
  bodyType: string;

  // Campos existentes
  features: any;
  amenities: string[];
  // images?: string[];
  status?: string;
  images?: File[];
  // vinDocument?: File;
  // plateDocument?: File;
  // registrationDocument?: File;
  // insurancePolicyDocument?: File;
  // titleDocument?: File;
  vinDocument: File[];
  plateDocument: File[];
  registrationDocument: File[];
  insurancePolicyDocument: File[];
}

export interface Host {
  id: string;
  name: string;
  image?: string;
  email: string;
  phone: string;
  status: string;
  isVerified: boolean;
  isBlocked: boolean;

  createdAt: string;
  updatedAt: string;
}

export interface Vehicle {
  id: string;
  make: string;
  model: string;
  year: number;
  color: string;
  vin?: string;
  // licensePlate?: string;
  plate?: string;
  state_code?: string;
  country_code?: string;
  price: number;
  rating: number;
  reviews: number;

  // Nuevos campos estructurados
  engineSize: number;
  transmission: string;
  trim: string;
  bodyType: string;

  approvalHistory: {
    action: string;
    date: string;
    reason?: string;
    userId?: string;
    user?: {
      id: string;
      name: string;
      email: string;
    };
  }[];

  // Campos existentes
  images: string[];
  features: {
    fuelType: string;
    seats: number;
    mileage: number;
    registrationNumber: string;
    insurancePolicy: string;
    rules: string;
    location: string;
    weeklyRate: number;
    monthlyRate: number;
  };
  description: string;
  amenities: string[];
  host: Host;
  status: string;
  createdAt: string;
  updatedAt: string;
}

export interface VehicleResponse {
  data: Vehicle[];
  pagination: Pagination;
}

export interface TimeSlot {
  startTime: string;
  endTime: string;
}

export interface AvailabilitySettings {
  defaultCheckInTime: string;
  defaultCheckOutTime: string;
  minimumRentalNights: number;
  maximumRentalNights: number;
  // bufferTimeBetweenRentals: number; // horas
  advanceBookingPeriod: number; // días
  instantBooking: boolean;
  allowSameDayBooking: boolean;
  cancellationPolicy: "flexible" | "moderate" | "strict";

  mondayAvailable: boolean;
  tuesdayAvailable: boolean;
  wednesdayAvailable: boolean;
  thursdayAvailable: boolean;
  fridayAvailable: boolean;
  saturdayAvailable: boolean;
  sundayAvailable: boolean;
  blockedDates: any[];
}

export interface VehicleDocuments {
  vinDocument: string;
  plateDocument: string;
  registrationDocument: string;
  insurancePolicyDocument: string;
}

// Agregar esta interfaz para las estadísticas
export interface VehicleStats {
  stats: {
    total: number;
    active: number;
    rented: number;
    maintenance: number;
    pending: number;
    totalReservations?: number;
    averageRating?: number;
    averageIncome?: number;
  }
}

export const vehiclesApi = {
  // Obtener todos los vehículos (público)
  getAll: async (params: { page: number; limit: number }) => {
    const result = await apiService.get<VehicleResponse>('/vehicles', { params });
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.error);
    }
  },

  // Obtener un vehículo por ID (público)
  getById: async (id: string) => {
    const result = await apiService.get<Vehicle>(`/vehicles/${id}`);
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.error);
    }
  },

  // Obtener todos los vehículos (admin)
  getAllForAdmin: async () => {
    const result = await apiService.get<VehicleResponse>('/admin/vehicles');
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.error);
    }
  },

  // Obtener vehículos del host actual
  getMyVehicles: async (): Promise<VehicleResponse[]> => {
    const result = await apiService.get<VehicleResponse[]>('/host/vehicles');
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.error);
    }
  },

  // Añadir método para obtener un vehículo específico del host
  getMyVehicleById: async (id: string): Promise<VehicleResponse> => {
    const result = await apiService.get<VehicleResponse>(`/host/vehicles/${id}`);
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.error);
    }
  },

  getAvailabilitySettings: async (vehicleId: string): Promise<AvailabilitySettings> => {
    const result = await apiService.get<AvailabilitySettings>(`/vehicles/${vehicleId}/availability`);
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.error);
    }
  },



  host: {
    getAll: async (params: { page?: number; limit?: number }) => {
      const result = await apiService.get<VehicleResponse>('/host/vehicles', { params });
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },
    getById: async (id: string) => {
      const result = await apiService.get<Vehicle>(`/host/vehicles/${id}`);
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },
    create: async (data: VehicleFormData) => {
      const result = await apiService.post<Vehicle>('/host/vehicles', data);
      if (result.success) {
        return result.data;
      } else {
        // console.error('Error creating vehicle:', result.error);
        throw new Error(result.error.message);
      }
    },

    uploadFiles: async (vehicleId: string, data: Partial<VehicleFormData>) => {
      const formData = new FormData();

      // Agregar archivos al formData para los campos images, vinDocument, plateDocument, registrationDocument, insurancePolicyDocument
      if (data.images) {
        for (const file of data.images) {
          formData.append('images', file);
        }
      }
      if (data.vinDocument) {
        for (const file of data.vinDocument) {
          formData.append('vinDocument', file);
        }
      }
      if (data.plateDocument) {
        for (const file of data.plateDocument) {
          formData.append('plateDocument', file);
        }
      }
      if (data.registrationDocument) {
        for (const file of data.registrationDocument) {
          formData.append('registrationDocument', file);
        }
      }
      if (data.insurancePolicyDocument) {
        for (const file of data.insurancePolicyDocument) {
          formData.append('insurancePolicyDocument', file);
        }
      }

      const result = await apiService.patch<Vehicle>(`/host/vehicles/${vehicleId}/upload-files`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },

    // Añadir método para actualizar archivos
    updateFiles: async (vehicleId: string, data: Partial<VehicleFormData>, imagesToRemove: string[] = []) => {

      const formData = new FormData();

      // Agregar archivos al formData solo si existen y tienen elementos
      if (data.images && data.images.length > 0) {
        for (const file of data.images) {
          formData.append('images', file);
        }
      }

      if (data.vinDocument && data.vinDocument.length > 0) {
        for (const file of data.vinDocument) {
          formData.append('vinDocument', file);
        }
      }

      if (data.plateDocument && data.plateDocument.length > 0) {
        for (const file of data.plateDocument) {
          formData.append('plateDocument', file);
        }
      }

      if (data.registrationDocument && data.registrationDocument.length > 0) {
        for (const file of data.registrationDocument) {
          formData.append('registrationDocument', file);
        }
      }

      if (data.insurancePolicyDocument && data.insurancePolicyDocument.length > 0) {
        for (const file of data.insurancePolicyDocument) {
          formData.append('insurancePolicyDocument', file);
        }
      }

      // Agregar imágenes a eliminar
      if (imagesToRemove.length > 0) {
        formData.append('imagesToRemove', JSON.stringify(imagesToRemove));
      }

      const result = await apiService.patch<Vehicle>(`/host/vehicles/${vehicleId}/update-files`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('Result:', result);
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error?.message || 'Ha ocurrido un error actualizando los archivos del vehículo.');
      }
    },

    update: async (id: string, data: Partial<VehicleFormData>) => {
      const result = await apiService.put<Vehicle>(`/host/vehicles/${id}`, data);
      console.log('Result of update vehicle:', result);
      if (result.success) {
        return result.data;
      } else {
        console.error('Error updating vehicle:', result.error);
        throw new Error(result.error);
      }
    },
    // Eliminar un vehículo (host)
    delete: async (id: string) => {
      const result = await apiService.delete(`/host/vehicles/${id}`);
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },
    blockDates: async (vehicleId: string, data: {
      startDate: string;
      endDate: string;
      reason?: string;
    }): Promise<any> => {

      if (!data.startDate || !data.endDate) {
        throw new Error("Se requieren fechas válidas para bloquear");
      }

      const result = await apiService.post('/host/reservations/block-dates', {
        vehicleId,
        startDate: data.startDate,
        endDate: data.endDate,
        reason: data.reason || "No disponible"
      });
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },
    // Actualizar configuración de disponibilidad
    updateAvailabilitySettings: async (vehicleId: string, data: Partial<AvailabilitySettings>): Promise<AvailabilitySettings> => {
      const result = await apiService.put<AvailabilitySettings>(`/host/vehicles/${vehicleId}/availability`, data);
      console.log('error: ', result.error);
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
    }
  },
    getStats: async (): Promise<any> => {
      const result = await apiService.get('/host/vehicles/stats');
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },

    // Obtener documentos de un vehículo
    getDocuments: async (vehicleId: string) => {
      const result = await apiService.get<VehicleDocuments>(`/host/vehicles/${vehicleId}/documents`);
      console.log('Result of get documents:', result);
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },
    requestReview: async (vehicleId: string) => {
      const result = await apiService.post(`/host/vehicles/${vehicleId}/request-review`);
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    }
  },



  // Actualizar estado de un vehículo (host)
  updateStatus: async (id: string, status: string): Promise<VehicleResponse> => {
    const result = await apiService.patch<VehicleResponse>(`/host/vehicles/${id}/status`, { status });
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.error);
    }
  },

  // Funciones específicas para administradores
  admin: {

    getById: async (id: string) => {
      const result = await apiService.get<Vehicle>(`/admin/vehicles/${id}`);
      if (result.success) {
        console.log('vehicle from api', result.data);
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },

    // Obtener vehículos pendientes de aprobación
    getPendingVehicles: async () => {
      const result = await apiService.get<Vehicle[]>('/admin/vehicles/pending');
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error.message);
      }
    },

    // Aprobar un vehículo
    approveVehicle: async (id: string) => {
      const result = await apiService.post<Vehicle>(`/admin/vehicles/${id}/approve`);
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },

    // Rechazar un vehículo
    rejectVehicle: async (id: string, reason: string): Promise<any> => {
      const result = await apiService.post(`/admin/vehicles/${id}/reject`, { reason });
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },

    // Actualizar un vehículo (admin)
    update: async (id: string, data: Partial<VehicleFormData>) => {
      const result = await apiService.put<Vehicle>(`/admin/vehicles/${id}`, data);
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },

    // Eliminar un vehículo (admin)
    delete: async (id: string): Promise<any> => {
      const result = await apiService.delete(`/admin/vehicles/${id}`);
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },

    // Actualizar estado de un vehículo (admin)
    updateStatus: async (id: string, status: string) => {
      const result = await apiService.patch<Vehicle>(`/admin/vehicles/${id}/status`, { status });
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },
    // Obtener todos los vehículos para el administrador
    getAll: async (params: { page: number; limit: number }) => {
      const result = await apiService.get<VehicleResponse>('/admin/vehicles', { params });
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },
    // Nuevo método para obtener estadísticas
    getStats: async (): Promise<VehicleStats> => {
      const result = await apiService.get<VehicleStats>('/admin/vehicles/stats');
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },
  },

  // Obtener fechas no disponibles para un vehículo
  getUnavailableDates: async (vehicleId: string): Promise<string[]> => {
    const result = await apiService.get</* string[] */
      { date: string, by: string, reason?: string }[]
    >(`/reservations/unavailable-dates/${vehicleId}`);
    if (result.success) {
      // return { data: result.data };
      // return { data: result.data.map((item) => item.date) };
      return result.data.map((item) => item.date);
    } else {
      throw new Error(result.error);
    }
  },
};

export const getVehicleById = cache(vehiclesApi.getById);



