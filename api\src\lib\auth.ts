// src/lib/auth.ts
import { betterAuth, BetterAuthOptions } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { createAuthMiddleware, /* multiSession, */ openAPI } from "better-auth/plugins";
import { admin } from "better-auth/plugins";
import { prisma } from './prisma';
import { sendEmail } from './sendVerificationEmail';
import { bearer } from "better-auth/plugins"
import { sendVerificationEmail, sendWelcomeEmail, sendResetPasswordEmail } from '@/emails/senders';


export const auth = betterAuth({
  database: prismaAdapter(prisma, {
    provider: "postgresql",
  }),
  databaseHooks: {
    user: {
      create: {
        after: async (user) => {
          // send a welcome email
          await sendWelcomeEmail(user.email);
        }
      }
    }
  },
  advanced: {
    // Comentamos todas las validaciones de entorno y usamos siempre configuración de desarrollo
    disableCSRFCheck: true, // Siempre deshabilitado como en dev
    // crossSubDomainCookies y defaultCookieAttributes siempre undefined como en dev
  },
  trustedOrigins: async (req) => {
    const origin = req.headers.get('origin')! || 'http://localhost:4000';
    // console.log('Origin: ', origin);
    if (!origin) return [origin];
    return [origin];
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
    cookieCache: {
      enabled: false,
    },
  },
  user: {
    additionalFields: {
      userType: {
        type: "string",
        required: false,
      },
      availableUserTypes: {
        type: "string[]",
        required: false,
      },
      isHostVerified: {
        type: "boolean",
        required: false,
      },
    },
    changeEmail: {
      enabled: true,
      sendChangeEmailVerification: async ({ newEmail, url }) => {
        await sendEmail({
          to: newEmail,
          subject: 'Verify your email change',
          text: `Click the link to verify: ${url}`
        })
      }
    }
  },
  socialProviders: {
    google: {
      enabled: true,
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
    }
  },
  plugins: [
    openAPI(),
    admin({
      impersonationSessionDuration: 60 * 60 * 24 * 7, // 7 days
    }),
    bearer(),
  ], // api/auth/reference
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: true,
    sendResetPassword: async ({ user, token, url }, req) => {

      const origin = req?.headers.get('origin') || 'http://localhost:4000';
      const baseUrl = process.env.BETTER_AUTH_URL || 'http://localhost:3000';

      const resetUrl = `${baseUrl}/api/auth/reset-password?token=${token}&callbackURL=${origin}${process.env.RESET_PASSWORD_PATH || '/reset-password'}`;
      console.log('----------------------------------------------------')
      console.log('Origin: ', origin);

      console.log('Reset URL: ', resetUrl);

      console.log('url: ', resetUrl);
      console.log('----------------------------------------------------')
      await sendResetPasswordEmail({
        to: user.email,
        url: resetUrl,
        name: user.name,
      });
    },
  },
  emailVerification: {
    sendOnSignUp: true,
    autoSignInAfterVerification: true,
    sendVerificationEmail: async ({ user, token }, req) => {
      const origin = req?.headers.get('origin') || 'http://localhost:4000';
      const baseUrl = process.env.BETTER_AUTH_URL || 'http://localhost:3000';
      const verificationPath = process.env.EMAIL_VERIFICATION_PATH || '/email-verified';

      const verificationUrl = `${baseUrl}/api/auth/verify-email?token=${token}&callbackURL=${origin}${verificationPath}`;

      console.log('Verification URL: ', verificationUrl);

      await sendVerificationEmail({
        to: user.email,
        url: verificationUrl,
        name: user.name,
      });

    },
  },
  hooks: {
    after: createAuthMiddleware(async (ctx) => {
      // Siempre ejecutar el código como si fuera dev
      if (ctx.path.includes("/sign-up/email")) {
        console.log('------------------------------------------------------------')
        console.log('------------------------------------------------------------')
        console.log('------------------------------------------------------------')

        console.log('ctx path:', ctx.path, ctx.path.includes("/sign-up/email"));
        console.log('Context of sign up: ', ctx.context);

        // Inicializar availableUserTypes basado en el userType seleccionado
        const user = (ctx.context.returned as any)?.user;
        if (user && user.userType) {
          console.log('Initializing availableUserTypes for user:', user.id, 'with userType:', user.userType);

          // Actualizar el usuario para incluir availableUserTypes y mainUserType
          await prisma.user.update({
            where: { id: user.id },
            data: {
              availableUserTypes: [user.userType],
              mainUserType: user.userType  // Establecer el tipo de usuario original
            }
          });

          console.log('Successfully initialized availableUserTypes:', [user.userType]);
        }

        console.log('------------------------------------------------------------')
        console.log('------------------------------------------------------------')
        console.log('------------------------------------------------------------')
      }
      if (ctx.path.includes("/sign-in/email")) {
        console.log('ctx path:', ctx.path, ctx.path.includes("/sign-in/email"));
        // Add token to url in returned object instead of location header
        const origin = ctx.headers?.get('origin');
        // console.log('Complete context after sign in: ', ctx);
        console.log('Returned: ', ctx.context.returned);

        const url = `${origin}/?token=${(ctx.context.returned as any).token.substring(0, 32)}`;
        console.log('Full URL: ', url);

        ctx.context.returned = {
          ...ctx.context.returned as any,
          // url: `${origin}/?token=${(ctx.context.returned as any).token.substring(0, 32)}`
          url,
          redirect: true,
          location: url
        }

        // const { token } = await auth.api.generateOneTimeToken({
        //   headers: ctx.headers,
        // });
        // console.log('Token: ', token);

        // Make a request to the frontend to set the token in the cookie
        await fetch(`${origin}/api/set-private-token?token=${(ctx.context.returned as any).token.substring(0, 32)}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            token: (ctx.context.returned as any).token.substring(0, 32),
            url,
          })
        });

        // // const json = await response.json();
        // // console.log('Response from set-private-token: ', json);

        // ctx.setHeader('location', url);



        // Need to find a way to send the token to the frontend api request using Nextjs, help me set it please:

        // ctx.redirect(fullUrl);
        // console.log('Context after sign in with credentials: ', ctx);
        console.log('------------------------------------------------------------------------')
        // const destination = `${origin}/?token=${ctx.context.returned.token.substring(0, 32)}`;
        console.log('Destination: ', url);
        ctx.setHeader("location", url);
        // ctx.status = 307;
        return;
        // ctx.redirect(url);
        return;
      }

      if (ctx.path.startsWith("/callback")) {
        const headers = ctx.context.responseHeaders;
        const location = headers?.get('location');

        if (!location) return;

        const locationHost = new URL(location).host;

        if (locationHost == ctx.headers?.get('host')) return; // if same host/domain, return

        const token = ctx.getCookie('better-auth.session_token') || ctx.getCookie('__Secure-better-auth.session_token');
        if (!token) return;

        const newLocation = `${location}?token=${token.substring(0, 32)}`;
        ctx.setHeader('location', newLocation);
        console.log('Context on using callback: ', ctx);
        console.log('------------------------------------------------------------------------')
      }
    }),
  },
} satisfies BetterAuthOptions);

export type Session = typeof auth.$Infer.Session;



/* ========================================================================== */


//   type ExtractPermissionTypeParams = Parameters<typeof auth.api.hasPermission>[0]['body']['permission'];

// async function checkPermission(permission: ExtractPermissionTypeParams, headers: Headers) {

//   await auth.api.hasPermission({
//     headers,
//     body: {
//       permission
//     }
//   });
// }

// checkPermission({
//   organization: ["create"]
// }, new Headers());


