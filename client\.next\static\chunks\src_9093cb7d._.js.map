{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/lib/api/users.api.ts"], "sourcesContent": ["import { apiService } from \"@/services/api\";\n\n\nexport interface UserResponse {\n  id: string;\n  name: string;\n  email: string;\n  phone: string;\n  status: string;\n  image: string;\n  isVerified: boolean;\n  isBlocked: boolean;\n  role: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n\n\n// Tipos para paginación\ninterface PaginationParams {\n  page?: number;\n  limit?: number;\n}\n\ninterface PaginatedResponse<T> {\n  data: T[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n  };\n}\n\nexport const usersApi = {\n  // Obtener todos los hosts (admin)\n  getAllHosts: async (params: PaginationParams = {}) => {\n    const { page = 1, limit = 10 } = params;\n    const result = await apiService.get<PaginatedResponse<UserResponse>>(`/admin/users/hosts?page=${page}&limit=${limit}`);\n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error);\n    }\n  },\n\n  // Obtener todos los clientes (admin)\n  getAllClients: async (params: PaginationParams = {}) => {\n    const { page = 1, limit = 10 } = params;\n    const result = await apiService.get<PaginatedResponse<UserResponse>>(`/admin/users/clients?page=${page}&limit=${limit}`);\n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error);\n    }\n  },\n\n  // Verificar un usuario\n  verifyUser: async (userId: string) => {\n    const result = await apiService.patch<UserResponse>(`/admin/users/${userId}/verify`);\n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error);\n    }\n  },\n\n};"], "names": [], "mappings": ";;;AAAA;;AAmCO,MAAM,WAAW;IACtB,kCAAkC;IAClC,aAAa,OAAO,SAA2B,CAAC,CAAC;QAC/C,MAAM,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,GAAG;QACjC,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAkC,CAAC,wBAAwB,EAAE,KAAK,OAAO,EAAE,OAAO;QACrH,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,qCAAqC;IACrC,eAAe,OAAO,SAA2B,CAAC,CAAC;QACjD,MAAM,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,GAAG;QACjC,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAkC,CAAC,0BAA0B,EAAE,KAAK,OAAO,EAAE,OAAO;QACvH,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;IAEA,uBAAuB;IACvB,YAAY,OAAO;QACjB,MAAM,SAAS,MAAM,yHAAA,CAAA,aAAU,CAAC,KAAK,CAAe,CAAC,aAAa,EAAE,OAAO,OAAO,CAAC;QACnF,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK;QAC9B;IACF;AAEF", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/table.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Table = React.forwardRef<\r\n  HTMLTableElement,\r\n  React.HTMLAttributes<HTMLTableElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div className=\"relative w-full overflow-auto\">\r\n    <table\r\n      ref={ref}\r\n      className={cn(\"w-full caption-bottom text-sm\", className)}\r\n      {...props}\r\n    />\r\n  </div>\r\n))\r\nTable.displayName = \"Table\"\r\n\r\nconst TableHeader = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\r\n))\r\nTableHeader.displayName = \"TableHeader\"\r\n\r\nconst TableBody = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tbody\r\n    ref={ref}\r\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nTableBody.displayName = \"TableBody\"\r\n\r\nconst TableFooter = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tfoot\r\n    ref={ref}\r\n    className={cn(\r\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableFooter.displayName = \"TableFooter\"\r\n\r\nconst TableRow = React.forwardRef<\r\n  HTMLTableRowElement,\r\n  React.HTMLAttributes<HTMLTableRowElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tr\r\n    ref={ref}\r\n    className={cn(\r\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableRow.displayName = \"TableRow\"\r\n\r\nconst TableHead = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.ThHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <th\r\n    ref={ref}\r\n    className={cn(\r\n      \"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableHead.displayName = \"TableHead\"\r\n\r\nconst TableCell = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.TdHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <td\r\n    ref={ref}\r\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nTableCell.displayName = \"TableCell\"\r\n\r\nconst TableCaption = React.forwardRef<\r\n  HTMLTableCaptionElement,\r\n  React.HTMLAttributes<HTMLTableCaptionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <caption\r\n    ref={ref}\r\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nTableCaption.displayName = \"TableCaption\"\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAM,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oGACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;QAC/D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;;AAGb,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/ui/skeleton-row.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils';\nimport React from 'react';\n\ninterface SkeletonRowProps {\n  totalRows?: number;\n  className?: string;\n}\n\nexport function SkeletonRow({ totalRows = 1, className }: SkeletonRowProps) {\n  return (\n    <>\n      <tr className=\"border-b py-2\"  >\n        {\n\n          Array.from({ length: totalRows }).map((_, i) => {\n            return (\n              <td className=\"py-3\" key={i}>\n                {/* <div className=\"h-4 w-[100px] bg-gray-200 animate-pulse rounded-none\" /> */}\n                <div className={cn(\"h-4 w-[100px] bg-gray-200 animate-pulse rounded-none\", className)} />\n              </td>\n            )\n          })\n\n        }\n      </tr>\n    </>\n  )\n}\n\n"], "names": [], "mappings": ";;;;AAAA;;;AAQO,SAAS,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,EAAoB;IACxE,qBACE;kBACE,cAAA,6LAAC;YAAG,WAAU;sBAGV,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAU,GAAG,GAAG,CAAC,CAAC,GAAG;gBACxC,qBACE,6LAAC;oBAAG,WAAU;8BAEZ,cAAA,6LAAC;wBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;;;;;;mBAFnD;;;;;YAK9B;;;;;;;AAMV;KAnBgB", "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/data-table/data-table.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport {\r\n  type ColumnDef,\r\n  // type ColumnFiltersState,\r\n  // type SortingState,\r\n  // type VisibilityState,\r\n  flexRender,\r\n  getCoreRowModel,\r\n  // getFilteredRowModel,\r\n  getPaginationRowModel,\r\n  // getSortedRowModel,\r\n  useReactTable,\r\n} from \"@tanstack/react-table\"\r\n\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { SkeletonRow } from '../ui/skeleton-row'\r\n// import { Input } from \"@/components/ui/input\"\r\n// import {\r\n//   DropdownMenu,\r\n//   DropdownMenuCheckboxItem,\r\n//   DropdownMenuContent,\r\n//   DropdownMenuTrigger,\r\n// } from \"@/components/ui/dropdown-menu\"\r\n// import { ChevronDown } from \"lucide-react\"\r\n\r\ninterface DataTableProps<TData, TValue> {\r\n  columns: ColumnDef<TData, TValue>[]\r\n  data: TData[];\r\n  rowCount: number;\r\n  pageSize: number;\r\n  pageIndex: number;\r\n  onPageChange?: (page: number) => void;\r\n  isLoading?: boolean;\r\n}\r\n\r\nexport function DataTable<TData, TValue>({\r\n  columns,\r\n  data,\r\n  rowCount,\r\n  pageSize,\r\n  pageIndex,\r\n  onPageChange,\r\n  isLoading,\r\n}: DataTableProps<TData, TValue>) {\r\n  'use no memo';\r\n  const [pagination, setPagination] = React.useState({ pageIndex, pageSize })\r\n  // console.log('rowCount', rowCount, data.length);\r\n  const table = useReactTable({\r\n    data,\r\n    columns,\r\n    getCoreRowModel: getCoreRowModel(),\r\n    getPaginationRowModel: getPaginationRowModel(),\r\n    manualPagination: true,\r\n    rowCount: rowCount || data.length,\r\n    // pageCount is calculated automatically based on the rowCount and pageSize\r\n    pageCount: Math.ceil(rowCount / pageSize),\r\n    onPaginationChange: (updater) => {\r\n      // if (isLoading) {\r\n      //   setStateData([]); // Clear the data while loading\r\n      // }\r\n      const newPagination = typeof updater === 'function' ? updater(table.getState().pagination) : updater;\r\n      setPagination(newPagination);\r\n      if (onPageChange) {\r\n        onPageChange(newPagination.pageIndex + 1);\r\n      }\r\n    },\r\n    state: {\r\n      pagination,\r\n    },\r\n  })\r\n\r\n  return (\r\n    // <div className=\"space-y-4\">\r\n    //   <div className=\"flex items-center justify-between\">\r\n    //     {searchKey && (\r\n    //       <div className=\"flex items-center py-4\">\r\n    //         <Input\r\n    //           placeholder={searchPlaceholder}\r\n    //           value={(table.getColumn(searchKey)?.getFilterValue() as string) ?? \"\"}\r\n    //           onChange={(event) => table.getColumn(searchKey)?.setFilterValue(event.target.value)}\r\n    //           className=\"max-w-sm\"\r\n    //         />\r\n    //       </div>\r\n    //     )}\r\n    //     <div className=\"flex items-center space-x-2 ml-auto\">\r\n    //       <DropdownMenu>\r\n    //         <DropdownMenuTrigger asChild>\r\n    //           <Button variant=\"outline\" className=\"ml-auto\">\r\n    //             Columnas <ChevronDown className=\"ml-2 h-4 w-4\" />\r\n    //           </Button>\r\n    //         </DropdownMenuTrigger>\r\n    //         <DropdownMenuContent align=\"end\">\r\n    //           {table\r\n    //             .getAllColumns()\r\n    //             .filter((column) => column.getCanHide())\r\n    //             .map((column) => {\r\n    //               return (\r\n    //                 <DropdownMenuCheckboxItem\r\n    //                   key={column.id}\r\n    //                   className=\"capitalize\"\r\n    //                   checked={column.getIsVisible()}\r\n    //                   onCheckedChange={(value) => column.toggleVisibility(!!value)}\r\n    //                 >\r\n    //                   {column.id}\r\n    //                 </DropdownMenuCheckboxItem>\r\n    //               )\r\n    //             })}\r\n    //         </DropdownMenuContent>\r\n    //       </DropdownMenu>\r\n    //     </div>\r\n    //   </div>\r\n    //   <div className=\"rounded-md border\">\r\n    //     <Table>\r\n    //       <TableHeader>\r\n    //         {table.getHeaderGroups().map((headerGroup) => (\r\n    //           <TableRow key={headerGroup.id}>\r\n    //             {headerGroup.headers.map((header) => {\r\n    //               return (\r\n    //                 <TableHead key={header.id}>\r\n    //                   {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}\r\n    //                 </TableHead>\r\n    //               )\r\n    //             })}\r\n    //           </TableRow>\r\n    //         ))}\r\n    //       </TableHeader>\r\n    //       <TableBody>\r\n    //         {table.getRowModel().rows?.length ? (\r\n    //           table.getRowModel().rows.map((row) => (\r\n    //             <TableRow key={row.id} data-state={row.getIsSelected() && \"selected\"}>\r\n    //               {row.getVisibleCells().map((cell) => (\r\n    //                 <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>\r\n    //               ))}\r\n    //             </TableRow>\r\n    //           ))\r\n    //         ) : (\r\n    //           <TableRow>\r\n    //             <TableCell colSpan={columns.length} className=\"h-24 text-center\">\r\n    //               No se encontraron resultados.\r\n    //             </TableCell>\r\n    //           </TableRow>\r\n    //         )}\r\n    //       </TableBody>\r\n    //     </Table>\r\n    //   </div>\r\n    //   <div className=\"flex items-center justify-end space-x-2 py-4\">\r\n    //     <div className=\"flex-1 text-sm text-muted-foreground\">\r\n    //       {table.getFilteredSelectedRowModel().rows.length} de {table.getFilteredRowModel().rows.length} fila(s)\r\n    //       seleccionada(s).\r\n    //     </div>\r\n    //     <div className=\"space-x-2\">\r\n    //       <Button\r\n    //         variant=\"outline\"\r\n    //         size=\"sm\"\r\n    //         onClick={() => table.previousPage()}\r\n    //         disabled={!table.getCanPreviousPage()}\r\n    //       >\r\n    //         Anterior\r\n    //       </Button>\r\n    //       <Button variant=\"outline\" size=\"sm\" onClick={() => table.nextPage()} disabled={!table.getCanNextPage()}>\r\n    //         Siguiente\r\n    //       </Button>\r\n    //     </div>\r\n    //   </div>\r\n    // </div>\r\n    <div className=\"rounded-md border\">\r\n      <Table>\r\n        <TableHeader>\r\n          {table.getHeaderGroups().map((headerGroup) => (\r\n            <TableRow key={headerGroup.id}>\r\n              {headerGroup.headers.map((header) => {\r\n                return (\r\n                  <TableHead key={header.id}>\r\n                    {header.isPlaceholder\r\n                      ? null\r\n                      : flexRender(\r\n                        header.column.columnDef.header,\r\n                        header.getContext()\r\n                      )}\r\n                  </TableHead>\r\n                )\r\n              })}\r\n            </TableRow>\r\n          ))}\r\n        </TableHeader>\r\n        <TableBody>\r\n          {isLoading ? Array.from({ length: pageSize }).map((_, index) => (\r\n            <SkeletonRow key={index} totalRows={columns.length} className=\"h-8 w-[100%]\" />\r\n          )) : (\r\n            <>\r\n\r\n                {table.getRowModel().rows?.length ? (\r\n                  table.getPaginationRowModel().rows.map((row) => (\r\n                    <TableRow\r\n                      key={row.id}\r\n                      data-state={row.getIsSelected() && \"selected\"}\r\n                    >\r\n                      {row.getVisibleCells().map((cell) => (\r\n                        <TableCell key={cell.id}>\r\n                          {flexRender(cell.column.columnDef.cell, cell.getContext())}\r\n                        </TableCell>\r\n                      ))}\r\n                    </TableRow>\r\n                  ))\r\n                ) : (\r\n                  <TableRow>\r\n                    <TableCell colSpan={columns.length} className=\"h-24 text-center\">\r\n                      No results.\r\n                    </TableCell>\r\n                  </TableRow>\r\n                )}\r\n            </>\r\n          )}\r\n        </TableBody>\r\n      </Table>\r\n      <div className=\"flex items-center justify-end space-x-2 p-4\">\r\n        <div className=\"flex-1 text-sm text-muted-foreground\">\r\n          Página {table.getState().pagination.pageIndex + 1} de{\" \"}\r\n          {table.getPageCount()}. Total: {rowCount || data.length} registros.\r\n        </div>\r\n        <div className=\"space-x-2\">\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={() => table.previousPage()}\r\n            disabled={!table.getCanPreviousPage()}\r\n          >\r\n            Previous\r\n          </Button>\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={() => table.nextPage()}\r\n            disabled={!table.getCanNextPage()}\r\n          >\r\n            Next\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAaA;AACA;AACA;;;AAlBA;;;;;;AAsCO,SAAS,UAAyB,EACvC,OAAO,EACP,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,YAAY,EACZ,SAAS,EACqB;;IAC9B;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;QAAE;QAAW;IAAS;IACzE,kDAAkD;IAClD,MAAM,QAAQ,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B;QACA;QACA,iBAAiB,CAAA,GAAA,wKAAA,CAAA,kBAAe,AAAD;QAC/B,uBAAuB,CAAA,GAAA,wKAAA,CAAA,wBAAqB,AAAD;QAC3C,kBAAkB;QAClB,UAAU,YAAY,KAAK,MAAM;QACjC,2EAA2E;QAC3E,WAAW,KAAK,IAAI,CAAC,WAAW;QAChC,kBAAkB;8CAAE,CAAC;gBACnB,mBAAmB;gBACnB,sDAAsD;gBACtD,IAAI;gBACJ,MAAM,gBAAgB,OAAO,YAAY,aAAa,QAAQ,MAAM,QAAQ,GAAG,UAAU,IAAI;gBAC7F,cAAc;gBACd,IAAI,cAAc;oBAChB,aAAa,cAAc,SAAS,GAAG;gBACzC;YACF;;QACA,OAAO;YACL;QACF;IACF;IAEA,OACE,8BAA8B;IAC9B,wDAAwD;IACxD,sBAAsB;IACtB,iDAAiD;IACjD,iBAAiB;IACjB,4CAA4C;IAC5C,mFAAmF;IACnF,iGAAiG;IACjG,iCAAiC;IACjC,aAAa;IACb,eAAe;IACf,SAAS;IACT,4DAA4D;IAC5D,uBAAuB;IACvB,wCAAwC;IACxC,2DAA2D;IAC3D,gEAAgE;IAChE,sBAAsB;IACtB,iCAAiC;IACjC,4CAA4C;IAC5C,mBAAmB;IACnB,+BAA+B;IAC/B,uDAAuD;IACvD,iCAAiC;IACjC,yBAAyB;IACzB,4CAA4C;IAC5C,oCAAoC;IACpC,2CAA2C;IAC3C,oDAAoD;IACpD,kFAAkF;IAClF,oBAAoB;IACpB,gCAAgC;IAChC,8CAA8C;IAC9C,kBAAkB;IAClB,kBAAkB;IAClB,iCAAiC;IACjC,wBAAwB;IACxB,aAAa;IACb,WAAW;IACX,wCAAwC;IACxC,cAAc;IACd,sBAAsB;IACtB,0DAA0D;IAC1D,4CAA4C;IAC5C,qDAAqD;IACrD,yBAAyB;IACzB,8CAA8C;IAC9C,oHAAoH;IACpH,+BAA+B;IAC/B,kBAAkB;IAClB,kBAAkB;IAClB,wBAAwB;IACxB,cAAc;IACd,uBAAuB;IACvB,oBAAoB;IACpB,gDAAgD;IAChD,oDAAoD;IACpD,qFAAqF;IACrF,uDAAuD;IACvD,mHAAmH;IACnH,oBAAoB;IACpB,0BAA0B;IAC1B,eAAe;IACf,gBAAgB;IAChB,uBAAuB;IACvB,gFAAgF;IAChF,8CAA8C;IAC9C,2BAA2B;IAC3B,wBAAwB;IACxB,aAAa;IACb,qBAAqB;IACrB,eAAe;IACf,WAAW;IACX,mEAAmE;IACnE,6DAA6D;IAC7D,+GAA+G;IAC/G,yBAAyB;IACzB,aAAa;IACb,kCAAkC;IAClC,gBAAgB;IAChB,4BAA4B;IAC5B,oBAAoB;IACpB,+CAA+C;IAC/C,iDAAiD;IACjD,UAAU;IACV,mBAAmB;IACnB,kBAAkB;IAClB,iHAAiH;IACjH,oBAAoB;IACpB,kBAAkB;IAClB,aAAa;IACb,WAAW;IACX,SAAS;kBACT,6LAAC;QAAI,WAAU;;0BACb,6LAAC,oIAAA,CAAA,QAAK;;kCACJ,6LAAC,oIAAA,CAAA,cAAW;kCACT,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,4BAC5B,6LAAC,oIAAA,CAAA,WAAQ;0CACN,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC;oCACxB,qBACE,6LAAC,oIAAA,CAAA,YAAS;kDACP,OAAO,aAAa,GACjB,OACA,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EACT,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;uCALP,OAAO,EAAE;;;;;gCAS7B;+BAZa,YAAY,EAAE;;;;;;;;;;kCAgBjC,6LAAC,oIAAA,CAAA,YAAS;kCACP,YAAY,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAS,GAAG,GAAG,CAAC,CAAC,GAAG,sBACpD,6LAAC,8IAAA,CAAA,cAAW;gCAAa,WAAW,QAAQ,MAAM;gCAAE,WAAU;+BAA5C;;;;sDAElB;sCAEK,MAAM,WAAW,GAAG,IAAI,EAAE,SACzB,MAAM,qBAAqB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,oBACtC,6LAAC,oIAAA,CAAA,WAAQ;oCAEP,cAAY,IAAI,aAAa,MAAM;8CAElC,IAAI,eAAe,GAAG,GAAG,CAAC,CAAC,qBAC1B,6LAAC,oIAAA,CAAA,YAAS;sDACP,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EAAE,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,UAAU;2CADzC,KAAK,EAAE;;;;;mCAJpB,IAAI,EAAE;;;;0DAWf,6LAAC,oIAAA,CAAA,WAAQ;0CACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;oCAAC,SAAS,QAAQ,MAAM;oCAAE,WAAU;8CAAmB;;;;;;;;;;;;;;;;;;;;;;;0BAS/E,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BAAuC;4BAC5C,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG;4BAAE;4BAAI;4BACrD,MAAM,YAAY;4BAAG;4BAAU,YAAY,KAAK,MAAM;4BAAC;;;;;;;kCAE1D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,MAAM,YAAY;gCACjC,UAAU,CAAC,MAAM,kBAAkB;0CACpC;;;;;;0CAGD,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,MAAM,QAAQ;gCAC7B,UAAU,CAAC,MAAM,cAAc;0CAChC;;;;;;;;;;;;;;;;;;;;;;;;AAOX;GA9MgB;;QAYA,yLAAA,CAAA,gBAAa;;;KAZb", "debugId": null}}, {"offset": {"line": 602, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/hosts/host-table.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { Ava<PERSON>, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\r\nimport { Badge } from \"@/components/ui/badge\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from \"@/components/ui/dropdown-menu\"\r\nimport { formatDate } from \"@/lib/utils\"\r\nimport { MoreHorizontal, CheckCircle } from \"lucide-react\"\r\nimport { useQuery, useMutation, useQueryClient } from \"@tanstack/react-query\"\r\nimport { usersApi } from \"@/lib/api/users.api\"\r\nimport { toast } from \"sonner\"\r\nimport { DataTable } from \"@/components/data-table/data-table\"\r\nimport { ColumnDef } from \"@tanstack/react-table\"\r\nimport { useSearchParams, useRouter } from \"next/navigation\"\r\n\r\n\r\nexport function HostTable() {\r\n  const queryClient = useQueryClient()\r\n  const searchParams = useSearchParams()\r\n  const router = useRouter()\r\n\r\n  // Obtener parámetros de paginación\r\n  const page = Number(searchParams.get('page') || 1)\r\n  const limit = Number(searchParams.get('limit') || 10)\r\n\r\n  const { data, isLoading, error } = useQuery({\r\n    queryKey: ['admin-hosts', page, limit],\r\n    queryFn: () => usersApi.getAllHosts({ page, limit }),\r\n    staleTime: 60 * 1000, // 1 minuto\r\n  })\r\n\r\n  const verifyMutation = useMutation({\r\n    mutationFn: usersApi.verifyUser,\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: ['admin-hosts', page, limit] })\r\n      toast.success(\"Host verificado correctamente\")\r\n    },\r\n    onError: (error) => {\r\n      toast.error(`Error al verificar host: ${error.message}`)\r\n    }\r\n  })\r\n\r\n  const handlePageChange = (newPage: number) => {\r\n    const params = new URLSearchParams(searchParams.toString())\r\n    params.set('page', newPage.toString())\r\n    router.push(`?${params.toString()}`)\r\n  }\r\n\r\n  if (error) {\r\n    return <div className=\"p-4 text-red-500\">Error al cargar los hosts: {error.message}</div>\r\n  }\r\n\r\n  // Definir las columnas para el DataTable\r\n  const columns: ColumnDef<any>[] = [\r\n    {\r\n      accessorKey: \"name\",\r\n      header: \"Anfitrión\",\r\n      cell: ({ row }) => {\r\n        const host = row.original\r\n        return (\r\n          <div className=\"flex items-center space-x-3\">\r\n            <Avatar>\r\n              <AvatarImage src={host.image || \"/placeholder.svg\"} />\r\n              <AvatarFallback>\r\n                {host.name\r\n                  ?.split(\" \")\r\n                  .map((n: string) => n[0])\r\n                  .join(\"\")}\r\n              </AvatarFallback>\r\n            </Avatar>\r\n            <div>\r\n              <div className=\"font-medium\">{host.name}</div>\r\n              <div className=\"text-sm text-muted-foreground\">ID: {host.id.substring(0, 8)}</div>\r\n            </div>\r\n          </div>\r\n        )\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"email\",\r\n      header: \"Contacto\",\r\n      cell: ({ row }) => {\r\n        const host = row.original\r\n        return (\r\n          <div>\r\n            <div>{host.email}</div>\r\n            <div className=\"text-sm text-muted-foreground\">{host.phone || \"No disponible\"}</div>\r\n          </div>\r\n        )\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"isHostVerified\",\r\n      header: \"Estado\",\r\n      cell: ({ row }) => {\r\n        const host = row.original\r\n        return (\r\n          <Badge variant={host.isHostVerified ? \"default\" : \"outline\"}>\r\n            {host.isHostVerified ? \"Verificado\" : \"Pendiente\"}\r\n          </Badge>\r\n        )\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"createdAt\",\r\n      header: \"Fecha de registro\",\r\n      cell: ({ row }) => {\r\n        const host = row.original\r\n        return formatDate(host.createdAt)\r\n      },\r\n    },\r\n    {\r\n      id: \"actions\",\r\n      header: \"Acciones\",\r\n      cell: ({ row }) => {\r\n        const host = row.original\r\n        return (\r\n          <div className=\"text-right\">\r\n            <DropdownMenu>\r\n              <DropdownMenuTrigger asChild>\r\n                <Button variant=\"ghost\" className=\"h-8 w-8 p-0\">\r\n                  <span className=\"sr-only\">Abrir menú</span>\r\n                  <MoreHorizontal className=\"h-4 w-4\" />\r\n                </Button>\r\n              </DropdownMenuTrigger>\r\n              <DropdownMenuContent align=\"end\">\r\n                <DropdownMenuItem onClick={() => verifyMutation.mutate(host.id)}>\r\n                  <CheckCircle className=\"mr-2 h-4 w-4\" />\r\n                  Verificar\r\n                </DropdownMenuItem>\r\n              </DropdownMenuContent>\r\n            </DropdownMenu>\r\n          </div>\r\n        )\r\n      },\r\n    },\r\n  ]\r\n\r\n  return (\r\n    <DataTable\r\n      columns={columns}\r\n      data={data?.data || []}\r\n      rowCount={data?.pagination.total || 0}\r\n      pageSize={limit}\r\n      pageIndex={page - 1}\r\n      onPageChange={handlePageChange}\r\n      isLoading={isLoading}\r\n    />\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAEA;;;AAbA;;;;;;;;;;;;AAgBO,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,mCAAmC;IACnC,MAAM,OAAO,OAAO,aAAa,GAAG,CAAC,WAAW;IAChD,MAAM,QAAQ,OAAO,aAAa,GAAG,CAAC,YAAY;IAElD,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QAC1C,UAAU;YAAC;YAAe;YAAM;SAAM;QACtC,OAAO;kCAAE,IAAM,oIAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;oBAAE;oBAAM;gBAAM;;QAClD,WAAW,KAAK;IAClB;IAEA,MAAM,iBAAiB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjC,YAAY,oIAAA,CAAA,WAAQ,CAAC,UAAU;QAC/B,SAAS;qDAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAe;wBAAM;qBAAM;gBAAC;gBACvE,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;qDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,yBAAyB,EAAE,MAAM,OAAO,EAAE;YACzD;;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS,IAAI,gBAAgB,aAAa,QAAQ;QACxD,OAAO,GAAG,CAAC,QAAQ,QAAQ,QAAQ;QACnC,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI;IACrC;IAEA,IAAI,OAAO;QACT,qBAAO,6LAAC;YAAI,WAAU;;gBAAmB;gBAA4B,MAAM,OAAO;;;;;;;IACpF;IAEA,yCAAyC;IACzC,MAAM,UAA4B;QAChC;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ;gBACzB,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;;8CACL,6LAAC,qIAAA,CAAA,cAAW;oCAAC,KAAK,KAAK,KAAK,IAAI;;;;;;8CAChC,6LAAC,qIAAA,CAAA,iBAAc;8CACZ,KAAK,IAAI,EACN,MAAM,KACP,IAAI,CAAC,IAAc,CAAC,CAAC,EAAE,EACvB,KAAK;;;;;;;;;;;;sCAGZ,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;8CAAe,KAAK,IAAI;;;;;;8CACvC,6LAAC;oCAAI,WAAU;;wCAAgC;wCAAK,KAAK,EAAE,CAAC,SAAS,CAAC,GAAG;;;;;;;;;;;;;;;;;;;YAIjF;QACF;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ;gBACzB,qBACE,6LAAC;;sCACC,6LAAC;sCAAK,KAAK,KAAK;;;;;;sCAChB,6LAAC;4BAAI,WAAU;sCAAiC,KAAK,KAAK,IAAI;;;;;;;;;;;;YAGpE;QACF;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ;gBACzB,qBACE,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAS,KAAK,cAAc,GAAG,YAAY;8BAC/C,KAAK,cAAc,GAAG,eAAe;;;;;;YAG5C;QACF;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ;gBACzB,OAAO,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,SAAS;YAClC;QACF;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ;gBACzB,qBACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+IAAA,CAAA,eAAY;;0CACX,6LAAC,+IAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,WAAU;;sDAChC,6LAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,6LAAC,mNAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG9B,6LAAC,+IAAA,CAAA,sBAAmB;gCAAC,OAAM;0CACzB,cAAA,6LAAC,+IAAA,CAAA,mBAAgB;oCAAC,SAAS,IAAM,eAAe,MAAM,CAAC,KAAK,EAAE;;sDAC5D,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;YAOpD;QACF;KACD;IAED,qBACE,6LAAC,uJAAA,CAAA,YAAS;QACR,SAAS;QACT,MAAM,MAAM,QAAQ,EAAE;QACtB,UAAU,MAAM,WAAW,SAAS;QACpC,UAAU;QACV,WAAW,OAAO;QAClB,cAAc;QACd,WAAW;;;;;;AAGjB;GArIgB;;QACM,yLAAA,CAAA,iBAAc;QACb,qIAAA,CAAA,kBAAe;QACrB,qIAAA,CAAA,YAAS;QAMW,8KAAA,CAAA,WAAQ;QAMpB,iLAAA,CAAA,cAAW;;;KAfpB", "debugId": null}}, {"offset": {"line": 928, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/components/host-filters.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useState } from \"react\"\r\nimport { ChevronDown, Search } from \"lucide-react\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\n\r\nexport function HostFilters() {\r\n  const [searchQuery, setSearchQuery] = useState(\"\")\r\n\r\n  return (\r\n    <div className=\"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between\">\r\n      <div className=\"flex flex-1 items-center space-x-2\">\r\n        <div className=\"relative flex-1 max-w-sm\">\r\n          <Search className=\"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\" />\r\n          <Input\r\n            type=\"search\"\r\n            placeholder=\"Search by name, email or phone...\"\r\n            className=\"pl-8\"\r\n            value={searchQuery}\r\n            onChange={(e) => setSearchQuery(e.target.value)}\r\n          />\r\n        </div>\r\n      </div>\r\n      <div className=\"flex flex-wrap items-center gap-2\">\r\n        <Button variant=\"outline\" size=\"sm\" className=\"h-8 gap-1\">\r\n          All Verification Status\r\n          <ChevronDown className=\"h-4 w-4\" />\r\n        </Button>\r\n        <Button variant=\"outline\" size=\"sm\" className=\"h-8 gap-1\">\r\n          Number of Cars\r\n          <ChevronDown className=\"h-4 w-4\" />\r\n        </Button>\r\n        <Button variant=\"outline\" size=\"sm\" className=\"h-8 gap-1\">\r\n          Activity Level\r\n          <ChevronDown className=\"h-4 w-4\" />\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;;;AALA;;;;;AAOO,SAAS;;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC,oIAAA,CAAA,QAAK;4BACJ,MAAK;4BACL,aAAY;4BACZ,WAAU;4BACV,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;0BAIpD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,MAAK;wBAAK,WAAU;;4BAAY;0CAExD,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;;kCAEzB,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,MAAK;wBAAK,WAAU;;4BAAY;0CAExD,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;;kCAEzB,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,MAAK;wBAAK,WAAU;;4BAAY;0CAExD,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKjC;GAjCgB;KAAA", "debugId": null}}]}