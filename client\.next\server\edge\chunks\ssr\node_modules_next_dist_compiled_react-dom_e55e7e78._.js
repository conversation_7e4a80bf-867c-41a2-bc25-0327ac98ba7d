(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/ssr/node_modules_next_dist_compiled_react-dom_e55e7e78._.js", {

"[project]/node_modules/next/dist/compiled/react-dom/server.edge.js [app-edge-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var b;
var l;
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    b = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react-dom/cjs/react-dom-server.edge.development.js [app-edge-rsc] (ecmascript)");
    l = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react-dom/cjs/react-dom-server-legacy.browser.development.js [app-edge-rsc] (ecmascript)");
}
exports.version = b.version;
exports.renderToReadableStream = b.renderToReadableStream;
exports.renderToString = l.renderToString;
exports.renderToStaticMarkup = l.renderToStaticMarkup;
if (b.resume) {
    exports.resume = b.resume;
}
}}),
"[project]/node_modules/next/dist/compiled/react-dom/static.edge.js [app-edge-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var s;
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    s = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react-dom/cjs/react-dom-server.edge.development.js [app-edge-rsc] (ecmascript)");
}
exports.version = s.version;
exports.prerender = s.prerender;
exports.resumeAndPrerender = s.resumeAndPrerender;
}}),
"[project]/node_modules/next/dist/compiled/react-dom/index.js [app-edge-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
function checkDCE() {
    /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */ if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ === 'undefined' || typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE !== 'function') {
        return;
    }
    if ("TURBOPACK compile-time truthy", 1) {
        // This branch is unreachable because this function is only called
        // in production, but the condition is true only in development.
        // Therefore if the branch is still here, dead code elimination wasn't
        // properly applied.
        // Don't change the message. React DevTools relies on it. Also make sure
        // this message doesn't occur elsewhere in this function, or it will cause
        // a false positive.
        throw new Error('^_^');
    }
    try {
        // Verify that the code above has been dead code eliminated (DCE'd).
        __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(checkDCE);
    } catch (err) {
        // DevTools shouldn't crash React, no matter what.
        // We should still report in case we break this code.
        console.error(err);
    }
}
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react-dom/cjs/react-dom.development.js [app-edge-ssr] (ecmascript)");
}
}}),
"[project]/node_modules/next/dist/compiled/react-dom/server.edge.js [app-edge-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var b;
var l;
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    b = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react-dom/cjs/react-dom-server.edge.development.js [app-edge-ssr] (ecmascript)");
    l = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react-dom/cjs/react-dom-server-legacy.browser.development.js [app-edge-ssr] (ecmascript)");
}
exports.version = b.version;
exports.renderToReadableStream = b.renderToReadableStream;
exports.renderToString = l.renderToString;
exports.renderToStaticMarkup = l.renderToStaticMarkup;
if (b.resume) {
    exports.resume = b.resume;
}
}}),
"[project]/node_modules/next/dist/compiled/react-dom/static.edge.js [app-edge-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var s;
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    s = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react-dom/cjs/react-dom-server.edge.development.js [app-edge-ssr] (ecmascript)");
}
exports.version = s.version;
exports.prerender = s.prerender;
exports.resumeAndPrerender = s.resumeAndPrerender;
}}),
}]);

//# sourceMappingURL=node_modules_next_dist_compiled_react-dom_e55e7e78._.js.map