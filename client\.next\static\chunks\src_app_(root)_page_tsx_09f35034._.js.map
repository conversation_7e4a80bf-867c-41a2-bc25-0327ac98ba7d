{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/app/%28root%29/page.tsx"], "sourcesContent": ["'use client'\r\n\r\n// import { authClient } from '@/auth-client';\r\nimport { Calendar, Car, HeadphonesIcon, Key, Shield, Star, UserCheck } from 'lucide-react';\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\n\r\nexport default function Home() {\r\n\r\n  // const session = authClient.useSession();\r\n\r\n  // console.log('session: ', session.data);\r\n\r\n  return (\r\n    <>\r\n      {/* Navigation */}\r\n\r\n      {/* Hero Section */}\r\n      <Hero />\r\n\r\n      {/* How It Works */}\r\n      <section id=\"how-it-works\" className=\"py-16 md:py-24 bg-white\">\r\n        <div className=\"container mx-auto px-6\">\r\n          <h2 className=\"text-3xl font-bold text-center text-[#1a2b5e] mb-16\">How Autoop Works</h2>\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-10\">\r\n            <div className=\"flex flex-col items-center text-center\">\r\n              <div className=\"bg-blue-50 p-4 rounded-full mb-6\">\r\n                <Car className=\"w-10 h-10 text-[#1a2b5e]\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-semibold mb-3 text-[#1a2b5e]\">List or Browse</h3>\r\n              <p className=\"text-gray-600\">\r\n                List your car or browse available vehicles in your area with detailed descriptions.\r\n              </p>\r\n            </div>\r\n            <div className=\"flex flex-col items-center text-center\">\r\n              <div className=\"bg-blue-50 p-4 rounded-full mb-6\">\r\n                <Calendar className=\"w-10 h-10 text-[#1a2b5e]\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-semibold mb-3 text-[#1a2b5e]\">Book & Verify</h3>\r\n              <p className=\"text-gray-600\">Select your dates and complete the verification process securely.</p>\r\n            </div>\r\n            <div className=\"flex flex-col items-center text-center\">\r\n              <div className=\"bg-blue-50 p-4 rounded-full mb-6\">\r\n                <Key className=\"w-10 h-10 text-[#1a2b5e]\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-semibold mb-3 text-[#1a2b5e]\">Drive & Enjoy</h3>\r\n              <p className=\"text-gray-600\">Pick up your car and enjoy your journey with full insurance coverage.</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Trust & Safety Features */}\r\n      <section id=\"features\" className=\"py-16 md:py-24 bg-gray-50\">\r\n        <div className=\"container mx-auto px-6\">\r\n          <h2 className=\"text-3xl font-bold text-center text-[#1a2b5e] mb-16\">Trust & Safety Features</h2>\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\r\n            <div className=\"bg-white p-6 rounded-lg shadow-sm\">\r\n              <div className=\"text-[#ff8c00] mb-4\">\r\n                <Shield className=\"w-10 h-10\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-semibold mb-2 text-[#1a2b5e]\">Full Insurance</h3>\r\n              <p className=\"text-gray-600\">Comprehensive coverage for peace of mind during your rental.</p>\r\n            </div>\r\n            <div className=\"bg-white p-6 rounded-lg shadow-sm\">\r\n              <div className=\"text-[#ff8c00] mb-4\">\r\n                <UserCheck className=\"w-10 h-10\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-semibold mb-2 text-[#1a2b5e]\">ID Verification</h3>\r\n              <p className=\"text-gray-600\">Secure verification process for all users.</p>\r\n            </div>\r\n            <div className=\"bg-white p-6 rounded-lg shadow-sm\">\r\n              <div className=\"text-[#ff8c00] mb-4\">\r\n                <HeadphonesIcon className=\"w-10 h-10\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-semibold mb-2 text-[#1a2b5e]\">24/7 Support</h3>\r\n              <p className=\"text-gray-600\">Round-the-clock assistance whenever you need it.</p>\r\n            </div>\r\n            <div className=\"bg-white p-6 rounded-lg shadow-sm\">\r\n              <div className=\"text-[#ff8c00] mb-4\">\r\n                <Star className=\"w-10 h-10\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-semibold mb-2 text-[#1a2b5e]\">Rating System</h3>\r\n              <p className=\"text-gray-600\">Transparent feedback from verified users.</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Testimonials */}\r\n      <section id=\"testimonials\" className=\"py-16 md:py-24 bg-white\">\r\n        <div className=\"container mx-auto px-6\">\r\n          <h2 className=\"text-3xl font-bold text-center text-[#1a2b5e] mb-16\">What Our Users Say</h2>\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\r\n            <div className=\"bg-gray-50 p-6 rounded-lg\">\r\n              <div className=\"flex items-center mb-4\">\r\n                <div className=\"w-12 h-12 rounded-full overflow-hidden mr-4\">\r\n                  <Image\r\n                    src=\"/placeholder.svg?height=48&width=48\"\r\n                    alt=\"User avatar\"\r\n                    width={48}\r\n                    height={48}\r\n                    className=\"rounded-full\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <h4 className=\"font-semibold text-[#1a2b5e]\">Michael Roberts</h4>\r\n                  <div className=\"flex text-[#ff8c00]\">\r\n                    <Star className=\"w-4 h-4 fill-current\" />\r\n                    <Star className=\"w-4 h-4 fill-current\" />\r\n                    <Star className=\"w-4 h-4 fill-current\" />\r\n                    <Star className=\"w-4 h-4 fill-current\" />\r\n                    <Star className=\"w-4 h-4 fill-current\" />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <p className=\"text-gray-600\">\r\n                &quot;Renting a car through Autoop was a breeze. The verification process was thorough and I feel completely\r\n                safe.&quot;\r\n              </p>\r\n            </div>\r\n            <div className=\"bg-gray-50 p-6 rounded-lg\">\r\n              <div className=\"flex items-center mb-4\">\r\n                <div className=\"w-12 h-12 rounded-full overflow-hidden mr-4\">\r\n                  <Image\r\n                    src=\"/placeholder.svg?height=48&width=48\"\r\n                    alt=\"User avatar\"\r\n                    width={48}\r\n                    height={48}\r\n                    className=\"rounded-full\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <h4 className=\"font-semibold text-[#1a2b5e]\">Sarah Chen</h4>\r\n                  <div className=\"flex text-[#ff8c00]\">\r\n                    <Star className=\"w-4 h-4 fill-current\" />\r\n                    <Star className=\"w-4 h-4 fill-current\" />\r\n                    <Star className=\"w-4 h-4 fill-current\" />\r\n                    <Star className=\"w-4 h-4 fill-current\" />\r\n                    <Star className=\"w-4 h-4 fill-current\" />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <p className=\"text-gray-600\">\r\n                &quot;I love how easy it was to list my car. The process was smooth and the insurance coverage gave me peace\r\n                of mind.&quot;\r\n              </p>\r\n            </div>\r\n            <div className=\"bg-gray-50 p-6 rounded-lg\">\r\n              <div className=\"flex items-center mb-4\">\r\n                <div className=\"w-12 h-12 rounded-full overflow-hidden mr-4\">\r\n                  <Image\r\n                    src=\"/placeholder.svg?height=48&width=48\"\r\n                    alt=\"User avatar\"\r\n                    width={48}\r\n                    height={48}\r\n                    className=\"rounded-full\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <h4 className=\"font-semibold text-[#1a2b5e]\">David Thompson</h4>\r\n                  <div className=\"flex text-[#ff8c00]\">\r\n                    <Star className=\"w-4 h-4 fill-current\" />\r\n                    <Star className=\"w-4 h-4 fill-current\" />\r\n                    <Star className=\"w-4 h-4 fill-current\" />\r\n                    <Star className=\"w-4 h-4 fill-current\" />\r\n                    <Star className=\"w-4 h-4 fill-current\" />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <p className=\"text-gray-600\">\r\n                &quot;The support team was incredibly helpful when I needed assistance. Best car sharing platform I&apos;ve used!&quot;\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* CTA Section */}\r\n      <section id=\"get-started\" className=\"py-16 md:py-24 bg-[#1a2b5e] text-white\">\r\n        <div className=\"container mx-auto px-6 text-center\">\r\n          <h2 className=\"text-3xl font-bold mb-6\">Ready to Join Autoop?</h2>\r\n          <p className=\"text-lg mb-8 max-w-2xl mx-auto\">\r\n            Start your journey with us today and experience premium car sharing.\r\n          </p>\r\n          <div className=\"flex flex-col sm:flex-row justify-center gap-4\">\r\n            <Link\r\n              href=\"/vehicles\"\r\n              className=\"bg-[#ff8c00] text-white px-6 py-3 rounded-md text-center hover:bg-[#e67e00] transition\"\r\n            >\r\n              <span className=\"font-medium\">List Your Car</span>\r\n            </Link>\r\n            <Link\r\n              href=\"/vehicles\"\r\n              className=\"bg-white text-[#1a2b5e] px-6 py-3 rounded-md text-center hover:bg-gray-100 transition\"\r\n            >\r\n              <span className=\"font-medium\">Find a Ride</span>\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n\r\n    </>\r\n  );\r\n}\r\n\r\nfunction Hero() {\r\n  return (\r\n    <section className=\"w-full bg-[#1a2b5e] text-white\">\r\n      <div className=\"container mx-auto px-6 py-16 md:py-24 flex flex-col md:flex-row items-center\">\r\n        <div className=\"md:w-1/2 mb-10 md:mb-0\">\r\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold mb-4\">Premium Car Sharing for Modern Drivers</h2>\r\n          <p className=\"text-lg mb-8 text-gray-200\">\r\n            Connect with verified car owners and enjoy a seamless rental experience with full insurance coverage.\r\n          </p>\r\n          <div className=\"flex flex-col sm:flex-row gap-4\">\r\n            <Link\r\n              href=\"/vehicles\"\r\n              className=\"bg-[#ff8c00] text-white px-6 py-3 rounded-md text-center hover:bg-[#e67e00] transition\"\r\n            >\r\n              <span className=\"font-medium\">List Your Car</span>\r\n            </Link>\r\n            <Link\r\n              href=\"/vehicles\"\r\n              className=\"bg-white text-[#1a2b5e] px-6 py-3 rounded-md text-center hover:bg-gray-100 transition\"\r\n            >\r\n              <span className=\"font-medium\">Find a Ride</span>\r\n            </Link>\r\n          </div>\r\n        </div>\r\n        <div className=\"md:w-1/2 lg:pl-10\">\r\n          <Image\r\n            src=\"/home/<USER>\"\r\n            alt=\"Luxury car for sharing\"\r\n            width={600}\r\n            height={500}\r\n            className=\"rounded-md object-cover \"\r\n            priority\r\n          />\r\n        </div>\r\n      </div>\r\n    </section>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA,8CAA8C;AAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AALA;;;;;AAOe,SAAS;IAEtB,2CAA2C;IAE3C,0CAA0C;IAE1C,qBACE;;0BAIE,6LAAC;;;;;0BAGD,6LAAC;gBAAQ,IAAG;gBAAe,WAAU;0BACnC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsD;;;;;;sCACpE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAI/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrC,6LAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsD;;;;;;sCACpE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qNAAA,CAAA,iBAAc;gDAAC,WAAU;;;;;;;;;;;sDAE5B,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrC,6LAAC;gBAAQ,IAAG;gBAAe,WAAU;0BACnC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsD;;;;;;sCACpE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wDACJ,KAAI;wDACJ,KAAI;wDACJ,OAAO;wDACP,QAAQ;wDACR,WAAU;;;;;;;;;;;8DAGd,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA+B;;;;;;sEAC7C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAItB,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wDACJ,KAAI;wDACJ,KAAI;wDACJ,OAAO;wDACP,QAAQ;wDACR,WAAU;;;;;;;;;;;8DAGd,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA+B;;;;;;sEAC7C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAItB,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wDACJ,KAAI;wDACJ,KAAI;wDACJ,OAAO;wDACP,QAAQ;wDACR,WAAU;;;;;;;;;;;8DAGd,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA+B;;;;;;sEAC7C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAItB,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrC,6LAAC;gBAAQ,IAAG;gBAAc,WAAU;0BAClC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,6LAAC;4BAAE,WAAU;sCAAiC;;;;;;sCAG9C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAEV,cAAA,6LAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;8CAEhC,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAEV,cAAA,6LAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5C;KAtMwB;AAwMxB,SAAS;IACP,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAkD;;;;;;sCAChE,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAEV,cAAA,6LAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;8CAEhC,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAEV,cAAA,6LAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;;;;;;;;;;;;8BAIpC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;wBACV,QAAQ;;;;;;;;;;;;;;;;;;;;;;AAMpB;MArCS", "debugId": null}}]}