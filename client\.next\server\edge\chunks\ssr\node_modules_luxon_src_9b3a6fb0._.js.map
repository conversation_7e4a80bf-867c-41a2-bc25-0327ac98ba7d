{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/luxon/src/errors.js"], "sourcesContent": ["// these aren't really private, but nor are they really useful to document\n\n/**\n * @private\n */\nclass LuxonError extends Error {}\n\n/**\n * @private\n */\nexport class InvalidDateTimeError extends LuxonError {\n  constructor(reason) {\n    super(`Invalid DateTime: ${reason.toMessage()}`);\n  }\n}\n\n/**\n * @private\n */\nexport class InvalidIntervalError extends LuxonError {\n  constructor(reason) {\n    super(`Invalid Interval: ${reason.toMessage()}`);\n  }\n}\n\n/**\n * @private\n */\nexport class InvalidDurationError extends LuxonError {\n  constructor(reason) {\n    super(`Invalid Duration: ${reason.toMessage()}`);\n  }\n}\n\n/**\n * @private\n */\nexport class ConflictingSpecificationError extends LuxonError {}\n\n/**\n * @private\n */\nexport class InvalidUnitError extends LuxonError {\n  constructor(unit) {\n    super(`Invalid unit ${unit}`);\n  }\n}\n\n/**\n * @private\n */\nexport class InvalidArgumentError extends LuxonError {}\n\n/**\n * @private\n */\nexport class ZoneIsAbstractError extends LuxonError {\n  constructor() {\n    super(\"Zone is an abstract class\");\n  }\n}\n"], "names": [], "mappings": "AAAA,0EAA0E;AAE1E;;CAEC;;;;;;;;;AACD,MAAM,mBAAmB;AAAO;AAKzB,MAAM,6BAA6B;IACxC,YAAY,MAAM,CAAE;QAClB,KAAK,CAAC,CAAC,kBAAkB,EAAE,OAAO,SAAS,IAAI;IACjD;AACF;AAKO,MAAM,6BAA6B;IACxC,YAAY,MAAM,CAAE;QAClB,KAAK,CAAC,CAAC,kBAAkB,EAAE,OAAO,SAAS,IAAI;IACjD;AACF;AAKO,MAAM,6BAA6B;IACxC,YAAY,MAAM,CAAE;QAClB,KAAK,CAAC,CAAC,kBAAkB,EAAE,OAAO,SAAS,IAAI;IACjD;AACF;AAKO,MAAM,sCAAsC;AAAY;AAKxD,MAAM,yBAAyB;IACpC,YAAY,IAAI,CAAE;QAChB,KAAK,CAAC,CAAC,aAAa,EAAE,MAAM;IAC9B;AACF;AAKO,MAAM,6BAA6B;AAAY;AAK/C,MAAM,4BAA4B;IACvC,aAAc;QACZ,KAAK,CAAC;IACR;AACF", "ignoreList": [0]}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/luxon/src/impl/formats.js"], "sourcesContent": ["/**\n * @private\n */\n\nconst n = \"numeric\",\n  s = \"short\",\n  l = \"long\";\n\nexport const DATE_SHORT = {\n  year: n,\n  month: n,\n  day: n,\n};\n\nexport const DATE_MED = {\n  year: n,\n  month: s,\n  day: n,\n};\n\nexport const DATE_MED_WITH_WEEKDAY = {\n  year: n,\n  month: s,\n  day: n,\n  weekday: s,\n};\n\nexport const DATE_FULL = {\n  year: n,\n  month: l,\n  day: n,\n};\n\nexport const DATE_HUGE = {\n  year: n,\n  month: l,\n  day: n,\n  weekday: l,\n};\n\nexport const TIME_SIMPLE = {\n  hour: n,\n  minute: n,\n};\n\nexport const TIME_WITH_SECONDS = {\n  hour: n,\n  minute: n,\n  second: n,\n};\n\nexport const TIME_WITH_SHORT_OFFSET = {\n  hour: n,\n  minute: n,\n  second: n,\n  timeZoneName: s,\n};\n\nexport const TIME_WITH_LONG_OFFSET = {\n  hour: n,\n  minute: n,\n  second: n,\n  timeZoneName: l,\n};\n\nexport const TIME_24_SIMPLE = {\n  hour: n,\n  minute: n,\n  hourCycle: \"h23\",\n};\n\nexport const TIME_24_WITH_SECONDS = {\n  hour: n,\n  minute: n,\n  second: n,\n  hourCycle: \"h23\",\n};\n\nexport const TIME_24_WITH_SHORT_OFFSET = {\n  hour: n,\n  minute: n,\n  second: n,\n  hourCycle: \"h23\",\n  timeZoneName: s,\n};\n\nexport const TIME_24_WITH_LONG_OFFSET = {\n  hour: n,\n  minute: n,\n  second: n,\n  hourCycle: \"h23\",\n  timeZoneName: l,\n};\n\nexport const DATETIME_SHORT = {\n  year: n,\n  month: n,\n  day: n,\n  hour: n,\n  minute: n,\n};\n\nexport const DATETIME_SHORT_WITH_SECONDS = {\n  year: n,\n  month: n,\n  day: n,\n  hour: n,\n  minute: n,\n  second: n,\n};\n\nexport const DATETIME_MED = {\n  year: n,\n  month: s,\n  day: n,\n  hour: n,\n  minute: n,\n};\n\nexport const DATETIME_MED_WITH_SECONDS = {\n  year: n,\n  month: s,\n  day: n,\n  hour: n,\n  minute: n,\n  second: n,\n};\n\nexport const DATETIME_MED_WITH_WEEKDAY = {\n  year: n,\n  month: s,\n  day: n,\n  weekday: s,\n  hour: n,\n  minute: n,\n};\n\nexport const DATETIME_FULL = {\n  year: n,\n  month: l,\n  day: n,\n  hour: n,\n  minute: n,\n  timeZoneName: s,\n};\n\nexport const DATETIME_FULL_WITH_SECONDS = {\n  year: n,\n  month: l,\n  day: n,\n  hour: n,\n  minute: n,\n  second: n,\n  timeZoneName: s,\n};\n\nexport const DATETIME_HUGE = {\n  year: n,\n  month: l,\n  day: n,\n  weekday: l,\n  hour: n,\n  minute: n,\n  timeZoneName: l,\n};\n\nexport const DATETIME_HUGE_WITH_SECONDS = {\n  year: n,\n  month: l,\n  day: n,\n  weekday: l,\n  hour: n,\n  minute: n,\n  second: n,\n  timeZoneName: l,\n};\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;;;;;;;;;;;;;;AAED,MAAM,IAAI,WACR,IAAI,SACJ,IAAI;AAEC,MAAM,aAAa;IACxB,MAAM;IACN,OAAO;IACP,KAAK;AACP;AAEO,MAAM,WAAW;IACtB,MAAM;IACN,OAAO;IACP,KAAK;AACP;AAEO,MAAM,wBAAwB;IACnC,MAAM;IACN,OAAO;IACP,KAAK;IACL,SAAS;AACX;AAEO,MAAM,YAAY;IACvB,MAAM;IACN,OAAO;IACP,KAAK;AACP;AAEO,MAAM,YAAY;IACvB,MAAM;IACN,OAAO;IACP,KAAK;IACL,SAAS;AACX;AAEO,MAAM,cAAc;IACzB,MAAM;IACN,QAAQ;AACV;AAEO,MAAM,oBAAoB;IAC/B,MAAM;IACN,QAAQ;IACR,QAAQ;AACV;AAEO,MAAM,yBAAyB;IACpC,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,cAAc;AAChB;AAEO,MAAM,wBAAwB;IACnC,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,cAAc;AAChB;AAEO,MAAM,iBAAiB;IAC5B,MAAM;IACN,QAAQ;IACR,WAAW;AACb;AAEO,MAAM,uBAAuB;IAClC,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,WAAW;AACb;AAEO,MAAM,4BAA4B;IACvC,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,WAAW;IACX,cAAc;AAChB;AAEO,MAAM,2BAA2B;IACtC,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,WAAW;IACX,cAAc;AAChB;AAEO,MAAM,iBAAiB;IAC5B,MAAM;IACN,OAAO;IACP,KAAK;IACL,MAAM;IACN,QAAQ;AACV;AAEO,MAAM,8BAA8B;IACzC,MAAM;IACN,OAAO;IACP,KAAK;IACL,MAAM;IACN,QAAQ;IACR,QAAQ;AACV;AAEO,MAAM,eAAe;IAC1B,MAAM;IACN,OAAO;IACP,KAAK;IACL,MAAM;IACN,QAAQ;AACV;AAEO,MAAM,4BAA4B;IACvC,MAAM;IACN,OAAO;IACP,KAAK;IACL,MAAM;IACN,QAAQ;IACR,QAAQ;AACV;AAEO,MAAM,4BAA4B;IACvC,MAAM;IACN,OAAO;IACP,KAAK;IACL,SAAS;IACT,MAAM;IACN,QAAQ;AACV;AAEO,MAAM,gBAAgB;IAC3B,MAAM;IACN,OAAO;IACP,KAAK;IACL,MAAM;IACN,QAAQ;IACR,cAAc;AAChB;AAEO,MAAM,6BAA6B;IACxC,MAAM;IACN,OAAO;IACP,KAAK;IACL,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,cAAc;AAChB;AAEO,MAAM,gBAAgB;IAC3B,MAAM;IACN,OAAO;IACP,KAAK;IACL,SAAS;IACT,MAAM;IACN,QAAQ;IACR,cAAc;AAChB;AAEO,MAAM,6BAA6B;IACxC,MAAM;IACN,OAAO;IACP,KAAK;IACL,SAAS;IACT,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,cAAc;AAChB", "ignoreList": [0]}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/luxon/src/zone.js"], "sourcesContent": ["import { ZoneIsAbstractError } from \"./errors.js\";\n\n/**\n * @interface\n */\nexport default class Zone {\n  /**\n   * The type of zone\n   * @abstract\n   * @type {string}\n   */\n  get type() {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * The name of this zone.\n   * @abstract\n   * @type {string}\n   */\n  get name() {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * The IANA name of this zone.\n   * Defaults to `name` if not overwritten by a subclass.\n   * @abstract\n   * @type {string}\n   */\n  get ianaName() {\n    return this.name;\n  }\n\n  /**\n   * Returns whether the offset is known to be fixed for the whole year.\n   * @abstract\n   * @type {boolean}\n   */\n  get isUniversal() {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Returns the offset's common name (such as EST) at the specified timestamp\n   * @abstract\n   * @param {number} ts - Epoch milliseconds for which to get the name\n   * @param {Object} opts - Options to affect the format\n   * @param {string} opts.format - What style of offset to return. Accepts 'long' or 'short'.\n   * @param {string} opts.locale - What locale to return the offset name in.\n   * @return {string}\n   */\n  offsetName(ts, opts) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Returns the offset's value as a string\n   * @abstract\n   * @param {number} ts - Epoch milliseconds for which to get the offset\n   * @param {string} format - What style of offset to return.\n   *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively\n   * @return {string}\n   */\n  formatOffset(ts, format) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Return the offset in minutes for this zone at the specified timestamp.\n   * @abstract\n   * @param {number} ts - Epoch milliseconds for which to compute the offset\n   * @return {number}\n   */\n  offset(ts) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Return whether this Zone is equal to another zone\n   * @abstract\n   * @param {Zone} otherZone - the zone to compare\n   * @return {boolean}\n   */\n  equals(otherZone) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Return whether this Zone is valid.\n   * @abstract\n   * @type {boolean}\n   */\n  get isValid() {\n    throw new ZoneIsAbstractError();\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAKe,MAAM;IACnB;;;;GAIC,GACD,IAAI,OAAO;QACT,MAAM,IAAI,8IAAA,CAAA,sBAAmB;IAC/B;IAEA;;;;GAIC,GACD,IAAI,OAAO;QACT,MAAM,IAAI,8IAAA,CAAA,sBAAmB;IAC/B;IAEA;;;;;GAKC,GACD,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,IAAI;IAClB;IAEA;;;;GAIC,GACD,IAAI,cAAc;QAChB,MAAM,IAAI,8IAAA,CAAA,sBAAmB;IAC/B;IAEA;;;;;;;;GAQC,GACD,WAAW,EAAE,EAAE,IAAI,EAAE;QACnB,MAAM,IAAI,8IAAA,CAAA,sBAAmB;IAC/B;IAEA;;;;;;;GAOC,GACD,aAAa,EAAE,EAAE,MAAM,EAAE;QACvB,MAAM,IAAI,8IAAA,CAAA,sBAAmB;IAC/B;IAEA;;;;;GAKC,GACD,OAAO,EAAE,EAAE;QACT,MAAM,IAAI,8IAAA,CAAA,sBAAmB;IAC/B;IAEA;;;;;GAKC,GACD,OAAO,SAAS,EAAE;QAChB,MAAM,IAAI,8IAAA,CAAA,sBAAmB;IAC/B;IAEA;;;;GAIC,GACD,IAAI,UAAU;QACZ,MAAM,IAAI,8IAAA,CAAA,sBAAmB;IAC/B;AACF", "ignoreList": [0]}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/luxon/src/zones/systemZone.js"], "sourcesContent": ["import { formatOffset, parseZoneInfo } from \"../impl/util.js\";\nimport Zone from \"../zone.js\";\n\nlet singleton = null;\n\n/**\n * Represents the local zone for this JavaScript environment.\n * @implements {Zone}\n */\nexport default class SystemZone extends Zone {\n  /**\n   * Get a singleton instance of the local zone\n   * @return {SystemZone}\n   */\n  static get instance() {\n    if (singleton === null) {\n      singleton = new SystemZone();\n    }\n    return singleton;\n  }\n\n  /** @override **/\n  get type() {\n    return \"system\";\n  }\n\n  /** @override **/\n  get name() {\n    return new Intl.DateTimeFormat().resolvedOptions().timeZone;\n  }\n\n  /** @override **/\n  get isUniversal() {\n    return false;\n  }\n\n  /** @override **/\n  offsetName(ts, { format, locale }) {\n    return parseZoneInfo(ts, format, locale);\n  }\n\n  /** @override **/\n  formatOffset(ts, format) {\n    return formatOffset(this.offset(ts), format);\n  }\n\n  /** @override **/\n  offset(ts) {\n    return -new Date(ts).getTimezoneOffset();\n  }\n\n  /** @override **/\n  equals(otherZone) {\n    return otherZone.type === \"system\";\n  }\n\n  /** @override **/\n  get isValid() {\n    return true;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,IAAI,YAAY;AAMD,MAAM,mBAAmB,4IAAA,CAAA,UAAI;IAC1C;;;GAGC,GACD,WAAW,WAAW;QACpB,IAAI,cAAc,MAAM;YACtB,YAAY,IAAI;QAClB;QACA,OAAO;IACT;IAEA,eAAe,GACf,IAAI,OAAO;QACT,OAAO;IACT;IAEA,eAAe,GACf,IAAI,OAAO;QACT,OAAO,IAAI,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ;IAC7D;IAEA,eAAe,GACf,IAAI,cAAc;QAChB,OAAO;IACT;IAEA,eAAe,GACf,WAAW,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;QACjC,OAAO,CAAA,GAAA,oJAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,QAAQ;IACnC;IAEA,eAAe,GACf,aAAa,EAAE,EAAE,MAAM,EAAE;QACvB,OAAO,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;IACvC;IAEA,eAAe,GACf,OAAO,EAAE,EAAE;QACT,OAAO,CAAC,IAAI,KAAK,IAAI,iBAAiB;IACxC;IAEA,eAAe,GACf,OAAO,SAAS,EAAE;QAChB,OAAO,UAAU,IAAI,KAAK;IAC5B;IAEA,eAAe,GACf,IAAI,UAAU;QACZ,OAAO;IACT;AACF", "ignoreList": [0]}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/luxon/src/zones/IANAZone.js"], "sourcesContent": ["import { formatOffset, parseZoneInfo, isUndefined, objToLocalTS } from \"../impl/util.js\";\nimport Zone from \"../zone.js\";\n\nconst dtfCache = new Map();\nfunction makeDTF(zoneName) {\n  let dtf = dtfCache.get(zoneName);\n  if (dtf === undefined) {\n    dtf = new Intl.DateTimeFormat(\"en-US\", {\n      hour12: false,\n      timeZone: zoneName,\n      year: \"numeric\",\n      month: \"2-digit\",\n      day: \"2-digit\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n      second: \"2-digit\",\n      era: \"short\",\n    });\n    dtfCache.set(zoneName, dtf);\n  }\n  return dtf;\n}\n\nconst typeToPos = {\n  year: 0,\n  month: 1,\n  day: 2,\n  era: 3,\n  hour: 4,\n  minute: 5,\n  second: 6,\n};\n\nfunction hackyOffset(dtf, date) {\n  const formatted = dtf.format(date).replace(/\\u200E/g, \"\"),\n    parsed = /(\\d+)\\/(\\d+)\\/(\\d+) (AD|BC),? (\\d+):(\\d+):(\\d+)/.exec(formatted),\n    [, fMonth, fDay, fYear, fadOrBc, fHour, fMinute, fSecond] = parsed;\n  return [fYear, fMonth, fDay, fadOrBc, fHour, fMinute, fSecond];\n}\n\nfunction partsOffset(dtf, date) {\n  const formatted = dtf.formatToParts(date);\n  const filled = [];\n  for (let i = 0; i < formatted.length; i++) {\n    const { type, value } = formatted[i];\n    const pos = typeToPos[type];\n\n    if (type === \"era\") {\n      filled[pos] = value;\n    } else if (!isUndefined(pos)) {\n      filled[pos] = parseInt(value, 10);\n    }\n  }\n  return filled;\n}\n\nconst ianaZoneCache = new Map();\n/**\n * A zone identified by an IANA identifier, like America/New_York\n * @implements {Zone}\n */\nexport default class IANAZone extends Zone {\n  /**\n   * @param {string} name - Zone name\n   * @return {IANAZone}\n   */\n  static create(name) {\n    let zone = ianaZoneCache.get(name);\n    if (zone === undefined) {\n      ianaZoneCache.set(name, (zone = new IANAZone(name)));\n    }\n    return zone;\n  }\n\n  /**\n   * Reset local caches. Should only be necessary in testing scenarios.\n   * @return {void}\n   */\n  static resetCache() {\n    ianaZoneCache.clear();\n    dtfCache.clear();\n  }\n\n  /**\n   * Returns whether the provided string is a valid specifier. This only checks the string's format, not that the specifier identifies a known zone; see isValidZone for that.\n   * @param {string} s - The string to check validity on\n   * @example IANAZone.isValidSpecifier(\"America/New_York\") //=> true\n   * @example IANAZone.isValidSpecifier(\"Sport~~blorp\") //=> false\n   * @deprecated For backward compatibility, this forwards to isValidZone, better use `isValidZone()` directly instead.\n   * @return {boolean}\n   */\n  static isValidSpecifier(s) {\n    return this.isValidZone(s);\n  }\n\n  /**\n   * Returns whether the provided string identifies a real zone\n   * @param {string} zone - The string to check\n   * @example IANAZone.isValidZone(\"America/New_York\") //=> true\n   * @example IANAZone.isValidZone(\"Fantasia/Castle\") //=> false\n   * @example IANAZone.isValidZone(\"Sport~~blorp\") //=> false\n   * @return {boolean}\n   */\n  static isValidZone(zone) {\n    if (!zone) {\n      return false;\n    }\n    try {\n      new Intl.DateTimeFormat(\"en-US\", { timeZone: zone }).format();\n      return true;\n    } catch (e) {\n      return false;\n    }\n  }\n\n  constructor(name) {\n    super();\n    /** @private **/\n    this.zoneName = name;\n    /** @private **/\n    this.valid = IANAZone.isValidZone(name);\n  }\n\n  /**\n   * The type of zone. `iana` for all instances of `IANAZone`.\n   * @override\n   * @type {string}\n   */\n  get type() {\n    return \"iana\";\n  }\n\n  /**\n   * The name of this zone (i.e. the IANA zone name).\n   * @override\n   * @type {string}\n   */\n  get name() {\n    return this.zoneName;\n  }\n\n  /**\n   * Returns whether the offset is known to be fixed for the whole year:\n   * Always returns false for all IANA zones.\n   * @override\n   * @type {boolean}\n   */\n  get isUniversal() {\n    return false;\n  }\n\n  /**\n   * Returns the offset's common name (such as EST) at the specified timestamp\n   * @override\n   * @param {number} ts - Epoch milliseconds for which to get the name\n   * @param {Object} opts - Options to affect the format\n   * @param {string} opts.format - What style of offset to return. Accepts 'long' or 'short'.\n   * @param {string} opts.locale - What locale to return the offset name in.\n   * @return {string}\n   */\n  offsetName(ts, { format, locale }) {\n    return parseZoneInfo(ts, format, locale, this.name);\n  }\n\n  /**\n   * Returns the offset's value as a string\n   * @override\n   * @param {number} ts - Epoch milliseconds for which to get the offset\n   * @param {string} format - What style of offset to return.\n   *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively\n   * @return {string}\n   */\n  formatOffset(ts, format) {\n    return formatOffset(this.offset(ts), format);\n  }\n\n  /**\n   * Return the offset in minutes for this zone at the specified timestamp.\n   * @override\n   * @param {number} ts - Epoch milliseconds for which to compute the offset\n   * @return {number}\n   */\n  offset(ts) {\n    if (!this.valid) return NaN;\n    const date = new Date(ts);\n\n    if (isNaN(date)) return NaN;\n\n    const dtf = makeDTF(this.name);\n    let [year, month, day, adOrBc, hour, minute, second] = dtf.formatToParts\n      ? partsOffset(dtf, date)\n      : hackyOffset(dtf, date);\n\n    if (adOrBc === \"BC\") {\n      year = -Math.abs(year) + 1;\n    }\n\n    // because we're using hour12 and https://bugs.chromium.org/p/chromium/issues/detail?id=1025564&can=2&q=%2224%3A00%22%20datetimeformat\n    const adjustedHour = hour === 24 ? 0 : hour;\n\n    const asUTC = objToLocalTS({\n      year,\n      month,\n      day,\n      hour: adjustedHour,\n      minute,\n      second,\n      millisecond: 0,\n    });\n\n    let asTS = +date;\n    const over = asTS % 1000;\n    asTS -= over >= 0 ? over : 1000 + over;\n    return (asUTC - asTS) / (60 * 1000);\n  }\n\n  /**\n   * Return whether this Zone is equal to another zone\n   * @override\n   * @param {Zone} otherZone - the zone to compare\n   * @return {boolean}\n   */\n  equals(otherZone) {\n    return otherZone.type === \"iana\" && otherZone.name === this.name;\n  }\n\n  /**\n   * Return whether this Zone is valid.\n   * @override\n   * @type {boolean}\n   */\n  get isValid() {\n    return this.valid;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,WAAW,IAAI;AACrB,SAAS,QAAQ,QAAQ;IACvB,IAAI,MAAM,SAAS,GAAG,CAAC;IACvB,IAAI,QAAQ,WAAW;QACrB,MAAM,IAAI,KAAK,cAAc,CAAC,SAAS;YACrC,QAAQ;YACR,UAAU;YACV,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,KAAK;QACP;QACA,SAAS,GAAG,CAAC,UAAU;IACzB;IACA,OAAO;AACT;AAEA,MAAM,YAAY;IAChB,MAAM;IACN,OAAO;IACP,KAAK;IACL,KAAK;IACL,MAAM;IACN,QAAQ;IACR,QAAQ;AACV;AAEA,SAAS,YAAY,GAAG,EAAE,IAAI;IAC5B,MAAM,YAAY,IAAI,MAAM,CAAC,MAAM,OAAO,CAAC,WAAW,KACpD,SAAS,kDAAkD,IAAI,CAAC,YAChE,GAAG,QAAQ,MAAM,OAAO,SAAS,OAAO,SAAS,QAAQ,GAAG;IAC9D,OAAO;QAAC;QAAO;QAAQ;QAAM;QAAS;QAAO;QAAS;KAAQ;AAChE;AAEA,SAAS,YAAY,GAAG,EAAE,IAAI;IAC5B,MAAM,YAAY,IAAI,aAAa,CAAC;IACpC,MAAM,SAAS,EAAE;IACjB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACzC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,SAAS,CAAC,EAAE;QACpC,MAAM,MAAM,SAAS,CAAC,KAAK;QAE3B,IAAI,SAAS,OAAO;YAClB,MAAM,CAAC,IAAI,GAAG;QAChB,OAAO,IAAI,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,MAAM;YAC5B,MAAM,CAAC,IAAI,GAAG,SAAS,OAAO;QAChC;IACF;IACA,OAAO;AACT;AAEA,MAAM,gBAAgB,IAAI;AAKX,MAAM,iBAAiB,4IAAA,CAAA,UAAI;IACxC;;;GAGC,GACD,OAAO,OAAO,IAAI,EAAE;QAClB,IAAI,OAAO,cAAc,GAAG,CAAC;QAC7B,IAAI,SAAS,WAAW;YACtB,cAAc,GAAG,CAAC,MAAO,OAAO,IAAI,SAAS;QAC/C;QACA,OAAO;IACT;IAEA;;;GAGC,GACD,OAAO,aAAa;QAClB,cAAc,KAAK;QACnB,SAAS,KAAK;IAChB;IAEA;;;;;;;GAOC,GACD,OAAO,iBAAiB,CAAC,EAAE;QACzB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B;IAEA;;;;;;;GAOC,GACD,OAAO,YAAY,IAAI,EAAE;QACvB,IAAI,CAAC,MAAM;YACT,OAAO;QACT;QACA,IAAI;YACF,IAAI,KAAK,cAAc,CAAC,SAAS;gBAAE,UAAU;YAAK,GAAG,MAAM;YAC3D,OAAO;QACT,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IAEA,YAAY,IAAI,CAAE;QAChB,KAAK;QACL,cAAc,GACd,IAAI,CAAC,QAAQ,GAAG;QAChB,cAAc,GACd,IAAI,CAAC,KAAK,GAAG,SAAS,WAAW,CAAC;IACpC;IAEA;;;;GAIC,GACD,IAAI,OAAO;QACT,OAAO;IACT;IAEA;;;;GAIC,GACD,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ;IACtB;IAEA;;;;;GAKC,GACD,IAAI,cAAc;QAChB,OAAO;IACT;IAEA;;;;;;;;GAQC,GACD,WAAW,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;QACjC,OAAO,CAAA,GAAA,oJAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,QAAQ,QAAQ,IAAI,CAAC,IAAI;IACpD;IAEA;;;;;;;GAOC,GACD,aAAa,EAAE,EAAE,MAAM,EAAE;QACvB,OAAO,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;IACvC;IAEA;;;;;GAKC,GACD,OAAO,EAAE,EAAE;QACT,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO;QACxB,MAAM,OAAO,IAAI,KAAK;QAEtB,IAAI,MAAM,OAAO,OAAO;QAExB,MAAM,MAAM,QAAQ,IAAI,CAAC,IAAI;QAC7B,IAAI,CAAC,MAAM,OAAO,KAAK,QAAQ,MAAM,QAAQ,OAAO,GAAG,IAAI,aAAa,GACpE,YAAY,KAAK,QACjB,YAAY,KAAK;QAErB,IAAI,WAAW,MAAM;YACnB,OAAO,CAAC,KAAK,GAAG,CAAC,QAAQ;QAC3B;QAEA,sIAAsI;QACtI,MAAM,eAAe,SAAS,KAAK,IAAI;QAEvC,MAAM,QAAQ,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE;YACzB;YACA;YACA;YACA,MAAM;YACN;YACA;YACA,aAAa;QACf;QAEA,IAAI,OAAO,CAAC;QACZ,MAAM,OAAO,OAAO;QACpB,QAAQ,QAAQ,IAAI,OAAO,OAAO;QAClC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,IAAI;IACpC;IAEA;;;;;GAKC,GACD,OAAO,SAAS,EAAE;QAChB,OAAO,UAAU,IAAI,KAAK,UAAU,UAAU,IAAI,KAAK,IAAI,CAAC,IAAI;IAClE;IAEA;;;;GAIC,GACD,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK;IACnB;AACF", "ignoreList": [0]}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/luxon/src/impl/locale.js"], "sourcesContent": ["import { hasLocaleWeekInfo, hasRelative, padStart, roundTo, validateWeekSettings } from \"./util.js\";\nimport * as English from \"./english.js\";\nimport Settings from \"../settings.js\";\nimport DateTime from \"../datetime.js\";\nimport IANAZone from \"../zones/IANAZone.js\";\n\n// todo - remap caching\n\nlet intlLFCache = {};\nfunction getCachedLF(locString, opts = {}) {\n  const key = JSON.stringify([locString, opts]);\n  let dtf = intlLFCache[key];\n  if (!dtf) {\n    dtf = new Intl.ListFormat(locString, opts);\n    intlLFCache[key] = dtf;\n  }\n  return dtf;\n}\n\nconst intlDTCache = new Map();\nfunction getCachedDTF(locString, opts = {}) {\n  const key = JSON.stringify([locString, opts]);\n  let dtf = intlDTCache.get(key);\n  if (dtf === undefined) {\n    dtf = new Intl.DateTimeFormat(locString, opts);\n    intlDTCache.set(key, dtf);\n  }\n  return dtf;\n}\n\nconst intlNumCache = new Map();\nfunction getCachedINF(locString, opts = {}) {\n  const key = JSON.stringify([locString, opts]);\n  let inf = intlNumCache.get(key);\n  if (inf === undefined) {\n    inf = new Intl.NumberFormat(locString, opts);\n    intlNumCache.set(key, inf);\n  }\n  return inf;\n}\n\nconst intlRelCache = new Map();\nfunction getCachedRTF(locString, opts = {}) {\n  const { base, ...cacheKeyOpts } = opts; // exclude `base` from the options\n  const key = JSON.stringify([locString, cacheKeyOpts]);\n  let inf = intlRelCache.get(key);\n  if (inf === undefined) {\n    inf = new Intl.RelativeTimeFormat(locString, opts);\n    intlRelCache.set(key, inf);\n  }\n  return inf;\n}\n\nlet sysLocaleCache = null;\nfunction systemLocale() {\n  if (sysLocaleCache) {\n    return sysLocaleCache;\n  } else {\n    sysLocaleCache = new Intl.DateTimeFormat().resolvedOptions().locale;\n    return sysLocaleCache;\n  }\n}\n\nconst intlResolvedOptionsCache = new Map();\nfunction getCachedIntResolvedOptions(locString) {\n  let opts = intlResolvedOptionsCache.get(locString);\n  if (opts === undefined) {\n    opts = new Intl.DateTimeFormat(locString).resolvedOptions();\n    intlResolvedOptionsCache.set(locString, opts);\n  }\n  return opts;\n}\n\nconst weekInfoCache = new Map();\nfunction getCachedWeekInfo(locString) {\n  let data = weekInfoCache.get(locString);\n  if (!data) {\n    const locale = new Intl.Locale(locString);\n    // browsers currently implement this as a property, but spec says it should be a getter function\n    data = \"getWeekInfo\" in locale ? locale.getWeekInfo() : locale.weekInfo;\n    // minimalDays was removed from WeekInfo: https://github.com/tc39/proposal-intl-locale-info/issues/86\n    if (!(\"minimalDays\" in data)) {\n      data = { ...fallbackWeekSettings, ...data };\n    }\n    weekInfoCache.set(locString, data);\n  }\n  return data;\n}\n\nfunction parseLocaleString(localeStr) {\n  // I really want to avoid writing a BCP 47 parser\n  // see, e.g. https://github.com/wooorm/bcp-47\n  // Instead, we'll do this:\n\n  // a) if the string has no -u extensions, just leave it alone\n  // b) if it does, use Intl to resolve everything\n  // c) if Intl fails, try again without the -u\n\n  // private subtags and unicode subtags have ordering requirements,\n  // and we're not properly parsing this, so just strip out the\n  // private ones if they exist.\n  const xIndex = localeStr.indexOf(\"-x-\");\n  if (xIndex !== -1) {\n    localeStr = localeStr.substring(0, xIndex);\n  }\n\n  const uIndex = localeStr.indexOf(\"-u-\");\n  if (uIndex === -1) {\n    return [localeStr];\n  } else {\n    let options;\n    let selectedStr;\n    try {\n      options = getCachedDTF(localeStr).resolvedOptions();\n      selectedStr = localeStr;\n    } catch (e) {\n      const smaller = localeStr.substring(0, uIndex);\n      options = getCachedDTF(smaller).resolvedOptions();\n      selectedStr = smaller;\n    }\n\n    const { numberingSystem, calendar } = options;\n    return [selectedStr, numberingSystem, calendar];\n  }\n}\n\nfunction intlConfigString(localeStr, numberingSystem, outputCalendar) {\n  if (outputCalendar || numberingSystem) {\n    if (!localeStr.includes(\"-u-\")) {\n      localeStr += \"-u\";\n    }\n\n    if (outputCalendar) {\n      localeStr += `-ca-${outputCalendar}`;\n    }\n\n    if (numberingSystem) {\n      localeStr += `-nu-${numberingSystem}`;\n    }\n    return localeStr;\n  } else {\n    return localeStr;\n  }\n}\n\nfunction mapMonths(f) {\n  const ms = [];\n  for (let i = 1; i <= 12; i++) {\n    const dt = DateTime.utc(2009, i, 1);\n    ms.push(f(dt));\n  }\n  return ms;\n}\n\nfunction mapWeekdays(f) {\n  const ms = [];\n  for (let i = 1; i <= 7; i++) {\n    const dt = DateTime.utc(2016, 11, 13 + i);\n    ms.push(f(dt));\n  }\n  return ms;\n}\n\nfunction listStuff(loc, length, englishFn, intlFn) {\n  const mode = loc.listingMode();\n\n  if (mode === \"error\") {\n    return null;\n  } else if (mode === \"en\") {\n    return englishFn(length);\n  } else {\n    return intlFn(length);\n  }\n}\n\nfunction supportsFastNumbers(loc) {\n  if (loc.numberingSystem && loc.numberingSystem !== \"latn\") {\n    return false;\n  } else {\n    return (\n      loc.numberingSystem === \"latn\" ||\n      !loc.locale ||\n      loc.locale.startsWith(\"en\") ||\n      getCachedIntResolvedOptions(loc.locale).numberingSystem === \"latn\"\n    );\n  }\n}\n\n/**\n * @private\n */\n\nclass PolyNumberFormatter {\n  constructor(intl, forceSimple, opts) {\n    this.padTo = opts.padTo || 0;\n    this.floor = opts.floor || false;\n\n    const { padTo, floor, ...otherOpts } = opts;\n\n    if (!forceSimple || Object.keys(otherOpts).length > 0) {\n      const intlOpts = { useGrouping: false, ...opts };\n      if (opts.padTo > 0) intlOpts.minimumIntegerDigits = opts.padTo;\n      this.inf = getCachedINF(intl, intlOpts);\n    }\n  }\n\n  format(i) {\n    if (this.inf) {\n      const fixed = this.floor ? Math.floor(i) : i;\n      return this.inf.format(fixed);\n    } else {\n      // to match the browser's numberformatter defaults\n      const fixed = this.floor ? Math.floor(i) : roundTo(i, 3);\n      return padStart(fixed, this.padTo);\n    }\n  }\n}\n\n/**\n * @private\n */\n\nclass PolyDateFormatter {\n  constructor(dt, intl, opts) {\n    this.opts = opts;\n    this.originalZone = undefined;\n\n    let z = undefined;\n    if (this.opts.timeZone) {\n      // Don't apply any workarounds if a timeZone is explicitly provided in opts\n      this.dt = dt;\n    } else if (dt.zone.type === \"fixed\") {\n      // UTC-8 or Etc/UTC-8 are not part of tzdata, only Etc/GMT+8 and the like.\n      // That is why fixed-offset TZ is set to that unless it is:\n      // 1. Representing offset 0 when UTC is used to maintain previous behavior and does not become GMT.\n      // 2. Unsupported by the browser:\n      //    - some do not support Etc/\n      //    - < Etc/GMT-14, > Etc/GMT+12, and 30-minute or 45-minute offsets are not part of tzdata\n      const gmtOffset = -1 * (dt.offset / 60);\n      const offsetZ = gmtOffset >= 0 ? `Etc/GMT+${gmtOffset}` : `Etc/GMT${gmtOffset}`;\n      if (dt.offset !== 0 && IANAZone.create(offsetZ).valid) {\n        z = offsetZ;\n        this.dt = dt;\n      } else {\n        // Not all fixed-offset zones like Etc/+4:30 are present in tzdata so\n        // we manually apply the offset and substitute the zone as needed.\n        z = \"UTC\";\n        this.dt = dt.offset === 0 ? dt : dt.setZone(\"UTC\").plus({ minutes: dt.offset });\n        this.originalZone = dt.zone;\n      }\n    } else if (dt.zone.type === \"system\") {\n      this.dt = dt;\n    } else if (dt.zone.type === \"iana\") {\n      this.dt = dt;\n      z = dt.zone.name;\n    } else {\n      // Custom zones can have any offset / offsetName so we just manually\n      // apply the offset and substitute the zone as needed.\n      z = \"UTC\";\n      this.dt = dt.setZone(\"UTC\").plus({ minutes: dt.offset });\n      this.originalZone = dt.zone;\n    }\n\n    const intlOpts = { ...this.opts };\n    intlOpts.timeZone = intlOpts.timeZone || z;\n    this.dtf = getCachedDTF(intl, intlOpts);\n  }\n\n  format() {\n    if (this.originalZone) {\n      // If we have to substitute in the actual zone name, we have to use\n      // formatToParts so that the timezone can be replaced.\n      return this.formatToParts()\n        .map(({ value }) => value)\n        .join(\"\");\n    }\n    return this.dtf.format(this.dt.toJSDate());\n  }\n\n  formatToParts() {\n    const parts = this.dtf.formatToParts(this.dt.toJSDate());\n    if (this.originalZone) {\n      return parts.map((part) => {\n        if (part.type === \"timeZoneName\") {\n          const offsetName = this.originalZone.offsetName(this.dt.ts, {\n            locale: this.dt.locale,\n            format: this.opts.timeZoneName,\n          });\n          return {\n            ...part,\n            value: offsetName,\n          };\n        } else {\n          return part;\n        }\n      });\n    }\n    return parts;\n  }\n\n  resolvedOptions() {\n    return this.dtf.resolvedOptions();\n  }\n}\n\n/**\n * @private\n */\nclass PolyRelFormatter {\n  constructor(intl, isEnglish, opts) {\n    this.opts = { style: \"long\", ...opts };\n    if (!isEnglish && hasRelative()) {\n      this.rtf = getCachedRTF(intl, opts);\n    }\n  }\n\n  format(count, unit) {\n    if (this.rtf) {\n      return this.rtf.format(count, unit);\n    } else {\n      return English.formatRelativeTime(unit, count, this.opts.numeric, this.opts.style !== \"long\");\n    }\n  }\n\n  formatToParts(count, unit) {\n    if (this.rtf) {\n      return this.rtf.formatToParts(count, unit);\n    } else {\n      return [];\n    }\n  }\n}\n\nconst fallbackWeekSettings = {\n  firstDay: 1,\n  minimalDays: 4,\n  weekend: [6, 7],\n};\n\n/**\n * @private\n */\nexport default class Locale {\n  static fromOpts(opts) {\n    return Locale.create(\n      opts.locale,\n      opts.numberingSystem,\n      opts.outputCalendar,\n      opts.weekSettings,\n      opts.defaultToEN\n    );\n  }\n\n  static create(locale, numberingSystem, outputCalendar, weekSettings, defaultToEN = false) {\n    const specifiedLocale = locale || Settings.defaultLocale;\n    // the system locale is useful for human-readable strings but annoying for parsing/formatting known formats\n    const localeR = specifiedLocale || (defaultToEN ? \"en-US\" : systemLocale());\n    const numberingSystemR = numberingSystem || Settings.defaultNumberingSystem;\n    const outputCalendarR = outputCalendar || Settings.defaultOutputCalendar;\n    const weekSettingsR = validateWeekSettings(weekSettings) || Settings.defaultWeekSettings;\n    return new Locale(localeR, numberingSystemR, outputCalendarR, weekSettingsR, specifiedLocale);\n  }\n\n  static resetCache() {\n    sysLocaleCache = null;\n    intlDTCache.clear();\n    intlNumCache.clear();\n    intlRelCache.clear();\n    intlResolvedOptionsCache.clear();\n    weekInfoCache.clear();\n  }\n\n  static fromObject({ locale, numberingSystem, outputCalendar, weekSettings } = {}) {\n    return Locale.create(locale, numberingSystem, outputCalendar, weekSettings);\n  }\n\n  constructor(locale, numbering, outputCalendar, weekSettings, specifiedLocale) {\n    const [parsedLocale, parsedNumberingSystem, parsedOutputCalendar] = parseLocaleString(locale);\n\n    this.locale = parsedLocale;\n    this.numberingSystem = numbering || parsedNumberingSystem || null;\n    this.outputCalendar = outputCalendar || parsedOutputCalendar || null;\n    this.weekSettings = weekSettings;\n    this.intl = intlConfigString(this.locale, this.numberingSystem, this.outputCalendar);\n\n    this.weekdaysCache = { format: {}, standalone: {} };\n    this.monthsCache = { format: {}, standalone: {} };\n    this.meridiemCache = null;\n    this.eraCache = {};\n\n    this.specifiedLocale = specifiedLocale;\n    this.fastNumbersCached = null;\n  }\n\n  get fastNumbers() {\n    if (this.fastNumbersCached == null) {\n      this.fastNumbersCached = supportsFastNumbers(this);\n    }\n\n    return this.fastNumbersCached;\n  }\n\n  listingMode() {\n    const isActuallyEn = this.isEnglish();\n    const hasNoWeirdness =\n      (this.numberingSystem === null || this.numberingSystem === \"latn\") &&\n      (this.outputCalendar === null || this.outputCalendar === \"gregory\");\n    return isActuallyEn && hasNoWeirdness ? \"en\" : \"intl\";\n  }\n\n  clone(alts) {\n    if (!alts || Object.getOwnPropertyNames(alts).length === 0) {\n      return this;\n    } else {\n      return Locale.create(\n        alts.locale || this.specifiedLocale,\n        alts.numberingSystem || this.numberingSystem,\n        alts.outputCalendar || this.outputCalendar,\n        validateWeekSettings(alts.weekSettings) || this.weekSettings,\n        alts.defaultToEN || false\n      );\n    }\n  }\n\n  redefaultToEN(alts = {}) {\n    return this.clone({ ...alts, defaultToEN: true });\n  }\n\n  redefaultToSystem(alts = {}) {\n    return this.clone({ ...alts, defaultToEN: false });\n  }\n\n  months(length, format = false) {\n    return listStuff(this, length, English.months, () => {\n      const intl = format ? { month: length, day: \"numeric\" } : { month: length },\n        formatStr = format ? \"format\" : \"standalone\";\n      if (!this.monthsCache[formatStr][length]) {\n        this.monthsCache[formatStr][length] = mapMonths((dt) => this.extract(dt, intl, \"month\"));\n      }\n      return this.monthsCache[formatStr][length];\n    });\n  }\n\n  weekdays(length, format = false) {\n    return listStuff(this, length, English.weekdays, () => {\n      const intl = format\n          ? { weekday: length, year: \"numeric\", month: \"long\", day: \"numeric\" }\n          : { weekday: length },\n        formatStr = format ? \"format\" : \"standalone\";\n      if (!this.weekdaysCache[formatStr][length]) {\n        this.weekdaysCache[formatStr][length] = mapWeekdays((dt) =>\n          this.extract(dt, intl, \"weekday\")\n        );\n      }\n      return this.weekdaysCache[formatStr][length];\n    });\n  }\n\n  meridiems() {\n    return listStuff(\n      this,\n      undefined,\n      () => English.meridiems,\n      () => {\n        // In theory there could be aribitrary day periods. We're gonna assume there are exactly two\n        // for AM and PM. This is probably wrong, but it's makes parsing way easier.\n        if (!this.meridiemCache) {\n          const intl = { hour: \"numeric\", hourCycle: \"h12\" };\n          this.meridiemCache = [DateTime.utc(2016, 11, 13, 9), DateTime.utc(2016, 11, 13, 19)].map(\n            (dt) => this.extract(dt, intl, \"dayperiod\")\n          );\n        }\n\n        return this.meridiemCache;\n      }\n    );\n  }\n\n  eras(length) {\n    return listStuff(this, length, English.eras, () => {\n      const intl = { era: length };\n\n      // This is problematic. Different calendars are going to define eras totally differently. What I need is the minimum set of dates\n      // to definitely enumerate them.\n      if (!this.eraCache[length]) {\n        this.eraCache[length] = [DateTime.utc(-40, 1, 1), DateTime.utc(2017, 1, 1)].map((dt) =>\n          this.extract(dt, intl, \"era\")\n        );\n      }\n\n      return this.eraCache[length];\n    });\n  }\n\n  extract(dt, intlOpts, field) {\n    const df = this.dtFormatter(dt, intlOpts),\n      results = df.formatToParts(),\n      matching = results.find((m) => m.type.toLowerCase() === field);\n    return matching ? matching.value : null;\n  }\n\n  numberFormatter(opts = {}) {\n    // this forcesimple option is never used (the only caller short-circuits on it, but it seems safer to leave)\n    // (in contrast, the rest of the condition is used heavily)\n    return new PolyNumberFormatter(this.intl, opts.forceSimple || this.fastNumbers, opts);\n  }\n\n  dtFormatter(dt, intlOpts = {}) {\n    return new PolyDateFormatter(dt, this.intl, intlOpts);\n  }\n\n  relFormatter(opts = {}) {\n    return new PolyRelFormatter(this.intl, this.isEnglish(), opts);\n  }\n\n  listFormatter(opts = {}) {\n    return getCachedLF(this.intl, opts);\n  }\n\n  isEnglish() {\n    return (\n      this.locale === \"en\" ||\n      this.locale.toLowerCase() === \"en-us\" ||\n      getCachedIntResolvedOptions(this.intl).locale.startsWith(\"en-us\")\n    );\n  }\n\n  getWeekSettings() {\n    if (this.weekSettings) {\n      return this.weekSettings;\n    } else if (!hasLocaleWeekInfo()) {\n      return fallbackWeekSettings;\n    } else {\n      return getCachedWeekInfo(this.locale);\n    }\n  }\n\n  getStartOfWeek() {\n    return this.getWeekSettings().firstDay;\n  }\n\n  getMinDaysInFirstWeek() {\n    return this.getWeekSettings().minimalDays;\n  }\n\n  getWeekendDays() {\n    return this.getWeekSettings().weekend;\n  }\n\n  equals(other) {\n    return (\n      this.locale === other.locale &&\n      this.numberingSystem === other.numberingSystem &&\n      this.outputCalendar === other.outputCalendar\n    );\n  }\n\n  toString() {\n    return `Locale(${this.locale}, ${this.numberingSystem}, ${this.outputCalendar})`;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,uBAAuB;AAEvB,IAAI,cAAc,CAAC;AACnB,SAAS,YAAY,SAAS,EAAE,OAAO,CAAC,CAAC;IACvC,MAAM,MAAM,KAAK,SAAS,CAAC;QAAC;QAAW;KAAK;IAC5C,IAAI,MAAM,WAAW,CAAC,IAAI;IAC1B,IAAI,CAAC,KAAK;QACR,MAAM,IAAI,KAAK,UAAU,CAAC,WAAW;QACrC,WAAW,CAAC,IAAI,GAAG;IACrB;IACA,OAAO;AACT;AAEA,MAAM,cAAc,IAAI;AACxB,SAAS,aAAa,SAAS,EAAE,OAAO,CAAC,CAAC;IACxC,MAAM,MAAM,KAAK,SAAS,CAAC;QAAC;QAAW;KAAK;IAC5C,IAAI,MAAM,YAAY,GAAG,CAAC;IAC1B,IAAI,QAAQ,WAAW;QACrB,MAAM,IAAI,KAAK,cAAc,CAAC,WAAW;QACzC,YAAY,GAAG,CAAC,KAAK;IACvB;IACA,OAAO;AACT;AAEA,MAAM,eAAe,IAAI;AACzB,SAAS,aAAa,SAAS,EAAE,OAAO,CAAC,CAAC;IACxC,MAAM,MAAM,KAAK,SAAS,CAAC;QAAC;QAAW;KAAK;IAC5C,IAAI,MAAM,aAAa,GAAG,CAAC;IAC3B,IAAI,QAAQ,WAAW;QACrB,MAAM,IAAI,KAAK,YAAY,CAAC,WAAW;QACvC,aAAa,GAAG,CAAC,KAAK;IACxB;IACA,OAAO;AACT;AAEA,MAAM,eAAe,IAAI;AACzB,SAAS,aAAa,SAAS,EAAE,OAAO,CAAC,CAAC;IACxC,MAAM,EAAE,IAAI,EAAE,GAAG,cAAc,GAAG,MAAM,kCAAkC;IAC1E,MAAM,MAAM,KAAK,SAAS,CAAC;QAAC;QAAW;KAAa;IACpD,IAAI,MAAM,aAAa,GAAG,CAAC;IAC3B,IAAI,QAAQ,WAAW;QACrB,MAAM,IAAI,KAAK,kBAAkB,CAAC,WAAW;QAC7C,aAAa,GAAG,CAAC,KAAK;IACxB;IACA,OAAO;AACT;AAEA,IAAI,iBAAiB;AACrB,SAAS;IACP,IAAI,gBAAgB;QAClB,OAAO;IACT,OAAO;QACL,iBAAiB,IAAI,KAAK,cAAc,GAAG,eAAe,GAAG,MAAM;QACnE,OAAO;IACT;AACF;AAEA,MAAM,2BAA2B,IAAI;AACrC,SAAS,4BAA4B,SAAS;IAC5C,IAAI,OAAO,yBAAyB,GAAG,CAAC;IACxC,IAAI,SAAS,WAAW;QACtB,OAAO,IAAI,KAAK,cAAc,CAAC,WAAW,eAAe;QACzD,yBAAyB,GAAG,CAAC,WAAW;IAC1C;IACA,OAAO;AACT;AAEA,MAAM,gBAAgB,IAAI;AAC1B,SAAS,kBAAkB,SAAS;IAClC,IAAI,OAAO,cAAc,GAAG,CAAC;IAC7B,IAAI,CAAC,MAAM;QACT,MAAM,SAAS,IAAI,KAAK,MAAM,CAAC;QAC/B,gGAAgG;QAChG,OAAO,iBAAiB,SAAS,OAAO,WAAW,KAAK,OAAO,QAAQ;QACvE,qGAAqG;QACrG,IAAI,CAAC,CAAC,iBAAiB,IAAI,GAAG;YAC5B,OAAO;gBAAE,GAAG,oBAAoB;gBAAE,GAAG,IAAI;YAAC;QAC5C;QACA,cAAc,GAAG,CAAC,WAAW;IAC/B;IACA,OAAO;AACT;AAEA,SAAS,kBAAkB,SAAS;IAClC,iDAAiD;IACjD,6CAA6C;IAC7C,0BAA0B;IAE1B,6DAA6D;IAC7D,gDAAgD;IAChD,6CAA6C;IAE7C,kEAAkE;IAClE,6DAA6D;IAC7D,8BAA8B;IAC9B,MAAM,SAAS,UAAU,OAAO,CAAC;IACjC,IAAI,WAAW,CAAC,GAAG;QACjB,YAAY,UAAU,SAAS,CAAC,GAAG;IACrC;IAEA,MAAM,SAAS,UAAU,OAAO,CAAC;IACjC,IAAI,WAAW,CAAC,GAAG;QACjB,OAAO;YAAC;SAAU;IACpB,OAAO;QACL,IAAI;QACJ,IAAI;QACJ,IAAI;YACF,UAAU,aAAa,WAAW,eAAe;YACjD,cAAc;QAChB,EAAE,OAAO,GAAG;YACV,MAAM,UAAU,UAAU,SAAS,CAAC,GAAG;YACvC,UAAU,aAAa,SAAS,eAAe;YAC/C,cAAc;QAChB;QAEA,MAAM,EAAE,eAAe,EAAE,QAAQ,EAAE,GAAG;QACtC,OAAO;YAAC;YAAa;YAAiB;SAAS;IACjD;AACF;AAEA,SAAS,iBAAiB,SAAS,EAAE,eAAe,EAAE,cAAc;IAClE,IAAI,kBAAkB,iBAAiB;QACrC,IAAI,CAAC,UAAU,QAAQ,CAAC,QAAQ;YAC9B,aAAa;QACf;QAEA,IAAI,gBAAgB;YAClB,aAAa,CAAC,IAAI,EAAE,gBAAgB;QACtC;QAEA,IAAI,iBAAiB;YACnB,aAAa,CAAC,IAAI,EAAE,iBAAiB;QACvC;QACA,OAAO;IACT,OAAO;QACL,OAAO;IACT;AACF;AAEA,SAAS,UAAU,CAAC;IAClB,MAAM,KAAK,EAAE;IACb,IAAK,IAAI,IAAI,GAAG,KAAK,IAAI,IAAK;QAC5B,MAAM,KAAK,gJAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,MAAM,GAAG;QACjC,GAAG,IAAI,CAAC,EAAE;IACZ;IACA,OAAO;AACT;AAEA,SAAS,YAAY,CAAC;IACpB,MAAM,KAAK,EAAE;IACb,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;QAC3B,MAAM,KAAK,gJAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,MAAM,IAAI,KAAK;QACvC,GAAG,IAAI,CAAC,EAAE;IACZ;IACA,OAAO;AACT;AAEA,SAAS,UAAU,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM;IAC/C,MAAM,OAAO,IAAI,WAAW;IAE5B,IAAI,SAAS,SAAS;QACpB,OAAO;IACT,OAAO,IAAI,SAAS,MAAM;QACxB,OAAO,UAAU;IACnB,OAAO;QACL,OAAO,OAAO;IAChB;AACF;AAEA,SAAS,oBAAoB,GAAG;IAC9B,IAAI,IAAI,eAAe,IAAI,IAAI,eAAe,KAAK,QAAQ;QACzD,OAAO;IACT,OAAO;QACL,OACE,IAAI,eAAe,KAAK,UACxB,CAAC,IAAI,MAAM,IACX,IAAI,MAAM,CAAC,UAAU,CAAC,SACtB,4BAA4B,IAAI,MAAM,EAAE,eAAe,KAAK;IAEhE;AACF;AAEA;;CAEC,GAED,MAAM;IACJ,YAAY,IAAI,EAAE,WAAW,EAAE,IAAI,CAAE;QACnC,IAAI,CAAC,KAAK,GAAG,KAAK,KAAK,IAAI;QAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,KAAK,IAAI;QAE3B,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,WAAW,GAAG;QAEvC,IAAI,CAAC,eAAe,OAAO,IAAI,CAAC,WAAW,MAAM,GAAG,GAAG;YACrD,MAAM,WAAW;gBAAE,aAAa;gBAAO,GAAG,IAAI;YAAC;YAC/C,IAAI,KAAK,KAAK,GAAG,GAAG,SAAS,oBAAoB,GAAG,KAAK,KAAK;YAC9D,IAAI,CAAC,GAAG,GAAG,aAAa,MAAM;QAChC;IACF;IAEA,OAAO,CAAC,EAAE;QACR,IAAI,IAAI,CAAC,GAAG,EAAE;YACZ,MAAM,QAAQ,IAAI,CAAC,KAAK,GAAG,KAAK,KAAK,CAAC,KAAK;YAC3C,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;QACzB,OAAO;YACL,kDAAkD;YAClD,MAAM,QAAQ,IAAI,CAAC,KAAK,GAAG,KAAK,KAAK,CAAC,KAAK,CAAA,GAAA,oJAAA,CAAA,UAAO,AAAD,EAAE,GAAG;YACtD,OAAO,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,IAAI,CAAC,KAAK;QACnC;IACF;AACF;AAEA;;CAEC,GAED,MAAM;IACJ,YAAY,EAAE,EAAE,IAAI,EAAE,IAAI,CAAE;QAC1B,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,YAAY,GAAG;QAEpB,IAAI,IAAI;QACR,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACtB,2EAA2E;YAC3E,IAAI,CAAC,EAAE,GAAG;QACZ,OAAO,IAAI,GAAG,IAAI,CAAC,IAAI,KAAK,SAAS;YACnC,0EAA0E;YAC1E,2DAA2D;YAC3D,mGAAmG;YACnG,iCAAiC;YACjC,gCAAgC;YAChC,6FAA6F;YAC7F,MAAM,YAAY,CAAC,IAAI,CAAC,GAAG,MAAM,GAAG,EAAE;YACtC,MAAM,UAAU,aAAa,IAAI,CAAC,QAAQ,EAAE,WAAW,GAAG,CAAC,OAAO,EAAE,WAAW;YAC/E,IAAI,GAAG,MAAM,KAAK,KAAK,yJAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,SAAS,KAAK,EAAE;gBACrD,IAAI;gBACJ,IAAI,CAAC,EAAE,GAAG;YACZ,OAAO;gBACL,qEAAqE;gBACrE,kEAAkE;gBAClE,IAAI;gBACJ,IAAI,CAAC,EAAE,GAAG,GAAG,MAAM,KAAK,IAAI,KAAK,GAAG,OAAO,CAAC,OAAO,IAAI,CAAC;oBAAE,SAAS,GAAG,MAAM;gBAAC;gBAC7E,IAAI,CAAC,YAAY,GAAG,GAAG,IAAI;YAC7B;QACF,OAAO,IAAI,GAAG,IAAI,CAAC,IAAI,KAAK,UAAU;YACpC,IAAI,CAAC,EAAE,GAAG;QACZ,OAAO,IAAI,GAAG,IAAI,CAAC,IAAI,KAAK,QAAQ;YAClC,IAAI,CAAC,EAAE,GAAG;YACV,IAAI,GAAG,IAAI,CAAC,IAAI;QAClB,OAAO;YACL,oEAAoE;YACpE,sDAAsD;YACtD,IAAI;YACJ,IAAI,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,OAAO,IAAI,CAAC;gBAAE,SAAS,GAAG,MAAM;YAAC;YACtD,IAAI,CAAC,YAAY,GAAG,GAAG,IAAI;QAC7B;QAEA,MAAM,WAAW;YAAE,GAAG,IAAI,CAAC,IAAI;QAAC;QAChC,SAAS,QAAQ,GAAG,SAAS,QAAQ,IAAI;QACzC,IAAI,CAAC,GAAG,GAAG,aAAa,MAAM;IAChC;IAEA,SAAS;QACP,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,mEAAmE;YACnE,sDAAsD;YACtD,OAAO,IAAI,CAAC,aAAa,GACtB,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,GAAK,OACnB,IAAI,CAAC;QACV;QACA,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ;IACzC;IAEA,gBAAgB;QACd,MAAM,QAAQ,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ;QACrD,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,OAAO,MAAM,GAAG,CAAC,CAAC;gBAChB,IAAI,KAAK,IAAI,KAAK,gBAAgB;oBAChC,MAAM,aAAa,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;wBAC1D,QAAQ,IAAI,CAAC,EAAE,CAAC,MAAM;wBACtB,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY;oBAChC;oBACA,OAAO;wBACL,GAAG,IAAI;wBACP,OAAO;oBACT;gBACF,OAAO;oBACL,OAAO;gBACT;YACF;QACF;QACA,OAAO;IACT;IAEA,kBAAkB;QAChB,OAAO,IAAI,CAAC,GAAG,CAAC,eAAe;IACjC;AACF;AAEA;;CAEC,GACD,MAAM;IACJ,YAAY,IAAI,EAAE,SAAS,EAAE,IAAI,CAAE;QACjC,IAAI,CAAC,IAAI,GAAG;YAAE,OAAO;YAAQ,GAAG,IAAI;QAAC;QACrC,IAAI,CAAC,aAAa,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,KAAK;YAC/B,IAAI,CAAC,GAAG,GAAG,aAAa,MAAM;QAChC;IACF;IAEA,OAAO,KAAK,EAAE,IAAI,EAAE;QAClB,IAAI,IAAI,CAAC,GAAG,EAAE;YACZ,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO;QAChC,OAAO;YACL,OAAO,CAAA,GAAA,uJAAA,CAAA,qBAA0B,AAAD,EAAE,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK;QACxF;IACF;IAEA,cAAc,KAAK,EAAE,IAAI,EAAE;QACzB,IAAI,IAAI,CAAC,GAAG,EAAE;YACZ,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO;QACvC,OAAO;YACL,OAAO,EAAE;QACX;IACF;AACF;AAEA,MAAM,uBAAuB;IAC3B,UAAU;IACV,aAAa;IACb,SAAS;QAAC;QAAG;KAAE;AACjB;AAKe,MAAM;IACnB,OAAO,SAAS,IAAI,EAAE;QACpB,OAAO,OAAO,MAAM,CAClB,KAAK,MAAM,EACX,KAAK,eAAe,EACpB,KAAK,cAAc,EACnB,KAAK,YAAY,EACjB,KAAK,WAAW;IAEpB;IAEA,OAAO,OAAO,MAAM,EAAE,eAAe,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,KAAK,EAAE;QACxF,MAAM,kBAAkB,UAAU,gJAAA,CAAA,UAAQ,CAAC,aAAa;QACxD,2GAA2G;QAC3G,MAAM,UAAU,mBAAmB,CAAC,cAAc,UAAU,cAAc;QAC1E,MAAM,mBAAmB,mBAAmB,gJAAA,CAAA,UAAQ,CAAC,sBAAsB;QAC3E,MAAM,kBAAkB,kBAAkB,gJAAA,CAAA,UAAQ,CAAC,qBAAqB;QACxE,MAAM,gBAAgB,CAAA,GAAA,oJAAA,CAAA,uBAAoB,AAAD,EAAE,iBAAiB,gJAAA,CAAA,UAAQ,CAAC,mBAAmB;QACxF,OAAO,IAAI,OAAO,SAAS,kBAAkB,iBAAiB,eAAe;IAC/E;IAEA,OAAO,aAAa;QAClB,iBAAiB;QACjB,YAAY,KAAK;QACjB,aAAa,KAAK;QAClB,aAAa,KAAK;QAClB,yBAAyB,KAAK;QAC9B,cAAc,KAAK;IACrB;IAEA,OAAO,WAAW,EAAE,MAAM,EAAE,eAAe,EAAE,cAAc,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC,EAAE;QAChF,OAAO,OAAO,MAAM,CAAC,QAAQ,iBAAiB,gBAAgB;IAChE;IAEA,YAAY,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,YAAY,EAAE,eAAe,CAAE;QAC5E,MAAM,CAAC,cAAc,uBAAuB,qBAAqB,GAAG,kBAAkB;QAEtF,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,eAAe,GAAG,aAAa,yBAAyB;QAC7D,IAAI,CAAC,cAAc,GAAG,kBAAkB,wBAAwB;QAChE,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,IAAI,GAAG,iBAAiB,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,cAAc;QAEnF,IAAI,CAAC,aAAa,GAAG;YAAE,QAAQ,CAAC;YAAG,YAAY,CAAC;QAAE;QAClD,IAAI,CAAC,WAAW,GAAG;YAAE,QAAQ,CAAC;YAAG,YAAY,CAAC;QAAE;QAChD,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,QAAQ,GAAG,CAAC;QAEjB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,iBAAiB,GAAG;IAC3B;IAEA,IAAI,cAAc;QAChB,IAAI,IAAI,CAAC,iBAAiB,IAAI,MAAM;YAClC,IAAI,CAAC,iBAAiB,GAAG,oBAAoB,IAAI;QACnD;QAEA,OAAO,IAAI,CAAC,iBAAiB;IAC/B;IAEA,cAAc;QACZ,MAAM,eAAe,IAAI,CAAC,SAAS;QACnC,MAAM,iBACJ,CAAC,IAAI,CAAC,eAAe,KAAK,QAAQ,IAAI,CAAC,eAAe,KAAK,MAAM,KACjE,CAAC,IAAI,CAAC,cAAc,KAAK,QAAQ,IAAI,CAAC,cAAc,KAAK,SAAS;QACpE,OAAO,gBAAgB,iBAAiB,OAAO;IACjD;IAEA,MAAM,IAAI,EAAE;QACV,IAAI,CAAC,QAAQ,OAAO,mBAAmB,CAAC,MAAM,MAAM,KAAK,GAAG;YAC1D,OAAO,IAAI;QACb,OAAO;YACL,OAAO,OAAO,MAAM,CAClB,KAAK,MAAM,IAAI,IAAI,CAAC,eAAe,EACnC,KAAK,eAAe,IAAI,IAAI,CAAC,eAAe,EAC5C,KAAK,cAAc,IAAI,IAAI,CAAC,cAAc,EAC1C,CAAA,GAAA,oJAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK,YAAY,KAAK,IAAI,CAAC,YAAY,EAC5D,KAAK,WAAW,IAAI;QAExB;IACF;IAEA,cAAc,OAAO,CAAC,CAAC,EAAE;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC;YAAE,GAAG,IAAI;YAAE,aAAa;QAAK;IACjD;IAEA,kBAAkB,OAAO,CAAC,CAAC,EAAE;QAC3B,OAAO,IAAI,CAAC,KAAK,CAAC;YAAE,GAAG,IAAI;YAAE,aAAa;QAAM;IAClD;IAEA,OAAO,MAAM,EAAE,SAAS,KAAK,EAAE;QAC7B,OAAO,UAAU,IAAI,EAAE,QAAQ,uJAAA,CAAA,SAAc,EAAE;YAC7C,MAAM,OAAO,SAAS;gBAAE,OAAO;gBAAQ,KAAK;YAAU,IAAI;gBAAE,OAAO;YAAO,GACxE,YAAY,SAAS,WAAW;YAClC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,EAAE;gBACxC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,GAAG,UAAU,CAAC,KAAO,IAAI,CAAC,OAAO,CAAC,IAAI,MAAM;YACjF;YACA,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO;QAC5C;IACF;IAEA,SAAS,MAAM,EAAE,SAAS,KAAK,EAAE;QAC/B,OAAO,UAAU,IAAI,EAAE,QAAQ,uJAAA,CAAA,WAAgB,EAAE;YAC/C,MAAM,OAAO,SACP;gBAAE,SAAS;gBAAQ,MAAM;gBAAW,OAAO;gBAAQ,KAAK;YAAU,IAClE;gBAAE,SAAS;YAAO,GACtB,YAAY,SAAS,WAAW;YAClC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,OAAO,EAAE;gBAC1C,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,OAAO,GAAG,YAAY,CAAC,KACnD,IAAI,CAAC,OAAO,CAAC,IAAI,MAAM;YAE3B;YACA,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,OAAO;QAC9C;IACF;IAEA,YAAY;QACV,OAAO,UACL,IAAI,EACJ,WACA,IAAM,uJAAA,CAAA,YAAiB,EACvB;YACE,4FAA4F;YAC5F,4EAA4E;YAC5E,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;gBACvB,MAAM,OAAO;oBAAE,MAAM;oBAAW,WAAW;gBAAM;gBACjD,IAAI,CAAC,aAAa,GAAG;oBAAC,gJAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,MAAM,IAAI,IAAI;oBAAI,gJAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,MAAM,IAAI,IAAI;iBAAI,CAAC,GAAG,CACtF,CAAC,KAAO,IAAI,CAAC,OAAO,CAAC,IAAI,MAAM;YAEnC;YAEA,OAAO,IAAI,CAAC,aAAa;QAC3B;IAEJ;IAEA,KAAK,MAAM,EAAE;QACX,OAAO,UAAU,IAAI,EAAE,QAAQ,uJAAA,CAAA,OAAY,EAAE;YAC3C,MAAM,OAAO;gBAAE,KAAK;YAAO;YAE3B,iIAAiI;YACjI,gCAAgC;YAChC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;gBAC1B,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG;oBAAC,gJAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG;oBAAI,gJAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,MAAM,GAAG;iBAAG,CAAC,GAAG,CAAC,CAAC,KAC/E,IAAI,CAAC,OAAO,CAAC,IAAI,MAAM;YAE3B;YAEA,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO;QAC9B;IACF;IAEA,QAAQ,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE;QAC3B,MAAM,KAAK,IAAI,CAAC,WAAW,CAAC,IAAI,WAC9B,UAAU,GAAG,aAAa,IAC1B,WAAW,QAAQ,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI,CAAC,WAAW,OAAO;QAC1D,OAAO,WAAW,SAAS,KAAK,GAAG;IACrC;IAEA,gBAAgB,OAAO,CAAC,CAAC,EAAE;QACzB,4GAA4G;QAC5G,2DAA2D;QAC3D,OAAO,IAAI,oBAAoB,IAAI,CAAC,IAAI,EAAE,KAAK,WAAW,IAAI,IAAI,CAAC,WAAW,EAAE;IAClF;IAEA,YAAY,EAAE,EAAE,WAAW,CAAC,CAAC,EAAE;QAC7B,OAAO,IAAI,kBAAkB,IAAI,IAAI,CAAC,IAAI,EAAE;IAC9C;IAEA,aAAa,OAAO,CAAC,CAAC,EAAE;QACtB,OAAO,IAAI,iBAAiB,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,IAAI;IAC3D;IAEA,cAAc,OAAO,CAAC,CAAC,EAAE;QACvB,OAAO,YAAY,IAAI,CAAC,IAAI,EAAE;IAChC;IAEA,YAAY;QACV,OACE,IAAI,CAAC,MAAM,KAAK,QAChB,IAAI,CAAC,MAAM,CAAC,WAAW,OAAO,WAC9B,4BAA4B,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,UAAU,CAAC;IAE7D;IAEA,kBAAkB;QAChB,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,OAAO,IAAI,CAAC,YAAY;QAC1B,OAAO,IAAI,CAAC,CAAA,GAAA,oJAAA,CAAA,oBAAiB,AAAD,KAAK;YAC/B,OAAO;QACT,OAAO;YACL,OAAO,kBAAkB,IAAI,CAAC,MAAM;QACtC;IACF;IAEA,iBAAiB;QACf,OAAO,IAAI,CAAC,eAAe,GAAG,QAAQ;IACxC;IAEA,wBAAwB;QACtB,OAAO,IAAI,CAAC,eAAe,GAAG,WAAW;IAC3C;IAEA,iBAAiB;QACf,OAAO,IAAI,CAAC,eAAe,GAAG,OAAO;IACvC;IAEA,OAAO,KAAK,EAAE;QACZ,OACE,IAAI,CAAC,MAAM,KAAK,MAAM,MAAM,IAC5B,IAAI,CAAC,eAAe,KAAK,MAAM,eAAe,IAC9C,IAAI,CAAC,cAAc,KAAK,MAAM,cAAc;IAEhD;IAEA,WAAW;QACT,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;IAClF;AACF", "ignoreList": [0]}}, {"offset": {"line": 1105, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/luxon/src/zones/fixedOffsetZone.js"], "sourcesContent": ["import { formatOffset, signedOffset } from \"../impl/util.js\";\nimport Zone from \"../zone.js\";\n\nlet singleton = null;\n\n/**\n * A zone with a fixed offset (meaning no DST)\n * @implements {Zone}\n */\nexport default class FixedOffsetZone extends Zone {\n  /**\n   * Get a singleton instance of UTC\n   * @return {FixedOffsetZone}\n   */\n  static get utcInstance() {\n    if (singleton === null) {\n      singleton = new FixedOffsetZone(0);\n    }\n    return singleton;\n  }\n\n  /**\n   * Get an instance with a specified offset\n   * @param {number} offset - The offset in minutes\n   * @return {FixedOffsetZone}\n   */\n  static instance(offset) {\n    return offset === 0 ? FixedOffsetZone.utcInstance : new FixedOffsetZone(offset);\n  }\n\n  /**\n   * Get an instance of FixedOffsetZone from a UTC offset string, like \"UTC+6\"\n   * @param {string} s - The offset string to parse\n   * @example FixedOffsetZone.parseSpecifier(\"UTC+6\")\n   * @example FixedOffsetZone.parseSpecifier(\"UTC+06\")\n   * @example FixedOffsetZone.parseSpecifier(\"UTC-6:00\")\n   * @return {FixedOffsetZone}\n   */\n  static parseSpecifier(s) {\n    if (s) {\n      const r = s.match(/^utc(?:([+-]\\d{1,2})(?::(\\d{2}))?)?$/i);\n      if (r) {\n        return new FixedOffsetZone(signedOffset(r[1], r[2]));\n      }\n    }\n    return null;\n  }\n\n  constructor(offset) {\n    super();\n    /** @private **/\n    this.fixed = offset;\n  }\n\n  /**\n   * The type of zone. `fixed` for all instances of `FixedOffsetZone`.\n   * @override\n   * @type {string}\n   */\n  get type() {\n    return \"fixed\";\n  }\n\n  /**\n   * The name of this zone.\n   * All fixed zones' names always start with \"UTC\" (plus optional offset)\n   * @override\n   * @type {string}\n   */\n  get name() {\n    return this.fixed === 0 ? \"UTC\" : `UTC${formatOffset(this.fixed, \"narrow\")}`;\n  }\n\n  /**\n   * The IANA name of this zone, i.e. `Etc/UTC` or `Etc/GMT+/-nn`\n   *\n   * @override\n   * @type {string}\n   */\n  get ianaName() {\n    if (this.fixed === 0) {\n      return \"Etc/UTC\";\n    } else {\n      return `Etc/GMT${formatOffset(-this.fixed, \"narrow\")}`;\n    }\n  }\n\n  /**\n   * Returns the offset's common name at the specified timestamp.\n   *\n   * For fixed offset zones this equals to the zone name.\n   * @override\n   */\n  offsetName() {\n    return this.name;\n  }\n\n  /**\n   * Returns the offset's value as a string\n   * @override\n   * @param {number} ts - Epoch milliseconds for which to get the offset\n   * @param {string} format - What style of offset to return.\n   *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively\n   * @return {string}\n   */\n  formatOffset(ts, format) {\n    return formatOffset(this.fixed, format);\n  }\n\n  /**\n   * Returns whether the offset is known to be fixed for the whole year:\n   * Always returns true for all fixed offset zones.\n   * @override\n   * @type {boolean}\n   */\n  get isUniversal() {\n    return true;\n  }\n\n  /**\n   * Return the offset in minutes for this zone at the specified timestamp.\n   *\n   * For fixed offset zones, this is constant and does not depend on a timestamp.\n   * @override\n   * @return {number}\n   */\n  offset() {\n    return this.fixed;\n  }\n\n  /**\n   * Return whether this Zone is equal to another zone (i.e. also fixed and same offset)\n   * @override\n   * @param {Zone} otherZone - the zone to compare\n   * @return {boolean}\n   */\n  equals(otherZone) {\n    return otherZone.type === \"fixed\" && otherZone.fixed === this.fixed;\n  }\n\n  /**\n   * Return whether this Zone is valid:\n   * All fixed offset zones are valid.\n   * @override\n   * @type {boolean}\n   */\n  get isValid() {\n    return true;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,IAAI,YAAY;AAMD,MAAM,wBAAwB,4IAAA,CAAA,UAAI;IAC/C;;;GAGC,GACD,WAAW,cAAc;QACvB,IAAI,cAAc,MAAM;YACtB,YAAY,IAAI,gBAAgB;QAClC;QACA,OAAO;IACT;IAEA;;;;GAIC,GACD,OAAO,SAAS,MAAM,EAAE;QACtB,OAAO,WAAW,IAAI,gBAAgB,WAAW,GAAG,IAAI,gBAAgB;IAC1E;IAEA;;;;;;;GAOC,GACD,OAAO,eAAe,CAAC,EAAE;QACvB,IAAI,GAAG;YACL,MAAM,IAAI,EAAE,KAAK,CAAC;YAClB,IAAI,GAAG;gBACL,OAAO,IAAI,gBAAgB,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;YACpD;QACF;QACA,OAAO;IACT;IAEA,YAAY,MAAM,CAAE;QAClB,KAAK;QACL,cAAc,GACd,IAAI,CAAC,KAAK,GAAG;IACf;IAEA;;;;GAIC,GACD,IAAI,OAAO;QACT,OAAO;IACT;IAEA;;;;;GAKC,GACD,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,KAAK,KAAK,IAAI,QAAQ,CAAC,GAAG,EAAE,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,IAAI,CAAC,KAAK,EAAE,WAAW;IAC9E;IAEA;;;;;GAKC,GACD,IAAI,WAAW;QACb,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG;YACpB,OAAO;QACT,OAAO;YACL,OAAO,CAAC,OAAO,EAAE,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,WAAW;QACxD;IACF;IAEA;;;;;GAKC,GACD,aAAa;QACX,OAAO,IAAI,CAAC,IAAI;IAClB;IAEA;;;;;;;GAOC,GACD,aAAa,EAAE,EAAE,MAAM,EAAE;QACvB,OAAO,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,IAAI,CAAC,KAAK,EAAE;IAClC;IAEA;;;;;GAKC,GACD,IAAI,cAAc;QAChB,OAAO;IACT;IAEA;;;;;;GAMC,GACD,SAAS;QACP,OAAO,IAAI,CAAC,KAAK;IACnB;IAEA;;;;;GAKC,GACD,OAAO,SAAS,EAAE;QAChB,OAAO,UAAU,IAAI,KAAK,WAAW,UAAU,KAAK,KAAK,IAAI,CAAC,KAAK;IACrE;IAEA;;;;;GAKC,GACD,IAAI,UAAU;QACZ,OAAO;IACT;AACF", "ignoreList": [0]}}, {"offset": {"line": 1235, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/luxon/src/zones/invalidZone.js"], "sourcesContent": ["import Zone from \"../zone.js\";\n\n/**\n * A zone that failed to parse. You should never need to instantiate this.\n * @implements {Zone}\n */\nexport default class InvalidZone extends Zone {\n  constructor(zoneName) {\n    super();\n    /**  @private */\n    this.zoneName = zoneName;\n  }\n\n  /** @override **/\n  get type() {\n    return \"invalid\";\n  }\n\n  /** @override **/\n  get name() {\n    return this.zoneName;\n  }\n\n  /** @override **/\n  get isUniversal() {\n    return false;\n  }\n\n  /** @override **/\n  offsetName() {\n    return null;\n  }\n\n  /** @override **/\n  formatOffset() {\n    return \"\";\n  }\n\n  /** @override **/\n  offset() {\n    return NaN;\n  }\n\n  /** @override **/\n  equals() {\n    return false;\n  }\n\n  /** @override **/\n  get isValid() {\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAMe,MAAM,oBAAoB,4IAAA,CAAA,UAAI;IAC3C,YAAY,QAAQ,CAAE;QACpB,KAAK;QACL,cAAc,GACd,IAAI,CAAC,QAAQ,GAAG;IAClB;IAEA,eAAe,GACf,IAAI,OAAO;QACT,OAAO;IACT;IAEA,eAAe,GACf,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ;IACtB;IAEA,eAAe,GACf,IAAI,cAAc;QAChB,OAAO;IACT;IAEA,eAAe,GACf,aAAa;QACX,OAAO;IACT;IAEA,eAAe,GACf,eAAe;QACb,OAAO;IACT;IAEA,eAAe,GACf,SAAS;QACP,OAAO;IACT;IAEA,eAAe,GACf,SAAS;QACP,OAAO;IACT;IAEA,eAAe,GACf,IAAI,UAAU;QACZ,OAAO;IACT;AACF", "ignoreList": [0]}}, {"offset": {"line": 1276, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/luxon/src/impl/zoneUtil.js"], "sourcesContent": ["/**\n * @private\n */\n\nimport Zone from \"../zone.js\";\nimport IANAZone from \"../zones/IANAZone.js\";\nimport FixedOffsetZone from \"../zones/fixedOffsetZone.js\";\nimport InvalidZone from \"../zones/invalidZone.js\";\n\nimport { isUndefined, isString, isNumber } from \"./util.js\";\nimport SystemZone from \"../zones/systemZone.js\";\n\nexport function normalizeZone(input, defaultZone) {\n  let offset;\n  if (isUndefined(input) || input === null) {\n    return defaultZone;\n  } else if (input instanceof Zone) {\n    return input;\n  } else if (isString(input)) {\n    const lowered = input.toLowerCase();\n    if (lowered === \"default\") return defaultZone;\n    else if (lowered === \"local\" || lowered === \"system\") return SystemZone.instance;\n    else if (lowered === \"utc\" || lowered === \"gmt\") return FixedOffsetZone.utcInstance;\n    else return FixedOffsetZone.parseSpecifier(lowered) || IANAZone.create(input);\n  } else if (isNumber(input)) {\n    return FixedOffsetZone.instance(input);\n  } else if (typeof input === \"object\" && \"offset\" in input && typeof input.offset === \"function\") {\n    // This is dumb, but the instanceof check above doesn't seem to really work\n    // so we're duck checking it\n    return input;\n  } else {\n    return new InvalidZone(input);\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;AACA;AACA;AACA;AAEA;AACA;;;;;;;AAEO,SAAS,cAAc,KAAK,EAAE,WAAW;IAC9C,IAAI;IACJ,IAAI,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,UAAU,UAAU,MAAM;QACxC,OAAO;IACT,OAAO,IAAI,iBAAiB,4IAAA,CAAA,UAAI,EAAE;QAChC,OAAO;IACT,OAAO,IAAI,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC1B,MAAM,UAAU,MAAM,WAAW;QACjC,IAAI,YAAY,WAAW,OAAO;aAC7B,IAAI,YAAY,WAAW,YAAY,UAAU,OAAO,2JAAA,CAAA,UAAU,CAAC,QAAQ;aAC3E,IAAI,YAAY,SAAS,YAAY,OAAO,OAAO,gKAAA,CAAA,UAAe,CAAC,WAAW;aAC9E,OAAO,gKAAA,CAAA,UAAe,CAAC,cAAc,CAAC,YAAY,yJAAA,CAAA,UAAQ,CAAC,MAAM,CAAC;IACzE,OAAO,IAAI,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC1B,OAAO,gKAAA,CAAA,UAAe,CAAC,QAAQ,CAAC;IAClC,OAAO,IAAI,OAAO,UAAU,YAAY,YAAY,SAAS,OAAO,MAAM,MAAM,KAAK,YAAY;QAC/F,2EAA2E;QAC3E,4BAA4B;QAC5B,OAAO;IACT,OAAO;QACL,OAAO,IAAI,4JAAA,CAAA,UAAW,CAAC;IACzB;AACF", "ignoreList": [0]}}, {"offset": {"line": 1321, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/luxon/src/impl/digits.js"], "sourcesContent": ["const numberingSystems = {\n  arab: \"[\\u0660-\\u0669]\",\n  arabext: \"[\\u06F0-\\u06F9]\",\n  bali: \"[\\u1B50-\\u1B59]\",\n  beng: \"[\\u09E6-\\u09EF]\",\n  deva: \"[\\u0966-\\u096F]\",\n  fullwide: \"[\\uFF10-\\uFF19]\",\n  gujr: \"[\\u0AE6-\\u0AEF]\",\n  hanidec: \"[〇|一|二|三|四|五|六|七|八|九]\",\n  khmr: \"[\\u17E0-\\u17E9]\",\n  knda: \"[\\u0CE6-\\u0CEF]\",\n  laoo: \"[\\u0ED0-\\u0ED9]\",\n  limb: \"[\\u1946-\\u194F]\",\n  mlym: \"[\\u0D66-\\u0D6F]\",\n  mong: \"[\\u1810-\\u1819]\",\n  mymr: \"[\\u1040-\\u1049]\",\n  orya: \"[\\u0B66-\\u0B6F]\",\n  tamldec: \"[\\u0BE6-\\u0BEF]\",\n  telu: \"[\\u0C66-\\u0C6F]\",\n  thai: \"[\\u0E50-\\u0E59]\",\n  tibt: \"[\\u0F20-\\u0F29]\",\n  latn: \"\\\\d\",\n};\n\nconst numberingSystemsUTF16 = {\n  arab: [1632, 1641],\n  arabext: [1776, 1785],\n  bali: [6992, 7001],\n  beng: [2534, 2543],\n  deva: [2406, 2415],\n  fullwide: [65296, 65303],\n  gujr: [2790, 2799],\n  khmr: [6112, 6121],\n  knda: [3302, 3311],\n  laoo: [3792, 3801],\n  limb: [6470, 6479],\n  mlym: [3430, 3439],\n  mong: [6160, 6169],\n  mymr: [4160, 4169],\n  orya: [2918, 2927],\n  tamldec: [3046, 3055],\n  telu: [3174, 3183],\n  thai: [3664, 3673],\n  tibt: [3872, 3881],\n};\n\nconst hanidecChars = numberingSystems.hanidec.replace(/[\\[|\\]]/g, \"\").split(\"\");\n\nexport function parseDigits(str) {\n  let value = parseInt(str, 10);\n  if (isNaN(value)) {\n    value = \"\";\n    for (let i = 0; i < str.length; i++) {\n      const code = str.charCodeAt(i);\n\n      if (str[i].search(numberingSystems.hanidec) !== -1) {\n        value += hanidecChars.indexOf(str[i]);\n      } else {\n        for (const key in numberingSystemsUTF16) {\n          const [min, max] = numberingSystemsUTF16[key];\n          if (code >= min && code <= max) {\n            value += code - min;\n          }\n        }\n      }\n    }\n    return parseInt(value, 10);\n  } else {\n    return value;\n  }\n}\n\n// cache of {numberingSystem: {append: regex}}\nconst digitRegexCache = new Map();\nexport function resetDigitRegexCache() {\n  digitRegexCache.clear();\n}\n\nexport function digitRegex({ numberingSystem }, append = \"\") {\n  const ns = numberingSystem || \"latn\";\n\n  let appendCache = digitRegexCache.get(ns);\n  if (appendCache === undefined) {\n    appendCache = new Map();\n    digitRegexCache.set(ns, appendCache);\n  }\n  let regex = appendCache.get(append);\n  if (regex === undefined) {\n    regex = new RegExp(`${numberingSystems[ns]}${append}`);\n    appendCache.set(append, regex);\n  }\n\n  return regex;\n}\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,mBAAmB;IACvB,MAAM;IACN,SAAS;IACT,MAAM;IACN,MAAM;IACN,MAAM;IACN,UAAU;IACV,MAAM;IACN,SAAS;IACT,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,SAAS;IACT,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;AACR;AAEA,MAAM,wBAAwB;IAC5B,MAAM;QAAC;QAAM;KAAK;IAClB,SAAS;QAAC;QAAM;KAAK;IACrB,MAAM;QAAC;QAAM;KAAK;IAClB,MAAM;QAAC;QAAM;KAAK;IAClB,MAAM;QAAC;QAAM;KAAK;IAClB,UAAU;QAAC;QAAO;KAAM;IACxB,MAAM;QAAC;QAAM;KAAK;IAClB,MAAM;QAAC;QAAM;KAAK;IAClB,MAAM;QAAC;QAAM;KAAK;IAClB,MAAM;QAAC;QAAM;KAAK;IAClB,MAAM;QAAC;QAAM;KAAK;IAClB,MAAM;QAAC;QAAM;KAAK;IAClB,MAAM;QAAC;QAAM;KAAK;IAClB,MAAM;QAAC;QAAM;KAAK;IAClB,MAAM;QAAC;QAAM;KAAK;IAClB,SAAS;QAAC;QAAM;KAAK;IACrB,MAAM;QAAC;QAAM;KAAK;IAClB,MAAM;QAAC;QAAM;KAAK;IAClB,MAAM;QAAC;QAAM;KAAK;AACpB;AAEA,MAAM,eAAe,iBAAiB,OAAO,CAAC,OAAO,CAAC,YAAY,IAAI,KAAK,CAAC;AAErE,SAAS,YAAY,GAAG;IAC7B,IAAI,QAAQ,SAAS,KAAK;IAC1B,IAAI,MAAM,QAAQ;QAChB,QAAQ;QACR,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;YACnC,MAAM,OAAO,IAAI,UAAU,CAAC;YAE5B,IAAI,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,iBAAiB,OAAO,MAAM,CAAC,GAAG;gBAClD,SAAS,aAAa,OAAO,CAAC,GAAG,CAAC,EAAE;YACtC,OAAO;gBACL,IAAK,MAAM,OAAO,sBAAuB;oBACvC,MAAM,CAAC,KAAK,IAAI,GAAG,qBAAqB,CAAC,IAAI;oBAC7C,IAAI,QAAQ,OAAO,QAAQ,KAAK;wBAC9B,SAAS,OAAO;oBAClB;gBACF;YACF;QACF;QACA,OAAO,SAAS,OAAO;IACzB,OAAO;QACL,OAAO;IACT;AACF;AAEA,8CAA8C;AAC9C,MAAM,kBAAkB,IAAI;AACrB,SAAS;IACd,gBAAgB,KAAK;AACvB;AAEO,SAAS,WAAW,EAAE,eAAe,EAAE,EAAE,SAAS,EAAE;IACzD,MAAM,KAAK,mBAAmB;IAE9B,IAAI,cAAc,gBAAgB,GAAG,CAAC;IACtC,IAAI,gBAAgB,WAAW;QAC7B,cAAc,IAAI;QAClB,gBAAgB,GAAG,CAAC,IAAI;IAC1B;IACA,IAAI,QAAQ,YAAY,GAAG,CAAC;IAC5B,IAAI,UAAU,WAAW;QACvB,QAAQ,IAAI,OAAO,GAAG,gBAAgB,CAAC,GAAG,GAAG,QAAQ;QACrD,YAAY,GAAG,CAAC,QAAQ;IAC1B;IAEA,OAAO;AACT", "ignoreList": [0]}}, {"offset": {"line": 1475, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/luxon/src/settings.js"], "sourcesContent": ["import SystemZone from \"./zones/systemZone.js\";\nimport IANAZone from \"./zones/IANAZone.js\";\nimport Locale from \"./impl/locale.js\";\nimport DateTime from \"./datetime.js\";\n\nimport { normalizeZone } from \"./impl/zoneUtil.js\";\nimport { validateWeekSettings } from \"./impl/util.js\";\nimport { resetDigitRegexCache } from \"./impl/digits.js\";\n\nlet now = () => Date.now(),\n  defaultZone = \"system\",\n  defaultLocale = null,\n  defaultNumberingSystem = null,\n  defaultOutputCalendar = null,\n  twoDigitCutoffYear = 60,\n  throwOnInvalid,\n  defaultWeekSettings = null;\n\n/**\n * Settings contains static getters and setters that control <PERSON><PERSON>'s overall behavior. Luxon is a simple library with few options, but the ones it does have live here.\n */\nexport default class Settings {\n  /**\n   * Get the callback for returning the current timestamp.\n   * @type {function}\n   */\n  static get now() {\n    return now;\n  }\n\n  /**\n   * Set the callback for returning the current timestamp.\n   * The function should return a number, which will be interpreted as an Epoch millisecond count\n   * @type {function}\n   * @example Settings.now = () => Date.now() + 3000 // pretend it is 3 seconds in the future\n   * @example Settings.now = () => 0 // always pretend it's Jan 1, 1970 at midnight in UTC time\n   */\n  static set now(n) {\n    now = n;\n  }\n\n  /**\n   * Set the default time zone to create DateTimes in. Does not affect existing instances.\n   * Use the value \"system\" to reset this value to the system's time zone.\n   * @type {string}\n   */\n  static set defaultZone(zone) {\n    defaultZone = zone;\n  }\n\n  /**\n   * Get the default time zone object currently used to create DateTimes. Does not affect existing instances.\n   * The default value is the system's time zone (the one set on the machine that runs this code).\n   * @type {Zone}\n   */\n  static get defaultZone() {\n    return normalizeZone(defaultZone, SystemZone.instance);\n  }\n\n  /**\n   * Get the default locale to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static get defaultLocale() {\n    return defaultLocale;\n  }\n\n  /**\n   * Set the default locale to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static set defaultLocale(locale) {\n    defaultLocale = locale;\n  }\n\n  /**\n   * Get the default numbering system to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static get defaultNumberingSystem() {\n    return defaultNumberingSystem;\n  }\n\n  /**\n   * Set the default numbering system to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static set defaultNumberingSystem(numberingSystem) {\n    defaultNumberingSystem = numberingSystem;\n  }\n\n  /**\n   * Get the default output calendar to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static get defaultOutputCalendar() {\n    return defaultOutputCalendar;\n  }\n\n  /**\n   * Set the default output calendar to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static set defaultOutputCalendar(outputCalendar) {\n    defaultOutputCalendar = outputCalendar;\n  }\n\n  /**\n   * @typedef {Object} WeekSettings\n   * @property {number} firstDay\n   * @property {number} minimalDays\n   * @property {number[]} weekend\n   */\n\n  /**\n   * @return {WeekSettings|null}\n   */\n  static get defaultWeekSettings() {\n    return defaultWeekSettings;\n  }\n\n  /**\n   * Allows overriding the default locale week settings, i.e. the start of the week, the weekend and\n   * how many days are required in the first week of a year.\n   * Does not affect existing instances.\n   *\n   * @param {WeekSettings|null} weekSettings\n   */\n  static set defaultWeekSettings(weekSettings) {\n    defaultWeekSettings = validateWeekSettings(weekSettings);\n  }\n\n  /**\n   * Get the cutoff year for whether a 2-digit year string is interpreted in the current or previous century. Numbers higher than the cutoff will be considered to mean 19xx and numbers lower or equal to the cutoff will be considered 20xx.\n   * @type {number}\n   */\n  static get twoDigitCutoffYear() {\n    return twoDigitCutoffYear;\n  }\n\n  /**\n   * Set the cutoff year for whether a 2-digit year string is interpreted in the current or previous century. Numbers higher than the cutoff will be considered to mean 19xx and numbers lower or equal to the cutoff will be considered 20xx.\n   * @type {number}\n   * @example Settings.twoDigitCutoffYear = 0 // all 'yy' are interpreted as 20th century\n   * @example Settings.twoDigitCutoffYear = 99 // all 'yy' are interpreted as 21st century\n   * @example Settings.twoDigitCutoffYear = 50 // '49' -> 2049; '50' -> 1950\n   * @example Settings.twoDigitCutoffYear = 1950 // interpreted as 50\n   * @example Settings.twoDigitCutoffYear = 2050 // ALSO interpreted as 50\n   */\n  static set twoDigitCutoffYear(cutoffYear) {\n    twoDigitCutoffYear = cutoffYear % 100;\n  }\n\n  /**\n   * Get whether Luxon will throw when it encounters invalid DateTimes, Durations, or Intervals\n   * @type {boolean}\n   */\n  static get throwOnInvalid() {\n    return throwOnInvalid;\n  }\n\n  /**\n   * Set whether Luxon will throw when it encounters invalid DateTimes, Durations, or Intervals\n   * @type {boolean}\n   */\n  static set throwOnInvalid(t) {\n    throwOnInvalid = t;\n  }\n\n  /**\n   * Reset Luxon's global caches. Should only be necessary in testing scenarios.\n   * @return {void}\n   */\n  static resetCaches() {\n    Locale.resetCache();\n    IANAZone.resetCache();\n    DateTime.resetCache();\n    resetDigitRegexCache();\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;;;;;;;;AAEA,IAAI,MAAM,IAAM,KAAK,GAAG,IACtB,cAAc,UACd,gBAAgB,MAChB,yBAAyB,MACzB,wBAAwB,MACxB,qBAAqB,IACrB,gBACA,sBAAsB;AAKT,MAAM;IACnB;;;GAGC,GACD,WAAW,MAAM;QACf,OAAO;IACT;IAEA;;;;;;GAMC,GACD,WAAW,IAAI,CAAC,EAAE;QAChB,MAAM;IACR;IAEA;;;;GAIC,GACD,WAAW,YAAY,IAAI,EAAE;QAC3B,cAAc;IAChB;IAEA;;;;GAIC,GACD,WAAW,cAAc;QACvB,OAAO,CAAA,GAAA,wJAAA,CAAA,gBAAa,AAAD,EAAE,aAAa,2JAAA,CAAA,UAAU,CAAC,QAAQ;IACvD;IAEA;;;GAGC,GACD,WAAW,gBAAgB;QACzB,OAAO;IACT;IAEA;;;GAGC,GACD,WAAW,cAAc,MAAM,EAAE;QAC/B,gBAAgB;IAClB;IAEA;;;GAGC,GACD,WAAW,yBAAyB;QAClC,OAAO;IACT;IAEA;;;GAGC,GACD,WAAW,uBAAuB,eAAe,EAAE;QACjD,yBAAyB;IAC3B;IAEA;;;GAGC,GACD,WAAW,wBAAwB;QACjC,OAAO;IACT;IAEA;;;GAGC,GACD,WAAW,sBAAsB,cAAc,EAAE;QAC/C,wBAAwB;IAC1B;IAEA;;;;;GAKC,GAED;;GAEC,GACD,WAAW,sBAAsB;QAC/B,OAAO;IACT;IAEA;;;;;;GAMC,GACD,WAAW,oBAAoB,YAAY,EAAE;QAC3C,sBAAsB,CAAA,GAAA,oJAAA,CAAA,uBAAoB,AAAD,EAAE;IAC7C;IAEA;;;GAGC,GACD,WAAW,qBAAqB;QAC9B,OAAO;IACT;IAEA;;;;;;;;GAQC,GACD,WAAW,mBAAmB,UAAU,EAAE;QACxC,qBAAqB,aAAa;IACpC;IAEA;;;GAGC,GACD,WAAW,iBAAiB;QAC1B,OAAO;IACT;IAEA;;;GAGC,GACD,WAAW,eAAe,CAAC,EAAE;QAC3B,iBAAiB;IACnB;IAEA;;;GAGC,GACD,OAAO,cAAc;QACnB,sJAAA,CAAA,UAAM,CAAC,UAAU;QACjB,yJAAA,CAAA,UAAQ,CAAC,UAAU;QACnB,gJAAA,CAAA,UAAQ,CAAC,UAAU;QACnB,CAAA,GAAA,sJAAA,CAAA,uBAAoB,AAAD;IACrB;AACF", "ignoreList": [0]}}, {"offset": {"line": 1623, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/luxon/src/impl/invalid.js"], "sourcesContent": ["export default class Invalid {\n  constructor(reason, explanation) {\n    this.reason = reason;\n    this.explanation = explanation;\n  }\n\n  toMessage() {\n    if (this.explanation) {\n      return `${this.reason}: ${this.explanation}`;\n    } else {\n      return this.reason;\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAe,MAAM;IACnB,YAAY,MAAM,EAAE,WAAW,CAAE;QAC/B,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,WAAW,GAAG;IACrB;IAEA,YAAY;QACV,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,WAAW,EAAE;QAC9C,OAAO;YACL,OAAO,IAAI,CAAC,MAAM;QACpB;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 1645, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/luxon/src/impl/conversions.js"], "sourcesContent": ["import {\n  integerBetween,\n  isLeapYear,\n  timeObject,\n  daysInYear,\n  daysInMonth,\n  weeksInWeekYear,\n  isInteger,\n  isUndefined,\n} from \"./util.js\";\nimport Invalid from \"./invalid.js\";\nimport { ConflictingSpecificationError } from \"../errors.js\";\n\nconst nonLeapLadder = [0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334],\n  leapLadder = [0, 31, 60, 91, 121, 152, 182, 213, 244, 274, 305, 335];\n\nfunction unitOutOfRange(unit, value) {\n  return new Invalid(\n    \"unit out of range\",\n    `you specified ${value} (of type ${typeof value}) as a ${unit}, which is invalid`\n  );\n}\n\nexport function dayOfWeek(year, month, day) {\n  const d = new Date(Date.UTC(year, month - 1, day));\n\n  if (year < 100 && year >= 0) {\n    d.setUTCFullYear(d.getUTCFullYear() - 1900);\n  }\n\n  const js = d.getUTCDay();\n\n  return js === 0 ? 7 : js;\n}\n\nfunction computeOrdinal(year, month, day) {\n  return day + (isLeapYear(year) ? leapLadder : nonLeapLadder)[month - 1];\n}\n\nfunction uncomputeOrdinal(year, ordinal) {\n  const table = isLeapYear(year) ? leapLadder : nonLeapLadder,\n    month0 = table.findIndex((i) => i < ordinal),\n    day = ordinal - table[month0];\n  return { month: month0 + 1, day };\n}\n\nexport function isoWeekdayToLocal(isoWeekday, startOfWeek) {\n  return ((isoWeekday - startOfWeek + 7) % 7) + 1;\n}\n\n/**\n * @private\n */\n\nexport function gregorianToWeek(gregObj, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const { year, month, day } = gregObj,\n    ordinal = computeOrdinal(year, month, day),\n    weekday = isoWeekdayToLocal(dayOfWeek(year, month, day), startOfWeek);\n\n  let weekNumber = Math.floor((ordinal - weekday + 14 - minDaysInFirstWeek) / 7),\n    weekYear;\n\n  if (weekNumber < 1) {\n    weekYear = year - 1;\n    weekNumber = weeksInWeekYear(weekYear, minDaysInFirstWeek, startOfWeek);\n  } else if (weekNumber > weeksInWeekYear(year, minDaysInFirstWeek, startOfWeek)) {\n    weekYear = year + 1;\n    weekNumber = 1;\n  } else {\n    weekYear = year;\n  }\n\n  return { weekYear, weekNumber, weekday, ...timeObject(gregObj) };\n}\n\nexport function weekToGregorian(weekData, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const { weekYear, weekNumber, weekday } = weekData,\n    weekdayOfJan4 = isoWeekdayToLocal(dayOfWeek(weekYear, 1, minDaysInFirstWeek), startOfWeek),\n    yearInDays = daysInYear(weekYear);\n\n  let ordinal = weekNumber * 7 + weekday - weekdayOfJan4 - 7 + minDaysInFirstWeek,\n    year;\n\n  if (ordinal < 1) {\n    year = weekYear - 1;\n    ordinal += daysInYear(year);\n  } else if (ordinal > yearInDays) {\n    year = weekYear + 1;\n    ordinal -= daysInYear(weekYear);\n  } else {\n    year = weekYear;\n  }\n\n  const { month, day } = uncomputeOrdinal(year, ordinal);\n  return { year, month, day, ...timeObject(weekData) };\n}\n\nexport function gregorianToOrdinal(gregData) {\n  const { year, month, day } = gregData;\n  const ordinal = computeOrdinal(year, month, day);\n  return { year, ordinal, ...timeObject(gregData) };\n}\n\nexport function ordinalToGregorian(ordinalData) {\n  const { year, ordinal } = ordinalData;\n  const { month, day } = uncomputeOrdinal(year, ordinal);\n  return { year, month, day, ...timeObject(ordinalData) };\n}\n\n/**\n * Check if local week units like localWeekday are used in obj.\n * If so, validates that they are not mixed with ISO week units and then copies them to the normal week unit properties.\n * Modifies obj in-place!\n * @param obj the object values\n */\nexport function usesLocalWeekValues(obj, loc) {\n  const hasLocaleWeekData =\n    !isUndefined(obj.localWeekday) ||\n    !isUndefined(obj.localWeekNumber) ||\n    !isUndefined(obj.localWeekYear);\n  if (hasLocaleWeekData) {\n    const hasIsoWeekData =\n      !isUndefined(obj.weekday) || !isUndefined(obj.weekNumber) || !isUndefined(obj.weekYear);\n\n    if (hasIsoWeekData) {\n      throw new ConflictingSpecificationError(\n        \"Cannot mix locale-based week fields with ISO-based week fields\"\n      );\n    }\n    if (!isUndefined(obj.localWeekday)) obj.weekday = obj.localWeekday;\n    if (!isUndefined(obj.localWeekNumber)) obj.weekNumber = obj.localWeekNumber;\n    if (!isUndefined(obj.localWeekYear)) obj.weekYear = obj.localWeekYear;\n    delete obj.localWeekday;\n    delete obj.localWeekNumber;\n    delete obj.localWeekYear;\n    return {\n      minDaysInFirstWeek: loc.getMinDaysInFirstWeek(),\n      startOfWeek: loc.getStartOfWeek(),\n    };\n  } else {\n    return { minDaysInFirstWeek: 4, startOfWeek: 1 };\n  }\n}\n\nexport function hasInvalidWeekData(obj, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const validYear = isInteger(obj.weekYear),\n    validWeek = integerBetween(\n      obj.weekNumber,\n      1,\n      weeksInWeekYear(obj.weekYear, minDaysInFirstWeek, startOfWeek)\n    ),\n    validWeekday = integerBetween(obj.weekday, 1, 7);\n\n  if (!validYear) {\n    return unitOutOfRange(\"weekYear\", obj.weekYear);\n  } else if (!validWeek) {\n    return unitOutOfRange(\"week\", obj.weekNumber);\n  } else if (!validWeekday) {\n    return unitOutOfRange(\"weekday\", obj.weekday);\n  } else return false;\n}\n\nexport function hasInvalidOrdinalData(obj) {\n  const validYear = isInteger(obj.year),\n    validOrdinal = integerBetween(obj.ordinal, 1, daysInYear(obj.year));\n\n  if (!validYear) {\n    return unitOutOfRange(\"year\", obj.year);\n  } else if (!validOrdinal) {\n    return unitOutOfRange(\"ordinal\", obj.ordinal);\n  } else return false;\n}\n\nexport function hasInvalidGregorianData(obj) {\n  const validYear = isInteger(obj.year),\n    validMonth = integerBetween(obj.month, 1, 12),\n    validDay = integerBetween(obj.day, 1, daysInMonth(obj.year, obj.month));\n\n  if (!validYear) {\n    return unitOutOfRange(\"year\", obj.year);\n  } else if (!validMonth) {\n    return unitOutOfRange(\"month\", obj.month);\n  } else if (!validDay) {\n    return unitOutOfRange(\"day\", obj.day);\n  } else return false;\n}\n\nexport function hasInvalidTimeData(obj) {\n  const { hour, minute, second, millisecond } = obj;\n  const validHour =\n      integerBetween(hour, 0, 23) ||\n      (hour === 24 && minute === 0 && second === 0 && millisecond === 0),\n    validMinute = integerBetween(minute, 0, 59),\n    validSecond = integerBetween(second, 0, 59),\n    validMillisecond = integerBetween(millisecond, 0, 999);\n\n  if (!validHour) {\n    return unitOutOfRange(\"hour\", hour);\n  } else if (!validMinute) {\n    return unitOutOfRange(\"minute\", minute);\n  } else if (!validSecond) {\n    return unitOutOfRange(\"second\", second);\n  } else if (!validMillisecond) {\n    return unitOutOfRange(\"millisecond\", millisecond);\n  } else return false;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AAUA;AACA;;;;AAEA,MAAM,gBAAgB;IAAC;IAAG;IAAI;IAAI;IAAI;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;CAAI,EAC3E,aAAa;IAAC;IAAG;IAAI;IAAI;IAAI;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;CAAI;AAEtE,SAAS,eAAe,IAAI,EAAE,KAAK;IACjC,OAAO,IAAI,uJAAA,CAAA,UAAO,CAChB,qBACA,CAAC,cAAc,EAAE,MAAM,UAAU,EAAE,OAAO,MAAM,OAAO,EAAE,KAAK,kBAAkB,CAAC;AAErF;AAEO,SAAS,UAAU,IAAI,EAAE,KAAK,EAAE,GAAG;IACxC,MAAM,IAAI,IAAI,KAAK,KAAK,GAAG,CAAC,MAAM,QAAQ,GAAG;IAE7C,IAAI,OAAO,OAAO,QAAQ,GAAG;QAC3B,EAAE,cAAc,CAAC,EAAE,cAAc,KAAK;IACxC;IAEA,MAAM,KAAK,EAAE,SAAS;IAEtB,OAAO,OAAO,IAAI,IAAI;AACxB;AAEA,SAAS,eAAe,IAAI,EAAE,KAAK,EAAE,GAAG;IACtC,OAAO,MAAM,CAAC,CAAA,GAAA,oJAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,aAAa,aAAa,CAAC,CAAC,QAAQ,EAAE;AACzE;AAEA,SAAS,iBAAiB,IAAI,EAAE,OAAO;IACrC,MAAM,QAAQ,CAAA,GAAA,oJAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,aAAa,eAC5C,SAAS,MAAM,SAAS,CAAC,CAAC,IAAM,IAAI,UACpC,MAAM,UAAU,KAAK,CAAC,OAAO;IAC/B,OAAO;QAAE,OAAO,SAAS;QAAG;IAAI;AAClC;AAEO,SAAS,kBAAkB,UAAU,EAAE,WAAW;IACvD,OAAO,AAAC,CAAC,aAAa,cAAc,CAAC,IAAI,IAAK;AAChD;AAMO,SAAS,gBAAgB,OAAO,EAAE,qBAAqB,CAAC,EAAE,cAAc,CAAC;IAC9E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,SAC3B,UAAU,eAAe,MAAM,OAAO,MACtC,UAAU,kBAAkB,UAAU,MAAM,OAAO,MAAM;IAE3D,IAAI,aAAa,KAAK,KAAK,CAAC,CAAC,UAAU,UAAU,KAAK,kBAAkB,IAAI,IAC1E;IAEF,IAAI,aAAa,GAAG;QAClB,WAAW,OAAO;QAClB,aAAa,CAAA,GAAA,oJAAA,CAAA,kBAAe,AAAD,EAAE,UAAU,oBAAoB;IAC7D,OAAO,IAAI,aAAa,CAAA,GAAA,oJAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,oBAAoB,cAAc;QAC9E,WAAW,OAAO;QAClB,aAAa;IACf,OAAO;QACL,WAAW;IACb;IAEA,OAAO;QAAE;QAAU;QAAY;QAAS,GAAG,CAAA,GAAA,oJAAA,CAAA,aAAU,AAAD,EAAE,QAAQ;IAAC;AACjE;AAEO,SAAS,gBAAgB,QAAQ,EAAE,qBAAqB,CAAC,EAAE,cAAc,CAAC;IAC/E,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,UACxC,gBAAgB,kBAAkB,UAAU,UAAU,GAAG,qBAAqB,cAC9E,aAAa,CAAA,GAAA,oJAAA,CAAA,aAAU,AAAD,EAAE;IAE1B,IAAI,UAAU,aAAa,IAAI,UAAU,gBAAgB,IAAI,oBAC3D;IAEF,IAAI,UAAU,GAAG;QACf,OAAO,WAAW;QAClB,WAAW,CAAA,GAAA,oJAAA,CAAA,aAAU,AAAD,EAAE;IACxB,OAAO,IAAI,UAAU,YAAY;QAC/B,OAAO,WAAW;QAClB,WAAW,CAAA,GAAA,oJAAA,CAAA,aAAU,AAAD,EAAE;IACxB,OAAO;QACL,OAAO;IACT;IAEA,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,iBAAiB,MAAM;IAC9C,OAAO;QAAE;QAAM;QAAO;QAAK,GAAG,CAAA,GAAA,oJAAA,CAAA,aAAU,AAAD,EAAE,SAAS;IAAC;AACrD;AAEO,SAAS,mBAAmB,QAAQ;IACzC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG;IAC7B,MAAM,UAAU,eAAe,MAAM,OAAO;IAC5C,OAAO;QAAE;QAAM;QAAS,GAAG,CAAA,GAAA,oJAAA,CAAA,aAAU,AAAD,EAAE,SAAS;IAAC;AAClD;AAEO,SAAS,mBAAmB,WAAW;IAC5C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG;IAC1B,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,iBAAiB,MAAM;IAC9C,OAAO;QAAE;QAAM;QAAO;QAAK,GAAG,CAAA,GAAA,oJAAA,CAAA,aAAU,AAAD,EAAE,YAAY;IAAC;AACxD;AAQO,SAAS,oBAAoB,GAAG,EAAE,GAAG;IAC1C,MAAM,oBACJ,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,IAAI,YAAY,KAC7B,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,IAAI,eAAe,KAChC,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,IAAI,aAAa;IAChC,IAAI,mBAAmB;QACrB,MAAM,iBACJ,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,IAAI,OAAO,KAAK,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,IAAI,UAAU,KAAK,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,IAAI,QAAQ;QAExF,IAAI,gBAAgB;YAClB,MAAM,IAAI,8IAAA,CAAA,gCAA6B,CACrC;QAEJ;QACA,IAAI,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,IAAI,YAAY,GAAG,IAAI,OAAO,GAAG,IAAI,YAAY;QAClE,IAAI,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,IAAI,eAAe,GAAG,IAAI,UAAU,GAAG,IAAI,eAAe;QAC3E,IAAI,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,IAAI,aAAa,GAAG,IAAI,QAAQ,GAAG,IAAI,aAAa;QACrE,OAAO,IAAI,YAAY;QACvB,OAAO,IAAI,eAAe;QAC1B,OAAO,IAAI,aAAa;QACxB,OAAO;YACL,oBAAoB,IAAI,qBAAqB;YAC7C,aAAa,IAAI,cAAc;QACjC;IACF,OAAO;QACL,OAAO;YAAE,oBAAoB;YAAG,aAAa;QAAE;IACjD;AACF;AAEO,SAAS,mBAAmB,GAAG,EAAE,qBAAqB,CAAC,EAAE,cAAc,CAAC;IAC7E,MAAM,YAAY,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,IAAI,QAAQ,GACtC,YAAY,CAAA,GAAA,oJAAA,CAAA,iBAAc,AAAD,EACvB,IAAI,UAAU,EACd,GACA,CAAA,GAAA,oJAAA,CAAA,kBAAe,AAAD,EAAE,IAAI,QAAQ,EAAE,oBAAoB,eAEpD,eAAe,CAAA,GAAA,oJAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,OAAO,EAAE,GAAG;IAEhD,IAAI,CAAC,WAAW;QACd,OAAO,eAAe,YAAY,IAAI,QAAQ;IAChD,OAAO,IAAI,CAAC,WAAW;QACrB,OAAO,eAAe,QAAQ,IAAI,UAAU;IAC9C,OAAO,IAAI,CAAC,cAAc;QACxB,OAAO,eAAe,WAAW,IAAI,OAAO;IAC9C,OAAO,OAAO;AAChB;AAEO,SAAS,sBAAsB,GAAG;IACvC,MAAM,YAAY,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,IAAI,IAAI,GAClC,eAAe,CAAA,GAAA,oJAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,OAAO,EAAE,GAAG,CAAA,GAAA,oJAAA,CAAA,aAAU,AAAD,EAAE,IAAI,IAAI;IAEnE,IAAI,CAAC,WAAW;QACd,OAAO,eAAe,QAAQ,IAAI,IAAI;IACxC,OAAO,IAAI,CAAC,cAAc;QACxB,OAAO,eAAe,WAAW,IAAI,OAAO;IAC9C,OAAO,OAAO;AAChB;AAEO,SAAS,wBAAwB,GAAG;IACzC,MAAM,YAAY,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,IAAI,IAAI,GAClC,aAAa,CAAA,GAAA,oJAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,KAAK,EAAE,GAAG,KAC1C,WAAW,CAAA,GAAA,oJAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,GAAG,EAAE,GAAG,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,IAAI,IAAI,EAAE,IAAI,KAAK;IAEvE,IAAI,CAAC,WAAW;QACd,OAAO,eAAe,QAAQ,IAAI,IAAI;IACxC,OAAO,IAAI,CAAC,YAAY;QACtB,OAAO,eAAe,SAAS,IAAI,KAAK;IAC1C,OAAO,IAAI,CAAC,UAAU;QACpB,OAAO,eAAe,OAAO,IAAI,GAAG;IACtC,OAAO,OAAO;AAChB;AAEO,SAAS,mBAAmB,GAAG;IACpC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG;IAC9C,MAAM,YACF,CAAA,GAAA,oJAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,GAAG,OACvB,SAAS,MAAM,WAAW,KAAK,WAAW,KAAK,gBAAgB,GAClE,cAAc,CAAA,GAAA,oJAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,GAAG,KACxC,cAAc,CAAA,GAAA,oJAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,GAAG,KACxC,mBAAmB,CAAA,GAAA,oJAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,GAAG;IAEpD,IAAI,CAAC,WAAW;QACd,OAAO,eAAe,QAAQ;IAChC,OAAO,IAAI,CAAC,aAAa;QACvB,OAAO,eAAe,UAAU;IAClC,OAAO,IAAI,CAAC,aAAa;QACvB,OAAO,eAAe,UAAU;IAClC,OAAO,IAAI,CAAC,kBAAkB;QAC5B,OAAO,eAAe,eAAe;IACvC,OAAO,OAAO;AAChB", "ignoreList": [0]}}, {"offset": {"line": 1844, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/luxon/src/impl/util.js"], "sourcesContent": ["/*\n  This is just a junk drawer, containing anything used across multiple classes.\n  Because <PERSON>xon is small(ish), this should stay small and we won't worry about splitting\n  it up into, say, parsingUtil.js and basicUtil.js and so on. But they are divided up by feature area.\n*/\n\nimport { InvalidArgumentError } from \"../errors.js\";\nimport Settings from \"../settings.js\";\nimport { dayOfWeek, isoWeekdayToLocal } from \"./conversions.js\";\n\n/**\n * @private\n */\n\n// TYPES\n\nexport function isUndefined(o) {\n  return typeof o === \"undefined\";\n}\n\nexport function isNumber(o) {\n  return typeof o === \"number\";\n}\n\nexport function isInteger(o) {\n  return typeof o === \"number\" && o % 1 === 0;\n}\n\nexport function isString(o) {\n  return typeof o === \"string\";\n}\n\nexport function isDate(o) {\n  return Object.prototype.toString.call(o) === \"[object Date]\";\n}\n\n// CAPABILITIES\n\nexport function hasRelative() {\n  try {\n    return typeof Intl !== \"undefined\" && !!Intl.RelativeTimeFormat;\n  } catch (e) {\n    return false;\n  }\n}\n\nexport function hasLocaleWeekInfo() {\n  try {\n    return (\n      typeof Intl !== \"undefined\" &&\n      !!Intl.Locale &&\n      (\"weekInfo\" in Intl.Locale.prototype || \"getWeekInfo\" in Intl.Locale.prototype)\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\n// OBJECTS AND ARRAYS\n\nexport function maybeArray(thing) {\n  return Array.isArray(thing) ? thing : [thing];\n}\n\nexport function bestBy(arr, by, compare) {\n  if (arr.length === 0) {\n    return undefined;\n  }\n  return arr.reduce((best, next) => {\n    const pair = [by(next), next];\n    if (!best) {\n      return pair;\n    } else if (compare(best[0], pair[0]) === best[0]) {\n      return best;\n    } else {\n      return pair;\n    }\n  }, null)[1];\n}\n\nexport function pick(obj, keys) {\n  return keys.reduce((a, k) => {\n    a[k] = obj[k];\n    return a;\n  }, {});\n}\n\nexport function hasOwnProperty(obj, prop) {\n  return Object.prototype.hasOwnProperty.call(obj, prop);\n}\n\nexport function validateWeekSettings(settings) {\n  if (settings == null) {\n    return null;\n  } else if (typeof settings !== \"object\") {\n    throw new InvalidArgumentError(\"Week settings must be an object\");\n  } else {\n    if (\n      !integerBetween(settings.firstDay, 1, 7) ||\n      !integerBetween(settings.minimalDays, 1, 7) ||\n      !Array.isArray(settings.weekend) ||\n      settings.weekend.some((v) => !integerBetween(v, 1, 7))\n    ) {\n      throw new InvalidArgumentError(\"Invalid week settings\");\n    }\n    return {\n      firstDay: settings.firstDay,\n      minimalDays: settings.minimalDays,\n      weekend: Array.from(settings.weekend),\n    };\n  }\n}\n\n// NUMBERS AND STRINGS\n\nexport function integerBetween(thing, bottom, top) {\n  return isInteger(thing) && thing >= bottom && thing <= top;\n}\n\n// x % n but takes the sign of n instead of x\nexport function floorMod(x, n) {\n  return x - n * Math.floor(x / n);\n}\n\nexport function padStart(input, n = 2) {\n  const isNeg = input < 0;\n  let padded;\n  if (isNeg) {\n    padded = \"-\" + (\"\" + -input).padStart(n, \"0\");\n  } else {\n    padded = (\"\" + input).padStart(n, \"0\");\n  }\n  return padded;\n}\n\nexport function parseInteger(string) {\n  if (isUndefined(string) || string === null || string === \"\") {\n    return undefined;\n  } else {\n    return parseInt(string, 10);\n  }\n}\n\nexport function parseFloating(string) {\n  if (isUndefined(string) || string === null || string === \"\") {\n    return undefined;\n  } else {\n    return parseFloat(string);\n  }\n}\n\nexport function parseMillis(fraction) {\n  // Return undefined (instead of 0) in these cases, where fraction is not set\n  if (isUndefined(fraction) || fraction === null || fraction === \"\") {\n    return undefined;\n  } else {\n    const f = parseFloat(\"0.\" + fraction) * 1000;\n    return Math.floor(f);\n  }\n}\n\nexport function roundTo(number, digits, towardZero = false) {\n  const factor = 10 ** digits,\n    rounder = towardZero ? Math.trunc : Math.round;\n  return rounder(number * factor) / factor;\n}\n\n// DATE BASICS\n\nexport function isLeapYear(year) {\n  return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);\n}\n\nexport function daysInYear(year) {\n  return isLeapYear(year) ? 366 : 365;\n}\n\nexport function daysInMonth(year, month) {\n  const modMonth = floorMod(month - 1, 12) + 1,\n    modYear = year + (month - modMonth) / 12;\n\n  if (modMonth === 2) {\n    return isLeapYear(modYear) ? 29 : 28;\n  } else {\n    return [31, null, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][modMonth - 1];\n  }\n}\n\n// convert a calendar object to a local timestamp (epoch, but with the offset baked in)\nexport function objToLocalTS(obj) {\n  let d = Date.UTC(\n    obj.year,\n    obj.month - 1,\n    obj.day,\n    obj.hour,\n    obj.minute,\n    obj.second,\n    obj.millisecond\n  );\n\n  // for legacy reasons, years between 0 and 99 are interpreted as 19XX; revert that\n  if (obj.year < 100 && obj.year >= 0) {\n    d = new Date(d);\n    // set the month and day again, this is necessary because year 2000 is a leap year, but year 100 is not\n    // so if obj.year is in 99, but obj.day makes it roll over into year 100,\n    // the calculations done by Date.UTC are using year 2000 - which is incorrect\n    d.setUTCFullYear(obj.year, obj.month - 1, obj.day);\n  }\n  return +d;\n}\n\n// adapted from moment.js: https://github.com/moment/moment/blob/000ac1800e620f770f4eb31b5ae908f6167b0ab2/src/lib/units/week-calendar-utils.js\nfunction firstWeekOffset(year, minDaysInFirstWeek, startOfWeek) {\n  const fwdlw = isoWeekdayToLocal(dayOfWeek(year, 1, minDaysInFirstWeek), startOfWeek);\n  return -fwdlw + minDaysInFirstWeek - 1;\n}\n\nexport function weeksInWeekYear(weekYear, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const weekOffset = firstWeekOffset(weekYear, minDaysInFirstWeek, startOfWeek);\n  const weekOffsetNext = firstWeekOffset(weekYear + 1, minDaysInFirstWeek, startOfWeek);\n  return (daysInYear(weekYear) - weekOffset + weekOffsetNext) / 7;\n}\n\nexport function untruncateYear(year) {\n  if (year > 99) {\n    return year;\n  } else return year > Settings.twoDigitCutoffYear ? 1900 + year : 2000 + year;\n}\n\n// PARSING\n\nexport function parseZoneInfo(ts, offsetFormat, locale, timeZone = null) {\n  const date = new Date(ts),\n    intlOpts = {\n      hourCycle: \"h23\",\n      year: \"numeric\",\n      month: \"2-digit\",\n      day: \"2-digit\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n    };\n\n  if (timeZone) {\n    intlOpts.timeZone = timeZone;\n  }\n\n  const modified = { timeZoneName: offsetFormat, ...intlOpts };\n\n  const parsed = new Intl.DateTimeFormat(locale, modified)\n    .formatToParts(date)\n    .find((m) => m.type.toLowerCase() === \"timezonename\");\n  return parsed ? parsed.value : null;\n}\n\n// signedOffset('-5', '30') -> -330\nexport function signedOffset(offHourStr, offMinuteStr) {\n  let offHour = parseInt(offHourStr, 10);\n\n  // don't || this because we want to preserve -0\n  if (Number.isNaN(offHour)) {\n    offHour = 0;\n  }\n\n  const offMin = parseInt(offMinuteStr, 10) || 0,\n    offMinSigned = offHour < 0 || Object.is(offHour, -0) ? -offMin : offMin;\n  return offHour * 60 + offMinSigned;\n}\n\n// COERCION\n\nexport function asNumber(value) {\n  const numericValue = Number(value);\n  if (typeof value === \"boolean\" || value === \"\" || Number.isNaN(numericValue))\n    throw new InvalidArgumentError(`Invalid unit value ${value}`);\n  return numericValue;\n}\n\nexport function normalizeObject(obj, normalizer) {\n  const normalized = {};\n  for (const u in obj) {\n    if (hasOwnProperty(obj, u)) {\n      const v = obj[u];\n      if (v === undefined || v === null) continue;\n      normalized[normalizer(u)] = asNumber(v);\n    }\n  }\n  return normalized;\n}\n\n/**\n * Returns the offset's value as a string\n * @param {number} ts - Epoch milliseconds for which to get the offset\n * @param {string} format - What style of offset to return.\n *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively\n * @return {string}\n */\nexport function formatOffset(offset, format) {\n  const hours = Math.trunc(Math.abs(offset / 60)),\n    minutes = Math.trunc(Math.abs(offset % 60)),\n    sign = offset >= 0 ? \"+\" : \"-\";\n\n  switch (format) {\n    case \"short\":\n      return `${sign}${padStart(hours, 2)}:${padStart(minutes, 2)}`;\n    case \"narrow\":\n      return `${sign}${hours}${minutes > 0 ? `:${minutes}` : \"\"}`;\n    case \"techie\":\n      return `${sign}${padStart(hours, 2)}${padStart(minutes, 2)}`;\n    default:\n      throw new RangeError(`Value format ${format} is out of range for property format`);\n  }\n}\n\nexport function timeObject(obj) {\n  return pick(obj, [\"hour\", \"minute\", \"second\", \"millisecond\"]);\n}\n"], "names": [], "mappings": "AAAA;;;;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;;;;AAQO,SAAS,YAAY,CAAC;IAC3B,OAAO,OAAO,MAAM;AACtB;AAEO,SAAS,SAAS,CAAC;IACxB,OAAO,OAAO,MAAM;AACtB;AAEO,SAAS,UAAU,CAAC;IACzB,OAAO,OAAO,MAAM,YAAY,IAAI,MAAM;AAC5C;AAEO,SAAS,SAAS,CAAC;IACxB,OAAO,OAAO,MAAM;AACtB;AAEO,SAAS,OAAO,CAAC;IACtB,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO;AAC/C;AAIO,SAAS;IACd,IAAI;QACF,OAAO,OAAO,SAAS,eAAe,CAAC,CAAC,KAAK,kBAAkB;IACjE,EAAE,OAAO,GAAG;QACV,OAAO;IACT;AACF;AAEO,SAAS;IACd,IAAI;QACF,OACE,OAAO,SAAS,eAChB,CAAC,CAAC,KAAK,MAAM,IACb,CAAC,cAAc,KAAK,MAAM,CAAC,SAAS,IAAI,iBAAiB,KAAK,MAAM,CAAC,SAAS;IAElF,EAAE,OAAO,GAAG;QACV,OAAO;IACT;AACF;AAIO,SAAS,WAAW,KAAK;IAC9B,OAAO,MAAM,OAAO,CAAC,SAAS,QAAQ;QAAC;KAAM;AAC/C;AAEO,SAAS,OAAO,GAAG,EAAE,EAAE,EAAE,OAAO;IACrC,IAAI,IAAI,MAAM,KAAK,GAAG;QACpB,OAAO;IACT;IACA,OAAO,IAAI,MAAM,CAAC,CAAC,MAAM;QACvB,MAAM,OAAO;YAAC,GAAG;YAAO;SAAK;QAC7B,IAAI,CAAC,MAAM;YACT,OAAO;QACT,OAAO,IAAI,QAAQ,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,MAAM,IAAI,CAAC,EAAE,EAAE;YAChD,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF,GAAG,KAAK,CAAC,EAAE;AACb;AAEO,SAAS,KAAK,GAAG,EAAE,IAAI;IAC5B,OAAO,KAAK,MAAM,CAAC,CAAC,GAAG;QACrB,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE;QACb,OAAO;IACT,GAAG,CAAC;AACN;AAEO,SAAS,eAAe,GAAG,EAAE,IAAI;IACtC,OAAO,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK;AACnD;AAEO,SAAS,qBAAqB,QAAQ;IAC3C,IAAI,YAAY,MAAM;QACpB,OAAO;IACT,OAAO,IAAI,OAAO,aAAa,UAAU;QACvC,MAAM,IAAI,8IAAA,CAAA,uBAAoB,CAAC;IACjC,OAAO;QACL,IACE,CAAC,eAAe,SAAS,QAAQ,EAAE,GAAG,MACtC,CAAC,eAAe,SAAS,WAAW,EAAE,GAAG,MACzC,CAAC,MAAM,OAAO,CAAC,SAAS,OAAO,KAC/B,SAAS,OAAO,CAAC,IAAI,CAAC,CAAC,IAAM,CAAC,eAAe,GAAG,GAAG,KACnD;YACA,MAAM,IAAI,8IAAA,CAAA,uBAAoB,CAAC;QACjC;QACA,OAAO;YACL,UAAU,SAAS,QAAQ;YAC3B,aAAa,SAAS,WAAW;YACjC,SAAS,MAAM,IAAI,CAAC,SAAS,OAAO;QACtC;IACF;AACF;AAIO,SAAS,eAAe,KAAK,EAAE,MAAM,EAAE,GAAG;IAC/C,OAAO,UAAU,UAAU,SAAS,UAAU,SAAS;AACzD;AAGO,SAAS,SAAS,CAAC,EAAE,CAAC;IAC3B,OAAO,IAAI,IAAI,KAAK,KAAK,CAAC,IAAI;AAChC;AAEO,SAAS,SAAS,KAAK,EAAE,IAAI,CAAC;IACnC,MAAM,QAAQ,QAAQ;IACtB,IAAI;IACJ,IAAI,OAAO;QACT,SAAS,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG;IAC3C,OAAO;QACL,SAAS,CAAC,KAAK,KAAK,EAAE,QAAQ,CAAC,GAAG;IACpC;IACA,OAAO;AACT;AAEO,SAAS,aAAa,MAAM;IACjC,IAAI,YAAY,WAAW,WAAW,QAAQ,WAAW,IAAI;QAC3D,OAAO;IACT,OAAO;QACL,OAAO,SAAS,QAAQ;IAC1B;AACF;AAEO,SAAS,cAAc,MAAM;IAClC,IAAI,YAAY,WAAW,WAAW,QAAQ,WAAW,IAAI;QAC3D,OAAO;IACT,OAAO;QACL,OAAO,WAAW;IACpB;AACF;AAEO,SAAS,YAAY,QAAQ;IAClC,4EAA4E;IAC5E,IAAI,YAAY,aAAa,aAAa,QAAQ,aAAa,IAAI;QACjE,OAAO;IACT,OAAO;QACL,MAAM,IAAI,WAAW,OAAO,YAAY;QACxC,OAAO,KAAK,KAAK,CAAC;IACpB;AACF;AAEO,SAAS,QAAQ,MAAM,EAAE,MAAM,EAAE,aAAa,KAAK;IACxD,MAAM,SAAS,MAAM,QACnB,UAAU,aAAa,KAAK,KAAK,GAAG,KAAK,KAAK;IAChD,OAAO,QAAQ,SAAS,UAAU;AACpC;AAIO,SAAS,WAAW,IAAI;IAC7B,OAAO,OAAO,MAAM,KAAK,CAAC,OAAO,QAAQ,KAAK,OAAO,QAAQ,CAAC;AAChE;AAEO,SAAS,WAAW,IAAI;IAC7B,OAAO,WAAW,QAAQ,MAAM;AAClC;AAEO,SAAS,YAAY,IAAI,EAAE,KAAK;IACrC,MAAM,WAAW,SAAS,QAAQ,GAAG,MAAM,GACzC,UAAU,OAAO,CAAC,QAAQ,QAAQ,IAAI;IAExC,IAAI,aAAa,GAAG;QAClB,OAAO,WAAW,WAAW,KAAK;IACpC,OAAO;QACL,OAAO;YAAC;YAAI;YAAM;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;SAAG,CAAC,WAAW,EAAE;IACzE;AACF;AAGO,SAAS,aAAa,GAAG;IAC9B,IAAI,IAAI,KAAK,GAAG,CACd,IAAI,IAAI,EACR,IAAI,KAAK,GAAG,GACZ,IAAI,GAAG,EACP,IAAI,IAAI,EACR,IAAI,MAAM,EACV,IAAI,MAAM,EACV,IAAI,WAAW;IAGjB,kFAAkF;IAClF,IAAI,IAAI,IAAI,GAAG,OAAO,IAAI,IAAI,IAAI,GAAG;QACnC,IAAI,IAAI,KAAK;QACb,uGAAuG;QACvG,yEAAyE;QACzE,6EAA6E;QAC7E,EAAE,cAAc,CAAC,IAAI,IAAI,EAAE,IAAI,KAAK,GAAG,GAAG,IAAI,GAAG;IACnD;IACA,OAAO,CAAC;AACV;AAEA,8IAA8I;AAC9I,SAAS,gBAAgB,IAAI,EAAE,kBAAkB,EAAE,WAAW;IAC5D,MAAM,QAAQ,CAAA,GAAA,2JAAA,CAAA,oBAAiB,AAAD,EAAE,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE,MAAM,GAAG,qBAAqB;IACxE,OAAO,CAAC,QAAQ,qBAAqB;AACvC;AAEO,SAAS,gBAAgB,QAAQ,EAAE,qBAAqB,CAAC,EAAE,cAAc,CAAC;IAC/E,MAAM,aAAa,gBAAgB,UAAU,oBAAoB;IACjE,MAAM,iBAAiB,gBAAgB,WAAW,GAAG,oBAAoB;IACzE,OAAO,CAAC,WAAW,YAAY,aAAa,cAAc,IAAI;AAChE;AAEO,SAAS,eAAe,IAAI;IACjC,IAAI,OAAO,IAAI;QACb,OAAO;IACT,OAAO,OAAO,OAAO,gJAAA,CAAA,UAAQ,CAAC,kBAAkB,GAAG,OAAO,OAAO,OAAO;AAC1E;AAIO,SAAS,cAAc,EAAE,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW,IAAI;IACrE,MAAM,OAAO,IAAI,KAAK,KACpB,WAAW;QACT,WAAW;QACX,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;IAEF,IAAI,UAAU;QACZ,SAAS,QAAQ,GAAG;IACtB;IAEA,MAAM,WAAW;QAAE,cAAc;QAAc,GAAG,QAAQ;IAAC;IAE3D,MAAM,SAAS,IAAI,KAAK,cAAc,CAAC,QAAQ,UAC5C,aAAa,CAAC,MACd,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI,CAAC,WAAW,OAAO;IACxC,OAAO,SAAS,OAAO,KAAK,GAAG;AACjC;AAGO,SAAS,aAAa,UAAU,EAAE,YAAY;IACnD,IAAI,UAAU,SAAS,YAAY;IAEnC,+CAA+C;IAC/C,IAAI,OAAO,KAAK,CAAC,UAAU;QACzB,UAAU;IACZ;IAEA,MAAM,SAAS,SAAS,cAAc,OAAO,GAC3C,eAAe,UAAU,KAAK,OAAO,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS;IACnE,OAAO,UAAU,KAAK;AACxB;AAIO,SAAS,SAAS,KAAK;IAC5B,MAAM,eAAe,OAAO;IAC5B,IAAI,OAAO,UAAU,aAAa,UAAU,MAAM,OAAO,KAAK,CAAC,eAC7D,MAAM,IAAI,8IAAA,CAAA,uBAAoB,CAAC,CAAC,mBAAmB,EAAE,OAAO;IAC9D,OAAO;AACT;AAEO,SAAS,gBAAgB,GAAG,EAAE,UAAU;IAC7C,MAAM,aAAa,CAAC;IACpB,IAAK,MAAM,KAAK,IAAK;QACnB,IAAI,eAAe,KAAK,IAAI;YAC1B,MAAM,IAAI,GAAG,CAAC,EAAE;YAChB,IAAI,MAAM,aAAa,MAAM,MAAM;YACnC,UAAU,CAAC,WAAW,GAAG,GAAG,SAAS;QACvC;IACF;IACA,OAAO;AACT;AASO,SAAS,aAAa,MAAM,EAAE,MAAM;IACzC,MAAM,QAAQ,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,MACzC,UAAU,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,MACvC,OAAO,UAAU,IAAI,MAAM;IAE7B,OAAQ;QACN,KAAK;YACH,OAAO,GAAG,OAAO,SAAS,OAAO,GAAG,CAAC,EAAE,SAAS,SAAS,IAAI;QAC/D,KAAK;YACH,OAAO,GAAG,OAAO,QAAQ,UAAU,IAAI,CAAC,CAAC,EAAE,SAAS,GAAG,IAAI;QAC7D,KAAK;YACH,OAAO,GAAG,OAAO,SAAS,OAAO,KAAK,SAAS,SAAS,IAAI;QAC9D;YACE,MAAM,IAAI,WAAW,CAAC,aAAa,EAAE,OAAO,oCAAoC,CAAC;IACrF;AACF;AAEO,SAAS,WAAW,GAAG;IAC5B,OAAO,KAAK,KAAK;QAAC;QAAQ;QAAU;QAAU;KAAc;AAC9D", "ignoreList": [0]}}, {"offset": {"line": 2132, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/luxon/src/impl/english.js"], "sourcesContent": ["import * as Formats from \"./formats.js\";\nimport { pick } from \"./util.js\";\n\nfunction stringify(obj) {\n  return JSON.stringify(obj, Object.keys(obj).sort());\n}\n\n/**\n * @private\n */\n\nexport const monthsLong = [\n  \"January\",\n  \"February\",\n  \"March\",\n  \"April\",\n  \"May\",\n  \"June\",\n  \"July\",\n  \"August\",\n  \"September\",\n  \"October\",\n  \"November\",\n  \"December\",\n];\n\nexport const monthsShort = [\n  \"Jan\",\n  \"Feb\",\n  \"Mar\",\n  \"Apr\",\n  \"May\",\n  \"Jun\",\n  \"Jul\",\n  \"Aug\",\n  \"Sep\",\n  \"Oct\",\n  \"Nov\",\n  \"Dec\",\n];\n\nexport const monthsNarrow = [\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"];\n\nexport function months(length) {\n  switch (length) {\n    case \"narrow\":\n      return [...monthsNarrow];\n    case \"short\":\n      return [...monthsShort];\n    case \"long\":\n      return [...monthsLong];\n    case \"numeric\":\n      return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"];\n    case \"2-digit\":\n      return [\"01\", \"02\", \"03\", \"04\", \"05\", \"06\", \"07\", \"08\", \"09\", \"10\", \"11\", \"12\"];\n    default:\n      return null;\n  }\n}\n\nexport const weekdaysLong = [\n  \"Monday\",\n  \"Tuesday\",\n  \"Wednesday\",\n  \"Thursday\",\n  \"Friday\",\n  \"Saturday\",\n  \"Sunday\",\n];\n\nexport const weekdaysShort = [\"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\", \"Sun\"];\n\nexport const weekdaysNarrow = [\"M\", \"T\", \"W\", \"T\", \"F\", \"S\", \"S\"];\n\nexport function weekdays(length) {\n  switch (length) {\n    case \"narrow\":\n      return [...weekdaysNarrow];\n    case \"short\":\n      return [...weekdaysShort];\n    case \"long\":\n      return [...weekdaysLong];\n    case \"numeric\":\n      return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n    default:\n      return null;\n  }\n}\n\nexport const meridiems = [\"AM\", \"PM\"];\n\nexport const erasLong = [\"Before Christ\", \"Anno Domini\"];\n\nexport const erasShort = [\"BC\", \"AD\"];\n\nexport const erasNarrow = [\"B\", \"A\"];\n\nexport function eras(length) {\n  switch (length) {\n    case \"narrow\":\n      return [...erasNarrow];\n    case \"short\":\n      return [...erasShort];\n    case \"long\":\n      return [...erasLong];\n    default:\n      return null;\n  }\n}\n\nexport function meridiemForDateTime(dt) {\n  return meridiems[dt.hour < 12 ? 0 : 1];\n}\n\nexport function weekdayForDateTime(dt, length) {\n  return weekdays(length)[dt.weekday - 1];\n}\n\nexport function monthForDateTime(dt, length) {\n  return months(length)[dt.month - 1];\n}\n\nexport function eraForDateTime(dt, length) {\n  return eras(length)[dt.year < 0 ? 0 : 1];\n}\n\nexport function formatRelativeTime(unit, count, numeric = \"always\", narrow = false) {\n  const units = {\n    years: [\"year\", \"yr.\"],\n    quarters: [\"quarter\", \"qtr.\"],\n    months: [\"month\", \"mo.\"],\n    weeks: [\"week\", \"wk.\"],\n    days: [\"day\", \"day\", \"days\"],\n    hours: [\"hour\", \"hr.\"],\n    minutes: [\"minute\", \"min.\"],\n    seconds: [\"second\", \"sec.\"],\n  };\n\n  const lastable = [\"hours\", \"minutes\", \"seconds\"].indexOf(unit) === -1;\n\n  if (numeric === \"auto\" && lastable) {\n    const isDay = unit === \"days\";\n    switch (count) {\n      case 1:\n        return isDay ? \"tomorrow\" : `next ${units[unit][0]}`;\n      case -1:\n        return isDay ? \"yesterday\" : `last ${units[unit][0]}`;\n      case 0:\n        return isDay ? \"today\" : `this ${units[unit][0]}`;\n      default: // fall through\n    }\n  }\n\n  const isInPast = Object.is(count, -0) || count < 0,\n    fmtValue = Math.abs(count),\n    singular = fmtValue === 1,\n    lilUnits = units[unit],\n    fmtUnit = narrow\n      ? singular\n        ? lilUnits[1]\n        : lilUnits[2] || lilUnits[1]\n      : singular\n      ? units[unit][0]\n      : unit;\n  return isInPast ? `${fmtValue} ${fmtUnit} ago` : `in ${fmtValue} ${fmtUnit}`;\n}\n\nexport function formatString(knownFormat) {\n  // these all have the offsets removed because we don't have access to them\n  // without all the intl stuff this is backfilling\n  const filtered = pick(knownFormat, [\n      \"weekday\",\n      \"era\",\n      \"year\",\n      \"month\",\n      \"day\",\n      \"hour\",\n      \"minute\",\n      \"second\",\n      \"timeZoneName\",\n      \"hourCycle\",\n    ]),\n    key = stringify(filtered),\n    dateTimeHuge = \"EEEE, LLLL d, yyyy, h:mm a\";\n  switch (key) {\n    case stringify(Formats.DATE_SHORT):\n      return \"M/d/yyyy\";\n    case stringify(Formats.DATE_MED):\n      return \"LLL d, yyyy\";\n    case stringify(Formats.DATE_MED_WITH_WEEKDAY):\n      return \"EEE, LLL d, yyyy\";\n    case stringify(Formats.DATE_FULL):\n      return \"LLLL d, yyyy\";\n    case stringify(Formats.DATE_HUGE):\n      return \"EEEE, LLLL d, yyyy\";\n    case stringify(Formats.TIME_SIMPLE):\n      return \"h:mm a\";\n    case stringify(Formats.TIME_WITH_SECONDS):\n      return \"h:mm:ss a\";\n    case stringify(Formats.TIME_WITH_SHORT_OFFSET):\n      return \"h:mm a\";\n    case stringify(Formats.TIME_WITH_LONG_OFFSET):\n      return \"h:mm a\";\n    case stringify(Formats.TIME_24_SIMPLE):\n      return \"HH:mm\";\n    case stringify(Formats.TIME_24_WITH_SECONDS):\n      return \"HH:mm:ss\";\n    case stringify(Formats.TIME_24_WITH_SHORT_OFFSET):\n      return \"HH:mm\";\n    case stringify(Formats.TIME_24_WITH_LONG_OFFSET):\n      return \"HH:mm\";\n    case stringify(Formats.DATETIME_SHORT):\n      return \"M/d/yyyy, h:mm a\";\n    case stringify(Formats.DATETIME_MED):\n      return \"LLL d, yyyy, h:mm a\";\n    case stringify(Formats.DATETIME_FULL):\n      return \"LLLL d, yyyy, h:mm a\";\n    case stringify(Formats.DATETIME_HUGE):\n      return dateTimeHuge;\n    case stringify(Formats.DATETIME_SHORT_WITH_SECONDS):\n      return \"M/d/yyyy, h:mm:ss a\";\n    case stringify(Formats.DATETIME_MED_WITH_SECONDS):\n      return \"LLL d, yyyy, h:mm:ss a\";\n    case stringify(Formats.DATETIME_MED_WITH_WEEKDAY):\n      return \"EEE, d LLL yyyy, h:mm a\";\n    case stringify(Formats.DATETIME_FULL_WITH_SECONDS):\n      return \"LLLL d, yyyy, h:mm:ss a\";\n    case stringify(Formats.DATETIME_HUGE_WITH_SECONDS):\n      return \"EEEE, LLLL d, yyyy, h:mm:ss a\";\n    default:\n      return dateTimeHuge;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEA,SAAS,UAAU,GAAG;IACpB,OAAO,KAAK,SAAS,CAAC,KAAK,OAAO,IAAI,CAAC,KAAK,IAAI;AAClD;AAMO,MAAM,aAAa;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,cAAc;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,eAAe;IAAC;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;CAAI;AAEjF,SAAS,OAAO,MAAM;IAC3B,OAAQ;QACN,KAAK;YACH,OAAO;mBAAI;aAAa;QAC1B,KAAK;YACH,OAAO;mBAAI;aAAY;QACzB,KAAK;YACH,OAAO;mBAAI;aAAW;QACxB,KAAK;YACH,OAAO;gBAAC;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBAAM;gBAAM;aAAK;QACxE,KAAK;YACH,OAAO;gBAAC;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;aAAK;QACjF;YACE,OAAO;IACX;AACF;AAEO,MAAM,eAAe;IAC1B;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,gBAAgB;IAAC;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;CAAM;AAEvE,MAAM,iBAAiB;IAAC;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;CAAI;AAE1D,SAAS,SAAS,MAAM;IAC7B,OAAQ;QACN,KAAK;YACH,OAAO;mBAAI;aAAe;QAC5B,KAAK;YACH,OAAO;mBAAI;aAAc;QAC3B,KAAK;YACH,OAAO;mBAAI;aAAa;QAC1B,KAAK;YACH,OAAO;gBAAC;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;aAAI;QAC5C;YACE,OAAO;IACX;AACF;AAEO,MAAM,YAAY;IAAC;IAAM;CAAK;AAE9B,MAAM,WAAW;IAAC;IAAiB;CAAc;AAEjD,MAAM,YAAY;IAAC;IAAM;CAAK;AAE9B,MAAM,aAAa;IAAC;IAAK;CAAI;AAE7B,SAAS,KAAK,MAAM;IACzB,OAAQ;QACN,KAAK;YACH,OAAO;mBAAI;aAAW;QACxB,KAAK;YACH,OAAO;mBAAI;aAAU;QACvB,KAAK;YACH,OAAO;mBAAI;aAAS;QACtB;YACE,OAAO;IACX;AACF;AAEO,SAAS,oBAAoB,EAAE;IACpC,OAAO,SAAS,CAAC,GAAG,IAAI,GAAG,KAAK,IAAI,EAAE;AACxC;AAEO,SAAS,mBAAmB,EAAE,EAAE,MAAM;IAC3C,OAAO,SAAS,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE;AACzC;AAEO,SAAS,iBAAiB,EAAE,EAAE,MAAM;IACzC,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,GAAG,EAAE;AACrC;AAEO,SAAS,eAAe,EAAE,EAAE,MAAM;IACvC,OAAO,KAAK,OAAO,CAAC,GAAG,IAAI,GAAG,IAAI,IAAI,EAAE;AAC1C;AAEO,SAAS,mBAAmB,IAAI,EAAE,KAAK,EAAE,UAAU,QAAQ,EAAE,SAAS,KAAK;IAChF,MAAM,QAAQ;QACZ,OAAO;YAAC;YAAQ;SAAM;QACtB,UAAU;YAAC;YAAW;SAAO;QAC7B,QAAQ;YAAC;YAAS;SAAM;QACxB,OAAO;YAAC;YAAQ;SAAM;QACtB,MAAM;YAAC;YAAO;YAAO;SAAO;QAC5B,OAAO;YAAC;YAAQ;SAAM;QACtB,SAAS;YAAC;YAAU;SAAO;QAC3B,SAAS;YAAC;YAAU;SAAO;IAC7B;IAEA,MAAM,WAAW;QAAC;QAAS;QAAW;KAAU,CAAC,OAAO,CAAC,UAAU,CAAC;IAEpE,IAAI,YAAY,UAAU,UAAU;QAClC,MAAM,QAAQ,SAAS;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO,QAAQ,aAAa,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE;YACtD,KAAK,CAAC;gBACJ,OAAO,QAAQ,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE;YACvD,KAAK;gBACH,OAAO,QAAQ,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE;YACnD;QACF;IACF;IAEA,MAAM,WAAW,OAAO,EAAE,CAAC,OAAO,CAAC,MAAM,QAAQ,GAC/C,WAAW,KAAK,GAAG,CAAC,QACpB,WAAW,aAAa,GACxB,WAAW,KAAK,CAAC,KAAK,EACtB,UAAU,SACN,WACE,QAAQ,CAAC,EAAE,GACX,QAAQ,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,GAC5B,WACA,KAAK,CAAC,KAAK,CAAC,EAAE,GACd;IACN,OAAO,WAAW,GAAG,SAAS,CAAC,EAAE,QAAQ,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,EAAE,SAAS;AAC9E;AAEO,SAAS,aAAa,WAAW;IACtC,0EAA0E;IAC1E,iDAAiD;IACjD,MAAM,WAAW,CAAA,GAAA,oJAAA,CAAA,OAAI,AAAD,EAAE,aAAa;QAC/B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD,GACD,MAAM,UAAU,WAChB,eAAe;IACjB,OAAQ;QACN,KAAK,UAAU,uJAAA,CAAA,aAAkB;YAC/B,OAAO;QACT,KAAK,UAAU,uJAAA,CAAA,WAAgB;YAC7B,OAAO;QACT,KAAK,UAAU,uJAAA,CAAA,wBAA6B;YAC1C,OAAO;QACT,KAAK,UAAU,uJAAA,CAAA,YAAiB;YAC9B,OAAO;QACT,KAAK,UAAU,uJAAA,CAAA,YAAiB;YAC9B,OAAO;QACT,KAAK,UAAU,uJAAA,CAAA,cAAmB;YAChC,OAAO;QACT,KAAK,UAAU,uJAAA,CAAA,oBAAyB;YACtC,OAAO;QACT,KAAK,UAAU,uJAAA,CAAA,yBAA8B;YAC3C,OAAO;QACT,KAAK,UAAU,uJAAA,CAAA,wBAA6B;YAC1C,OAAO;QACT,KAAK,UAAU,uJAAA,CAAA,iBAAsB;YACnC,OAAO;QACT,KAAK,UAAU,uJAAA,CAAA,uBAA4B;YACzC,OAAO;QACT,KAAK,UAAU,uJAAA,CAAA,4BAAiC;YAC9C,OAAO;QACT,KAAK,UAAU,uJAAA,CAAA,2BAAgC;YAC7C,OAAO;QACT,KAAK,UAAU,uJAAA,CAAA,iBAAsB;YACnC,OAAO;QACT,KAAK,UAAU,uJAAA,CAAA,eAAoB;YACjC,OAAO;QACT,KAAK,UAAU,uJAAA,CAAA,gBAAqB;YAClC,OAAO;QACT,KAAK,UAAU,uJAAA,CAAA,gBAAqB;YAClC,OAAO;QACT,KAAK,UAAU,uJAAA,CAAA,8BAAmC;YAChD,OAAO;QACT,KAAK,UAAU,uJAAA,CAAA,4BAAiC;YAC9C,OAAO;QACT,KAAK,UAAU,uJAAA,CAAA,4BAAiC;YAC9C,OAAO;QACT,KAAK,UAAU,uJAAA,CAAA,6BAAkC;YAC/C,OAAO;QACT,KAAK,UAAU,uJAAA,CAAA,6BAAkC;YAC/C,OAAO;QACT;YACE,OAAO;IACX;AACF", "ignoreList": [0]}}, {"offset": {"line": 2477, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/luxon/src/impl/formatter.js"], "sourcesContent": ["import * as English from \"./english.js\";\nimport * as Formats from \"./formats.js\";\nimport { padStart } from \"./util.js\";\n\nfunction stringifyTokens(splits, tokenToString) {\n  let s = \"\";\n  for (const token of splits) {\n    if (token.literal) {\n      s += token.val;\n    } else {\n      s += tokenToString(token.val);\n    }\n  }\n  return s;\n}\n\nconst macroTokenToFormatOpts = {\n  D: Formats.DATE_SHORT,\n  DD: Formats.DATE_MED,\n  DDD: Formats.DATE_FULL,\n  DDDD: Formats.DATE_HUGE,\n  t: Formats.TIME_SIMPLE,\n  tt: Formats.TIME_WITH_SECONDS,\n  ttt: Formats.TIME_WITH_SHORT_OFFSET,\n  tttt: Formats.TIME_WITH_LONG_OFFSET,\n  T: Formats.TIME_24_SIMPLE,\n  TT: Formats.TIME_24_WITH_SECONDS,\n  TTT: Formats.TIME_24_WITH_SHORT_OFFSET,\n  TTTT: Formats.TIME_24_WITH_LONG_OFFSET,\n  f: Formats.DATETIME_SHORT,\n  ff: Formats.DATETIME_MED,\n  fff: Formats.DATETIME_FULL,\n  ffff: Formats.DATETIME_HUGE,\n  F: Formats.DATETIME_SHORT_WITH_SECONDS,\n  FF: Formats.DATETIME_MED_WITH_SECONDS,\n  FFF: Formats.DATETIME_FULL_WITH_SECONDS,\n  FFFF: Formats.DATETIME_HUGE_WITH_SECONDS,\n};\n\n/**\n * @private\n */\n\nexport default class Formatter {\n  static create(locale, opts = {}) {\n    return new Formatter(locale, opts);\n  }\n\n  static parseFormat(fmt) {\n    // white-space is always considered a literal in user-provided formats\n    // the \" \" token has a special meaning (see unitForToken)\n\n    let current = null,\n      currentFull = \"\",\n      bracketed = false;\n    const splits = [];\n    for (let i = 0; i < fmt.length; i++) {\n      const c = fmt.charAt(i);\n      if (c === \"'\") {\n        if (currentFull.length > 0) {\n          splits.push({ literal: bracketed || /^\\s+$/.test(currentFull), val: currentFull });\n        }\n        current = null;\n        currentFull = \"\";\n        bracketed = !bracketed;\n      } else if (bracketed) {\n        currentFull += c;\n      } else if (c === current) {\n        currentFull += c;\n      } else {\n        if (currentFull.length > 0) {\n          splits.push({ literal: /^\\s+$/.test(currentFull), val: currentFull });\n        }\n        currentFull = c;\n        current = c;\n      }\n    }\n\n    if (currentFull.length > 0) {\n      splits.push({ literal: bracketed || /^\\s+$/.test(currentFull), val: currentFull });\n    }\n\n    return splits;\n  }\n\n  static macroTokenToFormatOpts(token) {\n    return macroTokenToFormatOpts[token];\n  }\n\n  constructor(locale, formatOpts) {\n    this.opts = formatOpts;\n    this.loc = locale;\n    this.systemLoc = null;\n  }\n\n  formatWithSystemDefault(dt, opts) {\n    if (this.systemLoc === null) {\n      this.systemLoc = this.loc.redefaultToSystem();\n    }\n    const df = this.systemLoc.dtFormatter(dt, { ...this.opts, ...opts });\n    return df.format();\n  }\n\n  dtFormatter(dt, opts = {}) {\n    return this.loc.dtFormatter(dt, { ...this.opts, ...opts });\n  }\n\n  formatDateTime(dt, opts) {\n    return this.dtFormatter(dt, opts).format();\n  }\n\n  formatDateTimeParts(dt, opts) {\n    return this.dtFormatter(dt, opts).formatToParts();\n  }\n\n  formatInterval(interval, opts) {\n    const df = this.dtFormatter(interval.start, opts);\n    return df.dtf.formatRange(interval.start.toJSDate(), interval.end.toJSDate());\n  }\n\n  resolvedOptions(dt, opts) {\n    return this.dtFormatter(dt, opts).resolvedOptions();\n  }\n\n  num(n, p = 0) {\n    // we get some perf out of doing this here, annoyingly\n    if (this.opts.forceSimple) {\n      return padStart(n, p);\n    }\n\n    const opts = { ...this.opts };\n\n    if (p > 0) {\n      opts.padTo = p;\n    }\n\n    return this.loc.numberFormatter(opts).format(n);\n  }\n\n  formatDateTimeFromString(dt, fmt) {\n    const knownEnglish = this.loc.listingMode() === \"en\",\n      useDateTimeFormatter = this.loc.outputCalendar && this.loc.outputCalendar !== \"gregory\",\n      string = (opts, extract) => this.loc.extract(dt, opts, extract),\n      formatOffset = (opts) => {\n        if (dt.isOffsetFixed && dt.offset === 0 && opts.allowZ) {\n          return \"Z\";\n        }\n\n        return dt.isValid ? dt.zone.formatOffset(dt.ts, opts.format) : \"\";\n      },\n      meridiem = () =>\n        knownEnglish\n          ? English.meridiemForDateTime(dt)\n          : string({ hour: \"numeric\", hourCycle: \"h12\" }, \"dayperiod\"),\n      month = (length, standalone) =>\n        knownEnglish\n          ? English.monthForDateTime(dt, length)\n          : string(standalone ? { month: length } : { month: length, day: \"numeric\" }, \"month\"),\n      weekday = (length, standalone) =>\n        knownEnglish\n          ? English.weekdayForDateTime(dt, length)\n          : string(\n              standalone ? { weekday: length } : { weekday: length, month: \"long\", day: \"numeric\" },\n              \"weekday\"\n            ),\n      maybeMacro = (token) => {\n        const formatOpts = Formatter.macroTokenToFormatOpts(token);\n        if (formatOpts) {\n          return this.formatWithSystemDefault(dt, formatOpts);\n        } else {\n          return token;\n        }\n      },\n      era = (length) =>\n        knownEnglish ? English.eraForDateTime(dt, length) : string({ era: length }, \"era\"),\n      tokenToString = (token) => {\n        // Where possible: https://cldr.unicode.org/translation/date-time/date-time-symbols\n        switch (token) {\n          // ms\n          case \"S\":\n            return this.num(dt.millisecond);\n          case \"u\":\n          // falls through\n          case \"SSS\":\n            return this.num(dt.millisecond, 3);\n          // seconds\n          case \"s\":\n            return this.num(dt.second);\n          case \"ss\":\n            return this.num(dt.second, 2);\n          // fractional seconds\n          case \"uu\":\n            return this.num(Math.floor(dt.millisecond / 10), 2);\n          case \"uuu\":\n            return this.num(Math.floor(dt.millisecond / 100));\n          // minutes\n          case \"m\":\n            return this.num(dt.minute);\n          case \"mm\":\n            return this.num(dt.minute, 2);\n          // hours\n          case \"h\":\n            return this.num(dt.hour % 12 === 0 ? 12 : dt.hour % 12);\n          case \"hh\":\n            return this.num(dt.hour % 12 === 0 ? 12 : dt.hour % 12, 2);\n          case \"H\":\n            return this.num(dt.hour);\n          case \"HH\":\n            return this.num(dt.hour, 2);\n          // offset\n          case \"Z\":\n            // like +6\n            return formatOffset({ format: \"narrow\", allowZ: this.opts.allowZ });\n          case \"ZZ\":\n            // like +06:00\n            return formatOffset({ format: \"short\", allowZ: this.opts.allowZ });\n          case \"ZZZ\":\n            // like +0600\n            return formatOffset({ format: \"techie\", allowZ: this.opts.allowZ });\n          case \"ZZZZ\":\n            // like EST\n            return dt.zone.offsetName(dt.ts, { format: \"short\", locale: this.loc.locale });\n          case \"ZZZZZ\":\n            // like Eastern Standard Time\n            return dt.zone.offsetName(dt.ts, { format: \"long\", locale: this.loc.locale });\n          // zone\n          case \"z\":\n            // like America/New_York\n            return dt.zoneName;\n          // meridiems\n          case \"a\":\n            return meridiem();\n          // dates\n          case \"d\":\n            return useDateTimeFormatter ? string({ day: \"numeric\" }, \"day\") : this.num(dt.day);\n          case \"dd\":\n            return useDateTimeFormatter ? string({ day: \"2-digit\" }, \"day\") : this.num(dt.day, 2);\n          // weekdays - standalone\n          case \"c\":\n            // like 1\n            return this.num(dt.weekday);\n          case \"ccc\":\n            // like 'Tues'\n            return weekday(\"short\", true);\n          case \"cccc\":\n            // like 'Tuesday'\n            return weekday(\"long\", true);\n          case \"ccccc\":\n            // like 'T'\n            return weekday(\"narrow\", true);\n          // weekdays - format\n          case \"E\":\n            // like 1\n            return this.num(dt.weekday);\n          case \"EEE\":\n            // like 'Tues'\n            return weekday(\"short\", false);\n          case \"EEEE\":\n            // like 'Tuesday'\n            return weekday(\"long\", false);\n          case \"EEEEE\":\n            // like 'T'\n            return weekday(\"narrow\", false);\n          // months - standalone\n          case \"L\":\n            // like 1\n            return useDateTimeFormatter\n              ? string({ month: \"numeric\", day: \"numeric\" }, \"month\")\n              : this.num(dt.month);\n          case \"LL\":\n            // like 01, doesn't seem to work\n            return useDateTimeFormatter\n              ? string({ month: \"2-digit\", day: \"numeric\" }, \"month\")\n              : this.num(dt.month, 2);\n          case \"LLL\":\n            // like Jan\n            return month(\"short\", true);\n          case \"LLLL\":\n            // like January\n            return month(\"long\", true);\n          case \"LLLLL\":\n            // like J\n            return month(\"narrow\", true);\n          // months - format\n          case \"M\":\n            // like 1\n            return useDateTimeFormatter\n              ? string({ month: \"numeric\" }, \"month\")\n              : this.num(dt.month);\n          case \"MM\":\n            // like 01\n            return useDateTimeFormatter\n              ? string({ month: \"2-digit\" }, \"month\")\n              : this.num(dt.month, 2);\n          case \"MMM\":\n            // like Jan\n            return month(\"short\", false);\n          case \"MMMM\":\n            // like January\n            return month(\"long\", false);\n          case \"MMMMM\":\n            // like J\n            return month(\"narrow\", false);\n          // years\n          case \"y\":\n            // like 2014\n            return useDateTimeFormatter ? string({ year: \"numeric\" }, \"year\") : this.num(dt.year);\n          case \"yy\":\n            // like 14\n            return useDateTimeFormatter\n              ? string({ year: \"2-digit\" }, \"year\")\n              : this.num(dt.year.toString().slice(-2), 2);\n          case \"yyyy\":\n            // like 0012\n            return useDateTimeFormatter\n              ? string({ year: \"numeric\" }, \"year\")\n              : this.num(dt.year, 4);\n          case \"yyyyyy\":\n            // like 000012\n            return useDateTimeFormatter\n              ? string({ year: \"numeric\" }, \"year\")\n              : this.num(dt.year, 6);\n          // eras\n          case \"G\":\n            // like AD\n            return era(\"short\");\n          case \"GG\":\n            // like Anno Domini\n            return era(\"long\");\n          case \"GGGGG\":\n            return era(\"narrow\");\n          case \"kk\":\n            return this.num(dt.weekYear.toString().slice(-2), 2);\n          case \"kkkk\":\n            return this.num(dt.weekYear, 4);\n          case \"W\":\n            return this.num(dt.weekNumber);\n          case \"WW\":\n            return this.num(dt.weekNumber, 2);\n          case \"n\":\n            return this.num(dt.localWeekNumber);\n          case \"nn\":\n            return this.num(dt.localWeekNumber, 2);\n          case \"ii\":\n            return this.num(dt.localWeekYear.toString().slice(-2), 2);\n          case \"iiii\":\n            return this.num(dt.localWeekYear, 4);\n          case \"o\":\n            return this.num(dt.ordinal);\n          case \"ooo\":\n            return this.num(dt.ordinal, 3);\n          case \"q\":\n            // like 1\n            return this.num(dt.quarter);\n          case \"qq\":\n            // like 01\n            return this.num(dt.quarter, 2);\n          case \"X\":\n            return this.num(Math.floor(dt.ts / 1000));\n          case \"x\":\n            return this.num(dt.ts);\n          default:\n            return maybeMacro(token);\n        }\n      };\n\n    return stringifyTokens(Formatter.parseFormat(fmt), tokenToString);\n  }\n\n  formatDurationFromString(dur, fmt) {\n    const tokenToField = (token) => {\n        switch (token[0]) {\n          case \"S\":\n            return \"millisecond\";\n          case \"s\":\n            return \"second\";\n          case \"m\":\n            return \"minute\";\n          case \"h\":\n            return \"hour\";\n          case \"d\":\n            return \"day\";\n          case \"w\":\n            return \"week\";\n          case \"M\":\n            return \"month\";\n          case \"y\":\n            return \"year\";\n          default:\n            return null;\n        }\n      },\n      tokenToString = (lildur) => (token) => {\n        const mapped = tokenToField(token);\n        if (mapped) {\n          return this.num(lildur.get(mapped), token.length);\n        } else {\n          return token;\n        }\n      },\n      tokens = Formatter.parseFormat(fmt),\n      realTokens = tokens.reduce(\n        (found, { literal, val }) => (literal ? found : found.concat(val)),\n        []\n      ),\n      collapsed = dur.shiftTo(...realTokens.map(tokenToField).filter((t) => t));\n    return stringifyTokens(tokens, tokenToString(collapsed));\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS,gBAAgB,MAAM,EAAE,aAAa;IAC5C,IAAI,IAAI;IACR,KAAK,MAAM,SAAS,OAAQ;QAC1B,IAAI,MAAM,OAAO,EAAE;YACjB,KAAK,MAAM,GAAG;QAChB,OAAO;YACL,KAAK,cAAc,MAAM,GAAG;QAC9B;IACF;IACA,OAAO;AACT;AAEA,MAAM,yBAAyB;IAC7B,GAAG,uJAAA,CAAA,aAAkB;IACrB,IAAI,uJAAA,CAAA,WAAgB;IACpB,KAAK,uJAAA,CAAA,YAAiB;IACtB,MAAM,uJAAA,CAAA,YAAiB;IACvB,GAAG,uJAAA,CAAA,cAAmB;IACtB,IAAI,uJAAA,CAAA,oBAAyB;IAC7B,KAAK,uJAAA,CAAA,yBAA8B;IACnC,MAAM,uJAAA,CAAA,wBAA6B;IACnC,GAAG,uJAAA,CAAA,iBAAsB;IACzB,IAAI,uJAAA,CAAA,uBAA4B;IAChC,KAAK,uJAAA,CAAA,4BAAiC;IACtC,MAAM,uJAAA,CAAA,2BAAgC;IACtC,GAAG,uJAAA,CAAA,iBAAsB;IACzB,IAAI,uJAAA,CAAA,eAAoB;IACxB,KAAK,uJAAA,CAAA,gBAAqB;IAC1B,MAAM,uJAAA,CAAA,gBAAqB;IAC3B,GAAG,uJAAA,CAAA,8BAAmC;IACtC,IAAI,uJAAA,CAAA,4BAAiC;IACrC,KAAK,uJAAA,CAAA,6BAAkC;IACvC,MAAM,uJAAA,CAAA,6BAAkC;AAC1C;AAMe,MAAM;IACnB,OAAO,OAAO,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE;QAC/B,OAAO,IAAI,UAAU,QAAQ;IAC/B;IAEA,OAAO,YAAY,GAAG,EAAE;QACtB,sEAAsE;QACtE,yDAAyD;QAEzD,IAAI,UAAU,MACZ,cAAc,IACd,YAAY;QACd,MAAM,SAAS,EAAE;QACjB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;YACnC,MAAM,IAAI,IAAI,MAAM,CAAC;YACrB,IAAI,MAAM,KAAK;gBACb,IAAI,YAAY,MAAM,GAAG,GAAG;oBAC1B,OAAO,IAAI,CAAC;wBAAE,SAAS,aAAa,QAAQ,IAAI,CAAC;wBAAc,KAAK;oBAAY;gBAClF;gBACA,UAAU;gBACV,cAAc;gBACd,YAAY,CAAC;YACf,OAAO,IAAI,WAAW;gBACpB,eAAe;YACjB,OAAO,IAAI,MAAM,SAAS;gBACxB,eAAe;YACjB,OAAO;gBACL,IAAI,YAAY,MAAM,GAAG,GAAG;oBAC1B,OAAO,IAAI,CAAC;wBAAE,SAAS,QAAQ,IAAI,CAAC;wBAAc,KAAK;oBAAY;gBACrE;gBACA,cAAc;gBACd,UAAU;YACZ;QACF;QAEA,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,OAAO,IAAI,CAAC;gBAAE,SAAS,aAAa,QAAQ,IAAI,CAAC;gBAAc,KAAK;YAAY;QAClF;QAEA,OAAO;IACT;IAEA,OAAO,uBAAuB,KAAK,EAAE;QACnC,OAAO,sBAAsB,CAAC,MAAM;IACtC;IAEA,YAAY,MAAM,EAAE,UAAU,CAAE;QAC9B,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,SAAS,GAAG;IACnB;IAEA,wBAAwB,EAAE,EAAE,IAAI,EAAE;QAChC,IAAI,IAAI,CAAC,SAAS,KAAK,MAAM;YAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB;QAC7C;QACA,MAAM,KAAK,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI;YAAE,GAAG,IAAI,CAAC,IAAI;YAAE,GAAG,IAAI;QAAC;QAClE,OAAO,GAAG,MAAM;IAClB;IAEA,YAAY,EAAE,EAAE,OAAO,CAAC,CAAC,EAAE;QACzB,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI;YAAE,GAAG,IAAI,CAAC,IAAI;YAAE,GAAG,IAAI;QAAC;IAC1D;IAEA,eAAe,EAAE,EAAE,IAAI,EAAE;QACvB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,MAAM,MAAM;IAC1C;IAEA,oBAAoB,EAAE,EAAE,IAAI,EAAE;QAC5B,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,MAAM,aAAa;IACjD;IAEA,eAAe,QAAQ,EAAE,IAAI,EAAE;QAC7B,MAAM,KAAK,IAAI,CAAC,WAAW,CAAC,SAAS,KAAK,EAAE;QAC5C,OAAO,GAAG,GAAG,CAAC,WAAW,CAAC,SAAS,KAAK,CAAC,QAAQ,IAAI,SAAS,GAAG,CAAC,QAAQ;IAC5E;IAEA,gBAAgB,EAAE,EAAE,IAAI,EAAE;QACxB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,MAAM,eAAe;IACnD;IAEA,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE;QACZ,sDAAsD;QACtD,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACzB,OAAO,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,GAAG;QACrB;QAEA,MAAM,OAAO;YAAE,GAAG,IAAI,CAAC,IAAI;QAAC;QAE5B,IAAI,IAAI,GAAG;YACT,KAAK,KAAK,GAAG;QACf;QAEA,OAAO,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,MAAM,CAAC;IAC/C;IAEA,yBAAyB,EAAE,EAAE,GAAG,EAAE;QAChC,MAAM,eAAe,IAAI,CAAC,GAAG,CAAC,WAAW,OAAO,MAC9C,uBAAuB,IAAI,CAAC,GAAG,CAAC,cAAc,IAAI,IAAI,CAAC,GAAG,CAAC,cAAc,KAAK,WAC9E,SAAS,CAAC,MAAM,UAAY,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,MAAM,UACvD,eAAe,CAAC;YACd,IAAI,GAAG,aAAa,IAAI,GAAG,MAAM,KAAK,KAAK,KAAK,MAAM,EAAE;gBACtD,OAAO;YACT;YAEA,OAAO,GAAG,OAAO,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,EAAE,KAAK,MAAM,IAAI;QACjE,GACA,WAAW,IACT,eACI,CAAA,GAAA,uJAAA,CAAA,sBAA2B,AAAD,EAAE,MAC5B,OAAO;gBAAE,MAAM;gBAAW,WAAW;YAAM,GAAG,cACpD,QAAQ,CAAC,QAAQ,aACf,eACI,CAAA,GAAA,uJAAA,CAAA,mBAAwB,AAAD,EAAE,IAAI,UAC7B,OAAO,aAAa;gBAAE,OAAO;YAAO,IAAI;gBAAE,OAAO;gBAAQ,KAAK;YAAU,GAAG,UACjF,UAAU,CAAC,QAAQ,aACjB,eACI,CAAA,GAAA,uJAAA,CAAA,qBAA0B,AAAD,EAAE,IAAI,UAC/B,OACE,aAAa;gBAAE,SAAS;YAAO,IAAI;gBAAE,SAAS;gBAAQ,OAAO;gBAAQ,KAAK;YAAU,GACpF,YAER,aAAa,CAAC;YACZ,MAAM,aAAa,UAAU,sBAAsB,CAAC;YACpD,IAAI,YAAY;gBACd,OAAO,IAAI,CAAC,uBAAuB,CAAC,IAAI;YAC1C,OAAO;gBACL,OAAO;YACT;QACF,GACA,MAAM,CAAC,SACL,eAAe,CAAA,GAAA,uJAAA,CAAA,iBAAsB,AAAD,EAAE,IAAI,UAAU,OAAO;gBAAE,KAAK;YAAO,GAAG,QAC9E,gBAAgB,CAAC;YACf,mFAAmF;YACnF,OAAQ;gBACN,KAAK;gBACL,KAAK;oBACH,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,WAAW;gBAChC,KAAK;gBACL,gBAAgB;gBAChB,KAAK;oBACH,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE;gBAClC,UAAU;gBACV,KAAK;oBACH,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM;gBAC3B,KAAK;oBACH,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,EAAE;gBAC7B,qBAAqB;gBACrB,KAAK;oBACH,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC,GAAG,WAAW,GAAG,KAAK;gBACnD,KAAK;oBACH,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC,GAAG,WAAW,GAAG;gBAC9C,UAAU;gBACV,KAAK;oBACH,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM;gBAC3B,KAAK;oBACH,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,EAAE;gBAC7B,QAAQ;gBACR,KAAK;oBACH,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,OAAO,IAAI,KAAK,GAAG,IAAI,GAAG;gBACtD,KAAK;oBACH,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,OAAO,IAAI,KAAK,GAAG,IAAI,GAAG,IAAI;gBAC1D,KAAK;oBACH,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI;gBACzB,KAAK;oBACH,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE;gBAC3B,SAAS;gBACT,KAAK;oBACH,UAAU;oBACV,OAAO,aAAa;wBAAE,QAAQ;wBAAU,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM;oBAAC;gBACnE,KAAK;oBACH,cAAc;oBACd,OAAO,aAAa;wBAAE,QAAQ;wBAAS,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM;oBAAC;gBAClE,KAAK;oBACH,aAAa;oBACb,OAAO,aAAa;wBAAE,QAAQ;wBAAU,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM;oBAAC;gBACnE,KAAK;oBACH,WAAW;oBACX,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,EAAE;wBAAE,QAAQ;wBAAS,QAAQ,IAAI,CAAC,GAAG,CAAC,MAAM;oBAAC;gBAC9E,KAAK;oBACH,6BAA6B;oBAC7B,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,EAAE;wBAAE,QAAQ;wBAAQ,QAAQ,IAAI,CAAC,GAAG,CAAC,MAAM;oBAAC;gBAC7E,OAAO;gBACP,KAAK;oBACH,wBAAwB;oBACxB,OAAO,GAAG,QAAQ;gBACpB,YAAY;gBACZ,KAAK;oBACH,OAAO;gBACT,QAAQ;gBACR,KAAK;oBACH,OAAO,uBAAuB,OAAO;wBAAE,KAAK;oBAAU,GAAG,SAAS,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;gBACnF,KAAK;oBACH,OAAO,uBAAuB,OAAO;wBAAE,KAAK;oBAAU,GAAG,SAAS,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE;gBACrF,wBAAwB;gBACxB,KAAK;oBACH,SAAS;oBACT,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO;gBAC5B,KAAK;oBACH,cAAc;oBACd,OAAO,QAAQ,SAAS;gBAC1B,KAAK;oBACH,iBAAiB;oBACjB,OAAO,QAAQ,QAAQ;gBACzB,KAAK;oBACH,WAAW;oBACX,OAAO,QAAQ,UAAU;gBAC3B,oBAAoB;gBACpB,KAAK;oBACH,SAAS;oBACT,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO;gBAC5B,KAAK;oBACH,cAAc;oBACd,OAAO,QAAQ,SAAS;gBAC1B,KAAK;oBACH,iBAAiB;oBACjB,OAAO,QAAQ,QAAQ;gBACzB,KAAK;oBACH,WAAW;oBACX,OAAO,QAAQ,UAAU;gBAC3B,sBAAsB;gBACtB,KAAK;oBACH,SAAS;oBACT,OAAO,uBACH,OAAO;wBAAE,OAAO;wBAAW,KAAK;oBAAU,GAAG,WAC7C,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK;gBACvB,KAAK;oBACH,gCAAgC;oBAChC,OAAO,uBACH,OAAO;wBAAE,OAAO;wBAAW,KAAK;oBAAU,GAAG,WAC7C,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,EAAE;gBACzB,KAAK;oBACH,WAAW;oBACX,OAAO,MAAM,SAAS;gBACxB,KAAK;oBACH,eAAe;oBACf,OAAO,MAAM,QAAQ;gBACvB,KAAK;oBACH,SAAS;oBACT,OAAO,MAAM,UAAU;gBACzB,kBAAkB;gBAClB,KAAK;oBACH,SAAS;oBACT,OAAO,uBACH,OAAO;wBAAE,OAAO;oBAAU,GAAG,WAC7B,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK;gBACvB,KAAK;oBACH,UAAU;oBACV,OAAO,uBACH,OAAO;wBAAE,OAAO;oBAAU,GAAG,WAC7B,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,EAAE;gBACzB,KAAK;oBACH,WAAW;oBACX,OAAO,MAAM,SAAS;gBACxB,KAAK;oBACH,eAAe;oBACf,OAAO,MAAM,QAAQ;gBACvB,KAAK;oBACH,SAAS;oBACT,OAAO,MAAM,UAAU;gBACzB,QAAQ;gBACR,KAAK;oBACH,YAAY;oBACZ,OAAO,uBAAuB,OAAO;wBAAE,MAAM;oBAAU,GAAG,UAAU,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI;gBACtF,KAAK;oBACH,UAAU;oBACV,OAAO,uBACH,OAAO;wBAAE,MAAM;oBAAU,GAAG,UAC5B,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC,IAAI;gBAC7C,KAAK;oBACH,YAAY;oBACZ,OAAO,uBACH,OAAO;wBAAE,MAAM;oBAAU,GAAG,UAC5B,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE;gBACxB,KAAK;oBACH,cAAc;oBACd,OAAO,uBACH,OAAO;wBAAE,MAAM;oBAAU,GAAG,UAC5B,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE;gBACxB,OAAO;gBACP,KAAK;oBACH,UAAU;oBACV,OAAO,IAAI;gBACb,KAAK;oBACH,mBAAmB;oBACnB,OAAO,IAAI;gBACb,KAAK;oBACH,OAAO,IAAI;gBACb,KAAK;oBACH,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC,IAAI;gBACpD,KAAK;oBACH,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,QAAQ,EAAE;gBAC/B,KAAK;oBACH,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,UAAU;gBAC/B,KAAK;oBACH,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,UAAU,EAAE;gBACjC,KAAK;oBACH,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,eAAe;gBACpC,KAAK;oBACH,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,eAAe,EAAE;gBACtC,KAAK;oBACH,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC,IAAI;gBACzD,KAAK;oBACH,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,aAAa,EAAE;gBACpC,KAAK;oBACH,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO;gBAC5B,KAAK;oBACH,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,EAAE;gBAC9B,KAAK;oBACH,SAAS;oBACT,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO;gBAC5B,KAAK;oBACH,UAAU;oBACV,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,EAAE;gBAC9B,KAAK;oBACH,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG;gBACrC,KAAK;oBACH,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;gBACvB;oBACE,OAAO,WAAW;YACtB;QACF;QAEF,OAAO,gBAAgB,UAAU,WAAW,CAAC,MAAM;IACrD;IAEA,yBAAyB,GAAG,EAAE,GAAG,EAAE;QACjC,MAAM,eAAe,CAAC;YAClB,OAAQ,KAAK,CAAC,EAAE;gBACd,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT;oBACE,OAAO;YACX;QACF,GACA,gBAAgB,CAAC,SAAW,CAAC;gBAC3B,MAAM,SAAS,aAAa;gBAC5B,IAAI,QAAQ;oBACV,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,SAAS,MAAM,MAAM;gBAClD,OAAO;oBACL,OAAO;gBACT;YACF,GACA,SAAS,UAAU,WAAW,CAAC,MAC/B,aAAa,OAAO,MAAM,CACxB,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,GAAM,UAAU,QAAQ,MAAM,MAAM,CAAC,MAC7D,EAAE,GAEJ,YAAY,IAAI,OAAO,IAAI,WAAW,GAAG,CAAC,cAAc,MAAM,CAAC,CAAC,IAAM;QACxE,OAAO,gBAAgB,QAAQ,cAAc;IAC/C;AACF", "ignoreList": [0]}}, {"offset": {"line": 2896, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/luxon/src/impl/regexParser.js"], "sourcesContent": ["import {\n  untruncate<PERSON>ear,\n  signed<PERSON>ffset,\n  parseInteger,\n  parse<PERSON>illis,\n  isUndefined,\n  parseFloating,\n} from \"./util.js\";\nimport * as English from \"./english.js\";\nimport FixedOffsetZone from \"../zones/fixedOffsetZone.js\";\nimport IANAZone from \"../zones/IANAZone.js\";\n\n/*\n * This file handles parsing for well-specified formats. Here's how it works:\n * Two things go into parsing: a regex to match with and an extractor to take apart the groups in the match.\n * An extractor is just a function that takes a regex match array and returns a { year: ..., month: ... } object\n * parse() does the work of executing the regex and applying the extractor. It takes multiple regex/extractor pairs to try in sequence.\n * Extractors can take a \"cursor\" representing the offset in the match to look at. This makes it easy to combine extractors.\n * combineExtractors() does the work of combining them, keeping track of the cursor through multiple extractions.\n * Some extractions are super dumb and simpleParse and fromStrings help DRY them.\n */\n\nconst ianaRegex = /[A-Za-z_+-]{1,256}(?::?\\/[A-Za-z0-9_+-]{1,256}(?:\\/[A-Za-z0-9_+-]{1,256})?)?/;\n\nfunction combineRegexes(...regexes) {\n  const full = regexes.reduce((f, r) => f + r.source, \"\");\n  return RegExp(`^${full}$`);\n}\n\nfunction combineExtractors(...extractors) {\n  return (m) =>\n    extractors\n      .reduce(\n        ([mergedVals, mergedZone, cursor], ex) => {\n          const [val, zone, next] = ex(m, cursor);\n          return [{ ...mergedVals, ...val }, zone || mergedZone, next];\n        },\n        [{}, null, 1]\n      )\n      .slice(0, 2);\n}\n\nfunction parse(s, ...patterns) {\n  if (s == null) {\n    return [null, null];\n  }\n\n  for (const [regex, extractor] of patterns) {\n    const m = regex.exec(s);\n    if (m) {\n      return extractor(m);\n    }\n  }\n  return [null, null];\n}\n\nfunction simpleParse(...keys) {\n  return (match, cursor) => {\n    const ret = {};\n    let i;\n\n    for (i = 0; i < keys.length; i++) {\n      ret[keys[i]] = parseInteger(match[cursor + i]);\n    }\n    return [ret, null, cursor + i];\n  };\n}\n\n// ISO and SQL parsing\nconst offsetRegex = /(?:(Z)|([+-]\\d\\d)(?::?(\\d\\d))?)/;\nconst isoExtendedZone = `(?:${offsetRegex.source}?(?:\\\\[(${ianaRegex.source})\\\\])?)?`;\nconst isoTimeBaseRegex = /(\\d\\d)(?::?(\\d\\d)(?::?(\\d\\d)(?:[.,](\\d{1,30}))?)?)?/;\nconst isoTimeRegex = RegExp(`${isoTimeBaseRegex.source}${isoExtendedZone}`);\nconst isoTimeExtensionRegex = RegExp(`(?:T${isoTimeRegex.source})?`);\nconst isoYmdRegex = /([+-]\\d{6}|\\d{4})(?:-?(\\d\\d)(?:-?(\\d\\d))?)?/;\nconst isoWeekRegex = /(\\d{4})-?W(\\d\\d)(?:-?(\\d))?/;\nconst isoOrdinalRegex = /(\\d{4})-?(\\d{3})/;\nconst extractISOWeekData = simpleParse(\"weekYear\", \"weekNumber\", \"weekDay\");\nconst extractISOOrdinalData = simpleParse(\"year\", \"ordinal\");\nconst sqlYmdRegex = /(\\d{4})-(\\d\\d)-(\\d\\d)/; // dumbed-down version of the ISO one\nconst sqlTimeRegex = RegExp(\n  `${isoTimeBaseRegex.source} ?(?:${offsetRegex.source}|(${ianaRegex.source}))?`\n);\nconst sqlTimeExtensionRegex = RegExp(`(?: ${sqlTimeRegex.source})?`);\n\nfunction int(match, pos, fallback) {\n  const m = match[pos];\n  return isUndefined(m) ? fallback : parseInteger(m);\n}\n\nfunction extractISOYmd(match, cursor) {\n  const item = {\n    year: int(match, cursor),\n    month: int(match, cursor + 1, 1),\n    day: int(match, cursor + 2, 1),\n  };\n\n  return [item, null, cursor + 3];\n}\n\nfunction extractISOTime(match, cursor) {\n  const item = {\n    hours: int(match, cursor, 0),\n    minutes: int(match, cursor + 1, 0),\n    seconds: int(match, cursor + 2, 0),\n    milliseconds: parseMillis(match[cursor + 3]),\n  };\n\n  return [item, null, cursor + 4];\n}\n\nfunction extractISOOffset(match, cursor) {\n  const local = !match[cursor] && !match[cursor + 1],\n    fullOffset = signedOffset(match[cursor + 1], match[cursor + 2]),\n    zone = local ? null : FixedOffsetZone.instance(fullOffset);\n  return [{}, zone, cursor + 3];\n}\n\nfunction extractIANAZone(match, cursor) {\n  const zone = match[cursor] ? IANAZone.create(match[cursor]) : null;\n  return [{}, zone, cursor + 1];\n}\n\n// ISO time parsing\n\nconst isoTimeOnly = RegExp(`^T?${isoTimeBaseRegex.source}$`);\n\n// ISO duration parsing\n\nconst isoDuration =\n  /^-?P(?:(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)Y)?(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)M)?(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)W)?(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)D)?(?:T(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)H)?(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)M)?(?:(-?\\d{1,20})(?:[.,](-?\\d{1,20}))?S)?)?)$/;\n\nfunction extractISODuration(match) {\n  const [s, yearStr, monthStr, weekStr, dayStr, hourStr, minuteStr, secondStr, millisecondsStr] =\n    match;\n\n  const hasNegativePrefix = s[0] === \"-\";\n  const negativeSeconds = secondStr && secondStr[0] === \"-\";\n\n  const maybeNegate = (num, force = false) =>\n    num !== undefined && (force || (num && hasNegativePrefix)) ? -num : num;\n\n  return [\n    {\n      years: maybeNegate(parseFloating(yearStr)),\n      months: maybeNegate(parseFloating(monthStr)),\n      weeks: maybeNegate(parseFloating(weekStr)),\n      days: maybeNegate(parseFloating(dayStr)),\n      hours: maybeNegate(parseFloating(hourStr)),\n      minutes: maybeNegate(parseFloating(minuteStr)),\n      seconds: maybeNegate(parseFloating(secondStr), secondStr === \"-0\"),\n      milliseconds: maybeNegate(parseMillis(millisecondsStr), negativeSeconds),\n    },\n  ];\n}\n\n// These are a little braindead. EDT *should* tell us that we're in, say, America/New_York\n// and not just that we're in -240 *right now*. But since I don't think these are used that often\n// I'm just going to ignore that\nconst obsOffsets = {\n  GMT: 0,\n  EDT: -4 * 60,\n  EST: -5 * 60,\n  CDT: -5 * 60,\n  CST: -6 * 60,\n  MDT: -6 * 60,\n  MST: -7 * 60,\n  PDT: -7 * 60,\n  PST: -8 * 60,\n};\n\nfunction fromStrings(weekdayStr, yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr) {\n  const result = {\n    year: yearStr.length === 2 ? untruncateYear(parseInteger(yearStr)) : parseInteger(yearStr),\n    month: English.monthsShort.indexOf(monthStr) + 1,\n    day: parseInteger(dayStr),\n    hour: parseInteger(hourStr),\n    minute: parseInteger(minuteStr),\n  };\n\n  if (secondStr) result.second = parseInteger(secondStr);\n  if (weekdayStr) {\n    result.weekday =\n      weekdayStr.length > 3\n        ? English.weekdaysLong.indexOf(weekdayStr) + 1\n        : English.weekdaysShort.indexOf(weekdayStr) + 1;\n  }\n\n  return result;\n}\n\n// RFC 2822/5322\nconst rfc2822 =\n  /^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\\s)?(\\d{1,2})\\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\\s(\\d{2,4})\\s(\\d\\d):(\\d\\d)(?::(\\d\\d))?\\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\\d\\d)(\\d\\d)))$/;\n\nfunction extractRFC2822(match) {\n  const [\n      ,\n      weekdayStr,\n      dayStr,\n      monthStr,\n      yearStr,\n      hourStr,\n      minuteStr,\n      secondStr,\n      obsOffset,\n      milOffset,\n      offHourStr,\n      offMinuteStr,\n    ] = match,\n    result = fromStrings(weekdayStr, yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr);\n\n  let offset;\n  if (obsOffset) {\n    offset = obsOffsets[obsOffset];\n  } else if (milOffset) {\n    offset = 0;\n  } else {\n    offset = signedOffset(offHourStr, offMinuteStr);\n  }\n\n  return [result, new FixedOffsetZone(offset)];\n}\n\nfunction preprocessRFC2822(s) {\n  // Remove comments and folding whitespace and replace multiple-spaces with a single space\n  return s\n    .replace(/\\([^()]*\\)|[\\n\\t]/g, \" \")\n    .replace(/(\\s\\s+)/g, \" \")\n    .trim();\n}\n\n// http date\n\nconst rfc1123 =\n    /^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\\d\\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\\d{4}) (\\d\\d):(\\d\\d):(\\d\\d) GMT$/,\n  rfc850 =\n    /^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\\d\\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\\d\\d) (\\d\\d):(\\d\\d):(\\d\\d) GMT$/,\n  ascii =\n    /^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \\d|\\d\\d) (\\d\\d):(\\d\\d):(\\d\\d) (\\d{4})$/;\n\nfunction extractRFC1123Or850(match) {\n  const [, weekdayStr, dayStr, monthStr, yearStr, hourStr, minuteStr, secondStr] = match,\n    result = fromStrings(weekdayStr, yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr);\n  return [result, FixedOffsetZone.utcInstance];\n}\n\nfunction extractASCII(match) {\n  const [, weekdayStr, monthStr, dayStr, hourStr, minuteStr, secondStr, yearStr] = match,\n    result = fromStrings(weekdayStr, yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr);\n  return [result, FixedOffsetZone.utcInstance];\n}\n\nconst isoYmdWithTimeExtensionRegex = combineRegexes(isoYmdRegex, isoTimeExtensionRegex);\nconst isoWeekWithTimeExtensionRegex = combineRegexes(isoWeekRegex, isoTimeExtensionRegex);\nconst isoOrdinalWithTimeExtensionRegex = combineRegexes(isoOrdinalRegex, isoTimeExtensionRegex);\nconst isoTimeCombinedRegex = combineRegexes(isoTimeRegex);\n\nconst extractISOYmdTimeAndOffset = combineExtractors(\n  extractISOYmd,\n  extractISOTime,\n  extractISOOffset,\n  extractIANAZone\n);\nconst extractISOWeekTimeAndOffset = combineExtractors(\n  extractISOWeekData,\n  extractISOTime,\n  extractISOOffset,\n  extractIANAZone\n);\nconst extractISOOrdinalDateAndTime = combineExtractors(\n  extractISOOrdinalData,\n  extractISOTime,\n  extractISOOffset,\n  extractIANAZone\n);\nconst extractISOTimeAndOffset = combineExtractors(\n  extractISOTime,\n  extractISOOffset,\n  extractIANAZone\n);\n\n/*\n * @private\n */\n\nexport function parseISODate(s) {\n  return parse(\n    s,\n    [isoYmdWithTimeExtensionRegex, extractISOYmdTimeAndOffset],\n    [isoWeekWithTimeExtensionRegex, extractISOWeekTimeAndOffset],\n    [isoOrdinalWithTimeExtensionRegex, extractISOOrdinalDateAndTime],\n    [isoTimeCombinedRegex, extractISOTimeAndOffset]\n  );\n}\n\nexport function parseRFC2822Date(s) {\n  return parse(preprocessRFC2822(s), [rfc2822, extractRFC2822]);\n}\n\nexport function parseHTTPDate(s) {\n  return parse(\n    s,\n    [rfc1123, extractRFC1123Or850],\n    [rfc850, extractRFC1123Or850],\n    [ascii, extractASCII]\n  );\n}\n\nexport function parseISODuration(s) {\n  return parse(s, [isoDuration, extractISODuration]);\n}\n\nconst extractISOTimeOnly = combineExtractors(extractISOTime);\n\nexport function parseISOTimeOnly(s) {\n  return parse(s, [isoTimeOnly, extractISOTimeOnly]);\n}\n\nconst sqlYmdWithTimeExtensionRegex = combineRegexes(sqlYmdRegex, sqlTimeExtensionRegex);\nconst sqlTimeCombinedRegex = combineRegexes(sqlTimeRegex);\n\nconst extractISOTimeOffsetAndIANAZone = combineExtractors(\n  extractISOTime,\n  extractISOOffset,\n  extractIANAZone\n);\n\nexport function parseSQL(s) {\n  return parse(\n    s,\n    [sqlYmdWithTimeExtensionRegex, extractISOYmdTimeAndOffset],\n    [sqlTimeCombinedRegex, extractISOTimeOffsetAndIANAZone]\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAQA;AACA;AACA;;;;;AAEA;;;;;;;;CAQC,GAED,MAAM,YAAY;AAElB,SAAS,eAAe,GAAG,OAAO;IAChC,MAAM,OAAO,QAAQ,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,EAAE,MAAM,EAAE;IACpD,OAAO,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AAC3B;AAEA,SAAS,kBAAkB,GAAG,UAAU;IACtC,OAAO,CAAC,IACN,WACG,MAAM,CACL,CAAC,CAAC,YAAY,YAAY,OAAO,EAAE;YACjC,MAAM,CAAC,KAAK,MAAM,KAAK,GAAG,GAAG,GAAG;YAChC,OAAO;gBAAC;oBAAE,GAAG,UAAU;oBAAE,GAAG,GAAG;gBAAC;gBAAG,QAAQ;gBAAY;aAAK;QAC9D,GACA;YAAC,CAAC;YAAG;YAAM;SAAE,EAEd,KAAK,CAAC,GAAG;AAChB;AAEA,SAAS,MAAM,CAAC,EAAE,GAAG,QAAQ;IAC3B,IAAI,KAAK,MAAM;QACb,OAAO;YAAC;YAAM;SAAK;IACrB;IAEA,KAAK,MAAM,CAAC,OAAO,UAAU,IAAI,SAAU;QACzC,MAAM,IAAI,MAAM,IAAI,CAAC;QACrB,IAAI,GAAG;YACL,OAAO,UAAU;QACnB;IACF;IACA,OAAO;QAAC;QAAM;KAAK;AACrB;AAEA,SAAS,YAAY,GAAG,IAAI;IAC1B,OAAO,CAAC,OAAO;QACb,MAAM,MAAM,CAAC;QACb,IAAI;QAEJ,IAAK,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YAChC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,KAAK,CAAC,SAAS,EAAE;QAC/C;QACA,OAAO;YAAC;YAAK;YAAM,SAAS;SAAE;IAChC;AACF;AAEA,sBAAsB;AACtB,MAAM,cAAc;AACpB,MAAM,kBAAkB,CAAC,GAAG,EAAE,YAAY,MAAM,CAAC,QAAQ,EAAE,UAAU,MAAM,CAAC,QAAQ,CAAC;AACrF,MAAM,mBAAmB;AACzB,MAAM,eAAe,OAAO,GAAG,iBAAiB,MAAM,GAAG,iBAAiB;AAC1E,MAAM,wBAAwB,OAAO,CAAC,IAAI,EAAE,aAAa,MAAM,CAAC,EAAE,CAAC;AACnE,MAAM,cAAc;AACpB,MAAM,eAAe;AACrB,MAAM,kBAAkB;AACxB,MAAM,qBAAqB,YAAY,YAAY,cAAc;AACjE,MAAM,wBAAwB,YAAY,QAAQ;AAClD,MAAM,cAAc,yBAAyB,qCAAqC;AAClF,MAAM,eAAe,OACnB,GAAG,iBAAiB,MAAM,CAAC,KAAK,EAAE,YAAY,MAAM,CAAC,EAAE,EAAE,UAAU,MAAM,CAAC,GAAG,CAAC;AAEhF,MAAM,wBAAwB,OAAO,CAAC,IAAI,EAAE,aAAa,MAAM,CAAC,EAAE,CAAC;AAEnE,SAAS,IAAI,KAAK,EAAE,GAAG,EAAE,QAAQ;IAC/B,MAAM,IAAI,KAAK,CAAC,IAAI;IACpB,OAAO,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,KAAK,WAAW,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE;AAClD;AAEA,SAAS,cAAc,KAAK,EAAE,MAAM;IAClC,MAAM,OAAO;QACX,MAAM,IAAI,OAAO;QACjB,OAAO,IAAI,OAAO,SAAS,GAAG;QAC9B,KAAK,IAAI,OAAO,SAAS,GAAG;IAC9B;IAEA,OAAO;QAAC;QAAM;QAAM,SAAS;KAAE;AACjC;AAEA,SAAS,eAAe,KAAK,EAAE,MAAM;IACnC,MAAM,OAAO;QACX,OAAO,IAAI,OAAO,QAAQ;QAC1B,SAAS,IAAI,OAAO,SAAS,GAAG;QAChC,SAAS,IAAI,OAAO,SAAS,GAAG;QAChC,cAAc,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,KAAK,CAAC,SAAS,EAAE;IAC7C;IAEA,OAAO;QAAC;QAAM;QAAM,SAAS;KAAE;AACjC;AAEA,SAAS,iBAAiB,KAAK,EAAE,MAAM;IACrC,MAAM,QAAQ,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,EAChD,aAAa,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,CAAC,SAAS,EAAE,GAC9D,OAAO,QAAQ,OAAO,gKAAA,CAAA,UAAe,CAAC,QAAQ,CAAC;IACjD,OAAO;QAAC,CAAC;QAAG;QAAM,SAAS;KAAE;AAC/B;AAEA,SAAS,gBAAgB,KAAK,EAAE,MAAM;IACpC,MAAM,OAAO,KAAK,CAAC,OAAO,GAAG,yJAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,IAAI;IAC9D,OAAO;QAAC,CAAC;QAAG;QAAM,SAAS;KAAE;AAC/B;AAEA,mBAAmB;AAEnB,MAAM,cAAc,OAAO,CAAC,GAAG,EAAE,iBAAiB,MAAM,CAAC,CAAC,CAAC;AAE3D,uBAAuB;AAEvB,MAAM,cACJ;AAEF,SAAS,mBAAmB,KAAK;IAC/B,MAAM,CAAC,GAAG,SAAS,UAAU,SAAS,QAAQ,SAAS,WAAW,WAAW,gBAAgB,GAC3F;IAEF,MAAM,oBAAoB,CAAC,CAAC,EAAE,KAAK;IACnC,MAAM,kBAAkB,aAAa,SAAS,CAAC,EAAE,KAAK;IAEtD,MAAM,cAAc,CAAC,KAAK,QAAQ,KAAK,GACrC,QAAQ,aAAa,CAAC,SAAU,OAAO,iBAAkB,IAAI,CAAC,MAAM;IAEtE,OAAO;QACL;YACE,OAAO,YAAY,CAAA,GAAA,oJAAA,CAAA,gBAAa,AAAD,EAAE;YACjC,QAAQ,YAAY,CAAA,GAAA,oJAAA,CAAA,gBAAa,AAAD,EAAE;YAClC,OAAO,YAAY,CAAA,GAAA,oJAAA,CAAA,gBAAa,AAAD,EAAE;YACjC,MAAM,YAAY,CAAA,GAAA,oJAAA,CAAA,gBAAa,AAAD,EAAE;YAChC,OAAO,YAAY,CAAA,GAAA,oJAAA,CAAA,gBAAa,AAAD,EAAE;YACjC,SAAS,YAAY,CAAA,GAAA,oJAAA,CAAA,gBAAa,AAAD,EAAE;YACnC,SAAS,YAAY,CAAA,GAAA,oJAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,cAAc;YAC7D,cAAc,YAAY,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,kBAAkB;QAC1D;KACD;AACH;AAEA,0FAA0F;AAC1F,iGAAiG;AACjG,gCAAgC;AAChC,MAAM,aAAa;IACjB,KAAK;IACL,KAAK,CAAC,IAAI;IACV,KAAK,CAAC,IAAI;IACV,KAAK,CAAC,IAAI;IACV,KAAK,CAAC,IAAI;IACV,KAAK,CAAC,IAAI;IACV,KAAK,CAAC,IAAI;IACV,KAAK,CAAC,IAAI;IACV,KAAK,CAAC,IAAI;AACZ;AAEA,SAAS,YAAY,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS;IACvF,MAAM,SAAS;QACb,MAAM,QAAQ,MAAM,KAAK,IAAI,CAAA,GAAA,oJAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,YAAY,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE;QAClF,OAAO,uJAAA,CAAA,cAAmB,CAAC,OAAO,CAAC,YAAY;QAC/C,KAAK,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE;QAClB,MAAM,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE;QACnB,QAAQ,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE;IACvB;IAEA,IAAI,WAAW,OAAO,MAAM,GAAG,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE;IAC5C,IAAI,YAAY;QACd,OAAO,OAAO,GACZ,WAAW,MAAM,GAAG,IAChB,uJAAA,CAAA,eAAoB,CAAC,OAAO,CAAC,cAAc,IAC3C,uJAAA,CAAA,gBAAqB,CAAC,OAAO,CAAC,cAAc;IACpD;IAEA,OAAO;AACT;AAEA,gBAAgB;AAChB,MAAM,UACJ;AAEF,SAAS,eAAe,KAAK;IAC3B,MAAM,GAEF,YACA,QACA,UACA,SACA,SACA,WACA,WACA,WACA,WACA,YACA,aACD,GAAG,OACJ,SAAS,YAAY,YAAY,SAAS,UAAU,QAAQ,SAAS,WAAW;IAElF,IAAI;IACJ,IAAI,WAAW;QACb,SAAS,UAAU,CAAC,UAAU;IAChC,OAAO,IAAI,WAAW;QACpB,SAAS;IACX,OAAO;QACL,SAAS,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,YAAY;IACpC;IAEA,OAAO;QAAC;QAAQ,IAAI,gKAAA,CAAA,UAAe,CAAC;KAAQ;AAC9C;AAEA,SAAS,kBAAkB,CAAC;IAC1B,yFAAyF;IACzF,OAAO,EACJ,OAAO,CAAC,sBAAsB,KAC9B,OAAO,CAAC,YAAY,KACpB,IAAI;AACT;AAEA,YAAY;AAEZ,MAAM,UACF,8HACF,SACE,0JACF,QACE;AAEJ,SAAS,oBAAoB,KAAK;IAChC,MAAM,GAAG,YAAY,QAAQ,UAAU,SAAS,SAAS,WAAW,UAAU,GAAG,OAC/E,SAAS,YAAY,YAAY,SAAS,UAAU,QAAQ,SAAS,WAAW;IAClF,OAAO;QAAC;QAAQ,gKAAA,CAAA,UAAe,CAAC,WAAW;KAAC;AAC9C;AAEA,SAAS,aAAa,KAAK;IACzB,MAAM,GAAG,YAAY,UAAU,QAAQ,SAAS,WAAW,WAAW,QAAQ,GAAG,OAC/E,SAAS,YAAY,YAAY,SAAS,UAAU,QAAQ,SAAS,WAAW;IAClF,OAAO;QAAC;QAAQ,gKAAA,CAAA,UAAe,CAAC,WAAW;KAAC;AAC9C;AAEA,MAAM,+BAA+B,eAAe,aAAa;AACjE,MAAM,gCAAgC,eAAe,cAAc;AACnE,MAAM,mCAAmC,eAAe,iBAAiB;AACzE,MAAM,uBAAuB,eAAe;AAE5C,MAAM,6BAA6B,kBACjC,eACA,gBACA,kBACA;AAEF,MAAM,8BAA8B,kBAClC,oBACA,gBACA,kBACA;AAEF,MAAM,+BAA+B,kBACnC,uBACA,gBACA,kBACA;AAEF,MAAM,0BAA0B,kBAC9B,gBACA,kBACA;AAOK,SAAS,aAAa,CAAC;IAC5B,OAAO,MACL,GACA;QAAC;QAA8B;KAA2B,EAC1D;QAAC;QAA+B;KAA4B,EAC5D;QAAC;QAAkC;KAA6B,EAChE;QAAC;QAAsB;KAAwB;AAEnD;AAEO,SAAS,iBAAiB,CAAC;IAChC,OAAO,MAAM,kBAAkB,IAAI;QAAC;QAAS;KAAe;AAC9D;AAEO,SAAS,cAAc,CAAC;IAC7B,OAAO,MACL,GACA;QAAC;QAAS;KAAoB,EAC9B;QAAC;QAAQ;KAAoB,EAC7B;QAAC;QAAO;KAAa;AAEzB;AAEO,SAAS,iBAAiB,CAAC;IAChC,OAAO,MAAM,GAAG;QAAC;QAAa;KAAmB;AACnD;AAEA,MAAM,qBAAqB,kBAAkB;AAEtC,SAAS,iBAAiB,CAAC;IAChC,OAAO,MAAM,GAAG;QAAC;QAAa;KAAmB;AACnD;AAEA,MAAM,+BAA+B,eAAe,aAAa;AACjE,MAAM,uBAAuB,eAAe;AAE5C,MAAM,kCAAkC,kBACtC,gBACA,kBACA;AAGK,SAAS,SAAS,CAAC;IACxB,OAAO,MACL,GACA;QAAC;QAA8B;KAA2B,EAC1D;QAAC;QAAsB;KAAgC;AAE3D", "ignoreList": [0]}}, {"offset": {"line": 3192, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/luxon/src/duration.js"], "sourcesContent": ["import { InvalidArgumentError, InvalidDurationError, InvalidUnitError } from \"./errors.js\";\nimport Formatter from \"./impl/formatter.js\";\nimport Invalid from \"./impl/invalid.js\";\nimport Locale from \"./impl/locale.js\";\nimport { parseISODuration, parseISOTimeOnly } from \"./impl/regexParser.js\";\nimport {\n  asNumber,\n  hasOwnProperty,\n  isNumber,\n  isUndefined,\n  normalizeObject,\n  roundTo,\n} from \"./impl/util.js\";\nimport Settings from \"./settings.js\";\nimport DateTime from \"./datetime.js\";\n\nconst INVALID = \"Invalid Duration\";\n\n// unit conversion constants\nexport const lowOrderMatrix = {\n    weeks: {\n      days: 7,\n      hours: 7 * 24,\n      minutes: 7 * 24 * 60,\n      seconds: 7 * 24 * 60 * 60,\n      milliseconds: 7 * 24 * 60 * 60 * 1000,\n    },\n    days: {\n      hours: 24,\n      minutes: 24 * 60,\n      seconds: 24 * 60 * 60,\n      milliseconds: 24 * 60 * 60 * 1000,\n    },\n    hours: { minutes: 60, seconds: 60 * 60, milliseconds: 60 * 60 * 1000 },\n    minutes: { seconds: 60, milliseconds: 60 * 1000 },\n    seconds: { milliseconds: 1000 },\n  },\n  casualMatrix = {\n    years: {\n      quarters: 4,\n      months: 12,\n      weeks: 52,\n      days: 365,\n      hours: 365 * 24,\n      minutes: 365 * 24 * 60,\n      seconds: 365 * 24 * 60 * 60,\n      milliseconds: 365 * 24 * 60 * 60 * 1000,\n    },\n    quarters: {\n      months: 3,\n      weeks: 13,\n      days: 91,\n      hours: 91 * 24,\n      minutes: 91 * 24 * 60,\n      seconds: 91 * 24 * 60 * 60,\n      milliseconds: 91 * 24 * 60 * 60 * 1000,\n    },\n    months: {\n      weeks: 4,\n      days: 30,\n      hours: 30 * 24,\n      minutes: 30 * 24 * 60,\n      seconds: 30 * 24 * 60 * 60,\n      milliseconds: 30 * 24 * 60 * 60 * 1000,\n    },\n\n    ...lowOrderMatrix,\n  },\n  daysInYearAccurate = 146097.0 / 400,\n  daysInMonthAccurate = 146097.0 / 4800,\n  accurateMatrix = {\n    years: {\n      quarters: 4,\n      months: 12,\n      weeks: daysInYearAccurate / 7,\n      days: daysInYearAccurate,\n      hours: daysInYearAccurate * 24,\n      minutes: daysInYearAccurate * 24 * 60,\n      seconds: daysInYearAccurate * 24 * 60 * 60,\n      milliseconds: daysInYearAccurate * 24 * 60 * 60 * 1000,\n    },\n    quarters: {\n      months: 3,\n      weeks: daysInYearAccurate / 28,\n      days: daysInYearAccurate / 4,\n      hours: (daysInYearAccurate * 24) / 4,\n      minutes: (daysInYearAccurate * 24 * 60) / 4,\n      seconds: (daysInYearAccurate * 24 * 60 * 60) / 4,\n      milliseconds: (daysInYearAccurate * 24 * 60 * 60 * 1000) / 4,\n    },\n    months: {\n      weeks: daysInMonthAccurate / 7,\n      days: daysInMonthAccurate,\n      hours: daysInMonthAccurate * 24,\n      minutes: daysInMonthAccurate * 24 * 60,\n      seconds: daysInMonthAccurate * 24 * 60 * 60,\n      milliseconds: daysInMonthAccurate * 24 * 60 * 60 * 1000,\n    },\n    ...lowOrderMatrix,\n  };\n\n// units ordered by size\nconst orderedUnits = [\n  \"years\",\n  \"quarters\",\n  \"months\",\n  \"weeks\",\n  \"days\",\n  \"hours\",\n  \"minutes\",\n  \"seconds\",\n  \"milliseconds\",\n];\n\nconst reverseUnits = orderedUnits.slice(0).reverse();\n\n// clone really means \"create another instance just like this one, but with these changes\"\nfunction clone(dur, alts, clear = false) {\n  // deep merge for vals\n  const conf = {\n    values: clear ? alts.values : { ...dur.values, ...(alts.values || {}) },\n    loc: dur.loc.clone(alts.loc),\n    conversionAccuracy: alts.conversionAccuracy || dur.conversionAccuracy,\n    matrix: alts.matrix || dur.matrix,\n  };\n  return new Duration(conf);\n}\n\nfunction durationToMillis(matrix, vals) {\n  let sum = vals.milliseconds ?? 0;\n  for (const unit of reverseUnits.slice(1)) {\n    if (vals[unit]) {\n      sum += vals[unit] * matrix[unit][\"milliseconds\"];\n    }\n  }\n  return sum;\n}\n\n// NB: mutates parameters\nfunction normalizeValues(matrix, vals) {\n  // the logic below assumes the overall value of the duration is positive\n  // if this is not the case, factor is used to make it so\n  const factor = durationToMillis(matrix, vals) < 0 ? -1 : 1;\n\n  orderedUnits.reduceRight((previous, current) => {\n    if (!isUndefined(vals[current])) {\n      if (previous) {\n        const previousVal = vals[previous] * factor;\n        const conv = matrix[current][previous];\n\n        // if (previousVal < 0):\n        // lower order unit is negative (e.g. { years: 2, days: -2 })\n        // normalize this by reducing the higher order unit by the appropriate amount\n        // and increasing the lower order unit\n        // this can never make the higher order unit negative, because this function only operates\n        // on positive durations, so the amount of time represented by the lower order unit cannot\n        // be larger than the higher order unit\n        // else:\n        // lower order unit is positive (e.g. { years: 2, days: 450 } or { years: -2, days: 450 })\n        // in this case we attempt to convert as much as possible from the lower order unit into\n        // the higher order one\n        //\n        // Math.floor takes care of both of these cases, rounding away from 0\n        // if previousVal < 0 it makes the absolute value larger\n        // if previousVal >= it makes the absolute value smaller\n        const rollUp = Math.floor(previousVal / conv);\n        vals[current] += rollUp * factor;\n        vals[previous] -= rollUp * conv * factor;\n      }\n      return current;\n    } else {\n      return previous;\n    }\n  }, null);\n\n  // try to convert any decimals into smaller units if possible\n  // for example for { years: 2.5, days: 0, seconds: 0 } we want to get { years: 2, days: 182, hours: 12 }\n  orderedUnits.reduce((previous, current) => {\n    if (!isUndefined(vals[current])) {\n      if (previous) {\n        const fraction = vals[previous] % 1;\n        vals[previous] -= fraction;\n        vals[current] += fraction * matrix[previous][current];\n      }\n      return current;\n    } else {\n      return previous;\n    }\n  }, null);\n}\n\n// Remove all properties with a value of 0 from an object\nfunction removeZeroes(vals) {\n  const newVals = {};\n  for (const [key, value] of Object.entries(vals)) {\n    if (value !== 0) {\n      newVals[key] = value;\n    }\n  }\n  return newVals;\n}\n\n/**\n * A Duration object represents a period of time, like \"2 months\" or \"1 day, 1 hour\". Conceptually, it's just a map of units to their quantities, accompanied by some additional configuration and methods for creating, parsing, interrogating, transforming, and formatting them. They can be used on their own or in conjunction with other Luxon types; for example, you can use {@link DateTime#plus} to add a Duration object to a DateTime, producing another DateTime.\n *\n * Here is a brief overview of commonly used methods and getters in Duration:\n *\n * * **Creation** To create a Duration, use {@link Duration.fromMillis}, {@link Duration.fromObject}, or {@link Duration.fromISO}.\n * * **Unit values** See the {@link Duration#years}, {@link Duration#months}, {@link Duration#weeks}, {@link Duration#days}, {@link Duration#hours}, {@link Duration#minutes}, {@link Duration#seconds}, {@link Duration#milliseconds} accessors.\n * * **Configuration** See  {@link Duration#locale} and {@link Duration#numberingSystem} accessors.\n * * **Transformation** To create new Durations out of old ones use {@link Duration#plus}, {@link Duration#minus}, {@link Duration#normalize}, {@link Duration#set}, {@link Duration#reconfigure}, {@link Duration#shiftTo}, and {@link Duration#negate}.\n * * **Output** To convert the Duration into other representations, see {@link Duration#as}, {@link Duration#toISO}, {@link Duration#toFormat}, and {@link Duration#toJSON}\n *\n * There's are more methods documented below. In addition, for more information on subtler topics like internationalization and validity, see the external documentation.\n */\nexport default class Duration {\n  /**\n   * @private\n   */\n  constructor(config) {\n    const accurate = config.conversionAccuracy === \"longterm\" || false;\n    let matrix = accurate ? accurateMatrix : casualMatrix;\n\n    if (config.matrix) {\n      matrix = config.matrix;\n    }\n\n    /**\n     * @access private\n     */\n    this.values = config.values;\n    /**\n     * @access private\n     */\n    this.loc = config.loc || Locale.create();\n    /**\n     * @access private\n     */\n    this.conversionAccuracy = accurate ? \"longterm\" : \"casual\";\n    /**\n     * @access private\n     */\n    this.invalid = config.invalid || null;\n    /**\n     * @access private\n     */\n    this.matrix = matrix;\n    /**\n     * @access private\n     */\n    this.isLuxonDuration = true;\n  }\n\n  /**\n   * Create Duration from a number of milliseconds.\n   * @param {number} count of milliseconds\n   * @param {Object} opts - options for parsing\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @return {Duration}\n   */\n  static fromMillis(count, opts) {\n    return Duration.fromObject({ milliseconds: count }, opts);\n  }\n\n  /**\n   * Create a Duration from a JavaScript object with keys like 'years' and 'hours'.\n   * If this object is empty then a zero milliseconds duration is returned.\n   * @param {Object} obj - the object to create the DateTime from\n   * @param {number} obj.years\n   * @param {number} obj.quarters\n   * @param {number} obj.months\n   * @param {number} obj.weeks\n   * @param {number} obj.days\n   * @param {number} obj.hours\n   * @param {number} obj.minutes\n   * @param {number} obj.seconds\n   * @param {number} obj.milliseconds\n   * @param {Object} [opts=[]] - options for creating this Duration\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the preset conversion system to use\n   * @param {string} [opts.matrix=Object] - the custom conversion system to use\n   * @return {Duration}\n   */\n  static fromObject(obj, opts = {}) {\n    if (obj == null || typeof obj !== \"object\") {\n      throw new InvalidArgumentError(\n        `Duration.fromObject: argument expected to be an object, got ${\n          obj === null ? \"null\" : typeof obj\n        }`\n      );\n    }\n\n    return new Duration({\n      values: normalizeObject(obj, Duration.normalizeUnit),\n      loc: Locale.fromObject(opts),\n      conversionAccuracy: opts.conversionAccuracy,\n      matrix: opts.matrix,\n    });\n  }\n\n  /**\n   * Create a Duration from DurationLike.\n   *\n   * @param {Object | number | Duration} durationLike\n   * One of:\n   * - object with keys like 'years' and 'hours'.\n   * - number representing milliseconds\n   * - Duration instance\n   * @return {Duration}\n   */\n  static fromDurationLike(durationLike) {\n    if (isNumber(durationLike)) {\n      return Duration.fromMillis(durationLike);\n    } else if (Duration.isDuration(durationLike)) {\n      return durationLike;\n    } else if (typeof durationLike === \"object\") {\n      return Duration.fromObject(durationLike);\n    } else {\n      throw new InvalidArgumentError(\n        `Unknown duration argument ${durationLike} of type ${typeof durationLike}`\n      );\n    }\n  }\n\n  /**\n   * Create a Duration from an ISO 8601 duration string.\n   * @param {string} text - text to parse\n   * @param {Object} opts - options for parsing\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the preset conversion system to use\n   * @param {string} [opts.matrix=Object] - the preset conversion system to use\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Durations\n   * @example Duration.fromISO('P3Y6M1W4DT12H30M5S').toObject() //=> { years: 3, months: 6, weeks: 1, days: 4, hours: 12, minutes: 30, seconds: 5 }\n   * @example Duration.fromISO('PT23H').toObject() //=> { hours: 23 }\n   * @example Duration.fromISO('P5Y3M').toObject() //=> { years: 5, months: 3 }\n   * @return {Duration}\n   */\n  static fromISO(text, opts) {\n    const [parsed] = parseISODuration(text);\n    if (parsed) {\n      return Duration.fromObject(parsed, opts);\n    } else {\n      return Duration.invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ISO 8601`);\n    }\n  }\n\n  /**\n   * Create a Duration from an ISO 8601 time string.\n   * @param {string} text - text to parse\n   * @param {Object} opts - options for parsing\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the preset conversion system to use\n   * @param {string} [opts.matrix=Object] - the conversion system to use\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Times\n   * @example Duration.fromISOTime('11:22:33.444').toObject() //=> { hours: 11, minutes: 22, seconds: 33, milliseconds: 444 }\n   * @example Duration.fromISOTime('11:00').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @example Duration.fromISOTime('T11:00').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @example Duration.fromISOTime('1100').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @example Duration.fromISOTime('T1100').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @return {Duration}\n   */\n  static fromISOTime(text, opts) {\n    const [parsed] = parseISOTimeOnly(text);\n    if (parsed) {\n      return Duration.fromObject(parsed, opts);\n    } else {\n      return Duration.invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ISO 8601`);\n    }\n  }\n\n  /**\n   * Create an invalid Duration.\n   * @param {string} reason - simple string of why this datetime is invalid. Should not contain parameters or anything else data-dependent\n   * @param {string} [explanation=null] - longer explanation, may include parameters and other useful debugging information\n   * @return {Duration}\n   */\n  static invalid(reason, explanation = null) {\n    if (!reason) {\n      throw new InvalidArgumentError(\"need to specify a reason the Duration is invalid\");\n    }\n\n    const invalid = reason instanceof Invalid ? reason : new Invalid(reason, explanation);\n\n    if (Settings.throwOnInvalid) {\n      throw new InvalidDurationError(invalid);\n    } else {\n      return new Duration({ invalid });\n    }\n  }\n\n  /**\n   * @private\n   */\n  static normalizeUnit(unit) {\n    const normalized = {\n      year: \"years\",\n      years: \"years\",\n      quarter: \"quarters\",\n      quarters: \"quarters\",\n      month: \"months\",\n      months: \"months\",\n      week: \"weeks\",\n      weeks: \"weeks\",\n      day: \"days\",\n      days: \"days\",\n      hour: \"hours\",\n      hours: \"hours\",\n      minute: \"minutes\",\n      minutes: \"minutes\",\n      second: \"seconds\",\n      seconds: \"seconds\",\n      millisecond: \"milliseconds\",\n      milliseconds: \"milliseconds\",\n    }[unit ? unit.toLowerCase() : unit];\n\n    if (!normalized) throw new InvalidUnitError(unit);\n\n    return normalized;\n  }\n\n  /**\n   * Check if an object is a Duration. Works across context boundaries\n   * @param {object} o\n   * @return {boolean}\n   */\n  static isDuration(o) {\n    return (o && o.isLuxonDuration) || false;\n  }\n\n  /**\n   * Get  the locale of a Duration, such 'en-GB'\n   * @type {string}\n   */\n  get locale() {\n    return this.isValid ? this.loc.locale : null;\n  }\n\n  /**\n   * Get the numbering system of a Duration, such 'beng'. The numbering system is used when formatting the Duration\n   *\n   * @type {string}\n   */\n  get numberingSystem() {\n    return this.isValid ? this.loc.numberingSystem : null;\n  }\n\n  /**\n   * Returns a string representation of this Duration formatted according to the specified format string. You may use these tokens:\n   * * `S` for milliseconds\n   * * `s` for seconds\n   * * `m` for minutes\n   * * `h` for hours\n   * * `d` for days\n   * * `w` for weeks\n   * * `M` for months\n   * * `y` for years\n   * Notes:\n   * * Add padding by repeating the token, e.g. \"yy\" pads the years to two digits, \"hhhh\" pads the hours out to four digits\n   * * Tokens can be escaped by wrapping with single quotes.\n   * * The duration will be converted to the set of units in the format string using {@link Duration#shiftTo} and the Durations's conversion accuracy setting.\n   * @param {string} fmt - the format string\n   * @param {Object} opts - options\n   * @param {boolean} [opts.floor=true] - floor numerical values\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toFormat(\"y d s\") //=> \"1 6 2\"\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toFormat(\"yy dd sss\") //=> \"01 06 002\"\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toFormat(\"M S\") //=> \"12 518402000\"\n   * @return {string}\n   */\n  toFormat(fmt, opts = {}) {\n    // reverse-compat since 1.2; we always round down now, never up, and we do it by default\n    const fmtOpts = {\n      ...opts,\n      floor: opts.round !== false && opts.floor !== false,\n    };\n    return this.isValid\n      ? Formatter.create(this.loc, fmtOpts).formatDurationFromString(this, fmt)\n      : INVALID;\n  }\n\n  /**\n   * Returns a string representation of a Duration with all units included.\n   * To modify its behavior, use `listStyle` and any Intl.NumberFormat option, though `unitDisplay` is especially relevant.\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/NumberFormat/NumberFormat#options\n   * @param {Object} opts - Formatting options. Accepts the same keys as the options parameter of the native `Intl.NumberFormat` constructor, as well as `listStyle`.\n   * @param {string} [opts.listStyle='narrow'] - How to format the merged list. Corresponds to the `style` property of the options parameter of the native `Intl.ListFormat` constructor.\n   * @example\n   * ```js\n   * var dur = Duration.fromObject({ days: 1, hours: 5, minutes: 6 })\n   * dur.toHuman() //=> '1 day, 5 hours, 6 minutes'\n   * dur.toHuman({ listStyle: \"long\" }) //=> '1 day, 5 hours, and 6 minutes'\n   * dur.toHuman({ unitDisplay: \"short\" }) //=> '1 day, 5 hr, 6 min'\n   * ```\n   */\n  toHuman(opts = {}) {\n    if (!this.isValid) return INVALID;\n\n    const l = orderedUnits\n      .map((unit) => {\n        const val = this.values[unit];\n        if (isUndefined(val)) {\n          return null;\n        }\n        return this.loc\n          .numberFormatter({ style: \"unit\", unitDisplay: \"long\", ...opts, unit: unit.slice(0, -1) })\n          .format(val);\n      })\n      .filter((n) => n);\n\n    return this.loc\n      .listFormatter({ type: \"conjunction\", style: opts.listStyle || \"narrow\", ...opts })\n      .format(l);\n  }\n\n  /**\n   * Returns a JavaScript object with this Duration's values.\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toObject() //=> { years: 1, days: 6, seconds: 2 }\n   * @return {Object}\n   */\n  toObject() {\n    if (!this.isValid) return {};\n    return { ...this.values };\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this Duration.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Durations\n   * @example Duration.fromObject({ years: 3, seconds: 45 }).toISO() //=> 'P3YT45S'\n   * @example Duration.fromObject({ months: 4, seconds: 45 }).toISO() //=> 'P4MT45S'\n   * @example Duration.fromObject({ months: 5 }).toISO() //=> 'P5M'\n   * @example Duration.fromObject({ minutes: 5 }).toISO() //=> 'PT5M'\n   * @example Duration.fromObject({ milliseconds: 6 }).toISO() //=> 'PT0.006S'\n   * @return {string}\n   */\n  toISO() {\n    // we could use the formatter, but this is an easier way to get the minimum string\n    if (!this.isValid) return null;\n\n    let s = \"P\";\n    if (this.years !== 0) s += this.years + \"Y\";\n    if (this.months !== 0 || this.quarters !== 0) s += this.months + this.quarters * 3 + \"M\";\n    if (this.weeks !== 0) s += this.weeks + \"W\";\n    if (this.days !== 0) s += this.days + \"D\";\n    if (this.hours !== 0 || this.minutes !== 0 || this.seconds !== 0 || this.milliseconds !== 0)\n      s += \"T\";\n    if (this.hours !== 0) s += this.hours + \"H\";\n    if (this.minutes !== 0) s += this.minutes + \"M\";\n    if (this.seconds !== 0 || this.milliseconds !== 0)\n      // this will handle \"floating point madness\" by removing extra decimal places\n      // https://stackoverflow.com/questions/588004/is-floating-point-math-broken\n      s += roundTo(this.seconds + this.milliseconds / 1000, 3) + \"S\";\n    if (s === \"P\") s += \"T0S\";\n    return s;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this Duration, formatted as a time of day.\n   * Note that this will return null if the duration is invalid, negative, or equal to or greater than 24 hours.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Times\n   * @param {Object} opts - options\n   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0\n   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0\n   * @param {boolean} [opts.includePrefix=false] - include the `T` prefix\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example Duration.fromObject({ hours: 11 }).toISOTime() //=> '11:00:00.000'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ suppressMilliseconds: true }) //=> '11:00:00'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ suppressSeconds: true }) //=> '11:00'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ includePrefix: true }) //=> 'T11:00:00.000'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ format: 'basic' }) //=> '110000.000'\n   * @return {string}\n   */\n  toISOTime(opts = {}) {\n    if (!this.isValid) return null;\n\n    const millis = this.toMillis();\n    if (millis < 0 || millis >= 86400000) return null;\n\n    opts = {\n      suppressMilliseconds: false,\n      suppressSeconds: false,\n      includePrefix: false,\n      format: \"extended\",\n      ...opts,\n      includeOffset: false,\n    };\n\n    const dateTime = DateTime.fromMillis(millis, { zone: \"UTC\" });\n    return dateTime.toISOTime(opts);\n  }\n\n  /**\n   * Returns an ISO 8601 representation of this Duration appropriate for use in JSON.\n   * @return {string}\n   */\n  toJSON() {\n    return this.toISO();\n  }\n\n  /**\n   * Returns an ISO 8601 representation of this Duration appropriate for use in debugging.\n   * @return {string}\n   */\n  toString() {\n    return this.toISO();\n  }\n\n  /**\n   * Returns a string representation of this Duration appropriate for the REPL.\n   * @return {string}\n   */\n  [Symbol.for(\"nodejs.util.inspect.custom\")]() {\n    if (this.isValid) {\n      return `Duration { values: ${JSON.stringify(this.values)} }`;\n    } else {\n      return `Duration { Invalid, reason: ${this.invalidReason} }`;\n    }\n  }\n\n  /**\n   * Returns an milliseconds value of this Duration.\n   * @return {number}\n   */\n  toMillis() {\n    if (!this.isValid) return NaN;\n\n    return durationToMillis(this.matrix, this.values);\n  }\n\n  /**\n   * Returns an milliseconds value of this Duration. Alias of {@link toMillis}\n   * @return {number}\n   */\n  valueOf() {\n    return this.toMillis();\n  }\n\n  /**\n   * Make this Duration longer by the specified amount. Return a newly-constructed Duration.\n   * @param {Duration|Object|number} duration - The amount to add. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   * @return {Duration}\n   */\n  plus(duration) {\n    if (!this.isValid) return this;\n\n    const dur = Duration.fromDurationLike(duration),\n      result = {};\n\n    for (const k of orderedUnits) {\n      if (hasOwnProperty(dur.values, k) || hasOwnProperty(this.values, k)) {\n        result[k] = dur.get(k) + this.get(k);\n      }\n    }\n\n    return clone(this, { values: result }, true);\n  }\n\n  /**\n   * Make this Duration shorter by the specified amount. Return a newly-constructed Duration.\n   * @param {Duration|Object|number} duration - The amount to subtract. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   * @return {Duration}\n   */\n  minus(duration) {\n    if (!this.isValid) return this;\n\n    const dur = Duration.fromDurationLike(duration);\n    return this.plus(dur.negate());\n  }\n\n  /**\n   * Scale this Duration by the specified amount. Return a newly-constructed Duration.\n   * @param {function} fn - The function to apply to each unit. Arity is 1 or 2: the value of the unit and, optionally, the unit name. Must return a number.\n   * @example Duration.fromObject({ hours: 1, minutes: 30 }).mapUnits(x => x * 2) //=> { hours: 2, minutes: 60 }\n   * @example Duration.fromObject({ hours: 1, minutes: 30 }).mapUnits((x, u) => u === \"hours\" ? x * 2 : x) //=> { hours: 2, minutes: 30 }\n   * @return {Duration}\n   */\n  mapUnits(fn) {\n    if (!this.isValid) return this;\n    const result = {};\n    for (const k of Object.keys(this.values)) {\n      result[k] = asNumber(fn(this.values[k], k));\n    }\n    return clone(this, { values: result }, true);\n  }\n\n  /**\n   * Get the value of unit.\n   * @param {string} unit - a unit such as 'minute' or 'day'\n   * @example Duration.fromObject({years: 2, days: 3}).get('years') //=> 2\n   * @example Duration.fromObject({years: 2, days: 3}).get('months') //=> 0\n   * @example Duration.fromObject({years: 2, days: 3}).get('days') //=> 3\n   * @return {number}\n   */\n  get(unit) {\n    return this[Duration.normalizeUnit(unit)];\n  }\n\n  /**\n   * \"Set\" the values of specified units. Return a newly-constructed Duration.\n   * @param {Object} values - a mapping of units to numbers\n   * @example dur.set({ years: 2017 })\n   * @example dur.set({ hours: 8, minutes: 30 })\n   * @return {Duration}\n   */\n  set(values) {\n    if (!this.isValid) return this;\n\n    const mixed = { ...this.values, ...normalizeObject(values, Duration.normalizeUnit) };\n    return clone(this, { values: mixed });\n  }\n\n  /**\n   * \"Set\" the locale and/or numberingSystem.  Returns a newly-constructed Duration.\n   * @example dur.reconfigure({ locale: 'en-GB' })\n   * @return {Duration}\n   */\n  reconfigure({ locale, numberingSystem, conversionAccuracy, matrix } = {}) {\n    const loc = this.loc.clone({ locale, numberingSystem });\n    const opts = { loc, matrix, conversionAccuracy };\n    return clone(this, opts);\n  }\n\n  /**\n   * Return the length of the duration in the specified unit.\n   * @param {string} unit - a unit such as 'minutes' or 'days'\n   * @example Duration.fromObject({years: 1}).as('days') //=> 365\n   * @example Duration.fromObject({years: 1}).as('months') //=> 12\n   * @example Duration.fromObject({hours: 60}).as('days') //=> 2.5\n   * @return {number}\n   */\n  as(unit) {\n    return this.isValid ? this.shiftTo(unit).get(unit) : NaN;\n  }\n\n  /**\n   * Reduce this Duration to its canonical representation in its current units.\n   * Assuming the overall value of the Duration is positive, this means:\n   * - excessive values for lower-order units are converted to higher-order units (if possible, see first and second example)\n   * - negative lower-order units are converted to higher order units (there must be such a higher order unit, otherwise\n   *   the overall value would be negative, see third example)\n   * - fractional values for higher-order units are converted to lower-order units (if possible, see fourth example)\n   *\n   * If the overall value is negative, the result of this method is equivalent to `this.negate().normalize().negate()`.\n   * @example Duration.fromObject({ years: 2, days: 5000 }).normalize().toObject() //=> { years: 15, days: 255 }\n   * @example Duration.fromObject({ days: 5000 }).normalize().toObject() //=> { days: 5000 }\n   * @example Duration.fromObject({ hours: 12, minutes: -45 }).normalize().toObject() //=> { hours: 11, minutes: 15 }\n   * @example Duration.fromObject({ years: 2.5, days: 0, hours: 0 }).normalize().toObject() //=> { years: 2, days: 182, hours: 12 }\n   * @return {Duration}\n   */\n  normalize() {\n    if (!this.isValid) return this;\n    const vals = this.toObject();\n    normalizeValues(this.matrix, vals);\n    return clone(this, { values: vals }, true);\n  }\n\n  /**\n   * Rescale units to its largest representation\n   * @example Duration.fromObject({ milliseconds: 90000 }).rescale().toObject() //=> { minutes: 1, seconds: 30 }\n   * @return {Duration}\n   */\n  rescale() {\n    if (!this.isValid) return this;\n    const vals = removeZeroes(this.normalize().shiftToAll().toObject());\n    return clone(this, { values: vals }, true);\n  }\n\n  /**\n   * Convert this Duration into its representation in a different set of units.\n   * @example Duration.fromObject({ hours: 1, seconds: 30 }).shiftTo('minutes', 'milliseconds').toObject() //=> { minutes: 60, milliseconds: 30000 }\n   * @return {Duration}\n   */\n  shiftTo(...units) {\n    if (!this.isValid) return this;\n\n    if (units.length === 0) {\n      return this;\n    }\n\n    units = units.map((u) => Duration.normalizeUnit(u));\n\n    const built = {},\n      accumulated = {},\n      vals = this.toObject();\n    let lastUnit;\n\n    for (const k of orderedUnits) {\n      if (units.indexOf(k) >= 0) {\n        lastUnit = k;\n\n        let own = 0;\n\n        // anything we haven't boiled down yet should get boiled to this unit\n        for (const ak in accumulated) {\n          own += this.matrix[ak][k] * accumulated[ak];\n          accumulated[ak] = 0;\n        }\n\n        // plus anything that's already in this unit\n        if (isNumber(vals[k])) {\n          own += vals[k];\n        }\n\n        // only keep the integer part for now in the hopes of putting any decimal part\n        // into a smaller unit later\n        const i = Math.trunc(own);\n        built[k] = i;\n        accumulated[k] = (own * 1000 - i * 1000) / 1000;\n\n        // otherwise, keep it in the wings to boil it later\n      } else if (isNumber(vals[k])) {\n        accumulated[k] = vals[k];\n      }\n    }\n\n    // anything leftover becomes the decimal for the last unit\n    // lastUnit must be defined since units is not empty\n    for (const key in accumulated) {\n      if (accumulated[key] !== 0) {\n        built[lastUnit] +=\n          key === lastUnit ? accumulated[key] : accumulated[key] / this.matrix[lastUnit][key];\n      }\n    }\n\n    normalizeValues(this.matrix, built);\n    return clone(this, { values: built }, true);\n  }\n\n  /**\n   * Shift this Duration to all available units.\n   * Same as shiftTo(\"years\", \"months\", \"weeks\", \"days\", \"hours\", \"minutes\", \"seconds\", \"milliseconds\")\n   * @return {Duration}\n   */\n  shiftToAll() {\n    if (!this.isValid) return this;\n    return this.shiftTo(\n      \"years\",\n      \"months\",\n      \"weeks\",\n      \"days\",\n      \"hours\",\n      \"minutes\",\n      \"seconds\",\n      \"milliseconds\"\n    );\n  }\n\n  /**\n   * Return the negative of this Duration.\n   * @example Duration.fromObject({ hours: 1, seconds: 30 }).negate().toObject() //=> { hours: -1, seconds: -30 }\n   * @return {Duration}\n   */\n  negate() {\n    if (!this.isValid) return this;\n    const negated = {};\n    for (const k of Object.keys(this.values)) {\n      negated[k] = this.values[k] === 0 ? 0 : -this.values[k];\n    }\n    return clone(this, { values: negated }, true);\n  }\n\n  /**\n   * Get the years.\n   * @type {number}\n   */\n  get years() {\n    return this.isValid ? this.values.years || 0 : NaN;\n  }\n\n  /**\n   * Get the quarters.\n   * @type {number}\n   */\n  get quarters() {\n    return this.isValid ? this.values.quarters || 0 : NaN;\n  }\n\n  /**\n   * Get the months.\n   * @type {number}\n   */\n  get months() {\n    return this.isValid ? this.values.months || 0 : NaN;\n  }\n\n  /**\n   * Get the weeks\n   * @type {number}\n   */\n  get weeks() {\n    return this.isValid ? this.values.weeks || 0 : NaN;\n  }\n\n  /**\n   * Get the days.\n   * @type {number}\n   */\n  get days() {\n    return this.isValid ? this.values.days || 0 : NaN;\n  }\n\n  /**\n   * Get the hours.\n   * @type {number}\n   */\n  get hours() {\n    return this.isValid ? this.values.hours || 0 : NaN;\n  }\n\n  /**\n   * Get the minutes.\n   * @type {number}\n   */\n  get minutes() {\n    return this.isValid ? this.values.minutes || 0 : NaN;\n  }\n\n  /**\n   * Get the seconds.\n   * @return {number}\n   */\n  get seconds() {\n    return this.isValid ? this.values.seconds || 0 : NaN;\n  }\n\n  /**\n   * Get the milliseconds.\n   * @return {number}\n   */\n  get milliseconds() {\n    return this.isValid ? this.values.milliseconds || 0 : NaN;\n  }\n\n  /**\n   * Returns whether the Duration is invalid. Invalid durations are returned by diff operations\n   * on invalid DateTimes or Intervals.\n   * @return {boolean}\n   */\n  get isValid() {\n    return this.invalid === null;\n  }\n\n  /**\n   * Returns an error code if this Duration became invalid, or null if the Duration is valid\n   * @return {string}\n   */\n  get invalidReason() {\n    return this.invalid ? this.invalid.reason : null;\n  }\n\n  /**\n   * Returns an explanation of why this Duration became invalid, or null if the Duration is valid\n   * @type {string}\n   */\n  get invalidExplanation() {\n    return this.invalid ? this.invalid.explanation : null;\n  }\n\n  /**\n   * Equality check\n   * Two Durations are equal iff they have the same units and the same values for each unit.\n   * @param {Duration} other\n   * @return {boolean}\n   */\n  equals(other) {\n    if (!this.isValid || !other.isValid) {\n      return false;\n    }\n\n    if (!this.loc.equals(other.loc)) {\n      return false;\n    }\n\n    function eq(v1, v2) {\n      // Consider 0 and undefined as equal\n      if (v1 === undefined || v1 === 0) return v2 === undefined || v2 === 0;\n      return v1 === v2;\n    }\n\n    for (const u of orderedUnits) {\n      if (!eq(this.values[u], other.values[u])) {\n        return false;\n      }\n    }\n    return true;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;;;;;;;;;AAEA,MAAM,UAAU;AAGT,MAAM,iBAAiB;IAC1B,OAAO;QACL,MAAM;QACN,OAAO,IAAI;QACX,SAAS,IAAI,KAAK;QAClB,SAAS,IAAI,KAAK,KAAK;QACvB,cAAc,IAAI,KAAK,KAAK,KAAK;IACnC;IACA,MAAM;QACJ,OAAO;QACP,SAAS,KAAK;QACd,SAAS,KAAK,KAAK;QACnB,cAAc,KAAK,KAAK,KAAK;IAC/B;IACA,OAAO;QAAE,SAAS;QAAI,SAAS,KAAK;QAAI,cAAc,KAAK,KAAK;IAAK;IACrE,SAAS;QAAE,SAAS;QAAI,cAAc,KAAK;IAAK;IAChD,SAAS;QAAE,cAAc;IAAK;AAChC,GACA,eAAe;IACb,OAAO;QACL,UAAU;QACV,QAAQ;QACR,OAAO;QACP,MAAM;QACN,OAAO,MAAM;QACb,SAAS,MAAM,KAAK;QACpB,SAAS,MAAM,KAAK,KAAK;QACzB,cAAc,MAAM,KAAK,KAAK,KAAK;IACrC;IACA,UAAU;QACR,QAAQ;QACR,OAAO;QACP,MAAM;QACN,OAAO,KAAK;QACZ,SAAS,KAAK,KAAK;QACnB,SAAS,KAAK,KAAK,KAAK;QACxB,cAAc,KAAK,KAAK,KAAK,KAAK;IACpC;IACA,QAAQ;QACN,OAAO;QACP,MAAM;QACN,OAAO,KAAK;QACZ,SAAS,KAAK,KAAK;QACnB,SAAS,KAAK,KAAK,KAAK;QACxB,cAAc,KAAK,KAAK,KAAK,KAAK;IACpC;IAEA,GAAG,cAAc;AACnB,GACA,qBAAqB,WAAW,KAChC,sBAAsB,WAAW,MACjC,iBAAiB;IACf,OAAO;QACL,UAAU;QACV,QAAQ;QACR,OAAO,qBAAqB;QAC5B,MAAM;QACN,OAAO,qBAAqB;QAC5B,SAAS,qBAAqB,KAAK;QACnC,SAAS,qBAAqB,KAAK,KAAK;QACxC,cAAc,qBAAqB,KAAK,KAAK,KAAK;IACpD;IACA,UAAU;QACR,QAAQ;QACR,OAAO,qBAAqB;QAC5B,MAAM,qBAAqB;QAC3B,OAAO,AAAC,qBAAqB,KAAM;QACnC,SAAS,AAAC,qBAAqB,KAAK,KAAM;QAC1C,SAAS,AAAC,qBAAqB,KAAK,KAAK,KAAM;QAC/C,cAAc,AAAC,qBAAqB,KAAK,KAAK,KAAK,OAAQ;IAC7D;IACA,QAAQ;QACN,OAAO,sBAAsB;QAC7B,MAAM;QACN,OAAO,sBAAsB;QAC7B,SAAS,sBAAsB,KAAK;QACpC,SAAS,sBAAsB,KAAK,KAAK;QACzC,cAAc,sBAAsB,KAAK,KAAK,KAAK;IACrD;IACA,GAAG,cAAc;AACnB;AAEF,wBAAwB;AACxB,MAAM,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,eAAe,aAAa,KAAK,CAAC,GAAG,OAAO;AAElD,0FAA0F;AAC1F,SAAS,MAAM,GAAG,EAAE,IAAI,EAAE,QAAQ,KAAK;IACrC,sBAAsB;IACtB,MAAM,OAAO;QACX,QAAQ,QAAQ,KAAK,MAAM,GAAG;YAAE,GAAG,IAAI,MAAM;YAAE,GAAI,KAAK,MAAM,IAAI,CAAC,CAAC;QAAE;QACtE,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG;QAC3B,oBAAoB,KAAK,kBAAkB,IAAI,IAAI,kBAAkB;QACrE,QAAQ,KAAK,MAAM,IAAI,IAAI,MAAM;IACnC;IACA,OAAO,IAAI,SAAS;AACtB;AAEA,SAAS,iBAAiB,MAAM,EAAE,IAAI;IACpC,IAAI,MAAM,KAAK,YAAY,IAAI;IAC/B,KAAK,MAAM,QAAQ,aAAa,KAAK,CAAC,GAAI;QACxC,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,OAAO,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,eAAe;QAClD;IACF;IACA,OAAO;AACT;AAEA,yBAAyB;AACzB,SAAS,gBAAgB,MAAM,EAAE,IAAI;IACnC,wEAAwE;IACxE,wDAAwD;IACxD,MAAM,SAAS,iBAAiB,QAAQ,QAAQ,IAAI,CAAC,IAAI;IAEzD,aAAa,WAAW,CAAC,CAAC,UAAU;QAClC,IAAI,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,QAAQ,GAAG;YAC/B,IAAI,UAAU;gBACZ,MAAM,cAAc,IAAI,CAAC,SAAS,GAAG;gBACrC,MAAM,OAAO,MAAM,CAAC,QAAQ,CAAC,SAAS;gBAEtC,wBAAwB;gBACxB,6DAA6D;gBAC7D,6EAA6E;gBAC7E,sCAAsC;gBACtC,0FAA0F;gBAC1F,0FAA0F;gBAC1F,uCAAuC;gBACvC,QAAQ;gBACR,0FAA0F;gBAC1F,wFAAwF;gBACxF,uBAAuB;gBACvB,EAAE;gBACF,qEAAqE;gBACrE,wDAAwD;gBACxD,wDAAwD;gBACxD,MAAM,SAAS,KAAK,KAAK,CAAC,cAAc;gBACxC,IAAI,CAAC,QAAQ,IAAI,SAAS;gBAC1B,IAAI,CAAC,SAAS,IAAI,SAAS,OAAO;YACpC;YACA,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF,GAAG;IAEH,6DAA6D;IAC7D,wGAAwG;IACxG,aAAa,MAAM,CAAC,CAAC,UAAU;QAC7B,IAAI,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,QAAQ,GAAG;YAC/B,IAAI,UAAU;gBACZ,MAAM,WAAW,IAAI,CAAC,SAAS,GAAG;gBAClC,IAAI,CAAC,SAAS,IAAI;gBAClB,IAAI,CAAC,QAAQ,IAAI,WAAW,MAAM,CAAC,SAAS,CAAC,QAAQ;YACvD;YACA,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF,GAAG;AACL;AAEA,yDAAyD;AACzD,SAAS,aAAa,IAAI;IACxB,MAAM,UAAU,CAAC;IACjB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,MAAO;QAC/C,IAAI,UAAU,GAAG;YACf,OAAO,CAAC,IAAI,GAAG;QACjB;IACF;IACA,OAAO;AACT;AAee,MAAM;IACnB;;GAEC,GACD,YAAY,MAAM,CAAE;QAClB,MAAM,WAAW,OAAO,kBAAkB,KAAK,cAAc;QAC7D,IAAI,SAAS,WAAW,iBAAiB;QAEzC,IAAI,OAAO,MAAM,EAAE;YACjB,SAAS,OAAO,MAAM;QACxB;QAEA;;KAEC,GACD,IAAI,CAAC,MAAM,GAAG,OAAO,MAAM;QAC3B;;KAEC,GACD,IAAI,CAAC,GAAG,GAAG,OAAO,GAAG,IAAI,sJAAA,CAAA,UAAM,CAAC,MAAM;QACtC;;KAEC,GACD,IAAI,CAAC,kBAAkB,GAAG,WAAW,aAAa;QAClD;;KAEC,GACD,IAAI,CAAC,OAAO,GAAG,OAAO,OAAO,IAAI;QACjC;;KAEC,GACD,IAAI,CAAC,MAAM,GAAG;QACd;;KAEC,GACD,IAAI,CAAC,eAAe,GAAG;IACzB;IAEA;;;;;;;;GAQC,GACD,OAAO,WAAW,KAAK,EAAE,IAAI,EAAE;QAC7B,OAAO,SAAS,UAAU,CAAC;YAAE,cAAc;QAAM,GAAG;IACtD;IAEA;;;;;;;;;;;;;;;;;;;GAmBC,GACD,OAAO,WAAW,GAAG,EAAE,OAAO,CAAC,CAAC,EAAE;QAChC,IAAI,OAAO,QAAQ,OAAO,QAAQ,UAAU;YAC1C,MAAM,IAAI,8IAAA,CAAA,uBAAoB,CAC5B,CAAC,4DAA4D,EAC3D,QAAQ,OAAO,SAAS,OAAO,KAC/B;QAEN;QAEA,OAAO,IAAI,SAAS;YAClB,QAAQ,CAAA,GAAA,oJAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,SAAS,aAAa;YACnD,KAAK,sJAAA,CAAA,UAAM,CAAC,UAAU,CAAC;YACvB,oBAAoB,KAAK,kBAAkB;YAC3C,QAAQ,KAAK,MAAM;QACrB;IACF;IAEA;;;;;;;;;GASC,GACD,OAAO,iBAAiB,YAAY,EAAE;QACpC,IAAI,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,eAAe;YAC1B,OAAO,SAAS,UAAU,CAAC;QAC7B,OAAO,IAAI,SAAS,UAAU,CAAC,eAAe;YAC5C,OAAO;QACT,OAAO,IAAI,OAAO,iBAAiB,UAAU;YAC3C,OAAO,SAAS,UAAU,CAAC;QAC7B,OAAO;YACL,MAAM,IAAI,8IAAA,CAAA,uBAAoB,CAC5B,CAAC,0BAA0B,EAAE,aAAa,SAAS,EAAE,OAAO,cAAc;QAE9E;IACF;IAEA;;;;;;;;;;;;;GAaC,GACD,OAAO,QAAQ,IAAI,EAAE,IAAI,EAAE;QACzB,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,2JAAA,CAAA,mBAAgB,AAAD,EAAE;QAClC,IAAI,QAAQ;YACV,OAAO,SAAS,UAAU,CAAC,QAAQ;QACrC,OAAO;YACL,OAAO,SAAS,OAAO,CAAC,cAAc,CAAC,WAAW,EAAE,KAAK,6BAA6B,CAAC;QACzF;IACF;IAEA;;;;;;;;;;;;;;;GAeC,GACD,OAAO,YAAY,IAAI,EAAE,IAAI,EAAE;QAC7B,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,2JAAA,CAAA,mBAAgB,AAAD,EAAE;QAClC,IAAI,QAAQ;YACV,OAAO,SAAS,UAAU,CAAC,QAAQ;QACrC,OAAO;YACL,OAAO,SAAS,OAAO,CAAC,cAAc,CAAC,WAAW,EAAE,KAAK,6BAA6B,CAAC;QACzF;IACF;IAEA;;;;;GAKC,GACD,OAAO,QAAQ,MAAM,EAAE,cAAc,IAAI,EAAE;QACzC,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,8IAAA,CAAA,uBAAoB,CAAC;QACjC;QAEA,MAAM,UAAU,kBAAkB,uJAAA,CAAA,UAAO,GAAG,SAAS,IAAI,uJAAA,CAAA,UAAO,CAAC,QAAQ;QAEzE,IAAI,gJAAA,CAAA,UAAQ,CAAC,cAAc,EAAE;YAC3B,MAAM,IAAI,8IAAA,CAAA,uBAAoB,CAAC;QACjC,OAAO;YACL,OAAO,IAAI,SAAS;gBAAE;YAAQ;QAChC;IACF;IAEA;;GAEC,GACD,OAAO,cAAc,IAAI,EAAE;QACzB,MAAM,aAAa;YACjB,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,MAAM;YACN,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,SAAS;YACT,aAAa;YACb,cAAc;QAChB,CAAC,CAAC,OAAO,KAAK,WAAW,KAAK,KAAK;QAEnC,IAAI,CAAC,YAAY,MAAM,IAAI,8IAAA,CAAA,mBAAgB,CAAC;QAE5C,OAAO;IACT;IAEA;;;;GAIC,GACD,OAAO,WAAW,CAAC,EAAE;QACnB,OAAO,AAAC,KAAK,EAAE,eAAe,IAAK;IACrC;IAEA;;;GAGC,GACD,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG;IAC1C;IAEA;;;;GAIC,GACD,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,GAAG;IACnD;IAEA;;;;;;;;;;;;;;;;;;;;;GAqBC,GACD,SAAS,GAAG,EAAE,OAAO,CAAC,CAAC,EAAE;QACvB,wFAAwF;QACxF,MAAM,UAAU;YACd,GAAG,IAAI;YACP,OAAO,KAAK,KAAK,KAAK,SAAS,KAAK,KAAK,KAAK;QAChD;QACA,OAAO,IAAI,CAAC,OAAO,GACf,yJAAA,CAAA,UAAS,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,wBAAwB,CAAC,IAAI,EAAE,OACnE;IACN;IAEA;;;;;;;;;;;;;GAaC,GACD,QAAQ,OAAO,CAAC,CAAC,EAAE;QACjB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;QAE1B,MAAM,IAAI,aACP,GAAG,CAAC,CAAC;YACJ,MAAM,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK;YAC7B,IAAI,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,MAAM;gBACpB,OAAO;YACT;YACA,OAAO,IAAI,CAAC,GAAG,CACZ,eAAe,CAAC;gBAAE,OAAO;gBAAQ,aAAa;gBAAQ,GAAG,IAAI;gBAAE,MAAM,KAAK,KAAK,CAAC,GAAG,CAAC;YAAG,GACvF,MAAM,CAAC;QACZ,GACC,MAAM,CAAC,CAAC,IAAM;QAEjB,OAAO,IAAI,CAAC,GAAG,CACZ,aAAa,CAAC;YAAE,MAAM;YAAe,OAAO,KAAK,SAAS,IAAI;YAAU,GAAG,IAAI;QAAC,GAChF,MAAM,CAAC;IACZ;IAEA;;;;GAIC,GACD,WAAW;QACT,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC;QAC3B,OAAO;YAAE,GAAG,IAAI,CAAC,MAAM;QAAC;IAC1B;IAEA;;;;;;;;;GASC,GACD,QAAQ;QACN,kFAAkF;QAClF,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;QAE1B,IAAI,IAAI;QACR,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG,KAAK,IAAI,CAAC,KAAK,GAAG;QACxC,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,IAAI,CAAC,QAAQ,KAAK,GAAG,KAAK,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI;QACrF,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG,KAAK,IAAI,CAAC,KAAK,GAAG;QACxC,IAAI,IAAI,CAAC,IAAI,KAAK,GAAG,KAAK,IAAI,CAAC,IAAI,GAAG;QACtC,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI,CAAC,OAAO,KAAK,KAAK,IAAI,CAAC,OAAO,KAAK,KAAK,IAAI,CAAC,YAAY,KAAK,GACxF,KAAK;QACP,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG,KAAK,IAAI,CAAC,KAAK,GAAG;QACxC,IAAI,IAAI,CAAC,OAAO,KAAK,GAAG,KAAK,IAAI,CAAC,OAAO,GAAG;QAC5C,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,IAAI,CAAC,YAAY,KAAK,GAC9C,6EAA6E;QAC7E,2EAA2E;QAC3E,KAAK,CAAA,GAAA,oJAAA,CAAA,UAAO,AAAD,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,GAAG,MAAM,KAAK;QAC7D,IAAI,MAAM,KAAK,KAAK;QACpB,OAAO;IACT;IAEA;;;;;;;;;;;;;;;GAeC,GACD,UAAU,OAAO,CAAC,CAAC,EAAE;QACnB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;QAE1B,MAAM,SAAS,IAAI,CAAC,QAAQ;QAC5B,IAAI,SAAS,KAAK,UAAU,UAAU,OAAO;QAE7C,OAAO;YACL,sBAAsB;YACtB,iBAAiB;YACjB,eAAe;YACf,QAAQ;YACR,GAAG,IAAI;YACP,eAAe;QACjB;QAEA,MAAM,WAAW,gJAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,QAAQ;YAAE,MAAM;QAAM;QAC3D,OAAO,SAAS,SAAS,CAAC;IAC5B;IAEA;;;GAGC,GACD,SAAS;QACP,OAAO,IAAI,CAAC,KAAK;IACnB;IAEA;;;GAGC,GACD,WAAW;QACT,OAAO,IAAI,CAAC,KAAK;IACnB;IAEA;;;GAGC,GACD,CAAC,OAAO,GAAG,CAAC,8BAA8B,GAAG;QAC3C,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,OAAO,CAAC,mBAAmB,EAAE,KAAK,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;QAC9D,OAAO;YACL,OAAO,CAAC,4BAA4B,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;QAC9D;IACF;IAEA;;;GAGC,GACD,WAAW;QACT,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;QAE1B,OAAO,iBAAiB,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM;IAClD;IAEA;;;GAGC,GACD,UAAU;QACR,OAAO,IAAI,CAAC,QAAQ;IACtB;IAEA;;;;GAIC,GACD,KAAK,QAAQ,EAAE;QACb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI;QAE9B,MAAM,MAAM,SAAS,gBAAgB,CAAC,WACpC,SAAS,CAAC;QAEZ,KAAK,MAAM,KAAK,aAAc;YAC5B,IAAI,CAAA,GAAA,oJAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,MAAM,EAAE,MAAM,CAAA,GAAA,oJAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI;gBACnE,MAAM,CAAC,EAAE,GAAG,IAAI,GAAG,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC;YACpC;QACF;QAEA,OAAO,MAAM,IAAI,EAAE;YAAE,QAAQ;QAAO,GAAG;IACzC;IAEA;;;;GAIC,GACD,MAAM,QAAQ,EAAE;QACd,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI;QAE9B,MAAM,MAAM,SAAS,gBAAgB,CAAC;QACtC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM;IAC7B;IAEA;;;;;;GAMC,GACD,SAAS,EAAE,EAAE;QACX,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI;QAC9B,MAAM,SAAS,CAAC;QAChB,KAAK,MAAM,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,EAAG;YACxC,MAAM,CAAC,EAAE,GAAG,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;QAC1C;QACA,OAAO,MAAM,IAAI,EAAE;YAAE,QAAQ;QAAO,GAAG;IACzC;IAEA;;;;;;;GAOC,GACD,IAAI,IAAI,EAAE;QACR,OAAO,IAAI,CAAC,SAAS,aAAa,CAAC,MAAM;IAC3C;IAEA;;;;;;GAMC,GACD,IAAI,MAAM,EAAE;QACV,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI;QAE9B,MAAM,QAAQ;YAAE,GAAG,IAAI,CAAC,MAAM;YAAE,GAAG,CAAA,GAAA,oJAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,SAAS,aAAa,CAAC;QAAC;QACnF,OAAO,MAAM,IAAI,EAAE;YAAE,QAAQ;QAAM;IACrC;IAEA;;;;GAIC,GACD,YAAY,EAAE,MAAM,EAAE,eAAe,EAAE,kBAAkB,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,EAAE;QACxE,MAAM,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;YAAE;YAAQ;QAAgB;QACrD,MAAM,OAAO;YAAE;YAAK;YAAQ;QAAmB;QAC/C,OAAO,MAAM,IAAI,EAAE;IACrB;IAEA;;;;;;;GAOC,GACD,GAAG,IAAI,EAAE;QACP,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,QAAQ;IACvD;IAEA;;;;;;;;;;;;;;GAcC,GACD,YAAY;QACV,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI;QAC9B,MAAM,OAAO,IAAI,CAAC,QAAQ;QAC1B,gBAAgB,IAAI,CAAC,MAAM,EAAE;QAC7B,OAAO,MAAM,IAAI,EAAE;YAAE,QAAQ;QAAK,GAAG;IACvC;IAEA;;;;GAIC,GACD,UAAU;QACR,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI;QAC9B,MAAM,OAAO,aAAa,IAAI,CAAC,SAAS,GAAG,UAAU,GAAG,QAAQ;QAChE,OAAO,MAAM,IAAI,EAAE;YAAE,QAAQ;QAAK,GAAG;IACvC;IAEA;;;;GAIC,GACD,QAAQ,GAAG,KAAK,EAAE;QAChB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI;QAE9B,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,OAAO,IAAI;QACb;QAEA,QAAQ,MAAM,GAAG,CAAC,CAAC,IAAM,SAAS,aAAa,CAAC;QAEhD,MAAM,QAAQ,CAAC,GACb,cAAc,CAAC,GACf,OAAO,IAAI,CAAC,QAAQ;QACtB,IAAI;QAEJ,KAAK,MAAM,KAAK,aAAc;YAC5B,IAAI,MAAM,OAAO,CAAC,MAAM,GAAG;gBACzB,WAAW;gBAEX,IAAI,MAAM;gBAEV,qEAAqE;gBACrE,IAAK,MAAM,MAAM,YAAa;oBAC5B,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,WAAW,CAAC,GAAG;oBAC3C,WAAW,CAAC,GAAG,GAAG;gBACpB;gBAEA,4CAA4C;gBAC5C,IAAI,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,CAAC,EAAE,GAAG;oBACrB,OAAO,IAAI,CAAC,EAAE;gBAChB;gBAEA,8EAA8E;gBAC9E,4BAA4B;gBAC5B,MAAM,IAAI,KAAK,KAAK,CAAC;gBACrB,KAAK,CAAC,EAAE,GAAG;gBACX,WAAW,CAAC,EAAE,GAAG,CAAC,MAAM,OAAO,IAAI,IAAI,IAAI;YAE3C,mDAAmD;YACrD,OAAO,IAAI,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,CAAC,EAAE,GAAG;gBAC5B,WAAW,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;YAC1B;QACF;QAEA,0DAA0D;QAC1D,oDAAoD;QACpD,IAAK,MAAM,OAAO,YAAa;YAC7B,IAAI,WAAW,CAAC,IAAI,KAAK,GAAG;gBAC1B,KAAK,CAAC,SAAS,IACb,QAAQ,WAAW,WAAW,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI;YACvF;QACF;QAEA,gBAAgB,IAAI,CAAC,MAAM,EAAE;QAC7B,OAAO,MAAM,IAAI,EAAE;YAAE,QAAQ;QAAM,GAAG;IACxC;IAEA;;;;GAIC,GACD,aAAa;QACX,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI;QAC9B,OAAO,IAAI,CAAC,OAAO,CACjB,SACA,UACA,SACA,QACA,SACA,WACA,WACA;IAEJ;IAEA;;;;GAIC,GACD,SAAS;QACP,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI;QAC9B,MAAM,UAAU,CAAC;QACjB,KAAK,MAAM,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,EAAG;YACxC,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;QACzD;QACA,OAAO,MAAM,IAAI,EAAE;YAAE,QAAQ;QAAQ,GAAG;IAC1C;IAEA;;;GAGC,GACD,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI;IACjD;IAEA;;;GAGC,GACD,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,IAAI;IACpD;IAEA;;;GAGC,GACD,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI;IAClD;IAEA;;;GAGC,GACD,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI;IACjD;IAEA;;;GAGC,GACD,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI;IAChD;IAEA;;;GAGC,GACD,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI;IACjD;IAEA;;;GAGC,GACD,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,IAAI;IACnD;IAEA;;;GAGC,GACD,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,IAAI;IACnD;IAEA;;;GAGC,GACD,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI;IACxD;IAEA;;;;GAIC,GACD,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,OAAO,KAAK;IAC1B;IAEA;;;GAGC,GACD,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG;IAC9C;IAEA;;;GAGC,GACD,IAAI,qBAAqB;QACvB,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG;IACnD;IAEA;;;;;GAKC,GACD,OAAO,KAAK,EAAE;QACZ,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,OAAO,EAAE;YACnC,OAAO;QACT;QAEA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG;YAC/B,OAAO;QACT;QAEA,SAAS,GAAG,EAAE,EAAE,EAAE;YAChB,oCAAoC;YACpC,IAAI,OAAO,aAAa,OAAO,GAAG,OAAO,OAAO,aAAa,OAAO;YACpE,OAAO,OAAO;QAChB;QAEA,KAAK,MAAM,KAAK,aAAc;YAC5B,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,MAAM,CAAC,EAAE,GAAG;gBACxC,OAAO;YACT;QACF;QACA,OAAO;IACT;AACF", "ignoreList": [0]}}, {"offset": {"line": 4060, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/luxon/src/interval.js"], "sourcesContent": ["import DateTime, { friendlyDateTime } from \"./datetime.js\";\nimport Duration from \"./duration.js\";\nimport Settings from \"./settings.js\";\nimport { InvalidArgumentError, InvalidIntervalError } from \"./errors.js\";\nimport Invalid from \"./impl/invalid.js\";\nimport Formatter from \"./impl/formatter.js\";\nimport * as Formats from \"./impl/formats.js\";\n\nconst INVALID = \"Invalid Interval\";\n\n// checks if the start is equal to or before the end\nfunction validateStartEnd(start, end) {\n  if (!start || !start.isValid) {\n    return Interval.invalid(\"missing or invalid start\");\n  } else if (!end || !end.isValid) {\n    return Interval.invalid(\"missing or invalid end\");\n  } else if (end < start) {\n    return Interval.invalid(\n      \"end before start\",\n      `The end of an interval must be after its start, but you had start=${start.toISO()} and end=${end.toISO()}`\n    );\n  } else {\n    return null;\n  }\n}\n\n/**\n * An Interval object represents a half-open interval of time, where each endpoint is a {@link DateTime}. Conceptually, it's a container for those two endpoints, accompanied by methods for creating, parsing, interrogating, comparing, transforming, and formatting them.\n *\n * Here is a brief overview of the most commonly used methods and getters in Interval:\n *\n * * **Creation** To create an Interval, use {@link Interval.fromDateTimes}, {@link Interval.after}, {@link Interval.before}, or {@link Interval.fromISO}.\n * * **Accessors** Use {@link Interval#start} and {@link Interval#end} to get the start and end.\n * * **Interrogation** To analyze the Interval, use {@link Interval#count}, {@link Interval#length}, {@link Interval#hasSame}, {@link Interval#contains}, {@link Interval#isAfter}, or {@link Interval#isBefore}.\n * * **Transformation** To create other Intervals out of this one, use {@link Interval#set}, {@link Interval#splitAt}, {@link Interval#splitBy}, {@link Interval#divideEqually}, {@link Interval.merge}, {@link Interval.xor}, {@link Interval#union}, {@link Interval#intersection}, or {@link Interval#difference}.\n * * **Comparison** To compare this Interval to another one, use {@link Interval#equals}, {@link Interval#overlaps}, {@link Interval#abutsStart}, {@link Interval#abutsEnd}, {@link Interval#engulfs}\n * * **Output** To convert the Interval into other representations, see {@link Interval#toString}, {@link Interval#toLocaleString}, {@link Interval#toISO}, {@link Interval#toISODate}, {@link Interval#toISOTime}, {@link Interval#toFormat}, and {@link Interval#toDuration}.\n */\nexport default class Interval {\n  /**\n   * @private\n   */\n  constructor(config) {\n    /**\n     * @access private\n     */\n    this.s = config.start;\n    /**\n     * @access private\n     */\n    this.e = config.end;\n    /**\n     * @access private\n     */\n    this.invalid = config.invalid || null;\n    /**\n     * @access private\n     */\n    this.isLuxonInterval = true;\n  }\n\n  /**\n   * Create an invalid Interval.\n   * @param {string} reason - simple string of why this Interval is invalid. Should not contain parameters or anything else data-dependent\n   * @param {string} [explanation=null] - longer explanation, may include parameters and other useful debugging information\n   * @return {Interval}\n   */\n  static invalid(reason, explanation = null) {\n    if (!reason) {\n      throw new InvalidArgumentError(\"need to specify a reason the Interval is invalid\");\n    }\n\n    const invalid = reason instanceof Invalid ? reason : new Invalid(reason, explanation);\n\n    if (Settings.throwOnInvalid) {\n      throw new InvalidIntervalError(invalid);\n    } else {\n      return new Interval({ invalid });\n    }\n  }\n\n  /**\n   * Create an Interval from a start DateTime and an end DateTime. Inclusive of the start but not the end.\n   * @param {DateTime|Date|Object} start\n   * @param {DateTime|Date|Object} end\n   * @return {Interval}\n   */\n  static fromDateTimes(start, end) {\n    const builtStart = friendlyDateTime(start),\n      builtEnd = friendlyDateTime(end);\n\n    const validateError = validateStartEnd(builtStart, builtEnd);\n\n    if (validateError == null) {\n      return new Interval({\n        start: builtStart,\n        end: builtEnd,\n      });\n    } else {\n      return validateError;\n    }\n  }\n\n  /**\n   * Create an Interval from a start DateTime and a Duration to extend to.\n   * @param {DateTime|Date|Object} start\n   * @param {Duration|Object|number} duration - the length of the Interval.\n   * @return {Interval}\n   */\n  static after(start, duration) {\n    const dur = Duration.fromDurationLike(duration),\n      dt = friendlyDateTime(start);\n    return Interval.fromDateTimes(dt, dt.plus(dur));\n  }\n\n  /**\n   * Create an Interval from an end DateTime and a Duration to extend backwards to.\n   * @param {DateTime|Date|Object} end\n   * @param {Duration|Object|number} duration - the length of the Interval.\n   * @return {Interval}\n   */\n  static before(end, duration) {\n    const dur = Duration.fromDurationLike(duration),\n      dt = friendlyDateTime(end);\n    return Interval.fromDateTimes(dt.minus(dur), dt);\n  }\n\n  /**\n   * Create an Interval from an ISO 8601 string.\n   * Accepts `<start>/<end>`, `<start>/<duration>`, and `<duration>/<end>` formats.\n   * @param {string} text - the ISO string to parse\n   * @param {Object} [opts] - options to pass {@link DateTime#fromISO} and optionally {@link Duration#fromISO}\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @return {Interval}\n   */\n  static fromISO(text, opts) {\n    const [s, e] = (text || \"\").split(\"/\", 2);\n    if (s && e) {\n      let start, startIsValid;\n      try {\n        start = DateTime.fromISO(s, opts);\n        startIsValid = start.isValid;\n      } catch (e) {\n        startIsValid = false;\n      }\n\n      let end, endIsValid;\n      try {\n        end = DateTime.fromISO(e, opts);\n        endIsValid = end.isValid;\n      } catch (e) {\n        endIsValid = false;\n      }\n\n      if (startIsValid && endIsValid) {\n        return Interval.fromDateTimes(start, end);\n      }\n\n      if (startIsValid) {\n        const dur = Duration.fromISO(e, opts);\n        if (dur.isValid) {\n          return Interval.after(start, dur);\n        }\n      } else if (endIsValid) {\n        const dur = Duration.fromISO(s, opts);\n        if (dur.isValid) {\n          return Interval.before(end, dur);\n        }\n      }\n    }\n    return Interval.invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ISO 8601`);\n  }\n\n  /**\n   * Check if an object is an Interval. Works across context boundaries\n   * @param {object} o\n   * @return {boolean}\n   */\n  static isInterval(o) {\n    return (o && o.isLuxonInterval) || false;\n  }\n\n  /**\n   * Returns the start of the Interval\n   * @type {DateTime}\n   */\n  get start() {\n    return this.isValid ? this.s : null;\n  }\n\n  /**\n   * Returns the end of the Interval\n   * @type {DateTime}\n   */\n  get end() {\n    return this.isValid ? this.e : null;\n  }\n\n  /**\n   * Returns the last DateTime included in the interval (since end is not part of the interval)\n   * @type {DateTime}\n   */\n  get lastDateTime() {\n    return this.isValid ? (this.e ? this.e.minus(1) : null) : null;\n  }\n\n  /**\n   * Returns whether this Interval's end is at least its start, meaning that the Interval isn't 'backwards'.\n   * @type {boolean}\n   */\n  get isValid() {\n    return this.invalidReason === null;\n  }\n\n  /**\n   * Returns an error code if this Interval is invalid, or null if the Interval is valid\n   * @type {string}\n   */\n  get invalidReason() {\n    return this.invalid ? this.invalid.reason : null;\n  }\n\n  /**\n   * Returns an explanation of why this Interval became invalid, or null if the Interval is valid\n   * @type {string}\n   */\n  get invalidExplanation() {\n    return this.invalid ? this.invalid.explanation : null;\n  }\n\n  /**\n   * Returns the length of the Interval in the specified unit.\n   * @param {string} unit - the unit (such as 'hours' or 'days') to return the length in.\n   * @return {number}\n   */\n  length(unit = \"milliseconds\") {\n    return this.isValid ? this.toDuration(...[unit]).get(unit) : NaN;\n  }\n\n  /**\n   * Returns the count of minutes, hours, days, months, or years included in the Interval, even in part.\n   * Unlike {@link Interval#length} this counts sections of the calendar, not periods of time, e.g. specifying 'day'\n   * asks 'what dates are included in this interval?', not 'how many days long is this interval?'\n   * @param {string} [unit='milliseconds'] - the unit of time to count.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week; this operation will always use the locale of the start DateTime\n   * @return {number}\n   */\n  count(unit = \"milliseconds\", opts) {\n    if (!this.isValid) return NaN;\n    const start = this.start.startOf(unit, opts);\n    let end;\n    if (opts?.useLocaleWeeks) {\n      end = this.end.reconfigure({ locale: start.locale });\n    } else {\n      end = this.end;\n    }\n    end = end.startOf(unit, opts);\n    return Math.floor(end.diff(start, unit).get(unit)) + (end.valueOf() !== this.end.valueOf());\n  }\n\n  /**\n   * Returns whether this Interval's start and end are both in the same unit of time\n   * @param {string} unit - the unit of time to check sameness on\n   * @return {boolean}\n   */\n  hasSame(unit) {\n    return this.isValid ? this.isEmpty() || this.e.minus(1).hasSame(this.s, unit) : false;\n  }\n\n  /**\n   * Return whether this Interval has the same start and end DateTimes.\n   * @return {boolean}\n   */\n  isEmpty() {\n    return this.s.valueOf() === this.e.valueOf();\n  }\n\n  /**\n   * Return whether this Interval's start is after the specified DateTime.\n   * @param {DateTime} dateTime\n   * @return {boolean}\n   */\n  isAfter(dateTime) {\n    if (!this.isValid) return false;\n    return this.s > dateTime;\n  }\n\n  /**\n   * Return whether this Interval's end is before the specified DateTime.\n   * @param {DateTime} dateTime\n   * @return {boolean}\n   */\n  isBefore(dateTime) {\n    if (!this.isValid) return false;\n    return this.e <= dateTime;\n  }\n\n  /**\n   * Return whether this Interval contains the specified DateTime.\n   * @param {DateTime} dateTime\n   * @return {boolean}\n   */\n  contains(dateTime) {\n    if (!this.isValid) return false;\n    return this.s <= dateTime && this.e > dateTime;\n  }\n\n  /**\n   * \"Sets\" the start and/or end dates. Returns a newly-constructed Interval.\n   * @param {Object} values - the values to set\n   * @param {DateTime} values.start - the starting DateTime\n   * @param {DateTime} values.end - the ending DateTime\n   * @return {Interval}\n   */\n  set({ start, end } = {}) {\n    if (!this.isValid) return this;\n    return Interval.fromDateTimes(start || this.s, end || this.e);\n  }\n\n  /**\n   * Split this Interval at each of the specified DateTimes\n   * @param {...DateTime} dateTimes - the unit of time to count.\n   * @return {Array}\n   */\n  splitAt(...dateTimes) {\n    if (!this.isValid) return [];\n    const sorted = dateTimes\n        .map(friendlyDateTime)\n        .filter((d) => this.contains(d))\n        .sort((a, b) => a.toMillis() - b.toMillis()),\n      results = [];\n    let { s } = this,\n      i = 0;\n\n    while (s < this.e) {\n      const added = sorted[i] || this.e,\n        next = +added > +this.e ? this.e : added;\n      results.push(Interval.fromDateTimes(s, next));\n      s = next;\n      i += 1;\n    }\n\n    return results;\n  }\n\n  /**\n   * Split this Interval into smaller Intervals, each of the specified length.\n   * Left over time is grouped into a smaller interval\n   * @param {Duration|Object|number} duration - The length of each resulting interval.\n   * @return {Array}\n   */\n  splitBy(duration) {\n    const dur = Duration.fromDurationLike(duration);\n\n    if (!this.isValid || !dur.isValid || dur.as(\"milliseconds\") === 0) {\n      return [];\n    }\n\n    let { s } = this,\n      idx = 1,\n      next;\n\n    const results = [];\n    while (s < this.e) {\n      const added = this.start.plus(dur.mapUnits((x) => x * idx));\n      next = +added > +this.e ? this.e : added;\n      results.push(Interval.fromDateTimes(s, next));\n      s = next;\n      idx += 1;\n    }\n\n    return results;\n  }\n\n  /**\n   * Split this Interval into the specified number of smaller intervals.\n   * @param {number} numberOfParts - The number of Intervals to divide the Interval into.\n   * @return {Array}\n   */\n  divideEqually(numberOfParts) {\n    if (!this.isValid) return [];\n    return this.splitBy(this.length() / numberOfParts).slice(0, numberOfParts);\n  }\n\n  /**\n   * Return whether this Interval overlaps with the specified Interval\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  overlaps(other) {\n    return this.e > other.s && this.s < other.e;\n  }\n\n  /**\n   * Return whether this Interval's end is adjacent to the specified Interval's start.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  abutsStart(other) {\n    if (!this.isValid) return false;\n    return +this.e === +other.s;\n  }\n\n  /**\n   * Return whether this Interval's start is adjacent to the specified Interval's end.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  abutsEnd(other) {\n    if (!this.isValid) return false;\n    return +other.e === +this.s;\n  }\n\n  /**\n   * Returns true if this Interval fully contains the specified Interval, specifically if the intersect (of this Interval and the other Interval) is equal to the other Interval; false otherwise.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  engulfs(other) {\n    if (!this.isValid) return false;\n    return this.s <= other.s && this.e >= other.e;\n  }\n\n  /**\n   * Return whether this Interval has the same start and end as the specified Interval.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  equals(other) {\n    if (!this.isValid || !other.isValid) {\n      return false;\n    }\n\n    return this.s.equals(other.s) && this.e.equals(other.e);\n  }\n\n  /**\n   * Return an Interval representing the intersection of this Interval and the specified Interval.\n   * Specifically, the resulting Interval has the maximum start time and the minimum end time of the two Intervals.\n   * Returns null if the intersection is empty, meaning, the intervals don't intersect.\n   * @param {Interval} other\n   * @return {Interval}\n   */\n  intersection(other) {\n    if (!this.isValid) return this;\n    const s = this.s > other.s ? this.s : other.s,\n      e = this.e < other.e ? this.e : other.e;\n\n    if (s >= e) {\n      return null;\n    } else {\n      return Interval.fromDateTimes(s, e);\n    }\n  }\n\n  /**\n   * Return an Interval representing the union of this Interval and the specified Interval.\n   * Specifically, the resulting Interval has the minimum start time and the maximum end time of the two Intervals.\n   * @param {Interval} other\n   * @return {Interval}\n   */\n  union(other) {\n    if (!this.isValid) return this;\n    const s = this.s < other.s ? this.s : other.s,\n      e = this.e > other.e ? this.e : other.e;\n    return Interval.fromDateTimes(s, e);\n  }\n\n  /**\n   * Merge an array of Intervals into an equivalent minimal set of Intervals.\n   * Combines overlapping and adjacent Intervals.\n   * The resulting array will contain the Intervals in ascending order, that is, starting with the earliest Interval\n   * and ending with the latest.\n   *\n   * @param {Array} intervals\n   * @return {Array}\n   */\n  static merge(intervals) {\n    const [found, final] = intervals\n      .sort((a, b) => a.s - b.s)\n      .reduce(\n        ([sofar, current], item) => {\n          if (!current) {\n            return [sofar, item];\n          } else if (current.overlaps(item) || current.abutsStart(item)) {\n            return [sofar, current.union(item)];\n          } else {\n            return [sofar.concat([current]), item];\n          }\n        },\n        [[], null]\n      );\n    if (final) {\n      found.push(final);\n    }\n    return found;\n  }\n\n  /**\n   * Return an array of Intervals representing the spans of time that only appear in one of the specified Intervals.\n   * @param {Array} intervals\n   * @return {Array}\n   */\n  static xor(intervals) {\n    let start = null,\n      currentCount = 0;\n    const results = [],\n      ends = intervals.map((i) => [\n        { time: i.s, type: \"s\" },\n        { time: i.e, type: \"e\" },\n      ]),\n      flattened = Array.prototype.concat(...ends),\n      arr = flattened.sort((a, b) => a.time - b.time);\n\n    for (const i of arr) {\n      currentCount += i.type === \"s\" ? 1 : -1;\n\n      if (currentCount === 1) {\n        start = i.time;\n      } else {\n        if (start && +start !== +i.time) {\n          results.push(Interval.fromDateTimes(start, i.time));\n        }\n\n        start = null;\n      }\n    }\n\n    return Interval.merge(results);\n  }\n\n  /**\n   * Return an Interval representing the span of time in this Interval that doesn't overlap with any of the specified Intervals.\n   * @param {...Interval} intervals\n   * @return {Array}\n   */\n  difference(...intervals) {\n    return Interval.xor([this].concat(intervals))\n      .map((i) => this.intersection(i))\n      .filter((i) => i && !i.isEmpty());\n  }\n\n  /**\n   * Returns a string representation of this Interval appropriate for debugging.\n   * @return {string}\n   */\n  toString() {\n    if (!this.isValid) return INVALID;\n    return `[${this.s.toISO()} – ${this.e.toISO()})`;\n  }\n\n  /**\n   * Returns a string representation of this Interval appropriate for the REPL.\n   * @return {string}\n   */\n  [Symbol.for(\"nodejs.util.inspect.custom\")]() {\n    if (this.isValid) {\n      return `Interval { start: ${this.s.toISO()}, end: ${this.e.toISO()} }`;\n    } else {\n      return `Interval { Invalid, reason: ${this.invalidReason} }`;\n    }\n  }\n\n  /**\n   * Returns a localized string representing this Interval. Accepts the same options as the\n   * Intl.DateTimeFormat constructor and any presets defined by Luxon, such as\n   * {@link DateTime.DATE_FULL} or {@link DateTime.TIME_SIMPLE}. The exact behavior of this method\n   * is browser-specific, but in general it will return an appropriate representation of the\n   * Interval in the assigned locale. Defaults to the system's locale if no locale has been\n   * specified.\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat\n   * @param {Object} [formatOpts=DateTime.DATE_SHORT] - Either a DateTime preset or\n   * Intl.DateTimeFormat constructor options.\n   * @param {Object} opts - Options to override the configuration of the start DateTime.\n   * @example Interval.fromISO('2022-11-07T09:00Z/2022-11-08T09:00Z').toLocaleString(); //=> 11/7/2022 – 11/8/2022\n   * @example Interval.fromISO('2022-11-07T09:00Z/2022-11-08T09:00Z').toLocaleString(DateTime.DATE_FULL); //=> November 7 – 8, 2022\n   * @example Interval.fromISO('2022-11-07T09:00Z/2022-11-08T09:00Z').toLocaleString(DateTime.DATE_FULL, { locale: 'fr-FR' }); //=> 7–8 novembre 2022\n   * @example Interval.fromISO('2022-11-07T17:00Z/2022-11-07T19:00Z').toLocaleString(DateTime.TIME_SIMPLE); //=> 6:00 – 8:00 PM\n   * @example Interval.fromISO('2022-11-07T17:00Z/2022-11-07T19:00Z').toLocaleString({ weekday: 'short', month: 'short', day: '2-digit', hour: '2-digit', minute: '2-digit' }); //=> Mon, Nov 07, 6:00 – 8:00 p\n   * @return {string}\n   */\n  toLocaleString(formatOpts = Formats.DATE_SHORT, opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.s.loc.clone(opts), formatOpts).formatInterval(this)\n      : INVALID;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this Interval.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @param {Object} opts - The same options as {@link DateTime#toISO}\n   * @return {string}\n   */\n  toISO(opts) {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toISO(opts)}/${this.e.toISO(opts)}`;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of date of this Interval.\n   * The time components are ignored.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @return {string}\n   */\n  toISODate() {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toISODate()}/${this.e.toISODate()}`;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of time of this Interval.\n   * The date components are ignored.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @param {Object} opts - The same options as {@link DateTime#toISO}\n   * @return {string}\n   */\n  toISOTime(opts) {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toISOTime(opts)}/${this.e.toISOTime(opts)}`;\n  }\n\n  /**\n   * Returns a string representation of this Interval formatted according to the specified format\n   * string. **You may not want this.** See {@link Interval#toLocaleString} for a more flexible\n   * formatting tool.\n   * @param {string} dateFormat - The format string. This string formats the start and end time.\n   * See {@link DateTime#toFormat} for details.\n   * @param {Object} opts - Options.\n   * @param {string} [opts.separator =  ' – '] - A separator to place between the start and end\n   * representations.\n   * @return {string}\n   */\n  toFormat(dateFormat, { separator = \" – \" } = {}) {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toFormat(dateFormat)}${separator}${this.e.toFormat(dateFormat)}`;\n  }\n\n  /**\n   * Return a Duration representing the time spanned by this interval.\n   * @param {string|string[]} [unit=['milliseconds']] - the unit or units (such as 'hours' or 'days') to include in the duration.\n   * @param {Object} opts - options that affect the creation of the Duration\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration().toObject() //=> { milliseconds: 88489257 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration('days').toObject() //=> { days: 1.0241812152777778 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration(['hours', 'minutes']).toObject() //=> { hours: 24, minutes: 34.82095 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration(['hours', 'minutes', 'seconds']).toObject() //=> { hours: 24, minutes: 34, seconds: 49.257 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration('seconds').toObject() //=> { seconds: 88489.257 }\n   * @return {Duration}\n   */\n  toDuration(unit, opts) {\n    if (!this.isValid) {\n      return Duration.invalid(this.invalidReason);\n    }\n    return this.e.diff(this.s, unit, opts);\n  }\n\n  /**\n   * Run mapFn on the interval start and end, returning a new Interval from the resulting DateTimes\n   * @param {function} mapFn\n   * @return {Interval}\n   * @example Interval.fromDateTimes(dt1, dt2).mapEndpoints(endpoint => endpoint.toUTC())\n   * @example Interval.fromDateTimes(dt1, dt2).mapEndpoints(endpoint => endpoint.plus({ hours: 2 }))\n   */\n  mapEndpoints(mapFn) {\n    return Interval.fromDateTimes(mapFn(this.s), mapFn(this.e));\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,MAAM,UAAU;AAEhB,oDAAoD;AACpD,SAAS,iBAAiB,KAAK,EAAE,GAAG;IAClC,IAAI,CAAC,SAAS,CAAC,MAAM,OAAO,EAAE;QAC5B,OAAO,SAAS,OAAO,CAAC;IAC1B,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,OAAO,EAAE;QAC/B,OAAO,SAAS,OAAO,CAAC;IAC1B,OAAO,IAAI,MAAM,OAAO;QACtB,OAAO,SAAS,OAAO,CACrB,oBACA,CAAC,kEAAkE,EAAE,MAAM,KAAK,GAAG,SAAS,EAAE,IAAI,KAAK,IAAI;IAE/G,OAAO;QACL,OAAO;IACT;AACF;AAce,MAAM;IACnB;;GAEC,GACD,YAAY,MAAM,CAAE;QAClB;;KAEC,GACD,IAAI,CAAC,CAAC,GAAG,OAAO,KAAK;QACrB;;KAEC,GACD,IAAI,CAAC,CAAC,GAAG,OAAO,GAAG;QACnB;;KAEC,GACD,IAAI,CAAC,OAAO,GAAG,OAAO,OAAO,IAAI;QACjC;;KAEC,GACD,IAAI,CAAC,eAAe,GAAG;IACzB;IAEA;;;;;GAKC,GACD,OAAO,QAAQ,MAAM,EAAE,cAAc,IAAI,EAAE;QACzC,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,8IAAA,CAAA,uBAAoB,CAAC;QACjC;QAEA,MAAM,UAAU,kBAAkB,uJAAA,CAAA,UAAO,GAAG,SAAS,IAAI,uJAAA,CAAA,UAAO,CAAC,QAAQ;QAEzE,IAAI,gJAAA,CAAA,UAAQ,CAAC,cAAc,EAAE;YAC3B,MAAM,IAAI,8IAAA,CAAA,uBAAoB,CAAC;QACjC,OAAO;YACL,OAAO,IAAI,SAAS;gBAAE;YAAQ;QAChC;IACF;IAEA;;;;;GAKC,GACD,OAAO,cAAc,KAAK,EAAE,GAAG,EAAE;QAC/B,MAAM,aAAa,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD,EAAE,QAClC,WAAW,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD,EAAE;QAE9B,MAAM,gBAAgB,iBAAiB,YAAY;QAEnD,IAAI,iBAAiB,MAAM;YACzB,OAAO,IAAI,SAAS;gBAClB,OAAO;gBACP,KAAK;YACP;QACF,OAAO;YACL,OAAO;QACT;IACF;IAEA;;;;;GAKC,GACD,OAAO,MAAM,KAAK,EAAE,QAAQ,EAAE;QAC5B,MAAM,MAAM,gJAAA,CAAA,UAAQ,CAAC,gBAAgB,CAAC,WACpC,KAAK,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD,EAAE;QACxB,OAAO,SAAS,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC;IAC5C;IAEA;;;;;GAKC,GACD,OAAO,OAAO,GAAG,EAAE,QAAQ,EAAE;QAC3B,MAAM,MAAM,gJAAA,CAAA,UAAQ,CAAC,gBAAgB,CAAC,WACpC,KAAK,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD,EAAE;QACxB,OAAO,SAAS,aAAa,CAAC,GAAG,KAAK,CAAC,MAAM;IAC/C;IAEA;;;;;;;GAOC,GACD,OAAO,QAAQ,IAAI,EAAE,IAAI,EAAE;QACzB,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,KAAK;QACvC,IAAI,KAAK,GAAG;YACV,IAAI,OAAO;YACX,IAAI;gBACF,QAAQ,gJAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,GAAG;gBAC5B,eAAe,MAAM,OAAO;YAC9B,EAAE,OAAO,GAAG;gBACV,eAAe;YACjB;YAEA,IAAI,KAAK;YACT,IAAI;gBACF,MAAM,gJAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,GAAG;gBAC1B,aAAa,IAAI,OAAO;YAC1B,EAAE,OAAO,GAAG;gBACV,aAAa;YACf;YAEA,IAAI,gBAAgB,YAAY;gBAC9B,OAAO,SAAS,aAAa,CAAC,OAAO;YACvC;YAEA,IAAI,cAAc;gBAChB,MAAM,MAAM,gJAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,GAAG;gBAChC,IAAI,IAAI,OAAO,EAAE;oBACf,OAAO,SAAS,KAAK,CAAC,OAAO;gBAC/B;YACF,OAAO,IAAI,YAAY;gBACrB,MAAM,MAAM,gJAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,GAAG;gBAChC,IAAI,IAAI,OAAO,EAAE;oBACf,OAAO,SAAS,MAAM,CAAC,KAAK;gBAC9B;YACF;QACF;QACA,OAAO,SAAS,OAAO,CAAC,cAAc,CAAC,WAAW,EAAE,KAAK,6BAA6B,CAAC;IACzF;IAEA;;;;GAIC,GACD,OAAO,WAAW,CAAC,EAAE;QACnB,OAAO,AAAC,KAAK,EAAE,eAAe,IAAK;IACrC;IAEA;;;GAGC,GACD,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,GAAG;IACjC;IAEA;;;GAGC,GACD,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,GAAG;IACjC;IAEA;;;GAGC,GACD,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,OAAO,GAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,OAAQ;IAC5D;IAEA;;;GAGC,GACD,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,aAAa,KAAK;IAChC;IAEA;;;GAGC,GACD,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG;IAC9C;IAEA;;;GAGC,GACD,IAAI,qBAAqB;QACvB,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG;IACnD;IAEA;;;;GAIC,GACD,OAAO,OAAO,cAAc,EAAE;QAC5B,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,IAAI;YAAC;SAAK,EAAE,GAAG,CAAC,QAAQ;IAC/D;IAEA;;;;;;;;GAQC,GACD,MAAM,OAAO,cAAc,EAAE,IAAI,EAAE;QACjC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;QAC1B,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM;QACvC,IAAI;QACJ,IAAI,MAAM,gBAAgB;YACxB,MAAM,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC;gBAAE,QAAQ,MAAM,MAAM;YAAC;QACpD,OAAO;YACL,MAAM,IAAI,CAAC,GAAG;QAChB;QACA,MAAM,IAAI,OAAO,CAAC,MAAM;QACxB,OAAO,KAAK,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,MAAM,GAAG,CAAC,SAAS,CAAC,IAAI,OAAO,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;IAC5F;IAEA;;;;GAIC,GACD,QAAQ,IAAI,EAAE;QACZ,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,MAAM,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ;IAClF;IAEA;;;GAGC,GACD,UAAU;QACR,OAAO,IAAI,CAAC,CAAC,CAAC,OAAO,OAAO,IAAI,CAAC,CAAC,CAAC,OAAO;IAC5C;IAEA;;;;GAIC,GACD,QAAQ,QAAQ,EAAE;QAChB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;QAC1B,OAAO,IAAI,CAAC,CAAC,GAAG;IAClB;IAEA;;;;GAIC,GACD,SAAS,QAAQ,EAAE;QACjB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;QAC1B,OAAO,IAAI,CAAC,CAAC,IAAI;IACnB;IAEA;;;;GAIC,GACD,SAAS,QAAQ,EAAE;QACjB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;QAC1B,OAAO,IAAI,CAAC,CAAC,IAAI,YAAY,IAAI,CAAC,CAAC,GAAG;IACxC;IAEA;;;;;;GAMC,GACD,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE;QACvB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI;QAC9B,OAAO,SAAS,aAAa,CAAC,SAAS,IAAI,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC,CAAC;IAC9D;IAEA;;;;GAIC,GACD,QAAQ,GAAG,SAAS,EAAE;QACpB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE;QAC5B,MAAM,SAAS,UACV,GAAG,CAAC,gJAAA,CAAA,mBAAgB,EACpB,MAAM,CAAC,CAAC,IAAM,IAAI,CAAC,QAAQ,CAAC,IAC5B,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,KAAK,EAAE,QAAQ,KAC3C,UAAU,EAAE;QACd,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,EACd,IAAI;QAEN,MAAO,IAAI,IAAI,CAAC,CAAC,CAAE;YACjB,MAAM,QAAQ,MAAM,CAAC,EAAE,IAAI,IAAI,CAAC,CAAC,EAC/B,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG;YACrC,QAAQ,IAAI,CAAC,SAAS,aAAa,CAAC,GAAG;YACvC,IAAI;YACJ,KAAK;QACP;QAEA,OAAO;IACT;IAEA;;;;;GAKC,GACD,QAAQ,QAAQ,EAAE;QAChB,MAAM,MAAM,gJAAA,CAAA,UAAQ,CAAC,gBAAgB,CAAC;QAEtC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC,oBAAoB,GAAG;YACjE,OAAO,EAAE;QACX;QAEA,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,EACd,MAAM,GACN;QAEF,MAAM,UAAU,EAAE;QAClB,MAAO,IAAI,IAAI,CAAC,CAAC,CAAE;YACjB,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,CAAC,IAAM,IAAI;YACtD,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG;YACnC,QAAQ,IAAI,CAAC,SAAS,aAAa,CAAC,GAAG;YACvC,IAAI;YACJ,OAAO;QACT;QAEA,OAAO;IACT;IAEA;;;;GAIC,GACD,cAAc,aAAa,EAAE;QAC3B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,KAAK,eAAe,KAAK,CAAC,GAAG;IAC9D;IAEA;;;;GAIC,GACD,SAAS,KAAK,EAAE;QACd,OAAO,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC;IAC7C;IAEA;;;;GAIC,GACD,WAAW,KAAK,EAAE;QAChB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;QAC1B,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;IAC7B;IAEA;;;;GAIC,GACD,SAAS,KAAK,EAAE;QACd,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;QAC1B,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC7B;IAEA;;;;GAIC,GACD,QAAQ,KAAK,EAAE;QACb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;QAC1B,OAAO,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC;IAC/C;IAEA;;;;GAIC,GACD,OAAO,KAAK,EAAE;QACZ,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,OAAO,EAAE;YACnC,OAAO;QACT;QAEA,OAAO,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC;IACxD;IAEA;;;;;;GAMC,GACD,aAAa,KAAK,EAAE;QAClB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI;QAC9B,MAAM,IAAI,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,EAC3C,IAAI,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC;QAEzC,IAAI,KAAK,GAAG;YACV,OAAO;QACT,OAAO;YACL,OAAO,SAAS,aAAa,CAAC,GAAG;QACnC;IACF;IAEA;;;;;GAKC,GACD,MAAM,KAAK,EAAE;QACX,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI;QAC9B,MAAM,IAAI,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,EAC3C,IAAI,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC;QACzC,OAAO,SAAS,aAAa,CAAC,GAAG;IACnC;IAEA;;;;;;;;GAQC,GACD,OAAO,MAAM,SAAS,EAAE;QACtB,MAAM,CAAC,OAAO,MAAM,GAAG,UACpB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC,EACxB,MAAM,CACL,CAAC,CAAC,OAAO,QAAQ,EAAE;YACjB,IAAI,CAAC,SAAS;gBACZ,OAAO;oBAAC;oBAAO;iBAAK;YACtB,OAAO,IAAI,QAAQ,QAAQ,CAAC,SAAS,QAAQ,UAAU,CAAC,OAAO;gBAC7D,OAAO;oBAAC;oBAAO,QAAQ,KAAK,CAAC;iBAAM;YACrC,OAAO;gBACL,OAAO;oBAAC,MAAM,MAAM,CAAC;wBAAC;qBAAQ;oBAAG;iBAAK;YACxC;QACF,GACA;YAAC,EAAE;YAAE;SAAK;QAEd,IAAI,OAAO;YACT,MAAM,IAAI,CAAC;QACb;QACA,OAAO;IACT;IAEA;;;;GAIC,GACD,OAAO,IAAI,SAAS,EAAE;QACpB,IAAI,QAAQ,MACV,eAAe;QACjB,MAAM,UAAU,EAAE,EAChB,OAAO,UAAU,GAAG,CAAC,CAAC,IAAM;gBAC1B;oBAAE,MAAM,EAAE,CAAC;oBAAE,MAAM;gBAAI;gBACvB;oBAAE,MAAM,EAAE,CAAC;oBAAE,MAAM;gBAAI;aACxB,GACD,YAAY,MAAM,SAAS,CAAC,MAAM,IAAI,OACtC,MAAM,UAAU,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,GAAG,EAAE,IAAI;QAEhD,KAAK,MAAM,KAAK,IAAK;YACnB,gBAAgB,EAAE,IAAI,KAAK,MAAM,IAAI,CAAC;YAEtC,IAAI,iBAAiB,GAAG;gBACtB,QAAQ,EAAE,IAAI;YAChB,OAAO;gBACL,IAAI,SAAS,CAAC,UAAU,CAAC,EAAE,IAAI,EAAE;oBAC/B,QAAQ,IAAI,CAAC,SAAS,aAAa,CAAC,OAAO,EAAE,IAAI;gBACnD;gBAEA,QAAQ;YACV;QACF;QAEA,OAAO,SAAS,KAAK,CAAC;IACxB;IAEA;;;;GAIC,GACD,WAAW,GAAG,SAAS,EAAE;QACvB,OAAO,SAAS,GAAG,CAAC;YAAC,IAAI;SAAC,CAAC,MAAM,CAAC,YAC/B,GAAG,CAAC,CAAC,IAAM,IAAI,CAAC,YAAY,CAAC,IAC7B,MAAM,CAAC,CAAC,IAAM,KAAK,CAAC,EAAE,OAAO;IAClC;IAEA;;;GAGC,GACD,WAAW;QACT,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;QAC1B,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;IAClD;IAEA;;;GAGC,GACD,CAAC,OAAO,GAAG,CAAC,8BAA8B,GAAG;QAC3C,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,OAAO,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;QACxE,OAAO;YACL,OAAO,CAAC,4BAA4B,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;QAC9D;IACF;IAEA;;;;;;;;;;;;;;;;;GAiBC,GACD,eAAe,aAAa,uJAAA,CAAA,aAAkB,EAAE,OAAO,CAAC,CAAC,EAAE;QACzD,OAAO,IAAI,CAAC,OAAO,GACf,yJAAA,CAAA,UAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,YAAY,cAAc,CAAC,IAAI,IACxE;IACN;IAEA;;;;;GAKC,GACD,MAAM,IAAI,EAAE;QACV,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;QAC1B,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO;IACtD;IAEA;;;;;GAKC,GACD,YAAY;QACV,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;QAC1B,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,SAAS,IAAI;IACtD;IAEA;;;;;;GAMC,GACD,UAAU,IAAI,EAAE;QACd,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;QAC1B,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO;IAC9D;IAEA;;;;;;;;;;GAUC,GACD,SAAS,UAAU,EAAE,EAAE,YAAY,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE;QAC/C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;QAC1B,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,cAAc,YAAY,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa;IACnF;IAEA;;;;;;;;;;;GAWC,GACD,WAAW,IAAI,EAAE,IAAI,EAAE;QACrB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,OAAO,gJAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa;QAC5C;QACA,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM;IACnC;IAEA;;;;;;GAMC,GACD,aAAa,KAAK,EAAE;QAClB,OAAO,SAAS,aAAa,CAAC,MAAM,IAAI,CAAC,CAAC,GAAG,MAAM,IAAI,CAAC,CAAC;IAC3D;AACF", "ignoreList": [0]}}, {"offset": {"line": 4620, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/luxon/src/info.js"], "sourcesContent": ["import DateTime from \"./datetime.js\";\nimport Settings from \"./settings.js\";\nimport Locale from \"./impl/locale.js\";\nimport IANAZone from \"./zones/IANAZone.js\";\nimport { normalizeZone } from \"./impl/zoneUtil.js\";\n\nimport { hasLocaleWeekInfo, hasRelative } from \"./impl/util.js\";\n\n/**\n * The Info class contains static methods for retrieving general time and date related data. For example, it has methods for finding out if a time zone has a DST, for listing the months in any supported locale, and for discovering which of Luxon features are available in the current environment.\n */\nexport default class Info {\n  /**\n   * Return whether the specified zone contains a DST.\n   * @param {string|Zone} [zone='local'] - Zone to check. Defaults to the environment's local zone.\n   * @return {boolean}\n   */\n  static hasDST(zone = Settings.defaultZone) {\n    const proto = DateTime.now().setZone(zone).set({ month: 12 });\n\n    return !zone.isUniversal && proto.offset !== proto.set({ month: 6 }).offset;\n  }\n\n  /**\n   * Return whether the specified zone is a valid IANA specifier.\n   * @param {string} zone - Zone to check\n   * @return {boolean}\n   */\n  static isValidIANAZone(zone) {\n    return IANAZone.isValidZone(zone);\n  }\n\n  /**\n   * Converts the input into a {@link Zone} instance.\n   *\n   * * If `input` is already a Zone instance, it is returned unchanged.\n   * * If `input` is a string containing a valid time zone name, a Zone instance\n   *   with that name is returned.\n   * * If `input` is a string that doesn't refer to a known time zone, a Zone\n   *   instance with {@link Zone#isValid} == false is returned.\n   * * If `input is a number, a Zone instance with the specified fixed offset\n   *   in minutes is returned.\n   * * If `input` is `null` or `undefined`, the default zone is returned.\n   * @param {string|Zone|number} [input] - the value to be converted\n   * @return {Zone}\n   */\n  static normalizeZone(input) {\n    return normalizeZone(input, Settings.defaultZone);\n  }\n\n  /**\n   * Get the weekday on which the week starts according to the given locale.\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @returns {number} the start of the week, 1 for Monday through 7 for Sunday\n   */\n  static getStartOfWeek({ locale = null, locObj = null } = {}) {\n    return (locObj || Locale.create(locale)).getStartOfWeek();\n  }\n\n  /**\n   * Get the minimum number of days necessary in a week before it is considered part of the next year according\n   * to the given locale.\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @returns {number}\n   */\n  static getMinimumDaysInFirstWeek({ locale = null, locObj = null } = {}) {\n    return (locObj || Locale.create(locale)).getMinDaysInFirstWeek();\n  }\n\n  /**\n   * Get the weekdays, which are considered the weekend according to the given locale\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @returns {number[]} an array of weekdays, 1 for Monday through 7 for Sunday\n   */\n  static getWeekendWeekdays({ locale = null, locObj = null } = {}) {\n    // copy the array, because we cache it internally\n    return (locObj || Locale.create(locale)).getWeekendDays().slice();\n  }\n\n  /**\n   * Return an array of standalone month names.\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat\n   * @param {string} [length='long'] - the length of the month representation, such as \"numeric\", \"2-digit\", \"narrow\", \"short\", \"long\"\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.numberingSystem=null] - the numbering system\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @param {string} [opts.outputCalendar='gregory'] - the calendar\n   * @example Info.months()[0] //=> 'January'\n   * @example Info.months('short')[0] //=> 'Jan'\n   * @example Info.months('numeric')[0] //=> '1'\n   * @example Info.months('short', { locale: 'fr-CA' } )[0] //=> 'janv.'\n   * @example Info.months('numeric', { locale: 'ar' })[0] //=> '١'\n   * @example Info.months('long', { outputCalendar: 'islamic' })[0] //=> 'Rabiʻ I'\n   * @return {Array}\n   */\n  static months(\n    length = \"long\",\n    { locale = null, numberingSystem = null, locObj = null, outputCalendar = \"gregory\" } = {}\n  ) {\n    return (locObj || Locale.create(locale, numberingSystem, outputCalendar)).months(length);\n  }\n\n  /**\n   * Return an array of format month names.\n   * Format months differ from standalone months in that they're meant to appear next to the day of the month. In some languages, that\n   * changes the string.\n   * See {@link Info#months}\n   * @param {string} [length='long'] - the length of the month representation, such as \"numeric\", \"2-digit\", \"narrow\", \"short\", \"long\"\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.numberingSystem=null] - the numbering system\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @param {string} [opts.outputCalendar='gregory'] - the calendar\n   * @return {Array}\n   */\n  static monthsFormat(\n    length = \"long\",\n    { locale = null, numberingSystem = null, locObj = null, outputCalendar = \"gregory\" } = {}\n  ) {\n    return (locObj || Locale.create(locale, numberingSystem, outputCalendar)).months(length, true);\n  }\n\n  /**\n   * Return an array of standalone week names.\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat\n   * @param {string} [length='long'] - the length of the weekday representation, such as \"narrow\", \"short\", \"long\".\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.numberingSystem=null] - the numbering system\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @example Info.weekdays()[0] //=> 'Monday'\n   * @example Info.weekdays('short')[0] //=> 'Mon'\n   * @example Info.weekdays('short', { locale: 'fr-CA' })[0] //=> 'lun.'\n   * @example Info.weekdays('short', { locale: 'ar' })[0] //=> 'الاثنين'\n   * @return {Array}\n   */\n  static weekdays(length = \"long\", { locale = null, numberingSystem = null, locObj = null } = {}) {\n    return (locObj || Locale.create(locale, numberingSystem, null)).weekdays(length);\n  }\n\n  /**\n   * Return an array of format week names.\n   * Format weekdays differ from standalone weekdays in that they're meant to appear next to more date information. In some languages, that\n   * changes the string.\n   * See {@link Info#weekdays}\n   * @param {string} [length='long'] - the length of the month representation, such as \"narrow\", \"short\", \"long\".\n   * @param {Object} opts - options\n   * @param {string} [opts.locale=null] - the locale code\n   * @param {string} [opts.numberingSystem=null] - the numbering system\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @return {Array}\n   */\n  static weekdaysFormat(\n    length = \"long\",\n    { locale = null, numberingSystem = null, locObj = null } = {}\n  ) {\n    return (locObj || Locale.create(locale, numberingSystem, null)).weekdays(length, true);\n  }\n\n  /**\n   * Return an array of meridiems.\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @example Info.meridiems() //=> [ 'AM', 'PM' ]\n   * @example Info.meridiems({ locale: 'my' }) //=> [ 'နံနက်', 'ညနေ' ]\n   * @return {Array}\n   */\n  static meridiems({ locale = null } = {}) {\n    return Locale.create(locale).meridiems();\n  }\n\n  /**\n   * Return an array of eras, such as ['BC', 'AD']. The locale can be specified, but the calendar system is always Gregorian.\n   * @param {string} [length='short'] - the length of the era representation, such as \"short\" or \"long\".\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @example Info.eras() //=> [ 'BC', 'AD' ]\n   * @example Info.eras('long') //=> [ 'Before Christ', 'Anno Domini' ]\n   * @example Info.eras('long', { locale: 'fr' }) //=> [ 'avant Jésus-Christ', 'après Jésus-Christ' ]\n   * @return {Array}\n   */\n  static eras(length = \"short\", { locale = null } = {}) {\n    return Locale.create(locale, null, \"gregory\").eras(length);\n  }\n\n  /**\n   * Return the set of available features in this environment.\n   * Some features of Luxon are not available in all environments. For example, on older browsers, relative time formatting support is not available. Use this function to figure out if that's the case.\n   * Keys:\n   * * `relative`: whether this environment supports relative time formatting\n   * * `localeWeek`: whether this environment supports different weekdays for the start of the week based on the locale\n   * @example Info.features() //=> { relative: false, localeWeek: true }\n   * @return {Object}\n   */\n  static features() {\n    return { relative: hasRelative(), localeWeek: hasLocaleWeekInfo() };\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AAEA;;;;;;;AAKe,MAAM;IACnB;;;;GAIC,GACD,OAAO,OAAO,OAAO,gJAAA,CAAA,UAAQ,CAAC,WAAW,EAAE;QACzC,MAAM,QAAQ,gJAAA,CAAA,UAAQ,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO;QAAG;QAE3D,OAAO,CAAC,KAAK,WAAW,IAAI,MAAM,MAAM,KAAK,MAAM,GAAG,CAAC;YAAE,OAAO;QAAE,GAAG,MAAM;IAC7E;IAEA;;;;GAIC,GACD,OAAO,gBAAgB,IAAI,EAAE;QAC3B,OAAO,yJAAA,CAAA,UAAQ,CAAC,WAAW,CAAC;IAC9B;IAEA;;;;;;;;;;;;;GAaC,GACD,OAAO,cAAc,KAAK,EAAE;QAC1B,OAAO,CAAA,GAAA,wJAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,gJAAA,CAAA,UAAQ,CAAC,WAAW;IAClD;IAEA;;;;;;GAMC,GACD,OAAO,eAAe,EAAE,SAAS,IAAI,EAAE,SAAS,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE;QAC3D,OAAO,CAAC,UAAU,sJAAA,CAAA,UAAM,CAAC,MAAM,CAAC,OAAO,EAAE,cAAc;IACzD;IAEA;;;;;;;GAOC,GACD,OAAO,0BAA0B,EAAE,SAAS,IAAI,EAAE,SAAS,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE;QACtE,OAAO,CAAC,UAAU,sJAAA,CAAA,UAAM,CAAC,MAAM,CAAC,OAAO,EAAE,qBAAqB;IAChE;IAEA;;;;;;GAMC,GACD,OAAO,mBAAmB,EAAE,SAAS,IAAI,EAAE,SAAS,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE;QAC/D,iDAAiD;QACjD,OAAO,CAAC,UAAU,sJAAA,CAAA,UAAM,CAAC,MAAM,CAAC,OAAO,EAAE,cAAc,GAAG,KAAK;IACjE;IAEA;;;;;;;;;;;;;;;;GAgBC,GACD,OAAO,OACL,SAAS,MAAM,EACf,EAAE,SAAS,IAAI,EAAE,kBAAkB,IAAI,EAAE,SAAS,IAAI,EAAE,iBAAiB,SAAS,EAAE,GAAG,CAAC,CAAC,EACzF;QACA,OAAO,CAAC,UAAU,sJAAA,CAAA,UAAM,CAAC,MAAM,CAAC,QAAQ,iBAAiB,eAAe,EAAE,MAAM,CAAC;IACnF;IAEA;;;;;;;;;;;;GAYC,GACD,OAAO,aACL,SAAS,MAAM,EACf,EAAE,SAAS,IAAI,EAAE,kBAAkB,IAAI,EAAE,SAAS,IAAI,EAAE,iBAAiB,SAAS,EAAE,GAAG,CAAC,CAAC,EACzF;QACA,OAAO,CAAC,UAAU,sJAAA,CAAA,UAAM,CAAC,MAAM,CAAC,QAAQ,iBAAiB,eAAe,EAAE,MAAM,CAAC,QAAQ;IAC3F;IAEA;;;;;;;;;;;;;GAaC,GACD,OAAO,SAAS,SAAS,MAAM,EAAE,EAAE,SAAS,IAAI,EAAE,kBAAkB,IAAI,EAAE,SAAS,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE;QAC9F,OAAO,CAAC,UAAU,sJAAA,CAAA,UAAM,CAAC,MAAM,CAAC,QAAQ,iBAAiB,KAAK,EAAE,QAAQ,CAAC;IAC3E;IAEA;;;;;;;;;;;GAWC,GACD,OAAO,eACL,SAAS,MAAM,EACf,EAAE,SAAS,IAAI,EAAE,kBAAkB,IAAI,EAAE,SAAS,IAAI,EAAE,GAAG,CAAC,CAAC,EAC7D;QACA,OAAO,CAAC,UAAU,sJAAA,CAAA,UAAM,CAAC,MAAM,CAAC,QAAQ,iBAAiB,KAAK,EAAE,QAAQ,CAAC,QAAQ;IACnF;IAEA;;;;;;;GAOC,GACD,OAAO,UAAU,EAAE,SAAS,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE;QACvC,OAAO,sJAAA,CAAA,UAAM,CAAC,MAAM,CAAC,QAAQ,SAAS;IACxC;IAEA;;;;;;;;;GASC,GACD,OAAO,KAAK,SAAS,OAAO,EAAE,EAAE,SAAS,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE;QACpD,OAAO,sJAAA,CAAA,UAAM,CAAC,MAAM,CAAC,QAAQ,MAAM,WAAW,IAAI,CAAC;IACrD;IAEA;;;;;;;;GAQC,GACD,OAAO,WAAW;QAChB,OAAO;YAAE,UAAU,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD;YAAK,YAAY,CAAA,GAAA,oJAAA,CAAA,oBAAiB,AAAD;QAAI;IACpE;AACF", "ignoreList": [0]}}, {"offset": {"line": 4807, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/luxon/src/impl/diff.js"], "sourcesContent": ["import Duration from \"../duration.js\";\n\nfunction dayDiff(earlier, later) {\n  const utcDayStart = (dt) => dt.toUTC(0, { keepLocalTime: true }).startOf(\"day\").valueOf(),\n    ms = utcDayStart(later) - utcDayStart(earlier);\n  return Math.floor(Duration.fromMillis(ms).as(\"days\"));\n}\n\nfunction highOrderDiffs(cursor, later, units) {\n  const differs = [\n    [\"years\", (a, b) => b.year - a.year],\n    [\"quarters\", (a, b) => b.quarter - a.quarter + (b.year - a.year) * 4],\n    [\"months\", (a, b) => b.month - a.month + (b.year - a.year) * 12],\n    [\n      \"weeks\",\n      (a, b) => {\n        const days = dayDiff(a, b);\n        return (days - (days % 7)) / 7;\n      },\n    ],\n    [\"days\", dayDiff],\n  ];\n\n  const results = {};\n  const earlier = cursor;\n  let lowestOrder, highWater;\n\n  /* This loop tries to diff using larger units first.\n     If we overshoot, we backtrack and try the next smaller unit.\n     \"cursor\" starts out at the earlier timestamp and moves closer and closer to \"later\"\n     as we use smaller and smaller units.\n     highWater keeps track of where we would be if we added one more of the smallest unit,\n     this is used later to potentially convert any difference smaller than the smallest higher order unit\n     into a fraction of that smallest higher order unit\n  */\n  for (const [unit, differ] of differs) {\n    if (units.indexOf(unit) >= 0) {\n      lowestOrder = unit;\n\n      results[unit] = differ(cursor, later);\n      highWater = earlier.plus(results);\n\n      if (highWater > later) {\n        // we overshot the end point, backtrack cursor by 1\n        results[unit]--;\n        cursor = earlier.plus(results);\n\n        // if we are still overshooting now, we need to backtrack again\n        // this happens in certain situations when diffing times in different zones,\n        // because this calculation ignores time zones\n        if (cursor > later) {\n          // keep the \"overshot by 1\" around as highWater\n          highWater = cursor;\n          // backtrack cursor by 1\n          results[unit]--;\n          cursor = earlier.plus(results);\n        }\n      } else {\n        cursor = highWater;\n      }\n    }\n  }\n\n  return [cursor, results, highWater, lowestOrder];\n}\n\nexport default function (earlier, later, units, opts) {\n  let [cursor, results, highWater, lowestOrder] = highOrderDiffs(earlier, later, units);\n\n  const remainingMillis = later - cursor;\n\n  const lowerOrderUnits = units.filter(\n    (u) => [\"hours\", \"minutes\", \"seconds\", \"milliseconds\"].indexOf(u) >= 0\n  );\n\n  if (lowerOrderUnits.length === 0) {\n    if (highWater < later) {\n      highWater = cursor.plus({ [lowestOrder]: 1 });\n    }\n\n    if (highWater !== cursor) {\n      results[lowestOrder] = (results[lowestOrder] || 0) + remainingMillis / (highWater - cursor);\n    }\n  }\n\n  const duration = Duration.fromObject(results, opts);\n\n  if (lowerOrderUnits.length > 0) {\n    return Duration.fromMillis(remainingMillis, opts)\n      .shiftTo(...lowerOrderUnits)\n      .plus(duration);\n  } else {\n    return duration;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,QAAQ,OAAO,EAAE,KAAK;IAC7B,MAAM,cAAc,CAAC,KAAO,GAAG,KAAK,CAAC,GAAG;YAAE,eAAe;QAAK,GAAG,OAAO,CAAC,OAAO,OAAO,IACrF,KAAK,YAAY,SAAS,YAAY;IACxC,OAAO,KAAK,KAAK,CAAC,gJAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;AAC/C;AAEA,SAAS,eAAe,MAAM,EAAE,KAAK,EAAE,KAAK;IAC1C,MAAM,UAAU;QACd;YAAC;YAAS,CAAC,GAAG,IAAM,EAAE,IAAI,GAAG,EAAE,IAAI;SAAC;QACpC;YAAC;YAAY,CAAC,GAAG,IAAM,EAAE,OAAO,GAAG,EAAE,OAAO,GAAG,CAAC,EAAE,IAAI,GAAG,EAAE,IAAI,IAAI;SAAE;QACrE;YAAC;YAAU,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,GAAG,CAAC,EAAE,IAAI,GAAG,EAAE,IAAI,IAAI;SAAG;QAChE;YACE;YACA,CAAC,GAAG;gBACF,MAAM,OAAO,QAAQ,GAAG;gBACxB,OAAO,CAAC,OAAQ,OAAO,CAAE,IAAI;YAC/B;SACD;QACD;YAAC;YAAQ;SAAQ;KAClB;IAED,MAAM,UAAU,CAAC;IACjB,MAAM,UAAU;IAChB,IAAI,aAAa;IAEjB;;;;;;;EAOA,GACA,KAAK,MAAM,CAAC,MAAM,OAAO,IAAI,QAAS;QACpC,IAAI,MAAM,OAAO,CAAC,SAAS,GAAG;YAC5B,cAAc;YAEd,OAAO,CAAC,KAAK,GAAG,OAAO,QAAQ;YAC/B,YAAY,QAAQ,IAAI,CAAC;YAEzB,IAAI,YAAY,OAAO;gBACrB,mDAAmD;gBACnD,OAAO,CAAC,KAAK;gBACb,SAAS,QAAQ,IAAI,CAAC;gBAEtB,+DAA+D;gBAC/D,4EAA4E;gBAC5E,8CAA8C;gBAC9C,IAAI,SAAS,OAAO;oBAClB,+CAA+C;oBAC/C,YAAY;oBACZ,wBAAwB;oBACxB,OAAO,CAAC,KAAK;oBACb,SAAS,QAAQ,IAAI,CAAC;gBACxB;YACF,OAAO;gBACL,SAAS;YACX;QACF;IACF;IAEA,OAAO;QAAC;QAAQ;QAAS;QAAW;KAAY;AAClD;AAEe,wCAAU,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI;IAClD,IAAI,CAAC,QAAQ,SAAS,WAAW,YAAY,GAAG,eAAe,SAAS,OAAO;IAE/E,MAAM,kBAAkB,QAAQ;IAEhC,MAAM,kBAAkB,MAAM,MAAM,CAClC,CAAC,IAAM;YAAC;YAAS;YAAW;YAAW;SAAe,CAAC,OAAO,CAAC,MAAM;IAGvE,IAAI,gBAAgB,MAAM,KAAK,GAAG;QAChC,IAAI,YAAY,OAAO;YACrB,YAAY,OAAO,IAAI,CAAC;gBAAE,CAAC,YAAY,EAAE;YAAE;QAC7C;QAEA,IAAI,cAAc,QAAQ;YACxB,OAAO,CAAC,YAAY,GAAG,CAAC,OAAO,CAAC,YAAY,IAAI,CAAC,IAAI,kBAAkB,CAAC,YAAY,MAAM;QAC5F;IACF;IAEA,MAAM,WAAW,gJAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,SAAS;IAE9C,IAAI,gBAAgB,MAAM,GAAG,GAAG;QAC9B,OAAO,gJAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,iBAAiB,MACzC,OAAO,IAAI,iBACX,IAAI,CAAC;IACV,OAAO;QACL,OAAO;IACT;AACF", "ignoreList": [0]}}, {"offset": {"line": 4917, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/luxon/src/impl/tokenParser.js"], "sourcesContent": ["import { parseM<PERSON>is, isUndefined, untruncate<PERSON>ear, signedOffset, hasOwnProperty } from \"./util.js\";\nimport Formatter from \"./formatter.js\";\nimport FixedOffsetZone from \"../zones/fixedOffsetZone.js\";\nimport IANAZone from \"../zones/IANAZone.js\";\nimport DateTime from \"../datetime.js\";\nimport { digitRegex, parseDigits } from \"./digits.js\";\nimport { ConflictingSpecificationError } from \"../errors.js\";\n\nconst MISSING_FTP = \"missing Intl.DateTimeFormat.formatToParts support\";\n\nfunction intUnit(regex, post = (i) => i) {\n  return { regex, deser: ([s]) => post(parseDigits(s)) };\n}\n\nconst NBSP = String.fromCharCode(160);\nconst spaceOrNBSP = `[ ${NBSP}]`;\nconst spaceOrNBSPRegExp = new RegExp(spaceOrNBSP, \"g\");\n\nfunction fixListRegex(s) {\n  // make dots optional and also make them literal\n  // make space and non breakable space characters interchangeable\n  return s.replace(/\\./g, \"\\\\.?\").replace(spaceOrNBSPRegExp, spaceOrNBSP);\n}\n\nfunction stripInsensitivities(s) {\n  return s\n    .replace(/\\./g, \"\") // ignore dots that were made optional\n    .replace(spaceOrNBSPRegExp, \" \") // interchange space and nbsp\n    .toLowerCase();\n}\n\nfunction oneOf(strings, startIndex) {\n  if (strings === null) {\n    return null;\n  } else {\n    return {\n      regex: RegExp(strings.map(fixListRegex).join(\"|\")),\n      deser: ([s]) =>\n        strings.findIndex((i) => stripInsensitivities(s) === stripInsensitivities(i)) + startIndex,\n    };\n  }\n}\n\nfunction offset(regex, groups) {\n  return { regex, deser: ([, h, m]) => signedOffset(h, m), groups };\n}\n\nfunction simple(regex) {\n  return { regex, deser: ([s]) => s };\n}\n\nfunction escapeToken(value) {\n  return value.replace(/[\\-\\[\\]{}()*+?.,\\\\\\^$|#\\s]/g, \"\\\\$&\");\n}\n\n/**\n * @param token\n * @param {Locale} loc\n */\nfunction unitForToken(token, loc) {\n  const one = digitRegex(loc),\n    two = digitRegex(loc, \"{2}\"),\n    three = digitRegex(loc, \"{3}\"),\n    four = digitRegex(loc, \"{4}\"),\n    six = digitRegex(loc, \"{6}\"),\n    oneOrTwo = digitRegex(loc, \"{1,2}\"),\n    oneToThree = digitRegex(loc, \"{1,3}\"),\n    oneToSix = digitRegex(loc, \"{1,6}\"),\n    oneToNine = digitRegex(loc, \"{1,9}\"),\n    twoToFour = digitRegex(loc, \"{2,4}\"),\n    fourToSix = digitRegex(loc, \"{4,6}\"),\n    literal = (t) => ({ regex: RegExp(escapeToken(t.val)), deser: ([s]) => s, literal: true }),\n    unitate = (t) => {\n      if (token.literal) {\n        return literal(t);\n      }\n      switch (t.val) {\n        // era\n        case \"G\":\n          return oneOf(loc.eras(\"short\"), 0);\n        case \"GG\":\n          return oneOf(loc.eras(\"long\"), 0);\n        // years\n        case \"y\":\n          return intUnit(oneToSix);\n        case \"yy\":\n          return intUnit(twoToFour, untruncateYear);\n        case \"yyyy\":\n          return intUnit(four);\n        case \"yyyyy\":\n          return intUnit(fourToSix);\n        case \"yyyyyy\":\n          return intUnit(six);\n        // months\n        case \"M\":\n          return intUnit(oneOrTwo);\n        case \"MM\":\n          return intUnit(two);\n        case \"MMM\":\n          return oneOf(loc.months(\"short\", true), 1);\n        case \"MMMM\":\n          return oneOf(loc.months(\"long\", true), 1);\n        case \"L\":\n          return intUnit(oneOrTwo);\n        case \"LL\":\n          return intUnit(two);\n        case \"LLL\":\n          return oneOf(loc.months(\"short\", false), 1);\n        case \"LLLL\":\n          return oneOf(loc.months(\"long\", false), 1);\n        // dates\n        case \"d\":\n          return intUnit(oneOrTwo);\n        case \"dd\":\n          return intUnit(two);\n        // ordinals\n        case \"o\":\n          return intUnit(oneToThree);\n        case \"ooo\":\n          return intUnit(three);\n        // time\n        case \"HH\":\n          return intUnit(two);\n        case \"H\":\n          return intUnit(oneOrTwo);\n        case \"hh\":\n          return intUnit(two);\n        case \"h\":\n          return intUnit(oneOrTwo);\n        case \"mm\":\n          return intUnit(two);\n        case \"m\":\n          return intUnit(oneOrTwo);\n        case \"q\":\n          return intUnit(oneOrTwo);\n        case \"qq\":\n          return intUnit(two);\n        case \"s\":\n          return intUnit(oneOrTwo);\n        case \"ss\":\n          return intUnit(two);\n        case \"S\":\n          return intUnit(oneToThree);\n        case \"SSS\":\n          return intUnit(three);\n        case \"u\":\n          return simple(oneToNine);\n        case \"uu\":\n          return simple(oneOrTwo);\n        case \"uuu\":\n          return intUnit(one);\n        // meridiem\n        case \"a\":\n          return oneOf(loc.meridiems(), 0);\n        // weekYear (k)\n        case \"kkkk\":\n          return intUnit(four);\n        case \"kk\":\n          return intUnit(twoToFour, untruncateYear);\n        // weekNumber (W)\n        case \"W\":\n          return intUnit(oneOrTwo);\n        case \"WW\":\n          return intUnit(two);\n        // weekdays\n        case \"E\":\n        case \"c\":\n          return intUnit(one);\n        case \"EEE\":\n          return oneOf(loc.weekdays(\"short\", false), 1);\n        case \"EEEE\":\n          return oneOf(loc.weekdays(\"long\", false), 1);\n        case \"ccc\":\n          return oneOf(loc.weekdays(\"short\", true), 1);\n        case \"cccc\":\n          return oneOf(loc.weekdays(\"long\", true), 1);\n        // offset/zone\n        case \"Z\":\n        case \"ZZ\":\n          return offset(new RegExp(`([+-]${oneOrTwo.source})(?::(${two.source}))?`), 2);\n        case \"ZZZ\":\n          return offset(new RegExp(`([+-]${oneOrTwo.source})(${two.source})?`), 2);\n        // we don't support ZZZZ (PST) or ZZZZZ (Pacific Standard Time) in parsing\n        // because we don't have any way to figure out what they are\n        case \"z\":\n          return simple(/[a-z_+-/]{1,256}?/i);\n        // this special-case \"token\" represents a place where a macro-token expanded into a white-space literal\n        // in this case we accept any non-newline white-space\n        case \" \":\n          return simple(/[^\\S\\n\\r]/);\n        default:\n          return literal(t);\n      }\n    };\n\n  const unit = unitate(token) || {\n    invalidReason: MISSING_FTP,\n  };\n\n  unit.token = token;\n\n  return unit;\n}\n\nconst partTypeStyleToTokenVal = {\n  year: {\n    \"2-digit\": \"yy\",\n    numeric: \"yyyyy\",\n  },\n  month: {\n    numeric: \"M\",\n    \"2-digit\": \"MM\",\n    short: \"MMM\",\n    long: \"MMMM\",\n  },\n  day: {\n    numeric: \"d\",\n    \"2-digit\": \"dd\",\n  },\n  weekday: {\n    short: \"EEE\",\n    long: \"EEEE\",\n  },\n  dayperiod: \"a\",\n  dayPeriod: \"a\",\n  hour12: {\n    numeric: \"h\",\n    \"2-digit\": \"hh\",\n  },\n  hour24: {\n    numeric: \"H\",\n    \"2-digit\": \"HH\",\n  },\n  minute: {\n    numeric: \"m\",\n    \"2-digit\": \"mm\",\n  },\n  second: {\n    numeric: \"s\",\n    \"2-digit\": \"ss\",\n  },\n  timeZoneName: {\n    long: \"ZZZZZ\",\n    short: \"ZZZ\",\n  },\n};\n\nfunction tokenForPart(part, formatOpts, resolvedOpts) {\n  const { type, value } = part;\n\n  if (type === \"literal\") {\n    const isSpace = /^\\s+$/.test(value);\n    return {\n      literal: !isSpace,\n      val: isSpace ? \" \" : value,\n    };\n  }\n\n  const style = formatOpts[type];\n\n  // The user might have explicitly specified hour12 or hourCycle\n  // if so, respect their decision\n  // if not, refer back to the resolvedOpts, which are based on the locale\n  let actualType = type;\n  if (type === \"hour\") {\n    if (formatOpts.hour12 != null) {\n      actualType = formatOpts.hour12 ? \"hour12\" : \"hour24\";\n    } else if (formatOpts.hourCycle != null) {\n      if (formatOpts.hourCycle === \"h11\" || formatOpts.hourCycle === \"h12\") {\n        actualType = \"hour12\";\n      } else {\n        actualType = \"hour24\";\n      }\n    } else {\n      // tokens only differentiate between 24 hours or not,\n      // so we do not need to check hourCycle here, which is less supported anyways\n      actualType = resolvedOpts.hour12 ? \"hour12\" : \"hour24\";\n    }\n  }\n  let val = partTypeStyleToTokenVal[actualType];\n  if (typeof val === \"object\") {\n    val = val[style];\n  }\n\n  if (val) {\n    return {\n      literal: false,\n      val,\n    };\n  }\n\n  return undefined;\n}\n\nfunction buildRegex(units) {\n  const re = units.map((u) => u.regex).reduce((f, r) => `${f}(${r.source})`, \"\");\n  return [`^${re}$`, units];\n}\n\nfunction match(input, regex, handlers) {\n  const matches = input.match(regex);\n\n  if (matches) {\n    const all = {};\n    let matchIndex = 1;\n    for (const i in handlers) {\n      if (hasOwnProperty(handlers, i)) {\n        const h = handlers[i],\n          groups = h.groups ? h.groups + 1 : 1;\n        if (!h.literal && h.token) {\n          all[h.token.val[0]] = h.deser(matches.slice(matchIndex, matchIndex + groups));\n        }\n        matchIndex += groups;\n      }\n    }\n    return [matches, all];\n  } else {\n    return [matches, {}];\n  }\n}\n\nfunction dateTimeFromMatches(matches) {\n  const toField = (token) => {\n    switch (token) {\n      case \"S\":\n        return \"millisecond\";\n      case \"s\":\n        return \"second\";\n      case \"m\":\n        return \"minute\";\n      case \"h\":\n      case \"H\":\n        return \"hour\";\n      case \"d\":\n        return \"day\";\n      case \"o\":\n        return \"ordinal\";\n      case \"L\":\n      case \"M\":\n        return \"month\";\n      case \"y\":\n        return \"year\";\n      case \"E\":\n      case \"c\":\n        return \"weekday\";\n      case \"W\":\n        return \"weekNumber\";\n      case \"k\":\n        return \"weekYear\";\n      case \"q\":\n        return \"quarter\";\n      default:\n        return null;\n    }\n  };\n\n  let zone = null;\n  let specificOffset;\n  if (!isUndefined(matches.z)) {\n    zone = IANAZone.create(matches.z);\n  }\n\n  if (!isUndefined(matches.Z)) {\n    if (!zone) {\n      zone = new FixedOffsetZone(matches.Z);\n    }\n    specificOffset = matches.Z;\n  }\n\n  if (!isUndefined(matches.q)) {\n    matches.M = (matches.q - 1) * 3 + 1;\n  }\n\n  if (!isUndefined(matches.h)) {\n    if (matches.h < 12 && matches.a === 1) {\n      matches.h += 12;\n    } else if (matches.h === 12 && matches.a === 0) {\n      matches.h = 0;\n    }\n  }\n\n  if (matches.G === 0 && matches.y) {\n    matches.y = -matches.y;\n  }\n\n  if (!isUndefined(matches.u)) {\n    matches.S = parseMillis(matches.u);\n  }\n\n  const vals = Object.keys(matches).reduce((r, k) => {\n    const f = toField(k);\n    if (f) {\n      r[f] = matches[k];\n    }\n\n    return r;\n  }, {});\n\n  return [vals, zone, specificOffset];\n}\n\nlet dummyDateTimeCache = null;\n\nfunction getDummyDateTime() {\n  if (!dummyDateTimeCache) {\n    dummyDateTimeCache = DateTime.fromMillis(1555555555555);\n  }\n\n  return dummyDateTimeCache;\n}\n\nfunction maybeExpandMacroToken(token, locale) {\n  if (token.literal) {\n    return token;\n  }\n\n  const formatOpts = Formatter.macroTokenToFormatOpts(token.val);\n  const tokens = formatOptsToTokens(formatOpts, locale);\n\n  if (tokens == null || tokens.includes(undefined)) {\n    return token;\n  }\n\n  return tokens;\n}\n\nexport function expandMacroTokens(tokens, locale) {\n  return Array.prototype.concat(...tokens.map((t) => maybeExpandMacroToken(t, locale)));\n}\n\n/**\n * @private\n */\n\nexport class TokenParser {\n  constructor(locale, format) {\n    this.locale = locale;\n    this.format = format;\n    this.tokens = expandMacroTokens(Formatter.parseFormat(format), locale);\n    this.units = this.tokens.map((t) => unitForToken(t, locale));\n    this.disqualifyingUnit = this.units.find((t) => t.invalidReason);\n\n    if (!this.disqualifyingUnit) {\n      const [regexString, handlers] = buildRegex(this.units);\n      this.regex = RegExp(regexString, \"i\");\n      this.handlers = handlers;\n    }\n  }\n\n  explainFromTokens(input) {\n    if (!this.isValid) {\n      return { input, tokens: this.tokens, invalidReason: this.invalidReason };\n    } else {\n      const [rawMatches, matches] = match(input, this.regex, this.handlers),\n        [result, zone, specificOffset] = matches\n          ? dateTimeFromMatches(matches)\n          : [null, null, undefined];\n      if (hasOwnProperty(matches, \"a\") && hasOwnProperty(matches, \"H\")) {\n        throw new ConflictingSpecificationError(\n          \"Can't include meridiem when specifying 24-hour format\"\n        );\n      }\n      return {\n        input,\n        tokens: this.tokens,\n        regex: this.regex,\n        rawMatches,\n        matches,\n        result,\n        zone,\n        specificOffset,\n      };\n    }\n  }\n\n  get isValid() {\n    return !this.disqualifyingUnit;\n  }\n\n  get invalidReason() {\n    return this.disqualifyingUnit ? this.disqualifyingUnit.invalidReason : null;\n  }\n}\n\nexport function explainFromTokens(locale, input, format) {\n  const parser = new TokenParser(locale, format);\n  return parser.explainFromTokens(input);\n}\n\nexport function parseFromTokens(locale, input, format) {\n  const { result, zone, specificOffset, invalidReason } = explainFromTokens(locale, input, format);\n  return [result, zone, specificOffset, invalidReason];\n}\n\nexport function formatOptsToTokens(formatOpts, locale) {\n  if (!formatOpts) {\n    return null;\n  }\n\n  const formatter = Formatter.create(locale, formatOpts);\n  const df = formatter.dtFormatter(getDummyDateTime());\n  const parts = df.formatToParts();\n  const resolvedOpts = df.resolvedOptions();\n  return parts.map((p) => tokenForPart(p, formatOpts, resolvedOpts));\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,MAAM,cAAc;AAEpB,SAAS,QAAQ,KAAK,EAAE,OAAO,CAAC,IAAM,CAAC;IACrC,OAAO;QAAE;QAAO,OAAO,CAAC,CAAC,EAAE,GAAK,KAAK,CAAA,GAAA,sJAAA,CAAA,cAAW,AAAD,EAAE;IAAI;AACvD;AAEA,MAAM,OAAO,OAAO,YAAY,CAAC;AACjC,MAAM,cAAc,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;AAChC,MAAM,oBAAoB,IAAI,OAAO,aAAa;AAElD,SAAS,aAAa,CAAC;IACrB,gDAAgD;IAChD,gEAAgE;IAChE,OAAO,EAAE,OAAO,CAAC,OAAO,QAAQ,OAAO,CAAC,mBAAmB;AAC7D;AAEA,SAAS,qBAAqB,CAAC;IAC7B,OAAO,EACJ,OAAO,CAAC,OAAO,IAAI,sCAAsC;KACzD,OAAO,CAAC,mBAAmB,KAAK,6BAA6B;KAC7D,WAAW;AAChB;AAEA,SAAS,MAAM,OAAO,EAAE,UAAU;IAChC,IAAI,YAAY,MAAM;QACpB,OAAO;IACT,OAAO;QACL,OAAO;YACL,OAAO,OAAO,QAAQ,GAAG,CAAC,cAAc,IAAI,CAAC;YAC7C,OAAO,CAAC,CAAC,EAAE,GACT,QAAQ,SAAS,CAAC,CAAC,IAAM,qBAAqB,OAAO,qBAAqB,MAAM;QACpF;IACF;AACF;AAEA,SAAS,OAAO,KAAK,EAAE,MAAM;IAC3B,OAAO;QAAE;QAAO,OAAO,CAAC,GAAG,GAAG,EAAE,GAAK,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,GAAG;QAAI;IAAO;AAClE;AAEA,SAAS,OAAO,KAAK;IACnB,OAAO;QAAE;QAAO,OAAO,CAAC,CAAC,EAAE,GAAK;IAAE;AACpC;AAEA,SAAS,YAAY,KAAK;IACxB,OAAO,MAAM,OAAO,CAAC,+BAA+B;AACtD;AAEA;;;CAGC,GACD,SAAS,aAAa,KAAK,EAAE,GAAG;IAC9B,MAAM,MAAM,CAAA,GAAA,sJAAA,CAAA,aAAU,AAAD,EAAE,MACrB,MAAM,CAAA,GAAA,sJAAA,CAAA,aAAU,AAAD,EAAE,KAAK,QACtB,QAAQ,CAAA,GAAA,sJAAA,CAAA,aAAU,AAAD,EAAE,KAAK,QACxB,OAAO,CAAA,GAAA,sJAAA,CAAA,aAAU,AAAD,EAAE,KAAK,QACvB,MAAM,CAAA,GAAA,sJAAA,CAAA,aAAU,AAAD,EAAE,KAAK,QACtB,WAAW,CAAA,GAAA,sJAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAC3B,aAAa,CAAA,GAAA,sJAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAC7B,WAAW,CAAA,GAAA,sJAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAC3B,YAAY,CAAA,GAAA,sJAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAC5B,YAAY,CAAA,GAAA,sJAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAC5B,YAAY,CAAA,GAAA,sJAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAC5B,UAAU,CAAC,IAAM,CAAC;YAAE,OAAO,OAAO,YAAY,EAAE,GAAG;YAAI,OAAO,CAAC,CAAC,EAAE,GAAK;YAAG,SAAS;QAAK,CAAC,GACzF,UAAU,CAAC;QACT,IAAI,MAAM,OAAO,EAAE;YACjB,OAAO,QAAQ;QACjB;QACA,OAAQ,EAAE,GAAG;YACX,MAAM;YACN,KAAK;gBACH,OAAO,MAAM,IAAI,IAAI,CAAC,UAAU;YAClC,KAAK;gBACH,OAAO,MAAM,IAAI,IAAI,CAAC,SAAS;YACjC,QAAQ;YACR,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,QAAQ,WAAW,oJAAA,CAAA,iBAAc;YAC1C,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,QAAQ;YACjB,SAAS;YACT,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,MAAM,IAAI,MAAM,CAAC,SAAS,OAAO;YAC1C,KAAK;gBACH,OAAO,MAAM,IAAI,MAAM,CAAC,QAAQ,OAAO;YACzC,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,MAAM,IAAI,MAAM,CAAC,SAAS,QAAQ;YAC3C,KAAK;gBACH,OAAO,MAAM,IAAI,MAAM,CAAC,QAAQ,QAAQ;YAC1C,QAAQ;YACR,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,QAAQ;YACjB,WAAW;YACX,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,QAAQ;YACjB,OAAO;YACP,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,OAAO;YAChB,KAAK;gBACH,OAAO,OAAO;YAChB,KAAK;gBACH,OAAO,QAAQ;YACjB,WAAW;YACX,KAAK;gBACH,OAAO,MAAM,IAAI,SAAS,IAAI;YAChC,eAAe;YACf,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,QAAQ,WAAW,oJAAA,CAAA,iBAAc;YAC1C,iBAAiB;YACjB,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,QAAQ;YACjB,WAAW;YACX,KAAK;YACL,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,MAAM,IAAI,QAAQ,CAAC,SAAS,QAAQ;YAC7C,KAAK;gBACH,OAAO,MAAM,IAAI,QAAQ,CAAC,QAAQ,QAAQ;YAC5C,KAAK;gBACH,OAAO,MAAM,IAAI,QAAQ,CAAC,SAAS,OAAO;YAC5C,KAAK;gBACH,OAAO,MAAM,IAAI,QAAQ,CAAC,QAAQ,OAAO;YAC3C,cAAc;YACd,KAAK;YACL,KAAK;gBACH,OAAO,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,MAAM,EAAE,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG;YAC7E,KAAK;gBACH,OAAO,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG;YACxE,0EAA0E;YAC1E,4DAA4D;YAC5D,KAAK;gBACH,OAAO,OAAO;YAChB,uGAAuG;YACvG,qDAAqD;YACrD,KAAK;gBACH,OAAO,OAAO;YAChB;gBACE,OAAO,QAAQ;QACnB;IACF;IAEF,MAAM,OAAO,QAAQ,UAAU;QAC7B,eAAe;IACjB;IAEA,KAAK,KAAK,GAAG;IAEb,OAAO;AACT;AAEA,MAAM,0BAA0B;IAC9B,MAAM;QACJ,WAAW;QACX,SAAS;IACX;IACA,OAAO;QACL,SAAS;QACT,WAAW;QACX,OAAO;QACP,MAAM;IACR;IACA,KAAK;QACH,SAAS;QACT,WAAW;IACb;IACA,SAAS;QACP,OAAO;QACP,MAAM;IACR;IACA,WAAW;IACX,WAAW;IACX,QAAQ;QACN,SAAS;QACT,WAAW;IACb;IACA,QAAQ;QACN,SAAS;QACT,WAAW;IACb;IACA,QAAQ;QACN,SAAS;QACT,WAAW;IACb;IACA,QAAQ;QACN,SAAS;QACT,WAAW;IACb;IACA,cAAc;QACZ,MAAM;QACN,OAAO;IACT;AACF;AAEA,SAAS,aAAa,IAAI,EAAE,UAAU,EAAE,YAAY;IAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG;IAExB,IAAI,SAAS,WAAW;QACtB,MAAM,UAAU,QAAQ,IAAI,CAAC;QAC7B,OAAO;YACL,SAAS,CAAC;YACV,KAAK,UAAU,MAAM;QACvB;IACF;IAEA,MAAM,QAAQ,UAAU,CAAC,KAAK;IAE9B,+DAA+D;IAC/D,gCAAgC;IAChC,wEAAwE;IACxE,IAAI,aAAa;IACjB,IAAI,SAAS,QAAQ;QACnB,IAAI,WAAW,MAAM,IAAI,MAAM;YAC7B,aAAa,WAAW,MAAM,GAAG,WAAW;QAC9C,OAAO,IAAI,WAAW,SAAS,IAAI,MAAM;YACvC,IAAI,WAAW,SAAS,KAAK,SAAS,WAAW,SAAS,KAAK,OAAO;gBACpE,aAAa;YACf,OAAO;gBACL,aAAa;YACf;QACF,OAAO;YACL,qDAAqD;YACrD,6EAA6E;YAC7E,aAAa,aAAa,MAAM,GAAG,WAAW;QAChD;IACF;IACA,IAAI,MAAM,uBAAuB,CAAC,WAAW;IAC7C,IAAI,OAAO,QAAQ,UAAU;QAC3B,MAAM,GAAG,CAAC,MAAM;IAClB;IAEA,IAAI,KAAK;QACP,OAAO;YACL,SAAS;YACT;QACF;IACF;IAEA,OAAO;AACT;AAEA,SAAS,WAAW,KAAK;IACvB,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC,IAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,GAAG,IAAM,GAAG,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE;IAC3E,OAAO;QAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAAE;KAAM;AAC3B;AAEA,SAAS,MAAM,KAAK,EAAE,KAAK,EAAE,QAAQ;IACnC,MAAM,UAAU,MAAM,KAAK,CAAC;IAE5B,IAAI,SAAS;QACX,MAAM,MAAM,CAAC;QACb,IAAI,aAAa;QACjB,IAAK,MAAM,KAAK,SAAU;YACxB,IAAI,CAAA,GAAA,oJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,IAAI;gBAC/B,MAAM,IAAI,QAAQ,CAAC,EAAE,EACnB,SAAS,EAAE,MAAM,GAAG,EAAE,MAAM,GAAG,IAAI;gBACrC,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,KAAK,EAAE;oBACzB,GAAG,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,QAAQ,KAAK,CAAC,YAAY,aAAa;gBACvE;gBACA,cAAc;YAChB;QACF;QACA,OAAO;YAAC;YAAS;SAAI;IACvB,OAAO;QACL,OAAO;YAAC;YAAS,CAAC;SAAE;IACtB;AACF;AAEA,SAAS,oBAAoB,OAAO;IAClC,MAAM,UAAU,CAAC;QACf,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,OAAO;IACX,IAAI;IACJ,IAAI,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,CAAC,GAAG;QAC3B,OAAO,yJAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC;IAClC;IAEA,IAAI,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,CAAC,GAAG;QAC3B,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,gKAAA,CAAA,UAAe,CAAC,QAAQ,CAAC;QACtC;QACA,iBAAiB,QAAQ,CAAC;IAC5B;IAEA,IAAI,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,CAAC,GAAG;QAC3B,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI;IACpC;IAEA,IAAI,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,CAAC,GAAG;QAC3B,IAAI,QAAQ,CAAC,GAAG,MAAM,QAAQ,CAAC,KAAK,GAAG;YACrC,QAAQ,CAAC,IAAI;QACf,OAAO,IAAI,QAAQ,CAAC,KAAK,MAAM,QAAQ,CAAC,KAAK,GAAG;YAC9C,QAAQ,CAAC,GAAG;QACd;IACF;IAEA,IAAI,QAAQ,CAAC,KAAK,KAAK,QAAQ,CAAC,EAAE;QAChC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC;IACxB;IAEA,IAAI,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,CAAC,GAAG;QAC3B,QAAQ,CAAC,GAAG,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,CAAC;IACnC;IAEA,MAAM,OAAO,OAAO,IAAI,CAAC,SAAS,MAAM,CAAC,CAAC,GAAG;QAC3C,MAAM,IAAI,QAAQ;QAClB,IAAI,GAAG;YACL,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE;QACnB;QAEA,OAAO;IACT,GAAG,CAAC;IAEJ,OAAO;QAAC;QAAM;QAAM;KAAe;AACrC;AAEA,IAAI,qBAAqB;AAEzB,SAAS;IACP,IAAI,CAAC,oBAAoB;QACvB,qBAAqB,gJAAA,CAAA,UAAQ,CAAC,UAAU,CAAC;IAC3C;IAEA,OAAO;AACT;AAEA,SAAS,sBAAsB,KAAK,EAAE,MAAM;IAC1C,IAAI,MAAM,OAAO,EAAE;QACjB,OAAO;IACT;IAEA,MAAM,aAAa,yJAAA,CAAA,UAAS,CAAC,sBAAsB,CAAC,MAAM,GAAG;IAC7D,MAAM,SAAS,mBAAmB,YAAY;IAE9C,IAAI,UAAU,QAAQ,OAAO,QAAQ,CAAC,YAAY;QAChD,OAAO;IACT;IAEA,OAAO;AACT;AAEO,SAAS,kBAAkB,MAAM,EAAE,MAAM;IAC9C,OAAO,MAAM,SAAS,CAAC,MAAM,IAAI,OAAO,GAAG,CAAC,CAAC,IAAM,sBAAsB,GAAG;AAC9E;AAMO,MAAM;IACX,YAAY,MAAM,EAAE,MAAM,CAAE;QAC1B,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,GAAG,kBAAkB,yJAAA,CAAA,UAAS,CAAC,WAAW,CAAC,SAAS;QAC/D,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAM,aAAa,GAAG;QACpD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAM,EAAE,aAAa;QAE/D,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC3B,MAAM,CAAC,aAAa,SAAS,GAAG,WAAW,IAAI,CAAC,KAAK;YACrD,IAAI,CAAC,KAAK,GAAG,OAAO,aAAa;YACjC,IAAI,CAAC,QAAQ,GAAG;QAClB;IACF;IAEA,kBAAkB,KAAK,EAAE;QACvB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,OAAO;gBAAE;gBAAO,QAAQ,IAAI,CAAC,MAAM;gBAAE,eAAe,IAAI,CAAC,aAAa;YAAC;QACzE,OAAO;YACL,MAAM,CAAC,YAAY,QAAQ,GAAG,MAAM,OAAO,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,GAClE,CAAC,QAAQ,MAAM,eAAe,GAAG,UAC7B,oBAAoB,WACpB;gBAAC;gBAAM;gBAAM;aAAU;YAC7B,IAAI,CAAA,GAAA,oJAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,QAAQ,CAAA,GAAA,oJAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,MAAM;gBAChE,MAAM,IAAI,8IAAA,CAAA,gCAA6B,CACrC;YAEJ;YACA,OAAO;gBACL;gBACA,QAAQ,IAAI,CAAC,MAAM;gBACnB,OAAO,IAAI,CAAC,KAAK;gBACjB;gBACA;gBACA;gBACA;gBACA;YACF;QACF;IACF;IAEA,IAAI,UAAU;QACZ,OAAO,CAAC,IAAI,CAAC,iBAAiB;IAChC;IAEA,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,GAAG;IACzE;AACF;AAEO,SAAS,kBAAkB,MAAM,EAAE,KAAK,EAAE,MAAM;IACrD,MAAM,SAAS,IAAI,YAAY,QAAQ;IACvC,OAAO,OAAO,iBAAiB,CAAC;AAClC;AAEO,SAAS,gBAAgB,MAAM,EAAE,KAAK,EAAE,MAAM;IACnD,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,aAAa,EAAE,GAAG,kBAAkB,QAAQ,OAAO;IACzF,OAAO;QAAC;QAAQ;QAAM;QAAgB;KAAc;AACtD;AAEO,SAAS,mBAAmB,UAAU,EAAE,MAAM;IACnD,IAAI,CAAC,YAAY;QACf,OAAO;IACT;IAEA,MAAM,YAAY,yJAAA,CAAA,UAAS,CAAC,MAAM,CAAC,QAAQ;IAC3C,MAAM,KAAK,UAAU,WAAW,CAAC;IACjC,MAAM,QAAQ,GAAG,aAAa;IAC9B,MAAM,eAAe,GAAG,eAAe;IACvC,OAAO,MAAM,GAAG,CAAC,CAAC,IAAM,aAAa,GAAG,YAAY;AACtD", "ignoreList": [0]}}, {"offset": {"line": 5407, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/luxon/src/datetime.js"], "sourcesContent": ["import Duration from \"./duration.js\";\nimport Interval from \"./interval.js\";\nimport Settings from \"./settings.js\";\nimport Info from \"./info.js\";\nimport Formatter from \"./impl/formatter.js\";\nimport FixedOffsetZone from \"./zones/fixedOffsetZone.js\";\nimport Locale from \"./impl/locale.js\";\nimport {\n  isUndefined,\n  maybeArray,\n  isDate,\n  isNumber,\n  bestBy,\n  daysInMonth,\n  daysInYear,\n  isLeapYear,\n  weeksInWeekYear,\n  normalizeObject,\n  roundTo,\n  objToLocalTS,\n  padStart,\n} from \"./impl/util.js\";\nimport { normalizeZone } from \"./impl/zoneUtil.js\";\nimport diff from \"./impl/diff.js\";\nimport { parseRFC2822Date, parseISODate, parseHTTPDate, parseSQL } from \"./impl/regexParser.js\";\nimport {\n  parseFromTokens,\n  explainFromTokens,\n  formatOptsToTokens,\n  expandMacroTokens,\n  TokenParser,\n} from \"./impl/tokenParser.js\";\nimport {\n  gregorianToWeek,\n  weekToGregorian,\n  gregorianToOrdinal,\n  ordinalToGregorian,\n  hasInvalidGregorianData,\n  hasInvalidWeekData,\n  hasInvalidOrdinalData,\n  hasInvalidTimeData,\n  usesLocalWeekValues,\n  isoWeekdayToLocal,\n} from \"./impl/conversions.js\";\nimport * as Formats from \"./impl/formats.js\";\nimport {\n  InvalidArgumentError,\n  ConflictingSpecificationError,\n  InvalidUnitError,\n  InvalidDateTimeError,\n} from \"./errors.js\";\nimport Invalid from \"./impl/invalid.js\";\n\nconst INVALID = \"Invalid DateTime\";\nconst MAX_DATE = 8.64e15;\n\nfunction unsupportedZone(zone) {\n  return new Invalid(\"unsupported zone\", `the zone \"${zone.name}\" is not supported`);\n}\n\n// we cache week data on the DT object and this intermediates the cache\n/**\n * @param {DateTime} dt\n */\nfunction possiblyCachedWeekData(dt) {\n  if (dt.weekData === null) {\n    dt.weekData = gregorianToWeek(dt.c);\n  }\n  return dt.weekData;\n}\n\n/**\n * @param {DateTime} dt\n */\nfunction possiblyCachedLocalWeekData(dt) {\n  if (dt.localWeekData === null) {\n    dt.localWeekData = gregorianToWeek(\n      dt.c,\n      dt.loc.getMinDaysInFirstWeek(),\n      dt.loc.getStartOfWeek()\n    );\n  }\n  return dt.localWeekData;\n}\n\n// clone really means, \"make a new object with these modifications\". all \"setters\" really use this\n// to create a new object while only changing some of the properties\nfunction clone(inst, alts) {\n  const current = {\n    ts: inst.ts,\n    zone: inst.zone,\n    c: inst.c,\n    o: inst.o,\n    loc: inst.loc,\n    invalid: inst.invalid,\n  };\n  return new DateTime({ ...current, ...alts, old: current });\n}\n\n// find the right offset a given local time. The o input is our guess, which determines which\n// offset we'll pick in ambiguous cases (e.g. there are two 3 AMs b/c Fallback DST)\nfunction fixOffset(localTS, o, tz) {\n  // Our UTC time is just a guess because our offset is just a guess\n  let utcGuess = localTS - o * 60 * 1000;\n\n  // Test whether the zone matches the offset for this ts\n  const o2 = tz.offset(utcGuess);\n\n  // If so, offset didn't change and we're done\n  if (o === o2) {\n    return [utcGuess, o];\n  }\n\n  // If not, change the ts by the difference in the offset\n  utcGuess -= (o2 - o) * 60 * 1000;\n\n  // If that gives us the local time we want, we're done\n  const o3 = tz.offset(utcGuess);\n  if (o2 === o3) {\n    return [utcGuess, o2];\n  }\n\n  // If it's different, we're in a hole time. The offset has changed, but the we don't adjust the time\n  return [localTS - Math.min(o2, o3) * 60 * 1000, Math.max(o2, o3)];\n}\n\n// convert an epoch timestamp into a calendar object with the given offset\nfunction tsToObj(ts, offset) {\n  ts += offset * 60 * 1000;\n\n  const d = new Date(ts);\n\n  return {\n    year: d.getUTCFullYear(),\n    month: d.getUTCMonth() + 1,\n    day: d.getUTCDate(),\n    hour: d.getUTCHours(),\n    minute: d.getUTCMinutes(),\n    second: d.getUTCSeconds(),\n    millisecond: d.getUTCMilliseconds(),\n  };\n}\n\n// convert a calendar object to a epoch timestamp\nfunction objToTS(obj, offset, zone) {\n  return fixOffset(objToLocalTS(obj), offset, zone);\n}\n\n// create a new DT instance by adding a duration, adjusting for DSTs\nfunction adjustTime(inst, dur) {\n  const oPre = inst.o,\n    year = inst.c.year + Math.trunc(dur.years),\n    month = inst.c.month + Math.trunc(dur.months) + Math.trunc(dur.quarters) * 3,\n    c = {\n      ...inst.c,\n      year,\n      month,\n      day:\n        Math.min(inst.c.day, daysInMonth(year, month)) +\n        Math.trunc(dur.days) +\n        Math.trunc(dur.weeks) * 7,\n    },\n    millisToAdd = Duration.fromObject({\n      years: dur.years - Math.trunc(dur.years),\n      quarters: dur.quarters - Math.trunc(dur.quarters),\n      months: dur.months - Math.trunc(dur.months),\n      weeks: dur.weeks - Math.trunc(dur.weeks),\n      days: dur.days - Math.trunc(dur.days),\n      hours: dur.hours,\n      minutes: dur.minutes,\n      seconds: dur.seconds,\n      milliseconds: dur.milliseconds,\n    }).as(\"milliseconds\"),\n    localTS = objToLocalTS(c);\n\n  let [ts, o] = fixOffset(localTS, oPre, inst.zone);\n\n  if (millisToAdd !== 0) {\n    ts += millisToAdd;\n    // that could have changed the offset by going over a DST, but we want to keep the ts the same\n    o = inst.zone.offset(ts);\n  }\n\n  return { ts, o };\n}\n\n// helper useful in turning the results of parsing into real dates\n// by handling the zone options\nfunction parseDataToDateTime(parsed, parsedZone, opts, format, text, specificOffset) {\n  const { setZone, zone } = opts;\n  if ((parsed && Object.keys(parsed).length !== 0) || parsedZone) {\n    const interpretationZone = parsedZone || zone,\n      inst = DateTime.fromObject(parsed, {\n        ...opts,\n        zone: interpretationZone,\n        specificOffset,\n      });\n    return setZone ? inst : inst.setZone(zone);\n  } else {\n    return DateTime.invalid(\n      new Invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ${format}`)\n    );\n  }\n}\n\n// if you want to output a technical format (e.g. RFC 2822), this helper\n// helps handle the details\nfunction toTechFormat(dt, format, allowZ = true) {\n  return dt.isValid\n    ? Formatter.create(Locale.create(\"en-US\"), {\n        allowZ,\n        forceSimple: true,\n      }).formatDateTimeFromString(dt, format)\n    : null;\n}\n\nfunction toISODate(o, extended) {\n  const longFormat = o.c.year > 9999 || o.c.year < 0;\n  let c = \"\";\n  if (longFormat && o.c.year >= 0) c += \"+\";\n  c += padStart(o.c.year, longFormat ? 6 : 4);\n\n  if (extended) {\n    c += \"-\";\n    c += padStart(o.c.month);\n    c += \"-\";\n    c += padStart(o.c.day);\n  } else {\n    c += padStart(o.c.month);\n    c += padStart(o.c.day);\n  }\n  return c;\n}\n\nfunction toISOTime(\n  o,\n  extended,\n  suppressSeconds,\n  suppressMilliseconds,\n  includeOffset,\n  extendedZone\n) {\n  let c = padStart(o.c.hour);\n  if (extended) {\n    c += \":\";\n    c += padStart(o.c.minute);\n    if (o.c.millisecond !== 0 || o.c.second !== 0 || !suppressSeconds) {\n      c += \":\";\n    }\n  } else {\n    c += padStart(o.c.minute);\n  }\n\n  if (o.c.millisecond !== 0 || o.c.second !== 0 || !suppressSeconds) {\n    c += padStart(o.c.second);\n\n    if (o.c.millisecond !== 0 || !suppressMilliseconds) {\n      c += \".\";\n      c += padStart(o.c.millisecond, 3);\n    }\n  }\n\n  if (includeOffset) {\n    if (o.isOffsetFixed && o.offset === 0 && !extendedZone) {\n      c += \"Z\";\n    } else if (o.o < 0) {\n      c += \"-\";\n      c += padStart(Math.trunc(-o.o / 60));\n      c += \":\";\n      c += padStart(Math.trunc(-o.o % 60));\n    } else {\n      c += \"+\";\n      c += padStart(Math.trunc(o.o / 60));\n      c += \":\";\n      c += padStart(Math.trunc(o.o % 60));\n    }\n  }\n\n  if (extendedZone) {\n    c += \"[\" + o.zone.ianaName + \"]\";\n  }\n  return c;\n}\n\n// defaults for unspecified units in the supported calendars\nconst defaultUnitValues = {\n    month: 1,\n    day: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0,\n  },\n  defaultWeekUnitValues = {\n    weekNumber: 1,\n    weekday: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0,\n  },\n  defaultOrdinalUnitValues = {\n    ordinal: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0,\n  };\n\n// Units in the supported calendars, sorted by bigness\nconst orderedUnits = [\"year\", \"month\", \"day\", \"hour\", \"minute\", \"second\", \"millisecond\"],\n  orderedWeekUnits = [\n    \"weekYear\",\n    \"weekNumber\",\n    \"weekday\",\n    \"hour\",\n    \"minute\",\n    \"second\",\n    \"millisecond\",\n  ],\n  orderedOrdinalUnits = [\"year\", \"ordinal\", \"hour\", \"minute\", \"second\", \"millisecond\"];\n\n// standardize case and plurality in units\nfunction normalizeUnit(unit) {\n  const normalized = {\n    year: \"year\",\n    years: \"year\",\n    month: \"month\",\n    months: \"month\",\n    day: \"day\",\n    days: \"day\",\n    hour: \"hour\",\n    hours: \"hour\",\n    minute: \"minute\",\n    minutes: \"minute\",\n    quarter: \"quarter\",\n    quarters: \"quarter\",\n    second: \"second\",\n    seconds: \"second\",\n    millisecond: \"millisecond\",\n    milliseconds: \"millisecond\",\n    weekday: \"weekday\",\n    weekdays: \"weekday\",\n    weeknumber: \"weekNumber\",\n    weeksnumber: \"weekNumber\",\n    weeknumbers: \"weekNumber\",\n    weekyear: \"weekYear\",\n    weekyears: \"weekYear\",\n    ordinal: \"ordinal\",\n  }[unit.toLowerCase()];\n\n  if (!normalized) throw new InvalidUnitError(unit);\n\n  return normalized;\n}\n\nfunction normalizeUnitWithLocalWeeks(unit) {\n  switch (unit.toLowerCase()) {\n    case \"localweekday\":\n    case \"localweekdays\":\n      return \"localWeekday\";\n    case \"localweeknumber\":\n    case \"localweeknumbers\":\n      return \"localWeekNumber\";\n    case \"localweekyear\":\n    case \"localweekyears\":\n      return \"localWeekYear\";\n    default:\n      return normalizeUnit(unit);\n  }\n}\n\n// cache offsets for zones based on the current timestamp when this function is\n// first called. When we are handling a datetime from components like (year,\n// month, day, hour) in a time zone, we need a guess about what the timezone\n// offset is so that we can convert into a UTC timestamp. One way is to find the\n// offset of now in the zone. The actual date may have a different offset (for\n// example, if we handle a date in June while we're in December in a zone that\n// observes DST), but we can check and adjust that.\n//\n// When handling many dates, calculating the offset for now every time is\n// expensive. It's just a guess, so we can cache the offset to use even if we\n// are right on a time change boundary (we'll just correct in the other\n// direction). Using a timestamp from first read is a slight optimization for\n// handling dates close to the current date, since those dates will usually be\n// in the same offset (we could set the timestamp statically, instead). We use a\n// single timestamp for all zones to make things a bit more predictable.\n//\n// This is safe for quickDT (used by local() and utc()) because we don't fill in\n// higher-order units from tsNow (as we do in fromObject, this requires that\n// offset is calculated from tsNow).\n/**\n * @param {Zone} zone\n * @return {number}\n */\nfunction guessOffsetForZone(zone) {\n  if (zoneOffsetTs === undefined) {\n    zoneOffsetTs = Settings.now();\n  }\n\n  // Do not cache anything but IANA zones, because it is not safe to do so.\n  // Guessing an offset which is not present in the zone can cause wrong results from fixOffset\n  if (zone.type !== \"iana\") {\n    return zone.offset(zoneOffsetTs);\n  }\n  const zoneName = zone.name;\n  let offsetGuess = zoneOffsetGuessCache.get(zoneName);\n  if (offsetGuess === undefined) {\n    offsetGuess = zone.offset(zoneOffsetTs);\n    zoneOffsetGuessCache.set(zoneName, offsetGuess);\n  }\n  return offsetGuess;\n}\n\n// this is a dumbed down version of fromObject() that runs about 60% faster\n// but doesn't do any validation, makes a bunch of assumptions about what units\n// are present, and so on.\nfunction quickDT(obj, opts) {\n  const zone = normalizeZone(opts.zone, Settings.defaultZone);\n  if (!zone.isValid) {\n    return DateTime.invalid(unsupportedZone(zone));\n  }\n\n  const loc = Locale.fromObject(opts);\n\n  let ts, o;\n\n  // assume we have the higher-order units\n  if (!isUndefined(obj.year)) {\n    for (const u of orderedUnits) {\n      if (isUndefined(obj[u])) {\n        obj[u] = defaultUnitValues[u];\n      }\n    }\n\n    const invalid = hasInvalidGregorianData(obj) || hasInvalidTimeData(obj);\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    }\n\n    const offsetProvis = guessOffsetForZone(zone);\n    [ts, o] = objToTS(obj, offsetProvis, zone);\n  } else {\n    ts = Settings.now();\n  }\n\n  return new DateTime({ ts, zone, loc, o });\n}\n\nfunction diffRelative(start, end, opts) {\n  const round = isUndefined(opts.round) ? true : opts.round,\n    format = (c, unit) => {\n      c = roundTo(c, round || opts.calendary ? 0 : 2, true);\n      const formatter = end.loc.clone(opts).relFormatter(opts);\n      return formatter.format(c, unit);\n    },\n    differ = (unit) => {\n      if (opts.calendary) {\n        if (!end.hasSame(start, unit)) {\n          return end.startOf(unit).diff(start.startOf(unit), unit).get(unit);\n        } else return 0;\n      } else {\n        return end.diff(start, unit).get(unit);\n      }\n    };\n\n  if (opts.unit) {\n    return format(differ(opts.unit), opts.unit);\n  }\n\n  for (const unit of opts.units) {\n    const count = differ(unit);\n    if (Math.abs(count) >= 1) {\n      return format(count, unit);\n    }\n  }\n  return format(start > end ? -0 : 0, opts.units[opts.units.length - 1]);\n}\n\nfunction lastOpts(argList) {\n  let opts = {},\n    args;\n  if (argList.length > 0 && typeof argList[argList.length - 1] === \"object\") {\n    opts = argList[argList.length - 1];\n    args = Array.from(argList).slice(0, argList.length - 1);\n  } else {\n    args = Array.from(argList);\n  }\n  return [opts, args];\n}\n\n/**\n * Timestamp to use for cached zone offset guesses (exposed for test)\n */\nlet zoneOffsetTs;\n/**\n * Cache for zone offset guesses (exposed for test).\n *\n * This optimizes quickDT via guessOffsetForZone to avoid repeated calls of\n * zone.offset().\n */\nconst zoneOffsetGuessCache = new Map();\n\n/**\n * A DateTime is an immutable data structure representing a specific date and time and accompanying methods. It contains class and instance methods for creating, parsing, interrogating, transforming, and formatting them.\n *\n * A DateTime comprises of:\n * * A timestamp. Each DateTime instance refers to a specific millisecond of the Unix epoch.\n * * A time zone. Each instance is considered in the context of a specific zone (by default the local system's zone).\n * * Configuration properties that effect how output strings are formatted, such as `locale`, `numberingSystem`, and `outputCalendar`.\n *\n * Here is a brief overview of the most commonly used functionality it provides:\n *\n * * **Creation**: To create a DateTime from its components, use one of its factory class methods: {@link DateTime.local}, {@link DateTime.utc}, and (most flexibly) {@link DateTime.fromObject}. To create one from a standard string format, use {@link DateTime.fromISO}, {@link DateTime.fromHTTP}, and {@link DateTime.fromRFC2822}. To create one from a custom string format, use {@link DateTime.fromFormat}. To create one from a native JS date, use {@link DateTime.fromJSDate}.\n * * **Gregorian calendar and time**: To examine the Gregorian properties of a DateTime individually (i.e as opposed to collectively through {@link DateTime#toObject}), use the {@link DateTime#year}, {@link DateTime#month},\n * {@link DateTime#day}, {@link DateTime#hour}, {@link DateTime#minute}, {@link DateTime#second}, {@link DateTime#millisecond} accessors.\n * * **Week calendar**: For ISO week calendar attributes, see the {@link DateTime#weekYear}, {@link DateTime#weekNumber}, and {@link DateTime#weekday} accessors.\n * * **Configuration** See the {@link DateTime#locale} and {@link DateTime#numberingSystem} accessors.\n * * **Transformation**: To transform the DateTime into other DateTimes, use {@link DateTime#set}, {@link DateTime#reconfigure}, {@link DateTime#setZone}, {@link DateTime#setLocale}, {@link DateTime.plus}, {@link DateTime#minus}, {@link DateTime#endOf}, {@link DateTime#startOf}, {@link DateTime#toUTC}, and {@link DateTime#toLocal}.\n * * **Output**: To convert the DateTime to other representations, use the {@link DateTime#toRelative}, {@link DateTime#toRelativeCalendar}, {@link DateTime#toJSON}, {@link DateTime#toISO}, {@link DateTime#toHTTP}, {@link DateTime#toObject}, {@link DateTime#toRFC2822}, {@link DateTime#toString}, {@link DateTime#toLocaleString}, {@link DateTime#toFormat}, {@link DateTime#toMillis} and {@link DateTime#toJSDate}.\n *\n * There's plenty others documented below. In addition, for more information on subtler topics like internationalization, time zones, alternative calendars, validity, and so on, see the external documentation.\n */\nexport default class DateTime {\n  /**\n   * @access private\n   */\n  constructor(config) {\n    const zone = config.zone || Settings.defaultZone;\n\n    let invalid =\n      config.invalid ||\n      (Number.isNaN(config.ts) ? new Invalid(\"invalid input\") : null) ||\n      (!zone.isValid ? unsupportedZone(zone) : null);\n    /**\n     * @access private\n     */\n    this.ts = isUndefined(config.ts) ? Settings.now() : config.ts;\n\n    let c = null,\n      o = null;\n    if (!invalid) {\n      const unchanged = config.old && config.old.ts === this.ts && config.old.zone.equals(zone);\n\n      if (unchanged) {\n        [c, o] = [config.old.c, config.old.o];\n      } else {\n        // If an offset has been passed and we have not been called from\n        // clone(), we can trust it and avoid the offset calculation.\n        const ot = isNumber(config.o) && !config.old ? config.o : zone.offset(this.ts);\n        c = tsToObj(this.ts, ot);\n        invalid = Number.isNaN(c.year) ? new Invalid(\"invalid input\") : null;\n        c = invalid ? null : c;\n        o = invalid ? null : ot;\n      }\n    }\n\n    /**\n     * @access private\n     */\n    this._zone = zone;\n    /**\n     * @access private\n     */\n    this.loc = config.loc || Locale.create();\n    /**\n     * @access private\n     */\n    this.invalid = invalid;\n    /**\n     * @access private\n     */\n    this.weekData = null;\n    /**\n     * @access private\n     */\n    this.localWeekData = null;\n    /**\n     * @access private\n     */\n    this.c = c;\n    /**\n     * @access private\n     */\n    this.o = o;\n    /**\n     * @access private\n     */\n    this.isLuxonDateTime = true;\n  }\n\n  // CONSTRUCT\n\n  /**\n   * Create a DateTime for the current instant, in the system's time zone.\n   *\n   * Use Settings to override these default values if needed.\n   * @example DateTime.now().toISO() //~> now in the ISO format\n   * @return {DateTime}\n   */\n  static now() {\n    return new DateTime({});\n  }\n\n  /**\n   * Create a local DateTime\n   * @param {number} [year] - The calendar year. If omitted (as in, call `local()` with no arguments), the current time will be used\n   * @param {number} [month=1] - The month, 1-indexed\n   * @param {number} [day=1] - The day of the month, 1-indexed\n   * @param {number} [hour=0] - The hour of the day, in 24-hour time\n   * @param {number} [minute=0] - The minute of the hour, meaning a number between 0 and 59\n   * @param {number} [second=0] - The second of the minute, meaning a number between 0 and 59\n   * @param {number} [millisecond=0] - The millisecond of the second, meaning a number between 0 and 999\n   * @example DateTime.local()                                  //~> now\n   * @example DateTime.local({ zone: \"America/New_York\" })      //~> now, in US east coast time\n   * @example DateTime.local(2017)                              //~> 2017-01-01T00:00:00\n   * @example DateTime.local(2017, 3)                           //~> 2017-03-01T00:00:00\n   * @example DateTime.local(2017, 3, 12, { locale: \"fr\" })     //~> 2017-03-12T00:00:00, with a French locale\n   * @example DateTime.local(2017, 3, 12, 5)                    //~> 2017-03-12T05:00:00\n   * @example DateTime.local(2017, 3, 12, 5, { zone: \"utc\" })   //~> 2017-03-12T05:00:00, in UTC\n   * @example DateTime.local(2017, 3, 12, 5, 45)                //~> 2017-03-12T05:45:00\n   * @example DateTime.local(2017, 3, 12, 5, 45, 10)            //~> 2017-03-12T05:45:10\n   * @example DateTime.local(2017, 3, 12, 5, 45, 10, 765)       //~> 2017-03-12T05:45:10.765\n   * @return {DateTime}\n   */\n  static local() {\n    const [opts, args] = lastOpts(arguments),\n      [year, month, day, hour, minute, second, millisecond] = args;\n    return quickDT({ year, month, day, hour, minute, second, millisecond }, opts);\n  }\n\n  /**\n   * Create a DateTime in UTC\n   * @param {number} [year] - The calendar year. If omitted (as in, call `utc()` with no arguments), the current time will be used\n   * @param {number} [month=1] - The month, 1-indexed\n   * @param {number} [day=1] - The day of the month\n   * @param {number} [hour=0] - The hour of the day, in 24-hour time\n   * @param {number} [minute=0] - The minute of the hour, meaning a number between 0 and 59\n   * @param {number} [second=0] - The second of the minute, meaning a number between 0 and 59\n   * @param {number} [millisecond=0] - The millisecond of the second, meaning a number between 0 and 999\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} [options.outputCalendar] - the output calendar to set on the resulting DateTime instance\n   * @param {string} [options.numberingSystem] - the numbering system to set on the resulting DateTime instance\n   * @param {string} [options.weekSettings] - the week settings to set on the resulting DateTime instance\n   * @example DateTime.utc()                                              //~> now\n   * @example DateTime.utc(2017)                                          //~> 2017-01-01T00:00:00Z\n   * @example DateTime.utc(2017, 3)                                       //~> 2017-03-01T00:00:00Z\n   * @example DateTime.utc(2017, 3, 12)                                   //~> 2017-03-12T00:00:00Z\n   * @example DateTime.utc(2017, 3, 12, 5)                                //~> 2017-03-12T05:00:00Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45)                            //~> 2017-03-12T05:45:00Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45, { locale: \"fr\" })          //~> 2017-03-12T05:45:00Z with a French locale\n   * @example DateTime.utc(2017, 3, 12, 5, 45, 10)                        //~> 2017-03-12T05:45:10Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45, 10, 765, { locale: \"fr\" }) //~> 2017-03-12T05:45:10.765Z with a French locale\n   * @return {DateTime}\n   */\n  static utc() {\n    const [opts, args] = lastOpts(arguments),\n      [year, month, day, hour, minute, second, millisecond] = args;\n\n    opts.zone = FixedOffsetZone.utcInstance;\n    return quickDT({ year, month, day, hour, minute, second, millisecond }, opts);\n  }\n\n  /**\n   * Create a DateTime from a JavaScript Date object. Uses the default zone.\n   * @param {Date} date - a JavaScript Date object\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @return {DateTime}\n   */\n  static fromJSDate(date, options = {}) {\n    const ts = isDate(date) ? date.valueOf() : NaN;\n    if (Number.isNaN(ts)) {\n      return DateTime.invalid(\"invalid input\");\n    }\n\n    const zoneToUse = normalizeZone(options.zone, Settings.defaultZone);\n    if (!zoneToUse.isValid) {\n      return DateTime.invalid(unsupportedZone(zoneToUse));\n    }\n\n    return new DateTime({\n      ts: ts,\n      zone: zoneToUse,\n      loc: Locale.fromObject(options),\n    });\n  }\n\n  /**\n   * Create a DateTime from a number of milliseconds since the epoch (meaning since 1 January 1970 00:00:00 UTC). Uses the default zone.\n   * @param {number} milliseconds - a number of milliseconds since 1970 UTC\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} options.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} options.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} options.weekSettings - the week settings to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromMillis(milliseconds, options = {}) {\n    if (!isNumber(milliseconds)) {\n      throw new InvalidArgumentError(\n        `fromMillis requires a numerical input, but received a ${typeof milliseconds} with value ${milliseconds}`\n      );\n    } else if (milliseconds < -MAX_DATE || milliseconds > MAX_DATE) {\n      // this isn't perfect because we can still end up out of range because of additional shifting, but it's a start\n      return DateTime.invalid(\"Timestamp out of range\");\n    } else {\n      return new DateTime({\n        ts: milliseconds,\n        zone: normalizeZone(options.zone, Settings.defaultZone),\n        loc: Locale.fromObject(options),\n      });\n    }\n  }\n\n  /**\n   * Create a DateTime from a number of seconds since the epoch (meaning since 1 January 1970 00:00:00 UTC). Uses the default zone.\n   * @param {number} seconds - a number of seconds since 1970 UTC\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} options.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} options.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} options.weekSettings - the week settings to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromSeconds(seconds, options = {}) {\n    if (!isNumber(seconds)) {\n      throw new InvalidArgumentError(\"fromSeconds requires a numerical input\");\n    } else {\n      return new DateTime({\n        ts: seconds * 1000,\n        zone: normalizeZone(options.zone, Settings.defaultZone),\n        loc: Locale.fromObject(options),\n      });\n    }\n  }\n\n  /**\n   * Create a DateTime from a JavaScript object with keys like 'year' and 'hour' with reasonable defaults.\n   * @param {Object} obj - the object to create the DateTime from\n   * @param {number} obj.year - a year, such as 1987\n   * @param {number} obj.month - a month, 1-12\n   * @param {number} obj.day - a day of the month, 1-31, depending on the month\n   * @param {number} obj.ordinal - day of the year, 1-365 or 366\n   * @param {number} obj.weekYear - an ISO week year\n   * @param {number} obj.weekNumber - an ISO week number, between 1 and 52 or 53, depending on the year\n   * @param {number} obj.weekday - an ISO weekday, 1-7, where 1 is Monday and 7 is Sunday\n   * @param {number} obj.localWeekYear - a week year, according to the locale\n   * @param {number} obj.localWeekNumber - a week number, between 1 and 52 or 53, depending on the year, according to the locale\n   * @param {number} obj.localWeekday - a weekday, 1-7, where 1 is the first and 7 is the last day of the week, according to the locale\n   * @param {number} obj.hour - hour of the day, 0-23\n   * @param {number} obj.minute - minute of the hour, 0-59\n   * @param {number} obj.second - second of the minute, 0-59\n   * @param {number} obj.millisecond - millisecond of the second, 0-999\n   * @param {Object} opts - options for creating this DateTime\n   * @param {string|Zone} [opts.zone='local'] - interpret the numbers in the context of a particular zone. Can take any value taken as the first argument to setZone()\n   * @param {string} [opts.locale='system\\'s locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromObject({ year: 1982, month: 5, day: 25}).toISODate() //=> '1982-05-25'\n   * @example DateTime.fromObject({ year: 1982 }).toISODate() //=> '1982-01-01'\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }) //~> today at 10:26:06\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'utc' }),\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'local' })\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'America/New_York' })\n   * @example DateTime.fromObject({ weekYear: 2016, weekNumber: 2, weekday: 3 }).toISODate() //=> '2016-01-13'\n   * @example DateTime.fromObject({ localWeekYear: 2022, localWeekNumber: 1, localWeekday: 1 }, { locale: \"en-US\" }).toISODate() //=> '2021-12-26'\n   * @return {DateTime}\n   */\n  static fromObject(obj, opts = {}) {\n    obj = obj || {};\n    const zoneToUse = normalizeZone(opts.zone, Settings.defaultZone);\n    if (!zoneToUse.isValid) {\n      return DateTime.invalid(unsupportedZone(zoneToUse));\n    }\n\n    const loc = Locale.fromObject(opts);\n    const normalized = normalizeObject(obj, normalizeUnitWithLocalWeeks);\n    const { minDaysInFirstWeek, startOfWeek } = usesLocalWeekValues(normalized, loc);\n\n    const tsNow = Settings.now(),\n      offsetProvis = !isUndefined(opts.specificOffset)\n        ? opts.specificOffset\n        : zoneToUse.offset(tsNow),\n      containsOrdinal = !isUndefined(normalized.ordinal),\n      containsGregorYear = !isUndefined(normalized.year),\n      containsGregorMD = !isUndefined(normalized.month) || !isUndefined(normalized.day),\n      containsGregor = containsGregorYear || containsGregorMD,\n      definiteWeekDef = normalized.weekYear || normalized.weekNumber;\n\n    // cases:\n    // just a weekday -> this week's instance of that weekday, no worries\n    // (gregorian data or ordinal) + (weekYear or weekNumber) -> error\n    // (gregorian month or day) + ordinal -> error\n    // otherwise just use weeks or ordinals or gregorian, depending on what's specified\n\n    if ((containsGregor || containsOrdinal) && definiteWeekDef) {\n      throw new ConflictingSpecificationError(\n        \"Can't mix weekYear/weekNumber units with year/month/day or ordinals\"\n      );\n    }\n\n    if (containsGregorMD && containsOrdinal) {\n      throw new ConflictingSpecificationError(\"Can't mix ordinal dates with month/day\");\n    }\n\n    const useWeekData = definiteWeekDef || (normalized.weekday && !containsGregor);\n\n    // configure ourselves to deal with gregorian dates or week stuff\n    let units,\n      defaultValues,\n      objNow = tsToObj(tsNow, offsetProvis);\n    if (useWeekData) {\n      units = orderedWeekUnits;\n      defaultValues = defaultWeekUnitValues;\n      objNow = gregorianToWeek(objNow, minDaysInFirstWeek, startOfWeek);\n    } else if (containsOrdinal) {\n      units = orderedOrdinalUnits;\n      defaultValues = defaultOrdinalUnitValues;\n      objNow = gregorianToOrdinal(objNow);\n    } else {\n      units = orderedUnits;\n      defaultValues = defaultUnitValues;\n    }\n\n    // set default values for missing stuff\n    let foundFirst = false;\n    for (const u of units) {\n      const v = normalized[u];\n      if (!isUndefined(v)) {\n        foundFirst = true;\n      } else if (foundFirst) {\n        normalized[u] = defaultValues[u];\n      } else {\n        normalized[u] = objNow[u];\n      }\n    }\n\n    // make sure the values we have are in range\n    const higherOrderInvalid = useWeekData\n        ? hasInvalidWeekData(normalized, minDaysInFirstWeek, startOfWeek)\n        : containsOrdinal\n        ? hasInvalidOrdinalData(normalized)\n        : hasInvalidGregorianData(normalized),\n      invalid = higherOrderInvalid || hasInvalidTimeData(normalized);\n\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    }\n\n    // compute the actual time\n    const gregorian = useWeekData\n        ? weekToGregorian(normalized, minDaysInFirstWeek, startOfWeek)\n        : containsOrdinal\n        ? ordinalToGregorian(normalized)\n        : normalized,\n      [tsFinal, offsetFinal] = objToTS(gregorian, offsetProvis, zoneToUse),\n      inst = new DateTime({\n        ts: tsFinal,\n        zone: zoneToUse,\n        o: offsetFinal,\n        loc,\n      });\n\n    // gregorian data + weekday serves only to validate\n    if (normalized.weekday && containsGregor && obj.weekday !== inst.weekday) {\n      return DateTime.invalid(\n        \"mismatched weekday\",\n        `you can't specify both a weekday of ${normalized.weekday} and a date of ${inst.toISO()}`\n      );\n    }\n\n    if (!inst.isValid) {\n      return DateTime.invalid(inst.invalid);\n    }\n\n    return inst;\n  }\n\n  /**\n   * Create a DateTime from an ISO 8601 string\n   * @param {string} text - the ISO string\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the time to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a fixed-offset zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} [opts.outputCalendar] - the output calendar to set on the resulting DateTime instance\n   * @param {string} [opts.numberingSystem] - the numbering system to set on the resulting DateTime instance\n   * @param {string} [opts.weekSettings] - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123')\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123+06:00')\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123+06:00', {setZone: true})\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123', {zone: 'utc'})\n   * @example DateTime.fromISO('2016-W05-4')\n   * @return {DateTime}\n   */\n  static fromISO(text, opts = {}) {\n    const [vals, parsedZone] = parseISODate(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"ISO 8601\", text);\n  }\n\n  /**\n   * Create a DateTime from an RFC 2822 string\n   * @param {string} text - the RFC 2822 string\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - convert the time to this zone. Since the offset is always specified in the string itself, this has no effect on the interpretation of string, merely the zone the resulting DateTime is expressed in.\n   * @param {boolean} [opts.setZone=false] - override the zone with a fixed-offset zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromRFC2822('25 Nov 2016 13:23:12 GMT')\n   * @example DateTime.fromRFC2822('Fri, 25 Nov 2016 13:23:12 +0600')\n   * @example DateTime.fromRFC2822('25 Nov 2016 13:23 Z')\n   * @return {DateTime}\n   */\n  static fromRFC2822(text, opts = {}) {\n    const [vals, parsedZone] = parseRFC2822Date(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"RFC 2822\", text);\n  }\n\n  /**\n   * Create a DateTime from an HTTP header date\n   * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec3.html#sec3.3.1\n   * @param {string} text - the HTTP header date\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - convert the time to this zone. Since HTTP dates are always in UTC, this has no effect on the interpretation of string, merely the zone the resulting DateTime is expressed in.\n   * @param {boolean} [opts.setZone=false] - override the zone with the fixed-offset zone specified in the string. For HTTP dates, this is always UTC, so this option is equivalent to setting the `zone` option to 'utc', but this option is included for consistency with similar methods.\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromHTTP('Sun, 06 Nov 1994 08:49:37 GMT')\n   * @example DateTime.fromHTTP('Sunday, 06-Nov-94 08:49:37 GMT')\n   * @example DateTime.fromHTTP('Sun Nov  6 08:49:37 1994')\n   * @return {DateTime}\n   */\n  static fromHTTP(text, opts = {}) {\n    const [vals, parsedZone] = parseHTTPDate(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"HTTP\", opts);\n  }\n\n  /**\n   * Create a DateTime from an input string and format string.\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale. For a table of tokens and their interpretations, see [here](https://moment.github.io/luxon/#/parsing?id=table-of-tokens).\n   * @param {string} text - the string to parse\n   * @param {string} fmt - the format the string is expected to be in (see the link below for the formats)\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the DateTime to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='en-US'] - a locale string to use when parsing. Will also set the DateTime to this locale\n   * @param {string} opts.numberingSystem - the numbering system to use when parsing. Will also set the resulting DateTime to this numbering system\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromFormat(text, fmt, opts = {}) {\n    if (isUndefined(text) || isUndefined(fmt)) {\n      throw new InvalidArgumentError(\"fromFormat requires an input string and a format\");\n    }\n\n    const { locale = null, numberingSystem = null } = opts,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      }),\n      [vals, parsedZone, specificOffset, invalid] = parseFromTokens(localeToUse, text, fmt);\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    } else {\n      return parseDataToDateTime(vals, parsedZone, opts, `format ${fmt}`, text, specificOffset);\n    }\n  }\n\n  /**\n   * @deprecated use fromFormat instead\n   */\n  static fromString(text, fmt, opts = {}) {\n    return DateTime.fromFormat(text, fmt, opts);\n  }\n\n  /**\n   * Create a DateTime from a SQL date, time, or datetime\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale\n   * @param {string} text - the string to parse\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the DateTime to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='en-US'] - a locale string to use when parsing. Will also set the DateTime to this locale\n   * @param {string} opts.numberingSystem - the numbering system to use when parsing. Will also set the resulting DateTime to this numbering system\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @example DateTime.fromSQL('2017-05-15')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342+06:00')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342 America/Los_Angeles')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342 America/Los_Angeles', { setZone: true })\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342', { zone: 'America/Los_Angeles' })\n   * @example DateTime.fromSQL('09:12:34.342')\n   * @return {DateTime}\n   */\n  static fromSQL(text, opts = {}) {\n    const [vals, parsedZone] = parseSQL(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"SQL\", text);\n  }\n\n  /**\n   * Create an invalid DateTime.\n   * @param {string} reason - simple string of why this DateTime is invalid. Should not contain parameters or anything else data-dependent.\n   * @param {string} [explanation=null] - longer explanation, may include parameters and other useful debugging information\n   * @return {DateTime}\n   */\n  static invalid(reason, explanation = null) {\n    if (!reason) {\n      throw new InvalidArgumentError(\"need to specify a reason the DateTime is invalid\");\n    }\n\n    const invalid = reason instanceof Invalid ? reason : new Invalid(reason, explanation);\n\n    if (Settings.throwOnInvalid) {\n      throw new InvalidDateTimeError(invalid);\n    } else {\n      return new DateTime({ invalid });\n    }\n  }\n\n  /**\n   * Check if an object is an instance of DateTime. Works across context boundaries\n   * @param {object} o\n   * @return {boolean}\n   */\n  static isDateTime(o) {\n    return (o && o.isLuxonDateTime) || false;\n  }\n\n  /**\n   * Produce the format string for a set of options\n   * @param formatOpts\n   * @param localeOpts\n   * @returns {string}\n   */\n  static parseFormatForOpts(formatOpts, localeOpts = {}) {\n    const tokenList = formatOptsToTokens(formatOpts, Locale.fromObject(localeOpts));\n    return !tokenList ? null : tokenList.map((t) => (t ? t.val : null)).join(\"\");\n  }\n\n  /**\n   * Produce the the fully expanded format token for the locale\n   * Does NOT quote characters, so quoted tokens will not round trip correctly\n   * @param fmt\n   * @param localeOpts\n   * @returns {string}\n   */\n  static expandFormat(fmt, localeOpts = {}) {\n    const expanded = expandMacroTokens(Formatter.parseFormat(fmt), Locale.fromObject(localeOpts));\n    return expanded.map((t) => t.val).join(\"\");\n  }\n\n  static resetCache() {\n    zoneOffsetTs = undefined;\n    zoneOffsetGuessCache.clear();\n  }\n\n  // INFO\n\n  /**\n   * Get the value of unit.\n   * @param {string} unit - a unit such as 'minute' or 'day'\n   * @example DateTime.local(2017, 7, 4).get('month'); //=> 7\n   * @example DateTime.local(2017, 7, 4).get('day'); //=> 4\n   * @return {number}\n   */\n  get(unit) {\n    return this[unit];\n  }\n\n  /**\n   * Returns whether the DateTime is valid. Invalid DateTimes occur when:\n   * * The DateTime was created from invalid calendar information, such as the 13th month or February 30\n   * * The DateTime was created by an operation on another invalid date\n   * @type {boolean}\n   */\n  get isValid() {\n    return this.invalid === null;\n  }\n\n  /**\n   * Returns an error code if this DateTime is invalid, or null if the DateTime is valid\n   * @type {string}\n   */\n  get invalidReason() {\n    return this.invalid ? this.invalid.reason : null;\n  }\n\n  /**\n   * Returns an explanation of why this DateTime became invalid, or null if the DateTime is valid\n   * @type {string}\n   */\n  get invalidExplanation() {\n    return this.invalid ? this.invalid.explanation : null;\n  }\n\n  /**\n   * Get the locale of a DateTime, such 'en-GB'. The locale is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get locale() {\n    return this.isValid ? this.loc.locale : null;\n  }\n\n  /**\n   * Get the numbering system of a DateTime, such 'beng'. The numbering system is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get numberingSystem() {\n    return this.isValid ? this.loc.numberingSystem : null;\n  }\n\n  /**\n   * Get the output calendar of a DateTime, such 'islamic'. The output calendar is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get outputCalendar() {\n    return this.isValid ? this.loc.outputCalendar : null;\n  }\n\n  /**\n   * Get the time zone associated with this DateTime.\n   * @type {Zone}\n   */\n  get zone() {\n    return this._zone;\n  }\n\n  /**\n   * Get the name of the time zone.\n   * @type {string}\n   */\n  get zoneName() {\n    return this.isValid ? this.zone.name : null;\n  }\n\n  /**\n   * Get the year\n   * @example DateTime.local(2017, 5, 25).year //=> 2017\n   * @type {number}\n   */\n  get year() {\n    return this.isValid ? this.c.year : NaN;\n  }\n\n  /**\n   * Get the quarter\n   * @example DateTime.local(2017, 5, 25).quarter //=> 2\n   * @type {number}\n   */\n  get quarter() {\n    return this.isValid ? Math.ceil(this.c.month / 3) : NaN;\n  }\n\n  /**\n   * Get the month (1-12).\n   * @example DateTime.local(2017, 5, 25).month //=> 5\n   * @type {number}\n   */\n  get month() {\n    return this.isValid ? this.c.month : NaN;\n  }\n\n  /**\n   * Get the day of the month (1-30ish).\n   * @example DateTime.local(2017, 5, 25).day //=> 25\n   * @type {number}\n   */\n  get day() {\n    return this.isValid ? this.c.day : NaN;\n  }\n\n  /**\n   * Get the hour of the day (0-23).\n   * @example DateTime.local(2017, 5, 25, 9).hour //=> 9\n   * @type {number}\n   */\n  get hour() {\n    return this.isValid ? this.c.hour : NaN;\n  }\n\n  /**\n   * Get the minute of the hour (0-59).\n   * @example DateTime.local(2017, 5, 25, 9, 30).minute //=> 30\n   * @type {number}\n   */\n  get minute() {\n    return this.isValid ? this.c.minute : NaN;\n  }\n\n  /**\n   * Get the second of the minute (0-59).\n   * @example DateTime.local(2017, 5, 25, 9, 30, 52).second //=> 52\n   * @type {number}\n   */\n  get second() {\n    return this.isValid ? this.c.second : NaN;\n  }\n\n  /**\n   * Get the millisecond of the second (0-999).\n   * @example DateTime.local(2017, 5, 25, 9, 30, 52, 654).millisecond //=> 654\n   * @type {number}\n   */\n  get millisecond() {\n    return this.isValid ? this.c.millisecond : NaN;\n  }\n\n  /**\n   * Get the week year\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2014, 12, 31).weekYear //=> 2015\n   * @type {number}\n   */\n  get weekYear() {\n    return this.isValid ? possiblyCachedWeekData(this).weekYear : NaN;\n  }\n\n  /**\n   * Get the week number of the week year (1-52ish).\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2017, 5, 25).weekNumber //=> 21\n   * @type {number}\n   */\n  get weekNumber() {\n    return this.isValid ? possiblyCachedWeekData(this).weekNumber : NaN;\n  }\n\n  /**\n   * Get the day of the week.\n   * 1 is Monday and 7 is Sunday\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2014, 11, 31).weekday //=> 4\n   * @type {number}\n   */\n  get weekday() {\n    return this.isValid ? possiblyCachedWeekData(this).weekday : NaN;\n  }\n\n  /**\n   * Returns true if this date is on a weekend according to the locale, false otherwise\n   * @returns {boolean}\n   */\n  get isWeekend() {\n    return this.isValid && this.loc.getWeekendDays().includes(this.weekday);\n  }\n\n  /**\n   * Get the day of the week according to the locale.\n   * 1 is the first day of the week and 7 is the last day of the week.\n   * If the locale assigns Sunday as the first day of the week, then a date which is a Sunday will return 1,\n   * @returns {number}\n   */\n  get localWeekday() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekday : NaN;\n  }\n\n  /**\n   * Get the week number of the week year according to the locale. Different locales assign week numbers differently,\n   * because the week can start on different days of the week (see localWeekday) and because a different number of days\n   * is required for a week to count as the first week of a year.\n   * @returns {number}\n   */\n  get localWeekNumber() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekNumber : NaN;\n  }\n\n  /**\n   * Get the week year according to the locale. Different locales assign week numbers (and therefor week years)\n   * differently, see localWeekNumber.\n   * @returns {number}\n   */\n  get localWeekYear() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekYear : NaN;\n  }\n\n  /**\n   * Get the ordinal (meaning the day of the year)\n   * @example DateTime.local(2017, 5, 25).ordinal //=> 145\n   * @type {number|DateTime}\n   */\n  get ordinal() {\n    return this.isValid ? gregorianToOrdinal(this.c).ordinal : NaN;\n  }\n\n  /**\n   * Get the human readable short month name, such as 'Oct'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).monthShort //=> Oct\n   * @type {string}\n   */\n  get monthShort() {\n    return this.isValid ? Info.months(\"short\", { locObj: this.loc })[this.month - 1] : null;\n  }\n\n  /**\n   * Get the human readable long month name, such as 'October'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).monthLong //=> October\n   * @type {string}\n   */\n  get monthLong() {\n    return this.isValid ? Info.months(\"long\", { locObj: this.loc })[this.month - 1] : null;\n  }\n\n  /**\n   * Get the human readable short weekday, such as 'Mon'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).weekdayShort //=> Mon\n   * @type {string}\n   */\n  get weekdayShort() {\n    return this.isValid ? Info.weekdays(\"short\", { locObj: this.loc })[this.weekday - 1] : null;\n  }\n\n  /**\n   * Get the human readable long weekday, such as 'Monday'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).weekdayLong //=> Monday\n   * @type {string}\n   */\n  get weekdayLong() {\n    return this.isValid ? Info.weekdays(\"long\", { locObj: this.loc })[this.weekday - 1] : null;\n  }\n\n  /**\n   * Get the UTC offset of this DateTime in minutes\n   * @example DateTime.now().offset //=> -240\n   * @example DateTime.utc().offset //=> 0\n   * @type {number}\n   */\n  get offset() {\n    return this.isValid ? +this.o : NaN;\n  }\n\n  /**\n   * Get the short human name for the zone's current offset, for example \"EST\" or \"EDT\".\n   * Defaults to the system's locale if no locale has been specified\n   * @type {string}\n   */\n  get offsetNameShort() {\n    if (this.isValid) {\n      return this.zone.offsetName(this.ts, {\n        format: \"short\",\n        locale: this.locale,\n      });\n    } else {\n      return null;\n    }\n  }\n\n  /**\n   * Get the long human name for the zone's current offset, for example \"Eastern Standard Time\" or \"Eastern Daylight Time\".\n   * Defaults to the system's locale if no locale has been specified\n   * @type {string}\n   */\n  get offsetNameLong() {\n    if (this.isValid) {\n      return this.zone.offsetName(this.ts, {\n        format: \"long\",\n        locale: this.locale,\n      });\n    } else {\n      return null;\n    }\n  }\n\n  /**\n   * Get whether this zone's offset ever changes, as in a DST.\n   * @type {boolean}\n   */\n  get isOffsetFixed() {\n    return this.isValid ? this.zone.isUniversal : null;\n  }\n\n  /**\n   * Get whether the DateTime is in a DST.\n   * @type {boolean}\n   */\n  get isInDST() {\n    if (this.isOffsetFixed) {\n      return false;\n    } else {\n      return (\n        this.offset > this.set({ month: 1, day: 1 }).offset ||\n        this.offset > this.set({ month: 5 }).offset\n      );\n    }\n  }\n\n  /**\n   * Get those DateTimes which have the same local time as this DateTime, but a different offset from UTC\n   * in this DateTime's zone. During DST changes local time can be ambiguous, for example\n   * `2023-10-29T02:30:00` in `Europe/Berlin` can have offset `+01:00` or `+02:00`.\n   * This method will return both possible DateTimes if this DateTime's local time is ambiguous.\n   * @returns {DateTime[]}\n   */\n  getPossibleOffsets() {\n    if (!this.isValid || this.isOffsetFixed) {\n      return [this];\n    }\n    const dayMs = 86400000;\n    const minuteMs = 60000;\n    const localTS = objToLocalTS(this.c);\n    const oEarlier = this.zone.offset(localTS - dayMs);\n    const oLater = this.zone.offset(localTS + dayMs);\n\n    const o1 = this.zone.offset(localTS - oEarlier * minuteMs);\n    const o2 = this.zone.offset(localTS - oLater * minuteMs);\n    if (o1 === o2) {\n      return [this];\n    }\n    const ts1 = localTS - o1 * minuteMs;\n    const ts2 = localTS - o2 * minuteMs;\n    const c1 = tsToObj(ts1, o1);\n    const c2 = tsToObj(ts2, o2);\n    if (\n      c1.hour === c2.hour &&\n      c1.minute === c2.minute &&\n      c1.second === c2.second &&\n      c1.millisecond === c2.millisecond\n    ) {\n      return [clone(this, { ts: ts1 }), clone(this, { ts: ts2 })];\n    }\n    return [this];\n  }\n\n  /**\n   * Returns true if this DateTime is in a leap year, false otherwise\n   * @example DateTime.local(2016).isInLeapYear //=> true\n   * @example DateTime.local(2013).isInLeapYear //=> false\n   * @type {boolean}\n   */\n  get isInLeapYear() {\n    return isLeapYear(this.year);\n  }\n\n  /**\n   * Returns the number of days in this DateTime's month\n   * @example DateTime.local(2016, 2).daysInMonth //=> 29\n   * @example DateTime.local(2016, 3).daysInMonth //=> 31\n   * @type {number}\n   */\n  get daysInMonth() {\n    return daysInMonth(this.year, this.month);\n  }\n\n  /**\n   * Returns the number of days in this DateTime's year\n   * @example DateTime.local(2016).daysInYear //=> 366\n   * @example DateTime.local(2013).daysInYear //=> 365\n   * @type {number}\n   */\n  get daysInYear() {\n    return this.isValid ? daysInYear(this.year) : NaN;\n  }\n\n  /**\n   * Returns the number of weeks in this DateTime's year\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2004).weeksInWeekYear //=> 53\n   * @example DateTime.local(2013).weeksInWeekYear //=> 52\n   * @type {number}\n   */\n  get weeksInWeekYear() {\n    return this.isValid ? weeksInWeekYear(this.weekYear) : NaN;\n  }\n\n  /**\n   * Returns the number of weeks in this DateTime's local week year\n   * @example DateTime.local(2020, 6, {locale: 'en-US'}).weeksInLocalWeekYear //=> 52\n   * @example DateTime.local(2020, 6, {locale: 'de-DE'}).weeksInLocalWeekYear //=> 53\n   * @type {number}\n   */\n  get weeksInLocalWeekYear() {\n    return this.isValid\n      ? weeksInWeekYear(\n          this.localWeekYear,\n          this.loc.getMinDaysInFirstWeek(),\n          this.loc.getStartOfWeek()\n        )\n      : NaN;\n  }\n\n  /**\n   * Returns the resolved Intl options for this DateTime.\n   * This is useful in understanding the behavior of formatting methods\n   * @param {Object} opts - the same options as toLocaleString\n   * @return {Object}\n   */\n  resolvedLocaleOptions(opts = {}) {\n    const { locale, numberingSystem, calendar } = Formatter.create(\n      this.loc.clone(opts),\n      opts\n    ).resolvedOptions(this);\n    return { locale, numberingSystem, outputCalendar: calendar };\n  }\n\n  // TRANSFORM\n\n  /**\n   * \"Set\" the DateTime's zone to UTC. Returns a newly-constructed DateTime.\n   *\n   * Equivalent to {@link DateTime#setZone}('utc')\n   * @param {number} [offset=0] - optionally, an offset from UTC in minutes\n   * @param {Object} [opts={}] - options to pass to `setZone()`\n   * @return {DateTime}\n   */\n  toUTC(offset = 0, opts = {}) {\n    return this.setZone(FixedOffsetZone.instance(offset), opts);\n  }\n\n  /**\n   * \"Set\" the DateTime's zone to the host's local zone. Returns a newly-constructed DateTime.\n   *\n   * Equivalent to `setZone('local')`\n   * @return {DateTime}\n   */\n  toLocal() {\n    return this.setZone(Settings.defaultZone);\n  }\n\n  /**\n   * \"Set\" the DateTime's zone to specified zone. Returns a newly-constructed DateTime.\n   *\n   * By default, the setter keeps the underlying time the same (as in, the same timestamp), but the new instance will report different local times and consider DSTs when making computations, as with {@link DateTime#plus}. You may wish to use {@link DateTime#toLocal} and {@link DateTime#toUTC} which provide simple convenience wrappers for commonly used zones.\n   * @param {string|Zone} [zone='local'] - a zone identifier. As a string, that can be any IANA zone supported by the host environment, or a fixed-offset name of the form 'UTC+3', or the strings 'local' or 'utc'. You may also supply an instance of a {@link DateTime#Zone} class.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.keepLocalTime=false] - If true, adjust the underlying time so that the local time stays the same, but in the target zone. You should rarely need this.\n   * @return {DateTime}\n   */\n  setZone(zone, { keepLocalTime = false, keepCalendarTime = false } = {}) {\n    zone = normalizeZone(zone, Settings.defaultZone);\n    if (zone.equals(this.zone)) {\n      return this;\n    } else if (!zone.isValid) {\n      return DateTime.invalid(unsupportedZone(zone));\n    } else {\n      let newTS = this.ts;\n      if (keepLocalTime || keepCalendarTime) {\n        const offsetGuess = zone.offset(this.ts);\n        const asObj = this.toObject();\n        [newTS] = objToTS(asObj, offsetGuess, zone);\n      }\n      return clone(this, { ts: newTS, zone });\n    }\n  }\n\n  /**\n   * \"Set\" the locale, numberingSystem, or outputCalendar. Returns a newly-constructed DateTime.\n   * @param {Object} properties - the properties to set\n   * @example DateTime.local(2017, 5, 25).reconfigure({ locale: 'en-GB' })\n   * @return {DateTime}\n   */\n  reconfigure({ locale, numberingSystem, outputCalendar } = {}) {\n    const loc = this.loc.clone({ locale, numberingSystem, outputCalendar });\n    return clone(this, { loc });\n  }\n\n  /**\n   * \"Set\" the locale. Returns a newly-constructed DateTime.\n   * Just a convenient alias for reconfigure({ locale })\n   * @example DateTime.local(2017, 5, 25).setLocale('en-GB')\n   * @return {DateTime}\n   */\n  setLocale(locale) {\n    return this.reconfigure({ locale });\n  }\n\n  /**\n   * \"Set\" the values of specified units. Returns a newly-constructed DateTime.\n   * You can only set units with this method; for \"setting\" metadata, see {@link DateTime#reconfigure} and {@link DateTime#setZone}.\n   *\n   * This method also supports setting locale-based week units, i.e. `localWeekday`, `localWeekNumber` and `localWeekYear`.\n   * They cannot be mixed with ISO-week units like `weekday`.\n   * @param {Object} values - a mapping of units to numbers\n   * @example dt.set({ year: 2017 })\n   * @example dt.set({ hour: 8, minute: 30 })\n   * @example dt.set({ weekday: 5 })\n   * @example dt.set({ year: 2005, ordinal: 234 })\n   * @return {DateTime}\n   */\n  set(values) {\n    if (!this.isValid) return this;\n\n    const normalized = normalizeObject(values, normalizeUnitWithLocalWeeks);\n    const { minDaysInFirstWeek, startOfWeek } = usesLocalWeekValues(normalized, this.loc);\n\n    const settingWeekStuff =\n        !isUndefined(normalized.weekYear) ||\n        !isUndefined(normalized.weekNumber) ||\n        !isUndefined(normalized.weekday),\n      containsOrdinal = !isUndefined(normalized.ordinal),\n      containsGregorYear = !isUndefined(normalized.year),\n      containsGregorMD = !isUndefined(normalized.month) || !isUndefined(normalized.day),\n      containsGregor = containsGregorYear || containsGregorMD,\n      definiteWeekDef = normalized.weekYear || normalized.weekNumber;\n\n    if ((containsGregor || containsOrdinal) && definiteWeekDef) {\n      throw new ConflictingSpecificationError(\n        \"Can't mix weekYear/weekNumber units with year/month/day or ordinals\"\n      );\n    }\n\n    if (containsGregorMD && containsOrdinal) {\n      throw new ConflictingSpecificationError(\"Can't mix ordinal dates with month/day\");\n    }\n\n    let mixed;\n    if (settingWeekStuff) {\n      mixed = weekToGregorian(\n        { ...gregorianToWeek(this.c, minDaysInFirstWeek, startOfWeek), ...normalized },\n        minDaysInFirstWeek,\n        startOfWeek\n      );\n    } else if (!isUndefined(normalized.ordinal)) {\n      mixed = ordinalToGregorian({ ...gregorianToOrdinal(this.c), ...normalized });\n    } else {\n      mixed = { ...this.toObject(), ...normalized };\n\n      // if we didn't set the day but we ended up on an overflow date,\n      // use the last day of the right month\n      if (isUndefined(normalized.day)) {\n        mixed.day = Math.min(daysInMonth(mixed.year, mixed.month), mixed.day);\n      }\n    }\n\n    const [ts, o] = objToTS(mixed, this.o, this.zone);\n    return clone(this, { ts, o });\n  }\n\n  /**\n   * Add a period of time to this DateTime and return the resulting DateTime\n   *\n   * Adding hours, minutes, seconds, or milliseconds increases the timestamp by the right number of milliseconds. Adding days, months, or years shifts the calendar, accounting for DSTs and leap years along the way. Thus, `dt.plus({ hours: 24 })` may result in a different time than `dt.plus({ days: 1 })` if there's a DST shift in between.\n   * @param {Duration|Object|number} duration - The amount to add. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   * @example DateTime.now().plus(123) //~> in 123 milliseconds\n   * @example DateTime.now().plus({ minutes: 15 }) //~> in 15 minutes\n   * @example DateTime.now().plus({ days: 1 }) //~> this time tomorrow\n   * @example DateTime.now().plus({ days: -1 }) //~> this time yesterday\n   * @example DateTime.now().plus({ hours: 3, minutes: 13 }) //~> in 3 hr, 13 min\n   * @example DateTime.now().plus(Duration.fromObject({ hours: 3, minutes: 13 })) //~> in 3 hr, 13 min\n   * @return {DateTime}\n   */\n  plus(duration) {\n    if (!this.isValid) return this;\n    const dur = Duration.fromDurationLike(duration);\n    return clone(this, adjustTime(this, dur));\n  }\n\n  /**\n   * Subtract a period of time to this DateTime and return the resulting DateTime\n   * See {@link DateTime#plus}\n   * @param {Duration|Object|number} duration - The amount to subtract. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   @return {DateTime}\n   */\n  minus(duration) {\n    if (!this.isValid) return this;\n    const dur = Duration.fromDurationLike(duration).negate();\n    return clone(this, adjustTime(this, dur));\n  }\n\n  /**\n   * \"Set\" this DateTime to the beginning of a unit of time.\n   * @param {string} unit - The unit to go to the beginning of. Can be 'year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second', or 'millisecond'.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week\n   * @example DateTime.local(2014, 3, 3).startOf('month').toISODate(); //=> '2014-03-01'\n   * @example DateTime.local(2014, 3, 3).startOf('year').toISODate(); //=> '2014-01-01'\n   * @example DateTime.local(2014, 3, 3).startOf('week').toISODate(); //=> '2014-03-03', weeks always start on Mondays\n   * @example DateTime.local(2014, 3, 3, 5, 30).startOf('day').toISOTime(); //=> '00:00.000-05:00'\n   * @example DateTime.local(2014, 3, 3, 5, 30).startOf('hour').toISOTime(); //=> '05:00:00.000-05:00'\n   * @return {DateTime}\n   */\n  startOf(unit, { useLocaleWeeks = false } = {}) {\n    if (!this.isValid) return this;\n\n    const o = {},\n      normalizedUnit = Duration.normalizeUnit(unit);\n    switch (normalizedUnit) {\n      case \"years\":\n        o.month = 1;\n      // falls through\n      case \"quarters\":\n      case \"months\":\n        o.day = 1;\n      // falls through\n      case \"weeks\":\n      case \"days\":\n        o.hour = 0;\n      // falls through\n      case \"hours\":\n        o.minute = 0;\n      // falls through\n      case \"minutes\":\n        o.second = 0;\n      // falls through\n      case \"seconds\":\n        o.millisecond = 0;\n        break;\n      case \"milliseconds\":\n        break;\n      // no default, invalid units throw in normalizeUnit()\n    }\n\n    if (normalizedUnit === \"weeks\") {\n      if (useLocaleWeeks) {\n        const startOfWeek = this.loc.getStartOfWeek();\n        const { weekday } = this;\n        if (weekday < startOfWeek) {\n          o.weekNumber = this.weekNumber - 1;\n        }\n        o.weekday = startOfWeek;\n      } else {\n        o.weekday = 1;\n      }\n    }\n\n    if (normalizedUnit === \"quarters\") {\n      const q = Math.ceil(this.month / 3);\n      o.month = (q - 1) * 3 + 1;\n    }\n\n    return this.set(o);\n  }\n\n  /**\n   * \"Set\" this DateTime to the end (meaning the last millisecond) of a unit of time\n   * @param {string} unit - The unit to go to the end of. Can be 'year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second', or 'millisecond'.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week\n   * @example DateTime.local(2014, 3, 3).endOf('month').toISO(); //=> '2014-03-31T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3).endOf('year').toISO(); //=> '2014-12-31T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3).endOf('week').toISO(); // => '2014-03-09T23:59:59.999-05:00', weeks start on Mondays\n   * @example DateTime.local(2014, 3, 3, 5, 30).endOf('day').toISO(); //=> '2014-03-03T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3, 5, 30).endOf('hour').toISO(); //=> '2014-03-03T05:59:59.999-05:00'\n   * @return {DateTime}\n   */\n  endOf(unit, opts) {\n    return this.isValid\n      ? this.plus({ [unit]: 1 })\n          .startOf(unit, opts)\n          .minus(1)\n      : this;\n  }\n\n  // OUTPUT\n\n  /**\n   * Returns a string representation of this DateTime formatted according to the specified format string.\n   * **You may not want this.** See {@link DateTime#toLocaleString} for a more flexible formatting tool. For a table of tokens and their interpretations, see [here](https://moment.github.io/luxon/#/formatting?id=table-of-tokens).\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale.\n   * @param {string} fmt - the format string\n   * @param {Object} opts - opts to override the configuration options on this DateTime\n   * @example DateTime.now().toFormat('yyyy LLL dd') //=> '2017 Apr 22'\n   * @example DateTime.now().setLocale('fr').toFormat('yyyy LLL dd') //=> '2017 avr. 22'\n   * @example DateTime.now().toFormat('yyyy LLL dd', { locale: \"fr\" }) //=> '2017 avr. 22'\n   * @example DateTime.now().toFormat(\"HH 'hours and' mm 'minutes'\") //=> '20 hours and 55 minutes'\n   * @return {string}\n   */\n  toFormat(fmt, opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.loc.redefaultToEN(opts)).formatDateTimeFromString(this, fmt)\n      : INVALID;\n  }\n\n  /**\n   * Returns a localized string representing this date. Accepts the same options as the Intl.DateTimeFormat constructor and any presets defined by Luxon, such as `DateTime.DATE_FULL` or `DateTime.TIME_SIMPLE`.\n   * The exact behavior of this method is browser-specific, but in general it will return an appropriate representation\n   * of the DateTime in the assigned locale.\n   * Defaults to the system's locale if no locale has been specified\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat\n   * @param formatOpts {Object} - Intl.DateTimeFormat constructor options and configuration options\n   * @param {Object} opts - opts to override the configuration options on this DateTime\n   * @example DateTime.now().toLocaleString(); //=> 4/20/2017\n   * @example DateTime.now().setLocale('en-gb').toLocaleString(); //=> '20/04/2017'\n   * @example DateTime.now().toLocaleString(DateTime.DATE_FULL); //=> 'April 20, 2017'\n   * @example DateTime.now().toLocaleString(DateTime.DATE_FULL, { locale: 'fr' }); //=> '28 août 2022'\n   * @example DateTime.now().toLocaleString(DateTime.TIME_SIMPLE); //=> '11:32 AM'\n   * @example DateTime.now().toLocaleString(DateTime.DATETIME_SHORT); //=> '4/20/2017, 11:32 AM'\n   * @example DateTime.now().toLocaleString({ weekday: 'long', month: 'long', day: '2-digit' }); //=> 'Thursday, April 20'\n   * @example DateTime.now().toLocaleString({ weekday: 'short', month: 'short', day: '2-digit', hour: '2-digit', minute: '2-digit' }); //=> 'Thu, Apr 20, 11:27 AM'\n   * @example DateTime.now().toLocaleString({ hour: '2-digit', minute: '2-digit', hourCycle: 'h23' }); //=> '11:32'\n   * @return {string}\n   */\n  toLocaleString(formatOpts = Formats.DATE_SHORT, opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.loc.clone(opts), formatOpts).formatDateTime(this)\n      : INVALID;\n  }\n\n  /**\n   * Returns an array of format \"parts\", meaning individual tokens along with metadata. This is allows callers to post-process individual sections of the formatted output.\n   * Defaults to the system's locale if no locale has been specified\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat/formatToParts\n   * @param opts {Object} - Intl.DateTimeFormat constructor options, same as `toLocaleString`.\n   * @example DateTime.now().toLocaleParts(); //=> [\n   *                                   //=>   { type: 'day', value: '25' },\n   *                                   //=>   { type: 'literal', value: '/' },\n   *                                   //=>   { type: 'month', value: '05' },\n   *                                   //=>   { type: 'literal', value: '/' },\n   *                                   //=>   { type: 'year', value: '1982' }\n   *                                   //=> ]\n   */\n  toLocaleParts(opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.loc.clone(opts), opts).formatDateTimeParts(this)\n      : [];\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime\n   * @param {Object} opts - options\n   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0\n   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.extendedZone=false] - add the time zone format extension\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example DateTime.utc(1983, 5, 25).toISO() //=> '1982-05-25T00:00:00.000Z'\n   * @example DateTime.now().toISO() //=> '2017-04-22T20:47:05.335-04:00'\n   * @example DateTime.now().toISO({ includeOffset: false }) //=> '2017-04-22T20:47:05.335'\n   * @example DateTime.now().toISO({ format: 'basic' }) //=> '20170422T204705.335-0400'\n   * @return {string|null}\n   */\n  toISO({\n    format = \"extended\",\n    suppressSeconds = false,\n    suppressMilliseconds = false,\n    includeOffset = true,\n    extendedZone = false,\n  } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n\n    const ext = format === \"extended\";\n\n    let c = toISODate(this, ext);\n    c += \"T\";\n    c += toISOTime(this, ext, suppressSeconds, suppressMilliseconds, includeOffset, extendedZone);\n    return c;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's date component\n   * @param {Object} opts - options\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example DateTime.utc(1982, 5, 25).toISODate() //=> '1982-05-25'\n   * @example DateTime.utc(1982, 5, 25).toISODate({ format: 'basic' }) //=> '19820525'\n   * @return {string|null}\n   */\n  toISODate({ format = \"extended\" } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n\n    return toISODate(this, format === \"extended\");\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's week date\n   * @example DateTime.utc(1982, 5, 25).toISOWeekDate() //=> '1982-W21-2'\n   * @return {string}\n   */\n  toISOWeekDate() {\n    return toTechFormat(this, \"kkkk-'W'WW-c\");\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's time component\n   * @param {Object} opts - options\n   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0\n   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.extendedZone=true] - add the time zone format extension\n   * @param {boolean} [opts.includePrefix=false] - include the `T` prefix\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime() //=> '07:34:19.361Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34, seconds: 0, milliseconds: 0 }).toISOTime({ suppressSeconds: true }) //=> '07:34Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime({ format: 'basic' }) //=> '073419.361Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime({ includePrefix: true }) //=> 'T07:34:19.361Z'\n   * @return {string}\n   */\n  toISOTime({\n    suppressMilliseconds = false,\n    suppressSeconds = false,\n    includeOffset = true,\n    includePrefix = false,\n    extendedZone = false,\n    format = \"extended\",\n  } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n\n    let c = includePrefix ? \"T\" : \"\";\n    return (\n      c +\n      toISOTime(\n        this,\n        format === \"extended\",\n        suppressSeconds,\n        suppressMilliseconds,\n        includeOffset,\n        extendedZone\n      )\n    );\n  }\n\n  /**\n   * Returns an RFC 2822-compatible string representation of this DateTime\n   * @example DateTime.utc(2014, 7, 13).toRFC2822() //=> 'Sun, 13 Jul 2014 00:00:00 +0000'\n   * @example DateTime.local(2014, 7, 13).toRFC2822() //=> 'Sun, 13 Jul 2014 00:00:00 -0400'\n   * @return {string}\n   */\n  toRFC2822() {\n    return toTechFormat(this, \"EEE, dd LLL yyyy HH:mm:ss ZZZ\", false);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in HTTP headers. The output is always expressed in GMT.\n   * Specifically, the string conforms to RFC 1123.\n   * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec3.html#sec3.3.1\n   * @example DateTime.utc(2014, 7, 13).toHTTP() //=> 'Sun, 13 Jul 2014 00:00:00 GMT'\n   * @example DateTime.utc(2014, 7, 13, 19).toHTTP() //=> 'Sun, 13 Jul 2014 19:00:00 GMT'\n   * @return {string}\n   */\n  toHTTP() {\n    return toTechFormat(this.toUTC(), \"EEE, dd LLL yyyy HH:mm:ss 'GMT'\");\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL Date\n   * @example DateTime.utc(2014, 7, 13).toSQLDate() //=> '2014-07-13'\n   * @return {string|null}\n   */\n  toSQLDate() {\n    if (!this.isValid) {\n      return null;\n    }\n    return toISODate(this, true);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL Time\n   * @param {Object} opts - options\n   * @param {boolean} [opts.includeZone=false] - include the zone, such as 'America/New_York'. Overrides includeOffset.\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.includeOffsetSpace=true] - include the space between the time and the offset, such as '05:15:16.345 -04:00'\n   * @example DateTime.utc().toSQL() //=> '05:15:16.345'\n   * @example DateTime.now().toSQL() //=> '05:15:16.345 -04:00'\n   * @example DateTime.now().toSQL({ includeOffset: false }) //=> '05:15:16.345'\n   * @example DateTime.now().toSQL({ includeZone: false }) //=> '05:15:16.345 America/New_York'\n   * @return {string}\n   */\n  toSQLTime({ includeOffset = true, includeZone = false, includeOffsetSpace = true } = {}) {\n    let fmt = \"HH:mm:ss.SSS\";\n\n    if (includeZone || includeOffset) {\n      if (includeOffsetSpace) {\n        fmt += \" \";\n      }\n      if (includeZone) {\n        fmt += \"z\";\n      } else if (includeOffset) {\n        fmt += \"ZZ\";\n      }\n    }\n\n    return toTechFormat(this, fmt, true);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL DateTime\n   * @param {Object} opts - options\n   * @param {boolean} [opts.includeZone=false] - include the zone, such as 'America/New_York'. Overrides includeOffset.\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.includeOffsetSpace=true] - include the space between the time and the offset, such as '05:15:16.345 -04:00'\n   * @example DateTime.utc(2014, 7, 13).toSQL() //=> '2014-07-13 00:00:00.000 Z'\n   * @example DateTime.local(2014, 7, 13).toSQL() //=> '2014-07-13 00:00:00.000 -04:00'\n   * @example DateTime.local(2014, 7, 13).toSQL({ includeOffset: false }) //=> '2014-07-13 00:00:00.000'\n   * @example DateTime.local(2014, 7, 13).toSQL({ includeZone: true }) //=> '2014-07-13 00:00:00.000 America/New_York'\n   * @return {string}\n   */\n  toSQL(opts = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n\n    return `${this.toSQLDate()} ${this.toSQLTime(opts)}`;\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for debugging\n   * @return {string}\n   */\n  toString() {\n    return this.isValid ? this.toISO() : INVALID;\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for the REPL.\n   * @return {string}\n   */\n  [Symbol.for(\"nodejs.util.inspect.custom\")]() {\n    if (this.isValid) {\n      return `DateTime { ts: ${this.toISO()}, zone: ${this.zone.name}, locale: ${this.locale} }`;\n    } else {\n      return `DateTime { Invalid, reason: ${this.invalidReason} }`;\n    }\n  }\n\n  /**\n   * Returns the epoch milliseconds of this DateTime. Alias of {@link DateTime#toMillis}\n   * @return {number}\n   */\n  valueOf() {\n    return this.toMillis();\n  }\n\n  /**\n   * Returns the epoch milliseconds of this DateTime.\n   * @return {number}\n   */\n  toMillis() {\n    return this.isValid ? this.ts : NaN;\n  }\n\n  /**\n   * Returns the epoch seconds (including milliseconds in the fractional part) of this DateTime.\n   * @return {number}\n   */\n  toSeconds() {\n    return this.isValid ? this.ts / 1000 : NaN;\n  }\n\n  /**\n   * Returns the epoch seconds (as a whole number) of this DateTime.\n   * @return {number}\n   */\n  toUnixInteger() {\n    return this.isValid ? Math.floor(this.ts / 1000) : NaN;\n  }\n\n  /**\n   * Returns an ISO 8601 representation of this DateTime appropriate for use in JSON.\n   * @return {string}\n   */\n  toJSON() {\n    return this.toISO();\n  }\n\n  /**\n   * Returns a BSON serializable equivalent to this DateTime.\n   * @return {Date}\n   */\n  toBSON() {\n    return this.toJSDate();\n  }\n\n  /**\n   * Returns a JavaScript object with this DateTime's year, month, day, and so on.\n   * @param opts - options for generating the object\n   * @param {boolean} [opts.includeConfig=false] - include configuration attributes in the output\n   * @example DateTime.now().toObject() //=> { year: 2017, month: 4, day: 22, hour: 20, minute: 49, second: 42, millisecond: 268 }\n   * @return {Object}\n   */\n  toObject(opts = {}) {\n    if (!this.isValid) return {};\n\n    const base = { ...this.c };\n\n    if (opts.includeConfig) {\n      base.outputCalendar = this.outputCalendar;\n      base.numberingSystem = this.loc.numberingSystem;\n      base.locale = this.loc.locale;\n    }\n    return base;\n  }\n\n  /**\n   * Returns a JavaScript Date equivalent to this DateTime.\n   * @return {Date}\n   */\n  toJSDate() {\n    return new Date(this.isValid ? this.ts : NaN);\n  }\n\n  // COMPARE\n\n  /**\n   * Return the difference between two DateTimes as a Duration.\n   * @param {DateTime} otherDateTime - the DateTime to compare this one to\n   * @param {string|string[]} [unit=['milliseconds']] - the unit or array of units (such as 'hours' or 'days') to include in the duration.\n   * @param {Object} opts - options that affect the creation of the Duration\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @example\n   * var i1 = DateTime.fromISO('1982-05-25T09:45'),\n   *     i2 = DateTime.fromISO('1983-10-14T10:30');\n   * i2.diff(i1).toObject() //=> { milliseconds: 43807500000 }\n   * i2.diff(i1, 'hours').toObject() //=> { hours: 12168.75 }\n   * i2.diff(i1, ['months', 'days']).toObject() //=> { months: 16, days: 19.03125 }\n   * i2.diff(i1, ['months', 'days', 'hours']).toObject() //=> { months: 16, days: 19, hours: 0.75 }\n   * @return {Duration}\n   */\n  diff(otherDateTime, unit = \"milliseconds\", opts = {}) {\n    if (!this.isValid || !otherDateTime.isValid) {\n      return Duration.invalid(\"created by diffing an invalid DateTime\");\n    }\n\n    const durOpts = { locale: this.locale, numberingSystem: this.numberingSystem, ...opts };\n\n    const units = maybeArray(unit).map(Duration.normalizeUnit),\n      otherIsLater = otherDateTime.valueOf() > this.valueOf(),\n      earlier = otherIsLater ? this : otherDateTime,\n      later = otherIsLater ? otherDateTime : this,\n      diffed = diff(earlier, later, units, durOpts);\n\n    return otherIsLater ? diffed.negate() : diffed;\n  }\n\n  /**\n   * Return the difference between this DateTime and right now.\n   * See {@link DateTime#diff}\n   * @param {string|string[]} [unit=['milliseconds']] - the unit or units units (such as 'hours' or 'days') to include in the duration\n   * @param {Object} opts - options that affect the creation of the Duration\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @return {Duration}\n   */\n  diffNow(unit = \"milliseconds\", opts = {}) {\n    return this.diff(DateTime.now(), unit, opts);\n  }\n\n  /**\n   * Return an Interval spanning between this DateTime and another DateTime\n   * @param {DateTime} otherDateTime - the other end point of the Interval\n   * @return {Interval|DateTime}\n   */\n  until(otherDateTime) {\n    return this.isValid ? Interval.fromDateTimes(this, otherDateTime) : this;\n  }\n\n  /**\n   * Return whether this DateTime is in the same unit of time as another DateTime.\n   * Higher-order units must also be identical for this function to return `true`.\n   * Note that time zones are **ignored** in this comparison, which compares the **local** calendar time. Use {@link DateTime#setZone} to convert one of the dates if needed.\n   * @param {DateTime} otherDateTime - the other DateTime\n   * @param {string} unit - the unit of time to check sameness on\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week; only the locale of this DateTime is used\n   * @example DateTime.now().hasSame(otherDT, 'day'); //~> true if otherDT is in the same current calendar day\n   * @return {boolean}\n   */\n  hasSame(otherDateTime, unit, opts) {\n    if (!this.isValid) return false;\n\n    const inputMs = otherDateTime.valueOf();\n    const adjustedToZone = this.setZone(otherDateTime.zone, { keepLocalTime: true });\n    return (\n      adjustedToZone.startOf(unit, opts) <= inputMs && inputMs <= adjustedToZone.endOf(unit, opts)\n    );\n  }\n\n  /**\n   * Equality check\n   * Two DateTimes are equal if and only if they represent the same millisecond, have the same zone and location, and are both valid.\n   * To compare just the millisecond values, use `+dt1 === +dt2`.\n   * @param {DateTime} other - the other DateTime\n   * @return {boolean}\n   */\n  equals(other) {\n    return (\n      this.isValid &&\n      other.isValid &&\n      this.valueOf() === other.valueOf() &&\n      this.zone.equals(other.zone) &&\n      this.loc.equals(other.loc)\n    );\n  }\n\n  /**\n   * Returns a string representation of a this time relative to now, such as \"in two days\". Can only internationalize if your\n   * platform supports Intl.RelativeTimeFormat. Rounds down by default.\n   * @param {Object} options - options that affect the output\n   * @param {DateTime} [options.base=DateTime.now()] - the DateTime to use as the basis to which this time is compared. Defaults to now.\n   * @param {string} [options.style=\"long\"] - the style of units, must be \"long\", \"short\", or \"narrow\"\n   * @param {string|string[]} options.unit - use a specific unit or array of units; if omitted, or an array, the method will pick the best unit. Use an array or one of \"years\", \"quarters\", \"months\", \"weeks\", \"days\", \"hours\", \"minutes\", or \"seconds\"\n   * @param {boolean} [options.round=true] - whether to round the numbers in the output.\n   * @param {number} [options.padding=0] - padding in milliseconds. This allows you to round up the result if it fits inside the threshold. Don't use in combination with {round: false} because the decimal output will include the padding.\n   * @param {string} options.locale - override the locale of this DateTime\n   * @param {string} options.numberingSystem - override the numberingSystem of this DateTime. The Intl system may choose not to honor this\n   * @example DateTime.now().plus({ days: 1 }).toRelative() //=> \"in 1 day\"\n   * @example DateTime.now().setLocale(\"es\").toRelative({ days: 1 }) //=> \"dentro de 1 día\"\n   * @example DateTime.now().plus({ days: 1 }).toRelative({ locale: \"fr\" }) //=> \"dans 23 heures\"\n   * @example DateTime.now().minus({ days: 2 }).toRelative() //=> \"2 days ago\"\n   * @example DateTime.now().minus({ days: 2 }).toRelative({ unit: \"hours\" }) //=> \"48 hours ago\"\n   * @example DateTime.now().minus({ hours: 36 }).toRelative({ round: false }) //=> \"1.5 days ago\"\n   */\n  toRelative(options = {}) {\n    if (!this.isValid) return null;\n    const base = options.base || DateTime.fromObject({}, { zone: this.zone }),\n      padding = options.padding ? (this < base ? -options.padding : options.padding) : 0;\n    let units = [\"years\", \"months\", \"days\", \"hours\", \"minutes\", \"seconds\"];\n    let unit = options.unit;\n    if (Array.isArray(options.unit)) {\n      units = options.unit;\n      unit = undefined;\n    }\n    return diffRelative(base, this.plus(padding), {\n      ...options,\n      numeric: \"always\",\n      units,\n      unit,\n    });\n  }\n\n  /**\n   * Returns a string representation of this date relative to today, such as \"yesterday\" or \"next month\".\n   * Only internationalizes on platforms that supports Intl.RelativeTimeFormat.\n   * @param {Object} options - options that affect the output\n   * @param {DateTime} [options.base=DateTime.now()] - the DateTime to use as the basis to which this time is compared. Defaults to now.\n   * @param {string} options.locale - override the locale of this DateTime\n   * @param {string} options.unit - use a specific unit; if omitted, the method will pick the unit. Use one of \"years\", \"quarters\", \"months\", \"weeks\", or \"days\"\n   * @param {string} options.numberingSystem - override the numberingSystem of this DateTime. The Intl system may choose not to honor this\n   * @example DateTime.now().plus({ days: 1 }).toRelativeCalendar() //=> \"tomorrow\"\n   * @example DateTime.now().setLocale(\"es\").plus({ days: 1 }).toRelative() //=> \"\"mañana\"\n   * @example DateTime.now().plus({ days: 1 }).toRelativeCalendar({ locale: \"fr\" }) //=> \"demain\"\n   * @example DateTime.now().minus({ days: 2 }).toRelativeCalendar() //=> \"2 days ago\"\n   */\n  toRelativeCalendar(options = {}) {\n    if (!this.isValid) return null;\n\n    return diffRelative(options.base || DateTime.fromObject({}, { zone: this.zone }), this, {\n      ...options,\n      numeric: \"auto\",\n      units: [\"years\", \"months\", \"days\"],\n      calendary: true,\n    });\n  }\n\n  /**\n   * Return the min of several date times\n   * @param {...DateTime} dateTimes - the DateTimes from which to choose the minimum\n   * @return {DateTime} the min DateTime, or undefined if called with no argument\n   */\n  static min(...dateTimes) {\n    if (!dateTimes.every(DateTime.isDateTime)) {\n      throw new InvalidArgumentError(\"min requires all arguments be DateTimes\");\n    }\n    return bestBy(dateTimes, (i) => i.valueOf(), Math.min);\n  }\n\n  /**\n   * Return the max of several date times\n   * @param {...DateTime} dateTimes - the DateTimes from which to choose the maximum\n   * @return {DateTime} the max DateTime, or undefined if called with no argument\n   */\n  static max(...dateTimes) {\n    if (!dateTimes.every(DateTime.isDateTime)) {\n      throw new InvalidArgumentError(\"max requires all arguments be DateTimes\");\n    }\n    return bestBy(dateTimes, (i) => i.valueOf(), Math.max);\n  }\n\n  // MISC\n\n  /**\n   * Explain how a string would be parsed by fromFormat()\n   * @param {string} text - the string to parse\n   * @param {string} fmt - the format the string is expected to be in (see description)\n   * @param {Object} options - options taken by fromFormat()\n   * @return {Object}\n   */\n  static fromFormatExplain(text, fmt, options = {}) {\n    const { locale = null, numberingSystem = null } = options,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      });\n    return explainFromTokens(localeToUse, text, fmt);\n  }\n\n  /**\n   * @deprecated use fromFormatExplain instead\n   */\n  static fromStringExplain(text, fmt, options = {}) {\n    return DateTime.fromFormatExplain(text, fmt, options);\n  }\n\n  /**\n   * Build a parser for `fmt` using the given locale. This parser can be passed\n   * to {@link DateTime.fromFormatParser} to a parse a date in this format. This\n   * can be used to optimize cases where many dates need to be parsed in a\n   * specific format.\n   *\n   * @param {String} fmt - the format the string is expected to be in (see\n   * description)\n   * @param {Object} options - options used to set locale and numberingSystem\n   * for parser\n   * @returns {TokenParser} - opaque object to be used\n   */\n  static buildFormatParser(fmt, options = {}) {\n    const { locale = null, numberingSystem = null } = options,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      });\n    return new TokenParser(localeToUse, fmt);\n  }\n\n  /**\n   * Create a DateTime from an input string and format parser.\n   *\n   * The format parser must have been created with the same locale as this call.\n   *\n   * @param {String} text - the string to parse\n   * @param {TokenParser} formatParser - parser from {@link DateTime.buildFormatParser}\n   * @param {Object} opts - options taken by fromFormat()\n   * @returns {DateTime}\n   */\n  static fromFormatParser(text, formatParser, opts = {}) {\n    if (isUndefined(text) || isUndefined(formatParser)) {\n      throw new InvalidArgumentError(\n        \"fromFormatParser requires an input string and a format parser\"\n      );\n    }\n    const { locale = null, numberingSystem = null } = opts,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      });\n\n    if (!localeToUse.equals(formatParser.locale)) {\n      throw new InvalidArgumentError(\n        `fromFormatParser called with a locale of ${localeToUse}, ` +\n          `but the format parser was created for ${formatParser.locale}`\n      );\n    }\n\n    const { result, zone, specificOffset, invalidReason } = formatParser.explainFromTokens(text);\n\n    if (invalidReason) {\n      return DateTime.invalid(invalidReason);\n    } else {\n      return parseDataToDateTime(\n        result,\n        zone,\n        opts,\n        `format ${formatParser.format}`,\n        text,\n        specificOffset\n      );\n    }\n  }\n\n  // FORMAT PRESETS\n\n  /**\n   * {@link DateTime#toLocaleString} format like 10/14/1983\n   * @type {Object}\n   */\n  static get DATE_SHORT() {\n    return Formats.DATE_SHORT;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_MED() {\n    return Formats.DATE_MED;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Fri, Oct 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_MED_WITH_WEEKDAY() {\n    return Formats.DATE_MED_WITH_WEEKDAY;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_FULL() {\n    return Formats.DATE_FULL;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Tuesday, October 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_HUGE() {\n    return Formats.DATE_HUGE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_SIMPLE() {\n    return Formats.TIME_SIMPLE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_SECONDS() {\n    return Formats.TIME_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_SHORT_OFFSET() {\n    return Formats.TIME_WITH_SHORT_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_LONG_OFFSET() {\n    return Formats.TIME_WITH_LONG_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_SIMPLE() {\n    return Formats.TIME_24_SIMPLE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_SECONDS() {\n    return Formats.TIME_24_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 EDT', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_SHORT_OFFSET() {\n    return Formats.TIME_24_WITH_SHORT_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 Eastern Daylight Time', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_LONG_OFFSET() {\n    return Formats.TIME_24_WITH_LONG_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '10/14/1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_SHORT() {\n    return Formats.DATETIME_SHORT;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '10/14/1983, 9:30:33 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_SHORT_WITH_SECONDS() {\n    return Formats.DATETIME_SHORT_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED() {\n    return Formats.DATETIME_MED;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983, 9:30:33 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED_WITH_SECONDS() {\n    return Formats.DATETIME_MED_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Fri, 14 Oct 1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED_WITH_WEEKDAY() {\n    return Formats.DATETIME_MED_WITH_WEEKDAY;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983, 9:30 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_FULL() {\n    return Formats.DATETIME_FULL;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983, 9:30:33 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_FULL_WITH_SECONDS() {\n    return Formats.DATETIME_FULL_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Friday, October 14, 1983, 9:30 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_HUGE() {\n    return Formats.DATETIME_HUGE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Friday, October 14, 1983, 9:30:33 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_HUGE_WITH_SECONDS() {\n    return Formats.DATETIME_HUGE_WITH_SECONDS;\n  }\n}\n\n/**\n * @private\n */\nexport function friendlyDateTime(dateTimeish) {\n  if (DateTime.isDateTime(dateTimeish)) {\n    return dateTimeish;\n  } else if (dateTimeish && dateTimeish.valueOf && isNumber(dateTimeish.valueOf())) {\n    return DateTime.fromJSDate(dateTimeish);\n  } else if (dateTimeish && typeof dateTimeish === \"object\") {\n    return DateTime.fromObject(dateTimeish);\n  } else {\n    throw new InvalidArgumentError(\n      `Unknown datetime argument: ${dateTimeish}, of type ${typeof dateTimeish}`\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAeA;AACA;AACA;AACA;AAOA;AAYA;AACA;AAMA;;;;;;;;;;;;;;;;;AAEA,MAAM,UAAU;AAChB,MAAM,WAAW;AAEjB,SAAS,gBAAgB,IAAI;IAC3B,OAAO,IAAI,uJAAA,CAAA,UAAO,CAAC,oBAAoB,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC,kBAAkB,CAAC;AACnF;AAEA,uEAAuE;AACvE;;CAEC,GACD,SAAS,uBAAuB,EAAE;IAChC,IAAI,GAAG,QAAQ,KAAK,MAAM;QACxB,GAAG,QAAQ,GAAG,CAAA,GAAA,2JAAA,CAAA,kBAAe,AAAD,EAAE,GAAG,CAAC;IACpC;IACA,OAAO,GAAG,QAAQ;AACpB;AAEA;;CAEC,GACD,SAAS,4BAA4B,EAAE;IACrC,IAAI,GAAG,aAAa,KAAK,MAAM;QAC7B,GAAG,aAAa,GAAG,CAAA,GAAA,2JAAA,CAAA,kBAAe,AAAD,EAC/B,GAAG,CAAC,EACJ,GAAG,GAAG,CAAC,qBAAqB,IAC5B,GAAG,GAAG,CAAC,cAAc;IAEzB;IACA,OAAO,GAAG,aAAa;AACzB;AAEA,kGAAkG;AAClG,oEAAoE;AACpE,SAAS,MAAM,IAAI,EAAE,IAAI;IACvB,MAAM,UAAU;QACd,IAAI,KAAK,EAAE;QACX,MAAM,KAAK,IAAI;QACf,GAAG,KAAK,CAAC;QACT,GAAG,KAAK,CAAC;QACT,KAAK,KAAK,GAAG;QACb,SAAS,KAAK,OAAO;IACvB;IACA,OAAO,IAAI,SAAS;QAAE,GAAG,OAAO;QAAE,GAAG,IAAI;QAAE,KAAK;IAAQ;AAC1D;AAEA,6FAA6F;AAC7F,mFAAmF;AACnF,SAAS,UAAU,OAAO,EAAE,CAAC,EAAE,EAAE;IAC/B,kEAAkE;IAClE,IAAI,WAAW,UAAU,IAAI,KAAK;IAElC,uDAAuD;IACvD,MAAM,KAAK,GAAG,MAAM,CAAC;IAErB,6CAA6C;IAC7C,IAAI,MAAM,IAAI;QACZ,OAAO;YAAC;YAAU;SAAE;IACtB;IAEA,wDAAwD;IACxD,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK;IAE5B,sDAAsD;IACtD,MAAM,KAAK,GAAG,MAAM,CAAC;IACrB,IAAI,OAAO,IAAI;QACb,OAAO;YAAC;YAAU;SAAG;IACvB;IAEA,oGAAoG;IACpG,OAAO;QAAC,UAAU,KAAK,GAAG,CAAC,IAAI,MAAM,KAAK;QAAM,KAAK,GAAG,CAAC,IAAI;KAAI;AACnE;AAEA,0EAA0E;AAC1E,SAAS,QAAQ,EAAE,EAAE,MAAM;IACzB,MAAM,SAAS,KAAK;IAEpB,MAAM,IAAI,IAAI,KAAK;IAEnB,OAAO;QACL,MAAM,EAAE,cAAc;QACtB,OAAO,EAAE,WAAW,KAAK;QACzB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,WAAW;QACnB,QAAQ,EAAE,aAAa;QACvB,QAAQ,EAAE,aAAa;QACvB,aAAa,EAAE,kBAAkB;IACnC;AACF;AAEA,iDAAiD;AACjD,SAAS,QAAQ,GAAG,EAAE,MAAM,EAAE,IAAI;IAChC,OAAO,UAAU,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,MAAM,QAAQ;AAC9C;AAEA,oEAAoE;AACpE,SAAS,WAAW,IAAI,EAAE,GAAG;IAC3B,MAAM,OAAO,KAAK,CAAC,EACjB,OAAO,KAAK,CAAC,CAAC,IAAI,GAAG,KAAK,KAAK,CAAC,IAAI,KAAK,GACzC,QAAQ,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,KAAK,CAAC,IAAI,MAAM,IAAI,KAAK,KAAK,CAAC,IAAI,QAAQ,IAAI,GAC3E,IAAI;QACF,GAAG,KAAK,CAAC;QACT;QACA;QACA,KACE,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,MAAM,UACvC,KAAK,KAAK,CAAC,IAAI,IAAI,IACnB,KAAK,KAAK,CAAC,IAAI,KAAK,IAAI;IAC5B,GACA,cAAc,gJAAA,CAAA,UAAQ,CAAC,UAAU,CAAC;QAChC,OAAO,IAAI,KAAK,GAAG,KAAK,KAAK,CAAC,IAAI,KAAK;QACvC,UAAU,IAAI,QAAQ,GAAG,KAAK,KAAK,CAAC,IAAI,QAAQ;QAChD,QAAQ,IAAI,MAAM,GAAG,KAAK,KAAK,CAAC,IAAI,MAAM;QAC1C,OAAO,IAAI,KAAK,GAAG,KAAK,KAAK,CAAC,IAAI,KAAK;QACvC,MAAM,IAAI,IAAI,GAAG,KAAK,KAAK,CAAC,IAAI,IAAI;QACpC,OAAO,IAAI,KAAK;QAChB,SAAS,IAAI,OAAO;QACpB,SAAS,IAAI,OAAO;QACpB,cAAc,IAAI,YAAY;IAChC,GAAG,EAAE,CAAC,iBACN,UAAU,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE;IAEzB,IAAI,CAAC,IAAI,EAAE,GAAG,UAAU,SAAS,MAAM,KAAK,IAAI;IAEhD,IAAI,gBAAgB,GAAG;QACrB,MAAM;QACN,8FAA8F;QAC9F,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC;IACvB;IAEA,OAAO;QAAE;QAAI;IAAE;AACjB;AAEA,kEAAkE;AAClE,+BAA+B;AAC/B,SAAS,oBAAoB,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,cAAc;IACjF,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG;IAC1B,IAAI,AAAC,UAAU,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK,KAAM,YAAY;QAC9D,MAAM,qBAAqB,cAAc,MACvC,OAAO,SAAS,UAAU,CAAC,QAAQ;YACjC,GAAG,IAAI;YACP,MAAM;YACN;QACF;QACF,OAAO,UAAU,OAAO,KAAK,OAAO,CAAC;IACvC,OAAO;QACL,OAAO,SAAS,OAAO,CACrB,IAAI,uJAAA,CAAA,UAAO,CAAC,cAAc,CAAC,WAAW,EAAE,KAAK,qBAAqB,EAAE,QAAQ;IAEhF;AACF;AAEA,wEAAwE;AACxE,2BAA2B;AAC3B,SAAS,aAAa,EAAE,EAAE,MAAM,EAAE,SAAS,IAAI;IAC7C,OAAO,GAAG,OAAO,GACb,yJAAA,CAAA,UAAS,CAAC,MAAM,CAAC,sJAAA,CAAA,UAAM,CAAC,MAAM,CAAC,UAAU;QACvC;QACA,aAAa;IACf,GAAG,wBAAwB,CAAC,IAAI,UAChC;AACN;AAEA,SAAS,UAAU,CAAC,EAAE,QAAQ;IAC5B,MAAM,aAAa,EAAE,CAAC,CAAC,IAAI,GAAG,QAAQ,EAAE,CAAC,CAAC,IAAI,GAAG;IACjD,IAAI,IAAI;IACR,IAAI,cAAc,EAAE,CAAC,CAAC,IAAI,IAAI,GAAG,KAAK;IACtC,KAAK,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,aAAa,IAAI;IAEzC,IAAI,UAAU;QACZ,KAAK;QACL,KAAK,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,EAAE,CAAC,CAAC,KAAK;QACvB,KAAK;QACL,KAAK,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,EAAE,CAAC,CAAC,GAAG;IACvB,OAAO;QACL,KAAK,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,EAAE,CAAC,CAAC,KAAK;QACvB,KAAK,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,EAAE,CAAC,CAAC,GAAG;IACvB;IACA,OAAO;AACT;AAEA,SAAS,UACP,CAAC,EACD,QAAQ,EACR,eAAe,EACf,oBAAoB,EACpB,aAAa,EACb,YAAY;IAEZ,IAAI,IAAI,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,EAAE,CAAC,CAAC,IAAI;IACzB,IAAI,UAAU;QACZ,KAAK;QACL,KAAK,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,EAAE,CAAC,CAAC,MAAM;QACxB,IAAI,EAAE,CAAC,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC,CAAC,MAAM,KAAK,KAAK,CAAC,iBAAiB;YACjE,KAAK;QACP;IACF,OAAO;QACL,KAAK,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,EAAE,CAAC,CAAC,MAAM;IAC1B;IAEA,IAAI,EAAE,CAAC,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC,CAAC,MAAM,KAAK,KAAK,CAAC,iBAAiB;QACjE,KAAK,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,EAAE,CAAC,CAAC,MAAM;QAExB,IAAI,EAAE,CAAC,CAAC,WAAW,KAAK,KAAK,CAAC,sBAAsB;YAClD,KAAK;YACL,KAAK,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,EAAE,CAAC,CAAC,WAAW,EAAE;QACjC;IACF;IAEA,IAAI,eAAe;QACjB,IAAI,EAAE,aAAa,IAAI,EAAE,MAAM,KAAK,KAAK,CAAC,cAAc;YACtD,KAAK;QACP,OAAO,IAAI,EAAE,CAAC,GAAG,GAAG;YAClB,KAAK;YACL,KAAK,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG;YAChC,KAAK;YACL,KAAK,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG;QAClC,OAAO;YACL,KAAK;YACL,KAAK,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC,GAAG;YAC/B,KAAK;YACL,KAAK,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC,GAAG;QACjC;IACF;IAEA,IAAI,cAAc;QAChB,KAAK,MAAM,EAAE,IAAI,CAAC,QAAQ,GAAG;IAC/B;IACA,OAAO;AACT;AAEA,4DAA4D;AAC5D,MAAM,oBAAoB;IACtB,OAAO;IACP,KAAK;IACL,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,aAAa;AACf,GACA,wBAAwB;IACtB,YAAY;IACZ,SAAS;IACT,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,aAAa;AACf,GACA,2BAA2B;IACzB,SAAS;IACT,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,aAAa;AACf;AAEF,sDAAsD;AACtD,MAAM,eAAe;IAAC;IAAQ;IAAS;IAAO;IAAQ;IAAU;IAAU;CAAc,EACtF,mBAAmB;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;CACD,EACD,sBAAsB;IAAC;IAAQ;IAAW;IAAQ;IAAU;IAAU;CAAc;AAEtF,0CAA0C;AAC1C,SAAS,cAAc,IAAI;IACzB,MAAM,aAAa;QACjB,MAAM;QACN,OAAO;QACP,OAAO;QACP,QAAQ;QACR,KAAK;QACL,MAAM;QACN,MAAM;QACN,OAAO;QACP,QAAQ;QACR,SAAS;QACT,SAAS;QACT,UAAU;QACV,QAAQ;QACR,SAAS;QACT,aAAa;QACb,cAAc;QACd,SAAS;QACT,UAAU;QACV,YAAY;QACZ,aAAa;QACb,aAAa;QACb,UAAU;QACV,WAAW;QACX,SAAS;IACX,CAAC,CAAC,KAAK,WAAW,GAAG;IAErB,IAAI,CAAC,YAAY,MAAM,IAAI,8IAAA,CAAA,mBAAgB,CAAC;IAE5C,OAAO;AACT;AAEA,SAAS,4BAA4B,IAAI;IACvC,OAAQ,KAAK,WAAW;QACtB,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT;YACE,OAAO,cAAc;IACzB;AACF;AAEA,+EAA+E;AAC/E,4EAA4E;AAC5E,4EAA4E;AAC5E,gFAAgF;AAChF,8EAA8E;AAC9E,8EAA8E;AAC9E,mDAAmD;AACnD,EAAE;AACF,yEAAyE;AACzE,6EAA6E;AAC7E,uEAAuE;AACvE,6EAA6E;AAC7E,8EAA8E;AAC9E,gFAAgF;AAChF,wEAAwE;AACxE,EAAE;AACF,gFAAgF;AAChF,4EAA4E;AAC5E,oCAAoC;AACpC;;;CAGC,GACD,SAAS,mBAAmB,IAAI;IAC9B,IAAI,iBAAiB,WAAW;QAC9B,eAAe,gJAAA,CAAA,UAAQ,CAAC,GAAG;IAC7B;IAEA,yEAAyE;IACzE,6FAA6F;IAC7F,IAAI,KAAK,IAAI,KAAK,QAAQ;QACxB,OAAO,KAAK,MAAM,CAAC;IACrB;IACA,MAAM,WAAW,KAAK,IAAI;IAC1B,IAAI,cAAc,qBAAqB,GAAG,CAAC;IAC3C,IAAI,gBAAgB,WAAW;QAC7B,cAAc,KAAK,MAAM,CAAC;QAC1B,qBAAqB,GAAG,CAAC,UAAU;IACrC;IACA,OAAO;AACT;AAEA,2EAA2E;AAC3E,+EAA+E;AAC/E,0BAA0B;AAC1B,SAAS,QAAQ,GAAG,EAAE,IAAI;IACxB,MAAM,OAAO,CAAA,GAAA,wJAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,IAAI,EAAE,gJAAA,CAAA,UAAQ,CAAC,WAAW;IAC1D,IAAI,CAAC,KAAK,OAAO,EAAE;QACjB,OAAO,SAAS,OAAO,CAAC,gBAAgB;IAC1C;IAEA,MAAM,MAAM,sJAAA,CAAA,UAAM,CAAC,UAAU,CAAC;IAE9B,IAAI,IAAI;IAER,wCAAwC;IACxC,IAAI,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,IAAI,IAAI,GAAG;QAC1B,KAAK,MAAM,KAAK,aAAc;YAC5B,IAAI,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,GAAG,CAAC,EAAE,GAAG;gBACvB,GAAG,CAAC,EAAE,GAAG,iBAAiB,CAAC,EAAE;YAC/B;QACF;QAEA,MAAM,UAAU,CAAA,GAAA,2JAAA,CAAA,0BAAuB,AAAD,EAAE,QAAQ,CAAA,GAAA,2JAAA,CAAA,qBAAkB,AAAD,EAAE;QACnE,IAAI,SAAS;YACX,OAAO,SAAS,OAAO,CAAC;QAC1B;QAEA,MAAM,eAAe,mBAAmB;QACxC,CAAC,IAAI,EAAE,GAAG,QAAQ,KAAK,cAAc;IACvC,OAAO;QACL,KAAK,gJAAA,CAAA,UAAQ,CAAC,GAAG;IACnB;IAEA,OAAO,IAAI,SAAS;QAAE;QAAI;QAAM;QAAK;IAAE;AACzC;AAEA,SAAS,aAAa,KAAK,EAAE,GAAG,EAAE,IAAI;IACpC,MAAM,QAAQ,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,EACvD,SAAS,CAAC,GAAG;QACX,IAAI,CAAA,GAAA,oJAAA,CAAA,UAAO,AAAD,EAAE,GAAG,SAAS,KAAK,SAAS,GAAG,IAAI,GAAG;QAChD,MAAM,YAAY,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,YAAY,CAAC;QACnD,OAAO,UAAU,MAAM,CAAC,GAAG;IAC7B,GACA,SAAS,CAAC;QACR,IAAI,KAAK,SAAS,EAAE;YAClB,IAAI,CAAC,IAAI,OAAO,CAAC,OAAO,OAAO;gBAC7B,OAAO,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO,MAAM,GAAG,CAAC;YAC/D,OAAO,OAAO;QAChB,OAAO;YACL,OAAO,IAAI,IAAI,CAAC,OAAO,MAAM,GAAG,CAAC;QACnC;IACF;IAEF,IAAI,KAAK,IAAI,EAAE;QACb,OAAO,OAAO,OAAO,KAAK,IAAI,GAAG,KAAK,IAAI;IAC5C;IAEA,KAAK,MAAM,QAAQ,KAAK,KAAK,CAAE;QAC7B,MAAM,QAAQ,OAAO;QACrB,IAAI,KAAK,GAAG,CAAC,UAAU,GAAG;YACxB,OAAO,OAAO,OAAO;QACvB;IACF;IACA,OAAO,OAAO,QAAQ,MAAM,CAAC,IAAI,GAAG,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,MAAM,GAAG,EAAE;AACvE;AAEA,SAAS,SAAS,OAAO;IACvB,IAAI,OAAO,CAAC,GACV;IACF,IAAI,QAAQ,MAAM,GAAG,KAAK,OAAO,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE,KAAK,UAAU;QACzE,OAAO,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;QAClC,OAAO,MAAM,IAAI,CAAC,SAAS,KAAK,CAAC,GAAG,QAAQ,MAAM,GAAG;IACvD,OAAO;QACL,OAAO,MAAM,IAAI,CAAC;IACpB;IACA,OAAO;QAAC;QAAM;KAAK;AACrB;AAEA;;CAEC,GACD,IAAI;AACJ;;;;;CAKC,GACD,MAAM,uBAAuB,IAAI;AAsBlB,MAAM;IACnB;;GAEC,GACD,YAAY,MAAM,CAAE;QAClB,MAAM,OAAO,OAAO,IAAI,IAAI,gJAAA,CAAA,UAAQ,CAAC,WAAW;QAEhD,IAAI,UACF,OAAO,OAAO,IACd,CAAC,OAAO,KAAK,CAAC,OAAO,EAAE,IAAI,IAAI,uJAAA,CAAA,UAAO,CAAC,mBAAmB,IAAI,KAC9D,CAAC,CAAC,KAAK,OAAO,GAAG,gBAAgB,QAAQ,IAAI;QAC/C;;KAEC,GACD,IAAI,CAAC,EAAE,GAAG,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,OAAO,EAAE,IAAI,gJAAA,CAAA,UAAQ,CAAC,GAAG,KAAK,OAAO,EAAE;QAE7D,IAAI,IAAI,MACN,IAAI;QACN,IAAI,CAAC,SAAS;YACZ,MAAM,YAAY,OAAO,GAAG,IAAI,OAAO,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,IAAI,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;YAEpF,IAAI,WAAW;gBACb,CAAC,GAAG,EAAE,GAAG;oBAAC,OAAO,GAAG,CAAC,CAAC;oBAAE,OAAO,GAAG,CAAC,CAAC;iBAAC;YACvC,OAAO;gBACL,gEAAgE;gBAChE,6DAA6D;gBAC7D,MAAM,KAAK,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,GAAG,KAAK,MAAM,CAAC,IAAI,CAAC,EAAE;gBAC7E,IAAI,QAAQ,IAAI,CAAC,EAAE,EAAE;gBACrB,UAAU,OAAO,KAAK,CAAC,EAAE,IAAI,IAAI,IAAI,uJAAA,CAAA,UAAO,CAAC,mBAAmB;gBAChE,IAAI,UAAU,OAAO;gBACrB,IAAI,UAAU,OAAO;YACvB;QACF;QAEA;;KAEC,GACD,IAAI,CAAC,KAAK,GAAG;QACb;;KAEC,GACD,IAAI,CAAC,GAAG,GAAG,OAAO,GAAG,IAAI,sJAAA,CAAA,UAAM,CAAC,MAAM;QACtC;;KAEC,GACD,IAAI,CAAC,OAAO,GAAG;QACf;;KAEC,GACD,IAAI,CAAC,QAAQ,GAAG;QAChB;;KAEC,GACD,IAAI,CAAC,aAAa,GAAG;QACrB;;KAEC,GACD,IAAI,CAAC,CAAC,GAAG;QACT;;KAEC,GACD,IAAI,CAAC,CAAC,GAAG;QACT;;KAEC,GACD,IAAI,CAAC,eAAe,GAAG;IACzB;IAEA,YAAY;IAEZ;;;;;;GAMC,GACD,OAAO,MAAM;QACX,OAAO,IAAI,SAAS,CAAC;IACvB;IAEA;;;;;;;;;;;;;;;;;;;;GAoBC,GACD,OAAO,QAAQ;QACb,MAAM,CAAC,MAAM,KAAK,GAAG,SAAS,YAC5B,CAAC,MAAM,OAAO,KAAK,MAAM,QAAQ,QAAQ,YAAY,GAAG;QAC1D,OAAO,QAAQ;YAAE;YAAM;YAAO;YAAK;YAAM;YAAQ;YAAQ;QAAY,GAAG;IAC1E;IAEA;;;;;;;;;;;;;;;;;;;;;;;;GAwBC,GACD,OAAO,MAAM;QACX,MAAM,CAAC,MAAM,KAAK,GAAG,SAAS,YAC5B,CAAC,MAAM,OAAO,KAAK,MAAM,QAAQ,QAAQ,YAAY,GAAG;QAE1D,KAAK,IAAI,GAAG,gKAAA,CAAA,UAAe,CAAC,WAAW;QACvC,OAAO,QAAQ;YAAE;YAAM;YAAO;YAAK;YAAM;YAAQ;YAAQ;QAAY,GAAG;IAC1E;IAEA;;;;;;GAMC,GACD,OAAO,WAAW,IAAI,EAAE,UAAU,CAAC,CAAC,EAAE;QACpC,MAAM,KAAK,CAAA,GAAA,oJAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,KAAK,OAAO,KAAK;QAC3C,IAAI,OAAO,KAAK,CAAC,KAAK;YACpB,OAAO,SAAS,OAAO,CAAC;QAC1B;QAEA,MAAM,YAAY,CAAA,GAAA,wJAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,IAAI,EAAE,gJAAA,CAAA,UAAQ,CAAC,WAAW;QAClE,IAAI,CAAC,UAAU,OAAO,EAAE;YACtB,OAAO,SAAS,OAAO,CAAC,gBAAgB;QAC1C;QAEA,OAAO,IAAI,SAAS;YAClB,IAAI;YACJ,MAAM;YACN,KAAK,sJAAA,CAAA,UAAM,CAAC,UAAU,CAAC;QACzB;IACF;IAEA;;;;;;;;;;GAUC,GACD,OAAO,WAAW,YAAY,EAAE,UAAU,CAAC,CAAC,EAAE;QAC5C,IAAI,CAAC,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,eAAe;YAC3B,MAAM,IAAI,8IAAA,CAAA,uBAAoB,CAC5B,CAAC,sDAAsD,EAAE,OAAO,aAAa,YAAY,EAAE,cAAc;QAE7G,OAAO,IAAI,eAAe,CAAC,YAAY,eAAe,UAAU;YAC9D,+GAA+G;YAC/G,OAAO,SAAS,OAAO,CAAC;QAC1B,OAAO;YACL,OAAO,IAAI,SAAS;gBAClB,IAAI;gBACJ,MAAM,CAAA,GAAA,wJAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,IAAI,EAAE,gJAAA,CAAA,UAAQ,CAAC,WAAW;gBACtD,KAAK,sJAAA,CAAA,UAAM,CAAC,UAAU,CAAC;YACzB;QACF;IACF;IAEA;;;;;;;;;;GAUC,GACD,OAAO,YAAY,OAAO,EAAE,UAAU,CAAC,CAAC,EAAE;QACxC,IAAI,CAAC,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,UAAU;YACtB,MAAM,IAAI,8IAAA,CAAA,uBAAoB,CAAC;QACjC,OAAO;YACL,OAAO,IAAI,SAAS;gBAClB,IAAI,UAAU;gBACd,MAAM,CAAA,GAAA,wJAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,IAAI,EAAE,gJAAA,CAAA,UAAQ,CAAC,WAAW;gBACtD,KAAK,sJAAA,CAAA,UAAM,CAAC,UAAU,CAAC;YACzB;QACF;IACF;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgCC,GACD,OAAO,WAAW,GAAG,EAAE,OAAO,CAAC,CAAC,EAAE;QAChC,MAAM,OAAO,CAAC;QACd,MAAM,YAAY,CAAA,GAAA,wJAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,IAAI,EAAE,gJAAA,CAAA,UAAQ,CAAC,WAAW;QAC/D,IAAI,CAAC,UAAU,OAAO,EAAE;YACtB,OAAO,SAAS,OAAO,CAAC,gBAAgB;QAC1C;QAEA,MAAM,MAAM,sJAAA,CAAA,UAAM,CAAC,UAAU,CAAC;QAC9B,MAAM,aAAa,CAAA,GAAA,oJAAA,CAAA,kBAAe,AAAD,EAAE,KAAK;QACxC,MAAM,EAAE,kBAAkB,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,sBAAmB,AAAD,EAAE,YAAY;QAE5E,MAAM,QAAQ,gJAAA,CAAA,UAAQ,CAAC,GAAG,IACxB,eAAe,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,KAAK,cAAc,IAC3C,KAAK,cAAc,GACnB,UAAU,MAAM,CAAC,QACrB,kBAAkB,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,WAAW,OAAO,GACjD,qBAAqB,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,WAAW,IAAI,GACjD,mBAAmB,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,WAAW,KAAK,KAAK,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,WAAW,GAAG,GAChF,iBAAiB,sBAAsB,kBACvC,kBAAkB,WAAW,QAAQ,IAAI,WAAW,UAAU;QAEhE,SAAS;QACT,qEAAqE;QACrE,kEAAkE;QAClE,8CAA8C;QAC9C,mFAAmF;QAEnF,IAAI,CAAC,kBAAkB,eAAe,KAAK,iBAAiB;YAC1D,MAAM,IAAI,8IAAA,CAAA,gCAA6B,CACrC;QAEJ;QAEA,IAAI,oBAAoB,iBAAiB;YACvC,MAAM,IAAI,8IAAA,CAAA,gCAA6B,CAAC;QAC1C;QAEA,MAAM,cAAc,mBAAoB,WAAW,OAAO,IAAI,CAAC;QAE/D,iEAAiE;QACjE,IAAI,OACF,eACA,SAAS,QAAQ,OAAO;QAC1B,IAAI,aAAa;YACf,QAAQ;YACR,gBAAgB;YAChB,SAAS,CAAA,GAAA,2JAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,oBAAoB;QACvD,OAAO,IAAI,iBAAiB;YAC1B,QAAQ;YACR,gBAAgB;YAChB,SAAS,CAAA,GAAA,2JAAA,CAAA,qBAAkB,AAAD,EAAE;QAC9B,OAAO;YACL,QAAQ;YACR,gBAAgB;QAClB;QAEA,uCAAuC;QACvC,IAAI,aAAa;QACjB,KAAK,MAAM,KAAK,MAAO;YACrB,MAAM,IAAI,UAAU,CAAC,EAAE;YACvB,IAAI,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,IAAI;gBACnB,aAAa;YACf,OAAO,IAAI,YAAY;gBACrB,UAAU,CAAC,EAAE,GAAG,aAAa,CAAC,EAAE;YAClC,OAAO;gBACL,UAAU,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE;YAC3B;QACF;QAEA,4CAA4C;QAC5C,MAAM,qBAAqB,cACrB,CAAA,GAAA,2JAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,oBAAoB,eACnD,kBACA,CAAA,GAAA,2JAAA,CAAA,wBAAqB,AAAD,EAAE,cACtB,CAAA,GAAA,2JAAA,CAAA,0BAAuB,AAAD,EAAE,aAC5B,UAAU,sBAAsB,CAAA,GAAA,2JAAA,CAAA,qBAAkB,AAAD,EAAE;QAErD,IAAI,SAAS;YACX,OAAO,SAAS,OAAO,CAAC;QAC1B;QAEA,0BAA0B;QAC1B,MAAM,YAAY,cACZ,CAAA,GAAA,2JAAA,CAAA,kBAAe,AAAD,EAAE,YAAY,oBAAoB,eAChD,kBACA,CAAA,GAAA,2JAAA,CAAA,qBAAkB,AAAD,EAAE,cACnB,YACJ,CAAC,SAAS,YAAY,GAAG,QAAQ,WAAW,cAAc,YAC1D,OAAO,IAAI,SAAS;YAClB,IAAI;YACJ,MAAM;YACN,GAAG;YACH;QACF;QAEF,mDAAmD;QACnD,IAAI,WAAW,OAAO,IAAI,kBAAkB,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE;YACxE,OAAO,SAAS,OAAO,CACrB,sBACA,CAAC,oCAAoC,EAAE,WAAW,OAAO,CAAC,eAAe,EAAE,KAAK,KAAK,IAAI;QAE7F;QAEA,IAAI,CAAC,KAAK,OAAO,EAAE;YACjB,OAAO,SAAS,OAAO,CAAC,KAAK,OAAO;QACtC;QAEA,OAAO;IACT;IAEA;;;;;;;;;;;;;;;;GAgBC,GACD,OAAO,QAAQ,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE;QAC9B,MAAM,CAAC,MAAM,WAAW,GAAG,CAAA,GAAA,2JAAA,CAAA,eAAY,AAAD,EAAE;QACxC,OAAO,oBAAoB,MAAM,YAAY,MAAM,YAAY;IACjE;IAEA;;;;;;;;;;;;;;GAcC,GACD,OAAO,YAAY,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE;QAClC,MAAM,CAAC,MAAM,WAAW,GAAG,CAAA,GAAA,2JAAA,CAAA,mBAAgB,AAAD,EAAE;QAC5C,OAAO,oBAAoB,MAAM,YAAY,MAAM,YAAY;IACjE;IAEA;;;;;;;;;;;;;;;GAeC,GACD,OAAO,SAAS,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE;QAC/B,MAAM,CAAC,MAAM,WAAW,GAAG,CAAA,GAAA,2JAAA,CAAA,gBAAa,AAAD,EAAE;QACzC,OAAO,oBAAoB,MAAM,YAAY,MAAM,QAAQ;IAC7D;IAEA;;;;;;;;;;;;;GAaC,GACD,OAAO,WAAW,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC,EAAE;QACtC,IAAI,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,SAAS,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,MAAM;YACzC,MAAM,IAAI,8IAAA,CAAA,uBAAoB,CAAC;QACjC;QAEA,MAAM,EAAE,SAAS,IAAI,EAAE,kBAAkB,IAAI,EAAE,GAAG,MAChD,cAAc,sJAAA,CAAA,UAAM,CAAC,QAAQ,CAAC;YAC5B;YACA;YACA,aAAa;QACf,IACA,CAAC,MAAM,YAAY,gBAAgB,QAAQ,GAAG,CAAA,GAAA,2JAAA,CAAA,kBAAe,AAAD,EAAE,aAAa,MAAM;QACnF,IAAI,SAAS;YACX,OAAO,SAAS,OAAO,CAAC;QAC1B,OAAO;YACL,OAAO,oBAAoB,MAAM,YAAY,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM;QAC5E;IACF;IAEA;;GAEC,GACD,OAAO,WAAW,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC,EAAE;QACtC,OAAO,SAAS,UAAU,CAAC,MAAM,KAAK;IACxC;IAEA;;;;;;;;;;;;;;;;;;;;GAoBC,GACD,OAAO,QAAQ,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE;QAC9B,MAAM,CAAC,MAAM,WAAW,GAAG,CAAA,GAAA,2JAAA,CAAA,WAAQ,AAAD,EAAE;QACpC,OAAO,oBAAoB,MAAM,YAAY,MAAM,OAAO;IAC5D;IAEA;;;;;GAKC,GACD,OAAO,QAAQ,MAAM,EAAE,cAAc,IAAI,EAAE;QACzC,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,8IAAA,CAAA,uBAAoB,CAAC;QACjC;QAEA,MAAM,UAAU,kBAAkB,uJAAA,CAAA,UAAO,GAAG,SAAS,IAAI,uJAAA,CAAA,UAAO,CAAC,QAAQ;QAEzE,IAAI,gJAAA,CAAA,UAAQ,CAAC,cAAc,EAAE;YAC3B,MAAM,IAAI,8IAAA,CAAA,uBAAoB,CAAC;QACjC,OAAO;YACL,OAAO,IAAI,SAAS;gBAAE;YAAQ;QAChC;IACF;IAEA;;;;GAIC,GACD,OAAO,WAAW,CAAC,EAAE;QACnB,OAAO,AAAC,KAAK,EAAE,eAAe,IAAK;IACrC;IAEA;;;;;GAKC,GACD,OAAO,mBAAmB,UAAU,EAAE,aAAa,CAAC,CAAC,EAAE;QACrD,MAAM,YAAY,CAAA,GAAA,2JAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,sJAAA,CAAA,UAAM,CAAC,UAAU,CAAC;QACnE,OAAO,CAAC,YAAY,OAAO,UAAU,GAAG,CAAC,CAAC,IAAO,IAAI,EAAE,GAAG,GAAG,MAAO,IAAI,CAAC;IAC3E;IAEA;;;;;;GAMC,GACD,OAAO,aAAa,GAAG,EAAE,aAAa,CAAC,CAAC,EAAE;QACxC,MAAM,WAAW,CAAA,GAAA,2JAAA,CAAA,oBAAiB,AAAD,EAAE,yJAAA,CAAA,UAAS,CAAC,WAAW,CAAC,MAAM,sJAAA,CAAA,UAAM,CAAC,UAAU,CAAC;QACjF,OAAO,SAAS,GAAG,CAAC,CAAC,IAAM,EAAE,GAAG,EAAE,IAAI,CAAC;IACzC;IAEA,OAAO,aAAa;QAClB,eAAe;QACf,qBAAqB,KAAK;IAC5B;IAEA,OAAO;IAEP;;;;;;GAMC,GACD,IAAI,IAAI,EAAE;QACR,OAAO,IAAI,CAAC,KAAK;IACnB;IAEA;;;;;GAKC,GACD,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,OAAO,KAAK;IAC1B;IAEA;;;GAGC,GACD,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG;IAC9C;IAEA;;;GAGC,GACD,IAAI,qBAAqB;QACvB,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG;IACnD;IAEA;;;;GAIC,GACD,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG;IAC1C;IAEA;;;;GAIC,GACD,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,GAAG;IACnD;IAEA;;;;GAIC,GACD,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG;IAClD;IAEA;;;GAGC,GACD,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,KAAK;IACnB;IAEA;;;GAGC,GACD,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG;IACzC;IAEA;;;;GAIC,GACD,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG;IACtC;IAEA;;;;GAIC,GACD,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,OAAO,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK;IACtD;IAEA;;;;GAIC,GACD,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG;IACvC;IAEA;;;;GAIC,GACD,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG;IACrC;IAEA;;;;GAIC,GACD,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG;IACtC;IAEA;;;;GAIC,GACD,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG;IACxC;IAEA;;;;GAIC,GACD,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG;IACxC;IAEA;;;;GAIC,GACD,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,WAAW,GAAG;IAC7C;IAEA;;;;;GAKC,GACD,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,OAAO,GAAG,uBAAuB,IAAI,EAAE,QAAQ,GAAG;IAChE;IAEA;;;;;GAKC,GACD,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,OAAO,GAAG,uBAAuB,IAAI,EAAE,UAAU,GAAG;IAClE;IAEA;;;;;;GAMC,GACD,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,OAAO,GAAG,uBAAuB,IAAI,EAAE,OAAO,GAAG;IAC/D;IAEA;;;GAGC,GACD,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO;IACxE;IAEA;;;;;GAKC,GACD,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,OAAO,GAAG,4BAA4B,IAAI,EAAE,OAAO,GAAG;IACpE;IAEA;;;;;GAKC,GACD,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,OAAO,GAAG,4BAA4B,IAAI,EAAE,UAAU,GAAG;IACvE;IAEA;;;;GAIC,GACD,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,OAAO,GAAG,4BAA4B,IAAI,EAAE,QAAQ,GAAG;IACrE;IAEA;;;;GAIC,GACD,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,OAAO,GAAG,CAAA,GAAA,2JAAA,CAAA,qBAAkB,AAAD,EAAE,IAAI,CAAC,CAAC,EAAE,OAAO,GAAG;IAC7D;IAEA;;;;;GAKC,GACD,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,OAAO,GAAG,4IAAA,CAAA,UAAI,CAAC,MAAM,CAAC,SAAS;YAAE,QAAQ,IAAI,CAAC,GAAG;QAAC,EAAE,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,GAAG;IACrF;IAEA;;;;;GAKC,GACD,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,OAAO,GAAG,4IAAA,CAAA,UAAI,CAAC,MAAM,CAAC,QAAQ;YAAE,QAAQ,IAAI,CAAC,GAAG;QAAC,EAAE,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,GAAG;IACpF;IAEA;;;;;GAKC,GACD,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,OAAO,GAAG,4IAAA,CAAA,UAAI,CAAC,QAAQ,CAAC,SAAS;YAAE,QAAQ,IAAI,CAAC,GAAG;QAAC,EAAE,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG;IACzF;IAEA;;;;;GAKC,GACD,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,OAAO,GAAG,4IAAA,CAAA,UAAI,CAAC,QAAQ,CAAC,QAAQ;YAAE,QAAQ,IAAI,CAAC,GAAG;QAAC,EAAE,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG;IACxF;IAEA;;;;;GAKC,GACD,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG;IAClC;IAEA;;;;GAIC,GACD,IAAI,kBAAkB;QACpB,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,EAAE;gBACnC,QAAQ;gBACR,QAAQ,IAAI,CAAC,MAAM;YACrB;QACF,OAAO;YACL,OAAO;QACT;IACF;IAEA;;;;GAIC,GACD,IAAI,iBAAiB;QACnB,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,EAAE;gBACnC,QAAQ;gBACR,QAAQ,IAAI,CAAC,MAAM;YACrB;QACF,OAAO;YACL,OAAO;QACT;IACF;IAEA;;;GAGC,GACD,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG;IAChD;IAEA;;;GAGC,GACD,IAAI,UAAU;QACZ,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,OAAO;QACT,OAAO;YACL,OACE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC;gBAAE,OAAO;gBAAG,KAAK;YAAE,GAAG,MAAM,IACnD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC;gBAAE,OAAO;YAAE,GAAG,MAAM;QAE/C;IACF;IAEA;;;;;;GAMC,GACD,qBAAqB;QACnB,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,aAAa,EAAE;YACvC,OAAO;gBAAC,IAAI;aAAC;QACf;QACA,MAAM,QAAQ;QACd,MAAM,WAAW;QACjB,MAAM,UAAU,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,IAAI,CAAC,CAAC;QACnC,MAAM,WAAW,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU;QAC5C,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU;QAE1C,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,WAAW;QACjD,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,SAAS;QAC/C,IAAI,OAAO,IAAI;YACb,OAAO;gBAAC,IAAI;aAAC;QACf;QACA,MAAM,MAAM,UAAU,KAAK;QAC3B,MAAM,MAAM,UAAU,KAAK;QAC3B,MAAM,KAAK,QAAQ,KAAK;QACxB,MAAM,KAAK,QAAQ,KAAK;QACxB,IACE,GAAG,IAAI,KAAK,GAAG,IAAI,IACnB,GAAG,MAAM,KAAK,GAAG,MAAM,IACvB,GAAG,MAAM,KAAK,GAAG,MAAM,IACvB,GAAG,WAAW,KAAK,GAAG,WAAW,EACjC;YACA,OAAO;gBAAC,MAAM,IAAI,EAAE;oBAAE,IAAI;gBAAI;gBAAI,MAAM,IAAI,EAAE;oBAAE,IAAI;gBAAI;aAAG;QAC7D;QACA,OAAO;YAAC,IAAI;SAAC;IACf;IAEA;;;;;GAKC,GACD,IAAI,eAAe;QACjB,OAAO,CAAA,GAAA,oJAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,IAAI;IAC7B;IAEA;;;;;GAKC,GACD,IAAI,cAAc;QAChB,OAAO,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK;IAC1C;IAEA;;;;;GAKC,GACD,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,OAAO,GAAG,CAAA,GAAA,oJAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,IAAI,IAAI;IAChD;IAEA;;;;;;GAMC,GACD,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,OAAO,GAAG,CAAA,GAAA,oJAAA,CAAA,kBAAe,AAAD,EAAE,IAAI,CAAC,QAAQ,IAAI;IACzD;IAEA;;;;;GAKC,GACD,IAAI,uBAAuB;QACzB,OAAO,IAAI,CAAC,OAAO,GACf,CAAA,GAAA,oJAAA,CAAA,kBAAe,AAAD,EACZ,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,GAAG,CAAC,qBAAqB,IAC9B,IAAI,CAAC,GAAG,CAAC,cAAc,MAEzB;IACN;IAEA;;;;;GAKC,GACD,sBAAsB,OAAO,CAAC,CAAC,EAAE;QAC/B,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,QAAQ,EAAE,GAAG,yJAAA,CAAA,UAAS,CAAC,MAAM,CAC5D,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OACf,MACA,eAAe,CAAC,IAAI;QACtB,OAAO;YAAE;YAAQ;YAAiB,gBAAgB;QAAS;IAC7D;IAEA,YAAY;IAEZ;;;;;;;GAOC,GACD,MAAM,SAAS,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE;QAC3B,OAAO,IAAI,CAAC,OAAO,CAAC,gKAAA,CAAA,UAAe,CAAC,QAAQ,CAAC,SAAS;IACxD;IAEA;;;;;GAKC,GACD,UAAU;QACR,OAAO,IAAI,CAAC,OAAO,CAAC,gJAAA,CAAA,UAAQ,CAAC,WAAW;IAC1C;IAEA;;;;;;;;GAQC,GACD,QAAQ,IAAI,EAAE,EAAE,gBAAgB,KAAK,EAAE,mBAAmB,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE;QACtE,OAAO,CAAA,GAAA,wJAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,gJAAA,CAAA,UAAQ,CAAC,WAAW;QAC/C,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG;YAC1B,OAAO,IAAI;QACb,OAAO,IAAI,CAAC,KAAK,OAAO,EAAE;YACxB,OAAO,SAAS,OAAO,CAAC,gBAAgB;QAC1C,OAAO;YACL,IAAI,QAAQ,IAAI,CAAC,EAAE;YACnB,IAAI,iBAAiB,kBAAkB;gBACrC,MAAM,cAAc,KAAK,MAAM,CAAC,IAAI,CAAC,EAAE;gBACvC,MAAM,QAAQ,IAAI,CAAC,QAAQ;gBAC3B,CAAC,MAAM,GAAG,QAAQ,OAAO,aAAa;YACxC;YACA,OAAO,MAAM,IAAI,EAAE;gBAAE,IAAI;gBAAO;YAAK;QACvC;IACF;IAEA;;;;;GAKC,GACD,YAAY,EAAE,MAAM,EAAE,eAAe,EAAE,cAAc,EAAE,GAAG,CAAC,CAAC,EAAE;QAC5D,MAAM,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;YAAE;YAAQ;YAAiB;QAAe;QACrE,OAAO,MAAM,IAAI,EAAE;YAAE;QAAI;IAC3B;IAEA;;;;;GAKC,GACD,UAAU,MAAM,EAAE;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC;YAAE;QAAO;IACnC;IAEA;;;;;;;;;;;;GAYC,GACD,IAAI,MAAM,EAAE;QACV,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI;QAE9B,MAAM,aAAa,CAAA,GAAA,oJAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ;QAC3C,MAAM,EAAE,kBAAkB,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,sBAAmB,AAAD,EAAE,YAAY,IAAI,CAAC,GAAG;QAEpF,MAAM,mBACF,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,WAAW,QAAQ,KAChC,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,WAAW,UAAU,KAClC,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,WAAW,OAAO,GACjC,kBAAkB,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,WAAW,OAAO,GACjD,qBAAqB,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,WAAW,IAAI,GACjD,mBAAmB,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,WAAW,KAAK,KAAK,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,WAAW,GAAG,GAChF,iBAAiB,sBAAsB,kBACvC,kBAAkB,WAAW,QAAQ,IAAI,WAAW,UAAU;QAEhE,IAAI,CAAC,kBAAkB,eAAe,KAAK,iBAAiB;YAC1D,MAAM,IAAI,8IAAA,CAAA,gCAA6B,CACrC;QAEJ;QAEA,IAAI,oBAAoB,iBAAiB;YACvC,MAAM,IAAI,8IAAA,CAAA,gCAA6B,CAAC;QAC1C;QAEA,IAAI;QACJ,IAAI,kBAAkB;YACpB,QAAQ,CAAA,GAAA,2JAAA,CAAA,kBAAe,AAAD,EACpB;gBAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,kBAAe,AAAD,EAAE,IAAI,CAAC,CAAC,EAAE,oBAAoB,YAAY;gBAAE,GAAG,UAAU;YAAC,GAC7E,oBACA;QAEJ,OAAO,IAAI,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,WAAW,OAAO,GAAG;YAC3C,QAAQ,CAAA,GAAA,2JAAA,CAAA,qBAAkB,AAAD,EAAE;gBAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,qBAAkB,AAAD,EAAE,IAAI,CAAC,CAAC,CAAC;gBAAE,GAAG,UAAU;YAAC;QAC5E,OAAO;YACL,QAAQ;gBAAE,GAAG,IAAI,CAAC,QAAQ,EAAE;gBAAE,GAAG,UAAU;YAAC;YAE5C,gEAAgE;YAChE,sCAAsC;YACtC,IAAI,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,WAAW,GAAG,GAAG;gBAC/B,MAAM,GAAG,GAAG,KAAK,GAAG,CAAC,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,MAAM,IAAI,EAAE,MAAM,KAAK,GAAG,MAAM,GAAG;YACtE;QACF;QAEA,MAAM,CAAC,IAAI,EAAE,GAAG,QAAQ,OAAO,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI;QAChD,OAAO,MAAM,IAAI,EAAE;YAAE;YAAI;QAAE;IAC7B;IAEA;;;;;;;;;;;;GAYC,GACD,KAAK,QAAQ,EAAE;QACb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI;QAC9B,MAAM,MAAM,gJAAA,CAAA,UAAQ,CAAC,gBAAgB,CAAC;QACtC,OAAO,MAAM,IAAI,EAAE,WAAW,IAAI,EAAE;IACtC;IAEA;;;;;GAKC,GACD,MAAM,QAAQ,EAAE;QACd,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI;QAC9B,MAAM,MAAM,gJAAA,CAAA,UAAQ,CAAC,gBAAgB,CAAC,UAAU,MAAM;QACtD,OAAO,MAAM,IAAI,EAAE,WAAW,IAAI,EAAE;IACtC;IAEA;;;;;;;;;;;GAWC,GACD,QAAQ,IAAI,EAAE,EAAE,iBAAiB,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE;QAC7C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI;QAE9B,MAAM,IAAI,CAAC,GACT,iBAAiB,gJAAA,CAAA,UAAQ,CAAC,aAAa,CAAC;QAC1C,OAAQ;YACN,KAAK;gBACH,EAAE,KAAK,GAAG;YACZ,gBAAgB;YAChB,KAAK;YACL,KAAK;gBACH,EAAE,GAAG,GAAG;YACV,gBAAgB;YAChB,KAAK;YACL,KAAK;gBACH,EAAE,IAAI,GAAG;YACX,gBAAgB;YAChB,KAAK;gBACH,EAAE,MAAM,GAAG;YACb,gBAAgB;YAChB,KAAK;gBACH,EAAE,MAAM,GAAG;YACb,gBAAgB;YAChB,KAAK;gBACH,EAAE,WAAW,GAAG;gBAChB;YACF,KAAK;gBACH;QAEJ;QAEA,IAAI,mBAAmB,SAAS;YAC9B,IAAI,gBAAgB;gBAClB,MAAM,cAAc,IAAI,CAAC,GAAG,CAAC,cAAc;gBAC3C,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI;gBACxB,IAAI,UAAU,aAAa;oBACzB,EAAE,UAAU,GAAG,IAAI,CAAC,UAAU,GAAG;gBACnC;gBACA,EAAE,OAAO,GAAG;YACd,OAAO;gBACL,EAAE,OAAO,GAAG;YACd;QACF;QAEA,IAAI,mBAAmB,YAAY;YACjC,MAAM,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG;YACjC,EAAE,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI;QAC1B;QAEA,OAAO,IAAI,CAAC,GAAG,CAAC;IAClB;IAEA;;;;;;;;;;;GAWC,GACD,MAAM,IAAI,EAAE,IAAI,EAAE;QAChB,OAAO,IAAI,CAAC,OAAO,GACf,IAAI,CAAC,IAAI,CAAC;YAAE,CAAC,KAAK,EAAE;QAAE,GACnB,OAAO,CAAC,MAAM,MACd,KAAK,CAAC,KACT,IAAI;IACV;IAEA,SAAS;IAET;;;;;;;;;;;GAWC,GACD,SAAS,GAAG,EAAE,OAAO,CAAC,CAAC,EAAE;QACvB,OAAO,IAAI,CAAC,OAAO,GACf,yJAAA,CAAA,UAAS,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,wBAAwB,CAAC,IAAI,EAAE,OAC9E;IACN;IAEA;;;;;;;;;;;;;;;;;;GAkBC,GACD,eAAe,aAAa,uJAAA,CAAA,aAAkB,EAAE,OAAO,CAAC,CAAC,EAAE;QACzD,OAAO,IAAI,CAAC,OAAO,GACf,yJAAA,CAAA,UAAS,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,YAAY,cAAc,CAAC,IAAI,IACtE;IACN;IAEA;;;;;;;;;;;;GAYC,GACD,cAAc,OAAO,CAAC,CAAC,EAAE;QACvB,OAAO,IAAI,CAAC,OAAO,GACf,yJAAA,CAAA,UAAS,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,MAAM,mBAAmB,CAAC,IAAI,IACrE,EAAE;IACR;IAEA;;;;;;;;;;;;;GAaC,GACD,MAAM,EACJ,SAAS,UAAU,EACnB,kBAAkB,KAAK,EACvB,uBAAuB,KAAK,EAC5B,gBAAgB,IAAI,EACpB,eAAe,KAAK,EACrB,GAAG,CAAC,CAAC,EAAE;QACN,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,OAAO;QACT;QAEA,MAAM,MAAM,WAAW;QAEvB,IAAI,IAAI,UAAU,IAAI,EAAE;QACxB,KAAK;QACL,KAAK,UAAU,IAAI,EAAE,KAAK,iBAAiB,sBAAsB,eAAe;QAChF,OAAO;IACT;IAEA;;;;;;;GAOC,GACD,UAAU,EAAE,SAAS,UAAU,EAAE,GAAG,CAAC,CAAC,EAAE;QACtC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,OAAO;QACT;QAEA,OAAO,UAAU,IAAI,EAAE,WAAW;IACpC;IAEA;;;;GAIC,GACD,gBAAgB;QACd,OAAO,aAAa,IAAI,EAAE;IAC5B;IAEA;;;;;;;;;;;;;;GAcC,GACD,UAAU,EACR,uBAAuB,KAAK,EAC5B,kBAAkB,KAAK,EACvB,gBAAgB,IAAI,EACpB,gBAAgB,KAAK,EACrB,eAAe,KAAK,EACpB,SAAS,UAAU,EACpB,GAAG,CAAC,CAAC,EAAE;QACN,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,OAAO;QACT;QAEA,IAAI,IAAI,gBAAgB,MAAM;QAC9B,OACE,IACA,UACE,IAAI,EACJ,WAAW,YACX,iBACA,sBACA,eACA;IAGN;IAEA;;;;;GAKC,GACD,YAAY;QACV,OAAO,aAAa,IAAI,EAAE,iCAAiC;IAC7D;IAEA;;;;;;;GAOC,GACD,SAAS;QACP,OAAO,aAAa,IAAI,CAAC,KAAK,IAAI;IACpC;IAEA;;;;GAIC,GACD,YAAY;QACV,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,OAAO;QACT;QACA,OAAO,UAAU,IAAI,EAAE;IACzB;IAEA;;;;;;;;;;;GAWC,GACD,UAAU,EAAE,gBAAgB,IAAI,EAAE,cAAc,KAAK,EAAE,qBAAqB,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE;QACvF,IAAI,MAAM;QAEV,IAAI,eAAe,eAAe;YAChC,IAAI,oBAAoB;gBACtB,OAAO;YACT;YACA,IAAI,aAAa;gBACf,OAAO;YACT,OAAO,IAAI,eAAe;gBACxB,OAAO;YACT;QACF;QAEA,OAAO,aAAa,IAAI,EAAE,KAAK;IACjC;IAEA;;;;;;;;;;;GAWC,GACD,MAAM,OAAO,CAAC,CAAC,EAAE;QACf,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,OAAO;QACT;QAEA,OAAO,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO;IACtD;IAEA;;;GAGC,GACD,WAAW;QACT,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,KAAK;IACvC;IAEA;;;GAGC,GACD,CAAC,OAAO,GAAG,CAAC,8BAA8B,GAAG;QAC3C,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,KAAK,GAAG,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;QAC5F,OAAO;YACL,OAAO,CAAC,4BAA4B,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;QAC9D;IACF;IAEA;;;GAGC,GACD,UAAU;QACR,OAAO,IAAI,CAAC,QAAQ;IACtB;IAEA;;;GAGC,GACD,WAAW;QACT,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,GAAG;IAClC;IAEA;;;GAGC,GACD,YAAY;QACV,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,GAAG,OAAO;IACzC;IAEA;;;GAGC,GACD,gBAAgB;QACd,OAAO,IAAI,CAAC,OAAO,GAAG,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,QAAQ;IACrD;IAEA;;;GAGC,GACD,SAAS;QACP,OAAO,IAAI,CAAC,KAAK;IACnB;IAEA;;;GAGC,GACD,SAAS;QACP,OAAO,IAAI,CAAC,QAAQ;IACtB;IAEA;;;;;;GAMC,GACD,SAAS,OAAO,CAAC,CAAC,EAAE;QAClB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC;QAE3B,MAAM,OAAO;YAAE,GAAG,IAAI,CAAC,CAAC;QAAC;QAEzB,IAAI,KAAK,aAAa,EAAE;YACtB,KAAK,cAAc,GAAG,IAAI,CAAC,cAAc;YACzC,KAAK,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe;YAC/C,KAAK,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM;QAC/B;QACA,OAAO;IACT;IAEA;;;GAGC,GACD,WAAW;QACT,OAAO,IAAI,KAAK,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,GAAG;IAC3C;IAEA,UAAU;IAEV;;;;;;;;;;;;;;GAcC,GACD,KAAK,aAAa,EAAE,OAAO,cAAc,EAAE,OAAO,CAAC,CAAC,EAAE;QACpD,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,cAAc,OAAO,EAAE;YAC3C,OAAO,gJAAA,CAAA,UAAQ,CAAC,OAAO,CAAC;QAC1B;QAEA,MAAM,UAAU;YAAE,QAAQ,IAAI,CAAC,MAAM;YAAE,iBAAiB,IAAI,CAAC,eAAe;YAAE,GAAG,IAAI;QAAC;QAEtF,MAAM,QAAQ,CAAA,GAAA,oJAAA,CAAA,aAAU,AAAD,EAAE,MAAM,GAAG,CAAC,gJAAA,CAAA,UAAQ,CAAC,aAAa,GACvD,eAAe,cAAc,OAAO,KAAK,IAAI,CAAC,OAAO,IACrD,UAAU,eAAe,IAAI,GAAG,eAChC,QAAQ,eAAe,gBAAgB,IAAI,EAC3C,SAAS,CAAA,GAAA,oJAAA,CAAA,UAAI,AAAD,EAAE,SAAS,OAAO,OAAO;QAEvC,OAAO,eAAe,OAAO,MAAM,KAAK;IAC1C;IAEA;;;;;;;GAOC,GACD,QAAQ,OAAO,cAAc,EAAE,OAAO,CAAC,CAAC,EAAE;QACxC,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,MAAM;IACzC;IAEA;;;;GAIC,GACD,MAAM,aAAa,EAAE;QACnB,OAAO,IAAI,CAAC,OAAO,GAAG,gJAAA,CAAA,UAAQ,CAAC,aAAa,CAAC,IAAI,EAAE,iBAAiB,IAAI;IAC1E;IAEA;;;;;;;;;;GAUC,GACD,QAAQ,aAAa,EAAE,IAAI,EAAE,IAAI,EAAE;QACjC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;QAE1B,MAAM,UAAU,cAAc,OAAO;QACrC,MAAM,iBAAiB,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,EAAE;YAAE,eAAe;QAAK;QAC9E,OACE,eAAe,OAAO,CAAC,MAAM,SAAS,WAAW,WAAW,eAAe,KAAK,CAAC,MAAM;IAE3F;IAEA;;;;;;GAMC,GACD,OAAO,KAAK,EAAE;QACZ,OACE,IAAI,CAAC,OAAO,IACZ,MAAM,OAAO,IACb,IAAI,CAAC,OAAO,OAAO,MAAM,OAAO,MAChC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,KAC3B,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG;IAE7B;IAEA;;;;;;;;;;;;;;;;;GAiBC,GACD,WAAW,UAAU,CAAC,CAAC,EAAE;QACvB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;QAC1B,MAAM,OAAO,QAAQ,IAAI,IAAI,SAAS,UAAU,CAAC,CAAC,GAAG;YAAE,MAAM,IAAI,CAAC,IAAI;QAAC,IACrE,UAAU,QAAQ,OAAO,GAAI,IAAI,GAAG,OAAO,CAAC,QAAQ,OAAO,GAAG,QAAQ,OAAO,GAAI;QACnF,IAAI,QAAQ;YAAC;YAAS;YAAU;YAAQ;YAAS;YAAW;SAAU;QACtE,IAAI,OAAO,QAAQ,IAAI;QACvB,IAAI,MAAM,OAAO,CAAC,QAAQ,IAAI,GAAG;YAC/B,QAAQ,QAAQ,IAAI;YACpB,OAAO;QACT;QACA,OAAO,aAAa,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU;YAC5C,GAAG,OAAO;YACV,SAAS;YACT;YACA;QACF;IACF;IAEA;;;;;;;;;;;;GAYC,GACD,mBAAmB,UAAU,CAAC,CAAC,EAAE;QAC/B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;QAE1B,OAAO,aAAa,QAAQ,IAAI,IAAI,SAAS,UAAU,CAAC,CAAC,GAAG;YAAE,MAAM,IAAI,CAAC,IAAI;QAAC,IAAI,IAAI,EAAE;YACtF,GAAG,OAAO;YACV,SAAS;YACT,OAAO;gBAAC;gBAAS;gBAAU;aAAO;YAClC,WAAW;QACb;IACF;IAEA;;;;GAIC,GACD,OAAO,IAAI,GAAG,SAAS,EAAE;QACvB,IAAI,CAAC,UAAU,KAAK,CAAC,SAAS,UAAU,GAAG;YACzC,MAAM,IAAI,8IAAA,CAAA,uBAAoB,CAAC;QACjC;QACA,OAAO,CAAA,GAAA,oJAAA,CAAA,SAAM,AAAD,EAAE,WAAW,CAAC,IAAM,EAAE,OAAO,IAAI,KAAK,GAAG;IACvD;IAEA;;;;GAIC,GACD,OAAO,IAAI,GAAG,SAAS,EAAE;QACvB,IAAI,CAAC,UAAU,KAAK,CAAC,SAAS,UAAU,GAAG;YACzC,MAAM,IAAI,8IAAA,CAAA,uBAAoB,CAAC;QACjC;QACA,OAAO,CAAA,GAAA,oJAAA,CAAA,SAAM,AAAD,EAAE,WAAW,CAAC,IAAM,EAAE,OAAO,IAAI,KAAK,GAAG;IACvD;IAEA,OAAO;IAEP;;;;;;GAMC,GACD,OAAO,kBAAkB,IAAI,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,EAAE;QAChD,MAAM,EAAE,SAAS,IAAI,EAAE,kBAAkB,IAAI,EAAE,GAAG,SAChD,cAAc,sJAAA,CAAA,UAAM,CAAC,QAAQ,CAAC;YAC5B;YACA;YACA,aAAa;QACf;QACF,OAAO,CAAA,GAAA,2JAAA,CAAA,oBAAiB,AAAD,EAAE,aAAa,MAAM;IAC9C;IAEA;;GAEC,GACD,OAAO,kBAAkB,IAAI,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,EAAE;QAChD,OAAO,SAAS,iBAAiB,CAAC,MAAM,KAAK;IAC/C;IAEA;;;;;;;;;;;GAWC,GACD,OAAO,kBAAkB,GAAG,EAAE,UAAU,CAAC,CAAC,EAAE;QAC1C,MAAM,EAAE,SAAS,IAAI,EAAE,kBAAkB,IAAI,EAAE,GAAG,SAChD,cAAc,sJAAA,CAAA,UAAM,CAAC,QAAQ,CAAC;YAC5B;YACA;YACA,aAAa;QACf;QACF,OAAO,IAAI,2JAAA,CAAA,cAAW,CAAC,aAAa;IACtC;IAEA;;;;;;;;;GASC,GACD,OAAO,iBAAiB,IAAI,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC,EAAE;QACrD,IAAI,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,SAAS,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,eAAe;YAClD,MAAM,IAAI,8IAAA,CAAA,uBAAoB,CAC5B;QAEJ;QACA,MAAM,EAAE,SAAS,IAAI,EAAE,kBAAkB,IAAI,EAAE,GAAG,MAChD,cAAc,sJAAA,CAAA,UAAM,CAAC,QAAQ,CAAC;YAC5B;YACA;YACA,aAAa;QACf;QAEF,IAAI,CAAC,YAAY,MAAM,CAAC,aAAa,MAAM,GAAG;YAC5C,MAAM,IAAI,8IAAA,CAAA,uBAAoB,CAC5B,CAAC,yCAAyC,EAAE,YAAY,EAAE,CAAC,GACzD,CAAC,sCAAsC,EAAE,aAAa,MAAM,EAAE;QAEpE;QAEA,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,aAAa,EAAE,GAAG,aAAa,iBAAiB,CAAC;QAEvF,IAAI,eAAe;YACjB,OAAO,SAAS,OAAO,CAAC;QAC1B,OAAO;YACL,OAAO,oBACL,QACA,MACA,MACA,CAAC,OAAO,EAAE,aAAa,MAAM,EAAE,EAC/B,MACA;QAEJ;IACF;IAEA,iBAAiB;IAEjB;;;GAGC,GACD,WAAW,aAAa;QACtB,OAAO,uJAAA,CAAA,aAAkB;IAC3B;IAEA;;;GAGC,GACD,WAAW,WAAW;QACpB,OAAO,uJAAA,CAAA,WAAgB;IACzB;IAEA;;;GAGC,GACD,WAAW,wBAAwB;QACjC,OAAO,uJAAA,CAAA,wBAA6B;IACtC;IAEA;;;GAGC,GACD,WAAW,YAAY;QACrB,OAAO,uJAAA,CAAA,YAAiB;IAC1B;IAEA;;;GAGC,GACD,WAAW,YAAY;QACrB,OAAO,uJAAA,CAAA,YAAiB;IAC1B;IAEA;;;GAGC,GACD,WAAW,cAAc;QACvB,OAAO,uJAAA,CAAA,cAAmB;IAC5B;IAEA;;;GAGC,GACD,WAAW,oBAAoB;QAC7B,OAAO,uJAAA,CAAA,oBAAyB;IAClC;IAEA;;;GAGC,GACD,WAAW,yBAAyB;QAClC,OAAO,uJAAA,CAAA,yBAA8B;IACvC;IAEA;;;GAGC,GACD,WAAW,wBAAwB;QACjC,OAAO,uJAAA,CAAA,wBAA6B;IACtC;IAEA;;;GAGC,GACD,WAAW,iBAAiB;QAC1B,OAAO,uJAAA,CAAA,iBAAsB;IAC/B;IAEA;;;GAGC,GACD,WAAW,uBAAuB;QAChC,OAAO,uJAAA,CAAA,uBAA4B;IACrC;IAEA;;;GAGC,GACD,WAAW,4BAA4B;QACrC,OAAO,uJAAA,CAAA,4BAAiC;IAC1C;IAEA;;;GAGC,GACD,WAAW,2BAA2B;QACpC,OAAO,uJAAA,CAAA,2BAAgC;IACzC;IAEA;;;GAGC,GACD,WAAW,iBAAiB;QAC1B,OAAO,uJAAA,CAAA,iBAAsB;IAC/B;IAEA;;;GAGC,GACD,WAAW,8BAA8B;QACvC,OAAO,uJAAA,CAAA,8BAAmC;IAC5C;IAEA;;;GAGC,GACD,WAAW,eAAe;QACxB,OAAO,uJAAA,CAAA,eAAoB;IAC7B;IAEA;;;GAGC,GACD,WAAW,4BAA4B;QACrC,OAAO,uJAAA,CAAA,4BAAiC;IAC1C;IAEA;;;GAGC,GACD,WAAW,4BAA4B;QACrC,OAAO,uJAAA,CAAA,4BAAiC;IAC1C;IAEA;;;GAGC,GACD,WAAW,gBAAgB;QACzB,OAAO,uJAAA,CAAA,gBAAqB;IAC9B;IAEA;;;GAGC,GACD,WAAW,6BAA6B;QACtC,OAAO,uJAAA,CAAA,6BAAkC;IAC3C;IAEA;;;GAGC,GACD,WAAW,gBAAgB;QACzB,OAAO,uJAAA,CAAA,gBAAqB;IAC9B;IAEA;;;GAGC,GACD,WAAW,6BAA6B;QACtC,OAAO,uJAAA,CAAA,6BAAkC;IAC3C;AACF;AAKO,SAAS,iBAAiB,WAAW;IAC1C,IAAI,SAAS,UAAU,CAAC,cAAc;QACpC,OAAO;IACT,OAAO,IAAI,eAAe,YAAY,OAAO,IAAI,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,OAAO,KAAK;QAChF,OAAO,SAAS,UAAU,CAAC;IAC7B,OAAO,IAAI,eAAe,OAAO,gBAAgB,UAAU;QACzD,OAAO,SAAS,UAAU,CAAC;IAC7B,OAAO;QACL,MAAM,IAAI,8IAAA,CAAA,uBAAoB,CAC5B,CAAC,2BAA2B,EAAE,YAAY,UAAU,EAAE,OAAO,aAAa;IAE9E;AACF", "ignoreList": [0]}}, {"offset": {"line": 7541, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/luxon/src/luxon.js"], "sourcesContent": ["import DateTime from \"./datetime.js\";\nimport Duration from \"./duration.js\";\nimport Interval from \"./interval.js\";\nimport Info from \"./info.js\";\nimport Zone from \"./zone.js\";\nimport FixedOffsetZone from \"./zones/fixedOffsetZone.js\";\nimport IANAZone from \"./zones/IANAZone.js\";\nimport InvalidZone from \"./zones/invalidZone.js\";\nimport SystemZone from \"./zones/systemZone.js\";\nimport Settings from \"./settings.js\";\n\nconst VERSION = \"3.6.1\";\n\nexport {\n  VERSION,\n  DateTime,\n  Duration,\n  Interval,\n  Info,\n  Zone,\n  FixedOffsetZone,\n  IANAZone,\n  InvalidZone,\n  SystemZone,\n  Settings,\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAEA,MAAM,UAAU", "ignoreList": [0]}}]}