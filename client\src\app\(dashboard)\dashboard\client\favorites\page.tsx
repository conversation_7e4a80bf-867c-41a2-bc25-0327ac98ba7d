"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Heart, Search, Star, MapPin, Calendar, DollarSign, Grid, List } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import Image from 'next/image'

const favoriteVehicles = [
  {
    id: 1,
    name: "Toyota Corolla 2023",
    image: "/placeholder.svg?height=200&width=300",
    price: 45,
    rating: 4.8,
    reviews: 124,
    location: "Centro, Ciudad de México",
    host: "<PERSON>",
    hostAvatar: "/placeholder.svg?height=40&width=40",
    features: ["Automático", "A/C", "GPS", "Bluetooth"],
    available: true,
    category: "Económico",
  },
  {
    id: 2,
    name: "Honda Civic 2022",
    image: "/placeholder.svg?height=200&width=300",
    price: 55,
    rating: 4.9,
    reviews: 89,
    location: "Polanco, Ciudad de México",
    host: "Ana Martinez",
    hostAvatar: "/placeholder.svg?height=40&width=40",
    features: ["Automático", "A/C", "GPS", "Cámara"],
    available: false,
    category: "Compacto",
  },
  {
    id: 3,
    name: "Mazda CX-5 2023",
    image: "/placeholder.svg?height=200&width=300",
    price: 75,
    rating: 4.7,
    reviews: 156,
    location: "Santa Fe, Ciudad de México",
    host: "Miguel Torres",
    hostAvatar: "/placeholder.svg?height=40&width=40",
    features: ["Automático", "A/C", "GPS", "AWD"],
    available: true,
    category: "SUV",
  },
  {
    id: 4,
    name: "Nissan Sentra 2023",
    image: "/placeholder.svg?height=200&width=300",
    price: 40,
    rating: 4.6,
    reviews: 78,
    location: "Roma Norte, Ciudad de México",
    host: "Laura Gonzalez",
    hostAvatar: "/placeholder.svg?height=40&width=40",
    features: ["Automático", "A/C", "GPS"],
    available: true,
    category: "Económico",
  },
]

export default function ClientFavoritesPage() {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [searchQuery, setSearchQuery] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [availabilityFilter, setAvailabilityFilter] = useState("all")

  const filteredVehicles = favoriteVehicles.filter((vehicle) => {
    const matchesSearch =
      vehicle.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      vehicle.location.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = categoryFilter === "all" || vehicle.category === categoryFilter
    const matchesAvailability =
      availabilityFilter === "all" ||
      (availabilityFilter === "available" && vehicle.available) ||
      (availabilityFilter === "unavailable" && !vehicle.available)

    return matchesSearch && matchesCategory && matchesAvailability
  })

  const VehicleCard = ({ vehicle, isListView = false }: { vehicle: any; isListView?: boolean }) => (
    <Card className={`overflow-hidden hover:shadow-lg transition-shadow ${isListView ? "flex" : ""}`}>
      <div className={`relative ${isListView ? "w-48 flex-shrink-0" : ""}`}>
        {/* <img
          src={vehicle.image || "/placeholder.svg"}
          alt={vehicle.name}
          className={`object-cover ${isListView ? "h-full w-full" : "w-full h-48"}`}
        /> */}
        <Image
          src={vehicle.image || "/placeholder.svg"}
          alt={vehicle.name}
          width={300}
          height={200}
          className={`object-cover ${isListView ? "h-full w-full" : "w-full h-48"}`}
        />
        <Button size="icon" variant="secondary" className="absolute top-2 right-2 h-8 w-8">
          <Heart className="h-4 w-4 fill-red-500 text-red-500" />
        </Button>
        {!vehicle.available && (
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
            <Badge variant="secondary">No Disponible</Badge>
          </div>
        )}
      </div>
      <CardContent className={`p-4 ${isListView ? "flex-1" : ""}`}>
        <div className="space-y-2">
          <div className="flex items-start justify-between">
            <h3 className="font-semibold text-lg">{vehicle.name}</h3>
            <div className="text-right">
              <p className="text-lg font-bold">${vehicle.price}</p>
              <p className="text-sm text-muted-foreground">por día</p>
            </div>
          </div>

          <div className="flex items-center space-x-1">
            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
            <span className="text-sm font-medium">{vehicle.rating}</span>
            <span className="text-sm text-muted-foreground">({vehicle.reviews} reseñas)</span>
          </div>

          <div className="flex items-center text-sm text-muted-foreground">
            <MapPin className="h-4 w-4 mr-1" />
            {vehicle.location}
          </div>

          <div className="flex items-center space-x-2">
            <Avatar className="h-6 w-6">
              <AvatarImage src={vehicle.hostAvatar || "/placeholder.svg"} />
              <AvatarFallback>{vehicle.host.charAt(0)}</AvatarFallback>
            </Avatar>
            <span className="text-sm text-muted-foreground">{vehicle.host}</span>
          </div>

          <div className="flex flex-wrap gap-1">
            {vehicle.features.map((feature: string, index: number) => (
              <Badge key={index} variant="outline" className="text-xs">
                {feature}
              </Badge>
            ))}
          </div>

          <div className="flex space-x-2 pt-2">
            <Button className="flex-1" disabled={!vehicle.available}>
              <Calendar className="h-4 w-4 mr-2" />
              {vehicle.available ? "Reservar" : "No Disponible"}
            </Button>
            <Button variant="outline" size="icon">
              <Heart className="h-4 w-4 fill-red-500 text-red-500" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Favoritos</h1>
        <p className="text-muted-foreground">Tus vehículos guardados para futuras reservas</p>
      </div>

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Favoritos</CardTitle>
            <Heart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{favoriteVehicles.length}</div>
            <p className="text-xs text-muted-foreground">Vehículos guardados</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Disponibles</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{favoriteVehicles.filter((v) => v.available).length}</div>
            <p className="text-xs text-muted-foreground">Para reservar ahora</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Precio Promedio</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${Math.round(favoriteVehicles.reduce((acc, v) => acc + v.price, 0) / favoriteVehicles.length)}
            </div>
            <p className="text-xs text-muted-foreground">por día</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rating Promedio</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(favoriteVehicles.reduce((acc, v) => acc + v.rating, 0) / favoriteVehicles.length).toFixed(1)}
            </div>
            <p className="text-xs text-muted-foreground">estrellas</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Filtros</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Buscar por nombre o ubicación..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Categoría" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas las categorías</SelectItem>
                <SelectItem value="Económico">Económico</SelectItem>
                <SelectItem value="Compacto">Compacto</SelectItem>
                <SelectItem value="SUV">SUV</SelectItem>
                <SelectItem value="Lujo">Lujo</SelectItem>
              </SelectContent>
            </Select>
            <Select value={availabilityFilter} onValueChange={setAvailabilityFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Disponibilidad" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos</SelectItem>
                <SelectItem value="available">Disponibles</SelectItem>
                <SelectItem value="unavailable">No disponibles</SelectItem>
              </SelectContent>
            </Select>
            <div className="flex gap-2">
              <Button
                variant={viewMode === "grid" ? "default" : "outline"}
                size="icon"
                onClick={() => setViewMode("grid")}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "outline"}
                size="icon"
                onClick={() => setViewMode("list")}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <p className="text-sm text-muted-foreground">
            {filteredVehicles.length} vehículo{filteredVehicles.length !== 1 ? "s" : ""} encontrado
            {filteredVehicles.length !== 1 ? "s" : ""}
          </p>
        </div>

        {filteredVehicles.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Heart className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No se encontraron favoritos</h3>
              <p className="text-muted-foreground text-center">
                No tienes vehículos favoritos que coincidan con los filtros seleccionados.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className={viewMode === "grid" ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" : "space-y-4"}>
            {filteredVehicles.map((vehicle) => (
              <VehicleCard key={vehicle.id} vehicle={vehicle} isListView={viewMode === "list"} />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
