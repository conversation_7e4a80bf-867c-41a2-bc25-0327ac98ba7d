{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VSCODE/Additionals/Autoop-2/client/src/app/%28dashboard%29/dashboard/page.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useEffect } from \"react\"\r\nimport { useRouter } from \"next/navigation\"\r\n\r\nexport default function DashboardRedirect() {\r\n  const router = useRouter()\r\n\r\n  useEffect(() => {\r\n    // Mock user role - en producción vendría del contexto de autenticación\r\n    const userRole = \"admin\" // Cambiar según el usuario autenticado\r\n    router.push(`/dashboard/${userRole}`)\r\n  }, [router])\r\n\r\n  return (\r\n    <div className=\"flex items-center justify-center h-full\">\r\n      <div className=\"text-center\">\r\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto\"></div>\r\n        <p className=\"mt-2 text-muted-foreground\">Redirigiendo...</p>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,uEAAuE;YACvE,MAAM,WAAW,QAAQ,uCAAuC;;YAChE,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,UAAU;QACtC;sCAAG;QAAC;KAAO;IAEX,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAE,WAAU;8BAA6B;;;;;;;;;;;;;;;;;AAIlD;GAjBwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}