{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/constants/index.ts"], "sourcesContent": ["export const BEARER_COOKIE_NAME = \"bearer_token\";\r\nexport const PENDING_INVITATION_COOKIE = 'pending_invitation';\r\nexport const PENDING_INVITATION_EMAIL_COOKIE = 'pending_invitation_email';\r\nexport const PENDING_INVITATION_ORG_ID_COOKIE = 'pending_invitation_org_id';"], "names": [], "mappings": ";;;;;;AAAO,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAClC,MAAM,kCAAkC;AACxC,MAAM,mCAAmC"}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["// client/src/middleware.ts\nimport { NextResponse, NextRequest } from \"next/server\";\nimport { BEARER_COOKIE_NAME } from \"./constants\";\n\nexport const runtime = \"experimental-edge\";\n\nexport default function middleware(request: NextRequest) {\n  const url = request.nextUrl.clone();\n  const token = url.searchParams.get(\"token\");\n  const tcl = url.searchParams.get(\"tcl\");\n\n  if (token && !tcl) {\n    url.searchParams.delete(\"token\");\n\n    const res = NextResponse.redirect(url);\n    res.cookies.set(BEARER_COOKIE_NAME, token, {\n      httpOnly: false,\n      secure: true, // Siempre true para sameSite: \"none\"\n      sameSite: \"none\",\n      path: \"/\",\n    });\n    return res;\n  }\n\n  return NextResponse.next();\n}\n\nexport const config = {\n  matcher: [\"/((?!api|_next).*)\"],\n};\n"], "names": [], "mappings": "AAAA,2BAA2B;;;;;;AAC3B;AAAA;AACA;;;AAEO,MAAM,UAAU;AAER,SAAS,WAAW,OAAoB;IACrD,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;IACjC,MAAM,QAAQ,IAAI,YAAY,CAAC,GAAG,CAAC;IACnC,MAAM,MAAM,IAAI,YAAY,CAAC,GAAG,CAAC;IAEjC,IAAI,SAAS,CAAC,KAAK;QACjB,IAAI,YAAY,CAAC,MAAM,CAAC;QAExB,MAAM,MAAM,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAClC,IAAI,OAAO,CAAC,GAAG,CAAC,iIAAA,CAAA,qBAAkB,EAAE,OAAO;YACzC,UAAU;YACV,QAAQ;YACR,UAAU;YACV,MAAM;QACR;QACA,OAAO;IACT;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QAAC;KAAqB;AACjC"}}]}