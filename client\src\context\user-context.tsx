'use client';
import { createContext, useContext, useEffect, useState } from 'react';


interface UserContextType {
  user: User;
  orgId: string;
  setUser: React.Dispatch<React.SetStateAction<User>>;
  session: any;
  setSession: React.Dispatch<React.SetStateAction<any>>;
}

export const UserContext = createContext<UserContextType | undefined>(undefined);

export function UserProvider({ children, session: sessionData }: { children: React.ReactNode; session: any }) {
  const [session, setSession] = useState(sessionData.session);
  const [user, setUser] = useState(sessionData?.user);
  const [orgId, setOrgId] = useState(sessionData.session.activeOrganizationId);

  useEffect(() => {
    // set orgId from session
    setOrgId(sessionData.session.activeOrganizationId);
    // setSession(sessionData.session);
    setSession((prevSession: any) => ({ ...prevSession, activeOrganizationId: sessionData.session.activeOrganizationId }));
  }, [sessionData.session.activeOrganizationId]);

  return (
    <UserContext.Provider value={{ user, setUser, orgId, session, setSession }}>
      {children}
    </UserContext.Provider>
  );
}


export function useUser() {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
}