{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/react-scan.tsx"], "sourcesContent": ["'use client';\r\nimport { useEffect, useState } from 'react';\r\nimport { scan } from 'react-scan';\r\n\r\nexport function ReactScan() {\r\n  const [isMounted, setIsMounted] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsMounted(true);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (isMounted) {\r\n      scan();\r\n    }\r\n  }, [isMounted]);\r\n  return null\r\n}"], "names": [], "mappings": ";;;AACA;AACA;AAFA;;;AAIO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG,EAAE;IAEL,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,CAAA,GAAA,uJAAA,CAAA,OAAI,AAAD;QACL;IACF,GAAG;QAAC;KAAU;IACd,OAAO;AACT"}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/providers/Providers.tsx"], "sourcesContent": ["\"use client\"\r\n// Client providers:\r\nimport * as React from \"react\"\r\n// import { ThemeProvider as NextThemesProvider } from \"next-themes\"\r\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query'\r\n\r\nconst queryClient = new QueryClient()\r\n\r\nexport function Providers({\r\n  children,\r\n}: {\r\n  children: React.ReactNode\r\n}) {\r\n  return (\r\n    <>\r\n      {/* <NextThemesProvider\r\n        attribute=\"class\"\r\n        defaultTheme=\"system\"\r\n        enableSystem\r\n        disableTransitionOnChange\r\n      > */}\r\n        <QueryClientProvider client={queryClient}>\r\n\r\n        <>{children}</>\r\n        </QueryClientProvider>\r\n      {/* </NextThemesProvider> */}\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA,oEAAoE;AACpE;AAAA;AAJA;;;AAMA,MAAM,cAAc,IAAI,qLAAA,CAAA,cAAW;AAE5B,SAAS,UAAU,EACxB,QAAQ,EAGT;IACC,qBACE;kBAOI,cAAA,kMAAC,8LAAA,CAAA,sBAAmB;YAAC,QAAQ;sBAE7B,cAAA;0BAAG;;;;;;;;AAKX"}}]}