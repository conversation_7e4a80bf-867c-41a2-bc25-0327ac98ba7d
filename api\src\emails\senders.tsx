import * as React from 'react'
import WelcomeEmail from '@/emails/Welcome';
import VerificationEmail from '@/emails/Send-verification';
import { sendEmailWithNodemailer } from '@/lib/sendVerificationEmail';


export async function sendWelcomeEmail(to: string) {

  // send the email with the templates 
  const { data, error } = await sendEmailWithNodemailer({
    to: to,
    subject: 'Welcome to Autoop!',
    text: 'Welcome to Autoop!',
    html: React.createElement(WelcomeEmail).toString(),
  });

  if (error) {
    console.error('Error sending welcome email:', error);
    return;
  }

  console.log('Welcome email sent:', data);
}

export async function sendVerificationEmail({
  to,
  url,
  name,
}: {
  to: string;
  url: string;
  name: string;
}) {

  const { data, error } = await sendEmailWithNodemailer({
    to: to,
    subject: 'Verify your email',
    text: 'Verify your email',
    html: React.createElement(VerificationEmail, { name, url }).toString(),
  });

  if (error) {
    console.error('Error sending verification email:', error);
    return;
  }

  console.log('Verification email sent:', data);
}