{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/theme-provider-dashboard.tsx"], "sourcesContent": ["\"use client\"\r\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\"\r\nimport type { ThemeProviderProps } from \"next-themes\"\r\n\r\nexport function DashboardThemeProvider({ children, ...props }: ThemeProviderProps) {\r\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAIO,SAAS,uBAAuB,EAAE,QAAQ,EAAE,GAAG,OAA2B;IAC/E,qBAAO,kMAAC,wJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC"}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/dev/hmr-client/hmr-client.ts"], "sourcesContent": ["/// <reference path=\"../../../shared/runtime-types.d.ts\" />\r\n/// <reference path=\"../../runtime/base/dev-globals.d.ts\" />\r\n/// <reference path=\"../../runtime/base/dev-protocol.d.ts\" />\r\n/// <reference path=\"../../runtime/base/dev-extensions.ts\" />\r\n\r\ntype SendMessage = (msg: any) => void;\r\nexport type WebSocketMessage =\r\n  | {\r\n      type: \"turbopack-connected\";\r\n    }\r\n  | {\r\n      type: \"turbopack-message\";\r\n      data: Record<string, any>;\r\n    };\r\n\r\n\r\nexport type ClientOptions = {\r\n  addMessageListener: (cb: (msg: WebSocketMessage) => void) => void;\r\n  sendMessage: SendMessage;\r\n  onUpdateError: (err: unknown) => void;\r\n};\r\n\r\nexport function connect({\r\n  addMessageListener,\r\n  sendMessage,\r\n  onUpdateError = console.error,\r\n}: ClientOptions) {\r\n  addMessageListener((msg) => {\r\n    switch (msg.type) {\r\n      case \"turbopack-connected\":\r\n        handleSocketConnected(sendMessage);\r\n        break;\r\n      default:\r\n        try {\r\n          if (Array.isArray(msg.data)) {\r\n            for (let i = 0; i < msg.data.length; i++) {\r\n              handleSocketMessage(msg.data[i] as ServerMessage);\r\n            }\r\n          } else {\r\n            handleSocketMessage(msg.data as ServerMessage);\r\n          }\r\n          applyAggregatedUpdates();\r\n        } catch (e: unknown) {\r\n          console.warn(\r\n            \"[Fast Refresh] performing full reload\\n\\n\" +\r\n              \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" +\r\n              \"You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n\" +\r\n              \"Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n\" +\r\n              \"It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n\" +\r\n              \"Fast Refresh requires at least one parent function component in your React tree.\"\r\n          );\r\n          onUpdateError(e);\r\n          location.reload();\r\n        }\r\n        break;\r\n    }\r\n  });\r\n\r\n  const queued = globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS;\r\n  if (queued != null && !Array.isArray(queued)) {\r\n    throw new Error(\"A separate HMR handler was already registered\");\r\n  }\r\n  globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS = {\r\n    push: ([chunkPath, callback]: [ChunkListPath, UpdateCallback]) => {\r\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback);\r\n    },\r\n  };\r\n\r\n  if (Array.isArray(queued)) {\r\n    for (const [chunkPath, callback] of queued) {\r\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback);\r\n    }\r\n  }\r\n}\r\n\r\ntype UpdateCallbackSet = {\r\n  callbacks: Set<UpdateCallback>;\r\n  unsubscribe: () => void;\r\n};\r\n\r\nconst updateCallbackSets: Map<ResourceKey, UpdateCallbackSet> = new Map();\r\n\r\nfunction sendJSON(sendMessage: SendMessage, message: ClientMessage) {\r\n  sendMessage(JSON.stringify(message));\r\n}\r\n\r\ntype ResourceKey = string;\r\n\r\nfunction resourceKey(resource: ResourceIdentifier): ResourceKey {\r\n  return JSON.stringify({\r\n    path: resource.path,\r\n    headers: resource.headers || null,\r\n  });\r\n}\r\n\r\nfunction subscribeToUpdates(\r\n  sendMessage: SendMessage,\r\n  resource: ResourceIdentifier\r\n): () => void {\r\n  sendJSON(sendMessage, {\r\n    type: \"turbopack-subscribe\",\r\n    ...resource,\r\n  });\r\n\r\n  return () => {\r\n    sendJSON(sendMessage, {\r\n      type: \"turbopack-unsubscribe\",\r\n      ...resource,\r\n    });\r\n  };\r\n}\r\n\r\nfunction handleSocketConnected(sendMessage: SendMessage) {\r\n  for (const key of updateCallbackSets.keys()) {\r\n    subscribeToUpdates(sendMessage, JSON.parse(key));\r\n  }\r\n}\r\n\r\n// we aggregate all pending updates until the issues are resolved\r\nconst chunkListsWithPendingUpdates: Map<ResourceKey, PartialServerMessage> =\r\n  new Map();\r\n\r\nfunction aggregateUpdates(msg: PartialServerMessage) {\r\n  const key = resourceKey(msg.resource);\r\n  let aggregated = chunkListsWithPendingUpdates.get(key);\r\n\r\n  if (aggregated) {\r\n    aggregated.instruction = mergeChunkListUpdates(\r\n      aggregated.instruction,\r\n      msg.instruction\r\n    );\r\n  } else {\r\n    chunkListsWithPendingUpdates.set(key, msg);\r\n  }\r\n}\r\n\r\nfunction applyAggregatedUpdates() {\r\n  if (chunkListsWithPendingUpdates.size === 0) return;\r\n  hooks.beforeRefresh();\r\n  for (const msg of chunkListsWithPendingUpdates.values()) {\r\n    triggerUpdate(msg);\r\n  }\r\n  chunkListsWithPendingUpdates.clear();\r\n  finalizeUpdate();\r\n}\r\n\r\nfunction mergeChunkListUpdates(\r\n  updateA: ChunkListUpdate,\r\n  updateB: ChunkListUpdate\r\n): ChunkListUpdate {\r\n  let chunks;\r\n  if (updateA.chunks != null) {\r\n    if (updateB.chunks == null) {\r\n      chunks = updateA.chunks;\r\n    } else {\r\n      chunks = mergeChunkListChunks(updateA.chunks, updateB.chunks);\r\n    }\r\n  } else if (updateB.chunks != null) {\r\n    chunks = updateB.chunks;\r\n  }\r\n\r\n  let merged;\r\n  if (updateA.merged != null) {\r\n    if (updateB.merged == null) {\r\n      merged = updateA.merged;\r\n    } else {\r\n      // Since `merged` is an array of updates, we need to merge them all into\r\n      // one, consistent update.\r\n      // Since there can only be `EcmascriptMergeUpdates` in the array, there is\r\n      // no need to key on the `type` field.\r\n      let update = updateA.merged[0];\r\n      for (let i = 1; i < updateA.merged.length; i++) {\r\n        update = mergeChunkListEcmascriptMergedUpdates(\r\n          update,\r\n          updateA.merged[i]\r\n        );\r\n      }\r\n\r\n      for (let i = 0; i < updateB.merged.length; i++) {\r\n        update = mergeChunkListEcmascriptMergedUpdates(\r\n          update,\r\n          updateB.merged[i]\r\n        );\r\n      }\r\n\r\n      merged = [update];\r\n    }\r\n  } else if (updateB.merged != null) {\r\n    merged = updateB.merged;\r\n  }\r\n\r\n  return {\r\n    type: \"ChunkListUpdate\",\r\n    chunks,\r\n    merged,\r\n  };\r\n}\r\n\r\nfunction mergeChunkListChunks(\r\n  chunksA: Record<ChunkPath, ChunkUpdate>,\r\n  chunksB: Record<ChunkPath, ChunkUpdate>\r\n): Record<ChunkPath, ChunkUpdate> {\r\n  const chunks: Record<ChunkPath, ChunkUpdate> = {};\r\n\r\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<[ChunkPath, ChunkUpdate]>) {\r\n    const chunkUpdateB = chunksB[chunkPath];\r\n    if (chunkUpdateB != null) {\r\n      const mergedUpdate = mergeChunkUpdates(chunkUpdateA, chunkUpdateB);\r\n      if (mergedUpdate != null) {\r\n        chunks[chunkPath] = mergedUpdate;\r\n      }\r\n    } else {\r\n      chunks[chunkPath] = chunkUpdateA;\r\n    }\r\n  }\r\n\r\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<[ChunkPath, ChunkUpdate]>) {\r\n    if (chunks[chunkPath] == null) {\r\n      chunks[chunkPath] = chunkUpdateB;\r\n    }\r\n  }\r\n\r\n  return chunks;\r\n}\r\n\r\nfunction mergeChunkUpdates(\r\n  updateA: ChunkUpdate,\r\n  updateB: ChunkUpdate\r\n): ChunkUpdate | undefined {\r\n  if (\r\n    (updateA.type === \"added\" && updateB.type === \"deleted\") ||\r\n    (updateA.type === \"deleted\" && updateB.type === \"added\")\r\n  ) {\r\n    return undefined;\r\n  }\r\n\r\n  if (updateA.type === \"partial\") {\r\n    invariant(updateA.instruction, \"Partial updates are unsupported\");\r\n  }\r\n\r\n  if (updateB.type === \"partial\") {\r\n    invariant(updateB.instruction, \"Partial updates are unsupported\");\r\n  }\r\n\r\n  return undefined;\r\n}\r\n\r\nfunction mergeChunkListEcmascriptMergedUpdates(\r\n  mergedA: EcmascriptMergedUpdate,\r\n  mergedB: EcmascriptMergedUpdate\r\n): EcmascriptMergedUpdate {\r\n  const entries = mergeEcmascriptChunkEntries(mergedA.entries, mergedB.entries);\r\n  const chunks = mergeEcmascriptChunksUpdates(mergedA.chunks, mergedB.chunks);\r\n\r\n  return {\r\n    type: \"EcmascriptMergedUpdate\",\r\n    entries,\r\n    chunks,\r\n  };\r\n}\r\n\r\nfunction mergeEcmascriptChunkEntries(\r\n  entriesA: Record<ModuleId, EcmascriptModuleEntry> | undefined,\r\n  entriesB: Record<ModuleId, EcmascriptModuleEntry> | undefined\r\n): Record<ModuleId, EcmascriptModuleEntry> {\r\n  return { ...entriesA, ...entriesB };\r\n}\r\n\r\nfunction mergeEcmascriptChunksUpdates(\r\n  chunksA: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined,\r\n  chunksB: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined\r\n): Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined {\r\n  if (chunksA == null) {\r\n    return chunksB;\r\n  }\r\n\r\n  if (chunksB == null) {\r\n    return chunksA;\r\n  }\r\n\r\n  const chunks: Record<ChunkPath, EcmascriptMergedChunkUpdate> = {};\r\n\r\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<[ChunkPath, EcmascriptMergedChunkUpdate]>) {\r\n    const chunkUpdateB = chunksB[chunkPath];\r\n    if (chunkUpdateB != null) {\r\n      const mergedUpdate = mergeEcmascriptChunkUpdates(\r\n        chunkUpdateA,\r\n        chunkUpdateB\r\n      );\r\n      if (mergedUpdate != null) {\r\n        chunks[chunkPath] = mergedUpdate;\r\n      }\r\n    } else {\r\n      chunks[chunkPath] = chunkUpdateA;\r\n    }\r\n  }\r\n\r\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<[ChunkPath, EcmascriptMergedChunkUpdate]>) {\r\n    if (chunks[chunkPath] == null) {\r\n      chunks[chunkPath] = chunkUpdateB;\r\n    }\r\n  }\r\n\r\n  if (Object.keys(chunks).length === 0) {\r\n    return undefined;\r\n  }\r\n\r\n  return chunks;\r\n}\r\n\r\nfunction mergeEcmascriptChunkUpdates(\r\n  updateA: EcmascriptMergedChunkUpdate,\r\n  updateB: EcmascriptMergedChunkUpdate\r\n): EcmascriptMergedChunkUpdate | undefined {\r\n  if (updateA.type === \"added\" && updateB.type === \"deleted\") {\r\n    // These two completely cancel each other out.\r\n    return undefined;\r\n  }\r\n\r\n  if (updateA.type === \"deleted\" && updateB.type === \"added\") {\r\n    const added = [];\r\n    const deleted = [];\r\n    const deletedModules = new Set(updateA.modules ?? []);\r\n    const addedModules = new Set(updateB.modules ?? []);\r\n\r\n    for (const moduleId of addedModules) {\r\n      if (!deletedModules.has(moduleId)) {\r\n        added.push(moduleId);\r\n      }\r\n    }\r\n\r\n    for (const moduleId of deletedModules) {\r\n      if (!addedModules.has(moduleId)) {\r\n        deleted.push(moduleId);\r\n      }\r\n    }\r\n\r\n    if (added.length === 0 && deleted.length === 0) {\r\n      return undefined;\r\n    }\r\n\r\n    return {\r\n      type: \"partial\",\r\n      added,\r\n      deleted,\r\n    };\r\n  }\r\n\r\n  if (updateA.type === \"partial\" && updateB.type === \"partial\") {\r\n    const added = new Set([...(updateA.added ?? []), ...(updateB.added ?? [])]);\r\n    const deleted = new Set([\r\n      ...(updateA.deleted ?? []),\r\n      ...(updateB.deleted ?? []),\r\n    ]);\r\n\r\n    if (updateB.added != null) {\r\n      for (const moduleId of updateB.added) {\r\n        deleted.delete(moduleId);\r\n      }\r\n    }\r\n\r\n    if (updateB.deleted != null) {\r\n      for (const moduleId of updateB.deleted) {\r\n        added.delete(moduleId);\r\n      }\r\n    }\r\n\r\n    return {\r\n      type: \"partial\",\r\n      added: [...added],\r\n      deleted: [...deleted],\r\n    };\r\n  }\r\n\r\n  if (updateA.type === \"added\" && updateB.type === \"partial\") {\r\n    const modules = new Set([\r\n      ...(updateA.modules ?? []),\r\n      ...(updateB.added ?? []),\r\n    ]);\r\n\r\n    for (const moduleId of updateB.deleted ?? []) {\r\n      modules.delete(moduleId);\r\n    }\r\n\r\n    return {\r\n      type: \"added\",\r\n      modules: [...modules],\r\n    };\r\n  }\r\n\r\n  if (updateA.type === \"partial\" && updateB.type === \"deleted\") {\r\n    // We could eagerly return `updateB` here, but this would potentially be\r\n    // incorrect if `updateA` has added modules.\r\n\r\n    const modules = new Set(updateB.modules ?? []);\r\n\r\n    if (updateA.added != null) {\r\n      for (const moduleId of updateA.added) {\r\n        modules.delete(moduleId);\r\n      }\r\n    }\r\n\r\n    return {\r\n      type: \"deleted\",\r\n      modules: [...modules],\r\n    };\r\n  }\r\n\r\n  // Any other update combination is invalid.\r\n\r\n  return undefined;\r\n}\r\n\r\nfunction invariant(_: never, message: string): never {\r\n  throw new Error(`Invariant: ${message}`);\r\n}\r\n\r\nconst CRITICAL = [\"bug\", \"error\", \"fatal\"];\r\n\r\nfunction compareByList(list: any[], a: any, b: any) {\r\n  const aI = list.indexOf(a) + 1 || list.length;\r\n  const bI = list.indexOf(b) + 1 || list.length;\r\n  return aI - bI;\r\n}\r\n\r\nconst chunksWithIssues: Map<ResourceKey, Issue[]> = new Map();\r\n\r\nfunction emitIssues() {\r\n  const issues = [];\r\n  const deduplicationSet = new Set();\r\n\r\n  for (const [_, chunkIssues] of chunksWithIssues) {\r\n    for (const chunkIssue of chunkIssues) {\r\n      if (deduplicationSet.has(chunkIssue.formatted)) continue;\r\n\r\n      issues.push(chunkIssue);\r\n      deduplicationSet.add(chunkIssue.formatted);\r\n    }\r\n  }\r\n\r\n  sortIssues(issues);\r\n\r\n  hooks.issues(issues);\r\n}\r\n\r\nfunction handleIssues(msg: ServerMessage): boolean {\r\n  const key = resourceKey(msg.resource);\r\n  let hasCriticalIssues = false;\r\n\r\n  for (const issue of msg.issues) {\r\n    if (CRITICAL.includes(issue.severity)) {\r\n      hasCriticalIssues = true;\r\n    }\r\n  }\r\n\r\n  if (msg.issues.length > 0) {\r\n    chunksWithIssues.set(key, msg.issues);\r\n  } else if (chunksWithIssues.has(key)) {\r\n    chunksWithIssues.delete(key);\r\n  }\r\n\r\n  emitIssues();\r\n\r\n  return hasCriticalIssues;\r\n}\r\n\r\nconst SEVERITY_ORDER = [\"bug\", \"fatal\", \"error\", \"warning\", \"info\", \"log\"];\r\nconst CATEGORY_ORDER = [\r\n  \"parse\",\r\n  \"resolve\",\r\n  \"code generation\",\r\n  \"rendering\",\r\n  \"typescript\",\r\n  \"other\",\r\n];\r\n\r\nfunction sortIssues(issues: Issue[]) {\r\n  issues.sort((a, b) => {\r\n    const first = compareByList(SEVERITY_ORDER, a.severity, b.severity);\r\n    if (first !== 0) return first;\r\n    return compareByList(CATEGORY_ORDER, a.category, b.category);\r\n  });\r\n}\r\n\r\nconst hooks = {\r\n  beforeRefresh: () => {},\r\n  refresh: () => {},\r\n  buildOk: () => {},\r\n  issues: (_issues: Issue[]) => {},\r\n};\r\n\r\nexport function setHooks(newHooks: typeof hooks) {\r\n  Object.assign(hooks, newHooks);\r\n}\r\n\r\nfunction handleSocketMessage(msg: ServerMessage) {\r\n  sortIssues(msg.issues);\r\n\r\n  handleIssues(msg);\r\n\r\n  switch (msg.type) {\r\n    case \"issues\":\r\n      // issues are already handled\r\n      break;\r\n    case \"partial\":\r\n      // aggregate updates\r\n      aggregateUpdates(msg);\r\n      break;\r\n    default:\r\n      // run single update\r\n      const runHooks = chunkListsWithPendingUpdates.size === 0;\r\n      if (runHooks) hooks.beforeRefresh();\r\n      triggerUpdate(msg);\r\n      if (runHooks) finalizeUpdate();\r\n      break;\r\n  }\r\n}\r\n\r\nfunction finalizeUpdate() {\r\n  hooks.refresh();\r\n  hooks.buildOk();\r\n\r\n  // This is used by the Next.js integration test suite to notify it when HMR\r\n  // updates have been completed.\r\n  // TODO: Only run this in test environments (gate by `process.env.__NEXT_TEST_MODE`)\r\n  if (globalThis.__NEXT_HMR_CB) {\r\n    globalThis.__NEXT_HMR_CB();\r\n    globalThis.__NEXT_HMR_CB = null;\r\n  }\r\n}\r\n\r\nfunction subscribeToChunkUpdate(\r\n  chunkListPath: ChunkListPath,\r\n  sendMessage: SendMessage,\r\n  callback: UpdateCallback\r\n): () => void {\r\n  return subscribeToUpdate(\r\n    {\r\n      path: chunkListPath,\r\n    },\r\n    sendMessage,\r\n    callback\r\n  );\r\n}\r\n\r\nexport function subscribeToUpdate(\r\n  resource: ResourceIdentifier,\r\n  sendMessage: SendMessage,\r\n  callback: UpdateCallback\r\n) {\r\n  const key = resourceKey(resource);\r\n  let callbackSet: UpdateCallbackSet;\r\n  const existingCallbackSet = updateCallbackSets.get(key);\r\n  if (!existingCallbackSet) {\r\n    callbackSet = {\r\n      callbacks: new Set([callback]),\r\n      unsubscribe: subscribeToUpdates(sendMessage, resource),\r\n    };\r\n    updateCallbackSets.set(key, callbackSet);\r\n  } else {\r\n    existingCallbackSet.callbacks.add(callback);\r\n    callbackSet = existingCallbackSet;\r\n  }\r\n\r\n  return () => {\r\n    callbackSet.callbacks.delete(callback);\r\n\r\n    if (callbackSet.callbacks.size === 0) {\r\n      callbackSet.unsubscribe();\r\n      updateCallbackSets.delete(key);\r\n    }\r\n  };\r\n}\r\n\r\nfunction triggerUpdate(msg: ServerMessage) {\r\n  const key = resourceKey(msg.resource);\r\n  const callbackSet = updateCallbackSets.get(key);\r\n  if (!callbackSet) {\r\n    return;\r\n  }\r\n\r\n  for (const callback of callbackSet.callbacks) {\r\n    callback(msg);\r\n  }\r\n\r\n  if (msg.type === \"notFound\") {\r\n    // This indicates that the resource which we subscribed to either does not exist or\r\n    // has been deleted. In either case, we should clear all update callbacks, so if a\r\n    // new subscription is created for the same resource, it will send a new \"subscribe\"\r\n    // message to the server.\r\n    // No need to send an \"unsubscribe\" message to the server, it will have already\r\n    // dropped the update stream before sending the \"notFound\" message.\r\n    updateCallbackSets.delete(key);\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D,4DAA4D;AAC5D,6DAA6D;AAC7D,6DAA6D;;;;;;AAmBtD,SAAS,QAAQ,EACtB,kBAAkB,EAClB,WAAW,EACX,gBAAgB,QAAQ,KAAK,EACf;IACd,mBAAmB,CAAC;QAClB,OAAQ,IAAI,IAAI;YACd,KAAK;gBACH,sBAAsB;gBACtB;YACF;gBACE,IAAI;oBACF,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,GAAG;wBAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,IAAK;4BACxC,oBAAoB,IAAI,IAAI,CAAC,EAAE;wBACjC;oBACF,OAAO;wBACL,oBAAoB,IAAI,IAAI;oBAC9B;oBACA;gBACF,EAAE,OAAO,GAAY;oBACnB,QAAQ,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;oBAEJ,cAAc;oBACd,SAAS,MAAM;gBACjB;gBACA;QACJ;IACF;IAEA,MAAM,SAAS,WAAW,gCAAgC;IAC1D,IAAI,UAAU,QAAQ,CAAC,MAAM,OAAO,CAAC,SAAS;QAC5C,MAAM,IAAI,MAAM;IAClB;IACA,WAAW,gCAAgC,GAAG;QAC5C,MAAM,CAAC,CAAC,WAAW,SAA0C;YAC3D,uBAAuB,WAAW,aAAa;QACjD;IACF;IAEA,IAAI,MAAM,OAAO,CAAC,SAAS;QACzB,KAAK,MAAM,CAAC,WAAW,SAAS,IAAI,OAAQ;YAC1C,uBAAuB,WAAW,aAAa;QACjD;IACF;AACF;AAOA,MAAM,qBAA0D,IAAI;AAEpE,SAAS,SAAS,WAAwB,EAAE,OAAsB;IAChE,YAAY,KAAK,SAAS,CAAC;AAC7B;AAIA,SAAS,YAAY,QAA4B;IAC/C,OAAO,KAAK,SAAS,CAAC;QACpB,MAAM,SAAS,IAAI;QACnB,SAAS,SAAS,OAAO,IAAI;IAC/B;AACF;AAEA,SAAS,mBACP,WAAwB,EACxB,QAA4B;IAE5B,SAAS,aAAa;QACpB,MAAM;QACN,GAAG,QAAQ;IACb;IAEA,OAAO;QACL,SAAS,aAAa;YACpB,MAAM;YACN,GAAG,QAAQ;QACb;IACF;AACF;AAEA,SAAS,sBAAsB,WAAwB;IACrD,KAAK,MAAM,OAAO,mBAAmB,IAAI,GAAI;QAC3C,mBAAmB,aAAa,KAAK,KAAK,CAAC;IAC7C;AACF;AAEA,iEAAiE;AACjE,MAAM,+BACJ,IAAI;AAEN,SAAS,iBAAiB,GAAyB;IACjD,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,aAAa,6BAA6B,GAAG,CAAC;IAElD,IAAI,YAAY;QACd,WAAW,WAAW,GAAG,sBACvB,WAAW,WAAW,EACtB,IAAI,WAAW;IAEnB,OAAO;QACL,6BAA6B,GAAG,CAAC,KAAK;IACxC;AACF;AAEA,SAAS;IACP,IAAI,6BAA6B,IAAI,KAAK,GAAG;IAC7C,MAAM,aAAa;IACnB,KAAK,MAAM,OAAO,6BAA6B,MAAM,GAAI;QACvD,cAAc;IAChB;IACA,6BAA6B,KAAK;IAClC;AACF;AAEA,SAAS,sBACP,OAAwB,EACxB,OAAwB;IAExB,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,SAAS,qBAAqB,QAAQ,MAAM,EAAE,QAAQ,MAAM;QAC9D;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,wEAAwE;YACxE,0BAA0B;YAC1B,0EAA0E;YAC1E,sCAAsC;YACtC,IAAI,SAAS,QAAQ,MAAM,CAAC,EAAE;YAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,SAAS;gBAAC;aAAO;QACnB;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,qBACP,OAAuC,EACvC,OAAuC;IAEvC,MAAM,SAAyC,CAAC;IAEhD,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6C;QAClG,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,kBAAkB,cAAc;YACrD,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6C;QAClG,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,kBACP,OAAoB,EACpB,OAAoB;IAEpB,IACE,AAAC,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,aAC7C,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAChD;QACA,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,OAAO;AACT;AAEA,SAAS,sCACP,OAA+B,EAC/B,OAA+B;IAE/B,MAAM,UAAU,4BAA4B,QAAQ,OAAO,EAAE,QAAQ,OAAO;IAC5E,MAAM,SAAS,6BAA6B,QAAQ,MAAM,EAAE,QAAQ,MAAM;IAE1E,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,4BACP,QAA6D,EAC7D,QAA6D;IAE7D,OAAO;QAAE,GAAG,QAAQ;QAAE,GAAG,QAAQ;IAAC;AACpC;AAEA,SAAS,6BACP,OAAmE,EACnE,OAAmE;IAEnE,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,MAAM,SAAyD,CAAC;IAEhE,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6D;QAClH,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,4BACnB,cACA;YAEF,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6D;QAClH,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,4BACP,OAAoC,EACpC,OAAoC;IAEpC,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,8CAA8C;QAC9C,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAAS;QAC1D,MAAM,QAAQ,EAAE;QAChB,MAAM,UAAU,EAAE;QAClB,MAAM,iBAAiB,IAAI,IAAI,QAAQ,OAAO,IAAI,EAAE;QACpD,MAAM,eAAe,IAAI,IAAI,QAAQ,OAAO,IAAI,EAAE;QAElD,KAAK,MAAM,YAAY,aAAc;YACnC,IAAI,CAAC,eAAe,GAAG,CAAC,WAAW;gBACjC,MAAM,IAAI,CAAC;YACb;QACF;QAEA,KAAK,MAAM,YAAY,eAAgB;YACrC,IAAI,CAAC,aAAa,GAAG,CAAC,WAAW;gBAC/B,QAAQ,IAAI,CAAC;YACf;QACF;QAEA,IAAI,MAAM,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;YAC9C,OAAO;QACT;QAEA,OAAO;YACL,MAAM;YACN;YACA;QACF;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;QAC5D,MAAM,QAAQ,IAAI,IAAI;eAAK,QAAQ,KAAK,IAAI,EAAE;eAAO,QAAQ,KAAK,IAAI,EAAE;SAAE;QAC1E,MAAM,UAAU,IAAI,IAAI;eAClB,QAAQ,OAAO,IAAI,EAAE;eACrB,QAAQ,OAAO,IAAI,EAAE;SAC1B;QAED,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,IAAI,QAAQ,OAAO,IAAI,MAAM;YAC3B,KAAK,MAAM,YAAY,QAAQ,OAAO,CAAE;gBACtC,MAAM,MAAM,CAAC;YACf;QACF;QAEA,OAAO;YACL,MAAM;YACN,OAAO;mBAAI;aAAM;YACjB,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,MAAM,UAAU,IAAI,IAAI;eAClB,QAAQ,OAAO,IAAI,EAAE;eACrB,QAAQ,KAAK,IAAI,EAAE;SACxB;QAED,KAAK,MAAM,YAAY,QAAQ,OAAO,IAAI,EAAE,CAAE;YAC5C,QAAQ,MAAM,CAAC;QACjB;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;QAC5D,wEAAwE;QACxE,4CAA4C;QAE5C,MAAM,UAAU,IAAI,IAAI,QAAQ,OAAO,IAAI,EAAE;QAE7C,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,2CAA2C;IAE3C,OAAO;AACT;AAEA,SAAS,UAAU,CAAQ,EAAE,OAAe;IAC1C,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS;AACzC;AAEA,MAAM,WAAW;IAAC;IAAO;IAAS;CAAQ;AAE1C,SAAS,cAAc,IAAW,EAAE,CAAM,EAAE,CAAM;IAChD,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,OAAO,KAAK;AACd;AAEA,MAAM,mBAA8C,IAAI;AAExD,SAAS;IACP,MAAM,SAAS,EAAE;IACjB,MAAM,mBAAmB,IAAI;IAE7B,KAAK,MAAM,CAAC,GAAG,YAAY,IAAI,iBAAkB;QAC/C,KAAK,MAAM,cAAc,YAAa;YACpC,IAAI,iBAAiB,GAAG,CAAC,WAAW,SAAS,GAAG;YAEhD,OAAO,IAAI,CAAC;YACZ,iBAAiB,GAAG,CAAC,WAAW,SAAS;QAC3C;IACF;IAEA,WAAW;IAEX,MAAM,MAAM,CAAC;AACf;AAEA,SAAS,aAAa,GAAkB;IACtC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,oBAAoB;IAExB,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;QAC9B,IAAI,SAAS,QAAQ,CAAC,MAAM,QAAQ,GAAG;YACrC,oBAAoB;QACtB;IACF;IAEA,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG;QACzB,iBAAiB,GAAG,CAAC,KAAK,IAAI,MAAM;IACtC,OAAO,IAAI,iBAAiB,GAAG,CAAC,MAAM;QACpC,iBAAiB,MAAM,CAAC;IAC1B;IAEA;IAEA,OAAO;AACT;AAEA,MAAM,iBAAiB;IAAC;IAAO;IAAS;IAAS;IAAW;IAAQ;CAAM;AAC1E,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAAS,WAAW,MAAe;IACjC,OAAO,IAAI,CAAC,CAAC,GAAG;QACd,MAAM,QAAQ,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;QAClE,IAAI,UAAU,GAAG,OAAO;QACxB,OAAO,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;IAC7D;AACF;AAEA,MAAM,QAAQ;IACZ,eAAe,KAAO;IACtB,SAAS,KAAO;IAChB,SAAS,KAAO;IAChB,QAAQ,CAAC,WAAsB;AACjC;AAEO,SAAS,SAAS,QAAsB;IAC7C,OAAO,MAAM,CAAC,OAAO;AACvB;AAEA,SAAS,oBAAoB,GAAkB;IAC7C,WAAW,IAAI,MAAM;IAErB,aAAa;IAEb,OAAQ,IAAI,IAAI;QACd,KAAK;YAEH;QACF,KAAK;YACH,oBAAoB;YACpB,iBAAiB;YACjB;QACF;YACE,oBAAoB;YACpB,MAAM,WAAW,6BAA6B,IAAI,KAAK;YACvD,IAAI,UAAU,MAAM,aAAa;YACjC,cAAc;YACd,IAAI,UAAU;YACd;IACJ;AACF;AAEA,SAAS;IACP,MAAM,OAAO;IACb,MAAM,OAAO;IAEb,2EAA2E;IAC3E,+BAA+B;IAC/B,oFAAoF;IACpF,IAAI,WAAW,aAAa,EAAE;QAC5B,WAAW,aAAa;QACxB,WAAW,aAAa,GAAG;IAC7B;AACF;AAEA,SAAS,uBACP,aAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,OAAO,kBACL;QACE,MAAM;IACR,GACA,aACA;AAEJ;AAEO,SAAS,kBACd,QAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,MAAM,MAAM,YAAY;IACxB,IAAI;IACJ,MAAM,sBAAsB,mBAAmB,GAAG,CAAC;IACnD,IAAI,CAAC,qBAAqB;QACxB,cAAc;YACZ,WAAW,IAAI,IAAI;gBAAC;aAAS;YAC7B,aAAa,mBAAmB,aAAa;QAC/C;QACA,mBAAmB,GAAG,CAAC,KAAK;IAC9B,OAAO;QACL,oBAAoB,SAAS,CAAC,GAAG,CAAC;QAClC,cAAc;IAChB;IAEA,OAAO;QACL,YAAY,SAAS,CAAC,MAAM,CAAC;QAE7B,IAAI,YAAY,SAAS,CAAC,IAAI,KAAK,GAAG;YACpC,YAAY,WAAW;YACvB,mBAAmB,MAAM,CAAC;QAC5B;IACF;AACF;AAEA,SAAS,cAAc,GAAkB;IACvC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,MAAM,cAAc,mBAAmB,GAAG,CAAC;IAC3C,IAAI,CAAC,aAAa;QAChB;IACF;IAEA,KAAK,MAAM,YAAY,YAAY,SAAS,CAAE;QAC5C,SAAS;IACX;IAEA,IAAI,IAAI,IAAI,KAAK,YAAY;QAC3B,mFAAmF;QACnF,kFAAkF;QAClF,oFAAoF;QACpF,yBAAyB;QACzB,+EAA+E;QAC/E,mEAAmE;QACnE,mBAAmB,MAAM,CAAC;IAC5B;AACF", "ignoreList": [0]}}, {"offset": {"line": 493, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\nimport { DateTime } from \"luxon\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));\r\n\r\n/**\r\n * Normaliza una fecha ISO a un objeto Date de JavaScript establecido a medianoche en la zona horaria local\r\n * @param dateStr Fecha en formato ISO string\r\n * @returns Objeto Date normalizado\r\n */\r\nexport function normalizeDate(dateStr: string): Date {\r\n  return DateTime.fromISO(dateStr)\r\n    .setZone('local')\r\n    .startOf('day')\r\n    .toJSDate();\r\n}\r\n\r\n/**\r\n * Convierte un array de fechas ISO a objetos Date normalizados\r\n * @param dateStrings Array de fechas en formato ISO string\r\n * @returns Array de objetos Date normalizados\r\n */\r\nexport function normalizeDates(dateStrings: string[]): Date[] {\r\n  if (!dateStrings || !Array.isArray(dateStrings)) return [];\r\n  return dateStrings.map(normalizeDate);\r\n}\r\n\r\n/**\r\n * Convierte un ISO a un string con formato \"dd de MMM de yyyy\"\r\n * @param date Fecha en formato ISO string\r\n * @returns String formateado\r\n */\r\nexport function formatISODate(date: string): string {\r\n  return DateTime.fromISO(date)\r\n    .setZone('local')\r\n    .toFormat('d \\'de\\' MMM \\'de\\' yyyy', { locale: 'es' });\r\n}\r\n\r\n/**\r\n * Verifica si una fecha está en un array de fechas deshabilitadas\r\n * @param date Fecha a verificar\r\n * @param disabledDates Array de fechas deshabilitadas\r\n * @returns true si la fecha está deshabilitada, false en caso contrario\r\n */\r\nexport function isDateDisabled(date: Date, disabledDates: Date[]): boolean {\r\n  const luxonDate = DateTime.fromJSDate(date)\r\n    .setZone('local')\r\n    .startOf('day');\r\n\r\n  return disabledDates.some(disabledDate => {\r\n    const luxonDisabledDate = DateTime.fromJSDate(disabledDate);\r\n    return luxonDate.hasSame(luxonDisabledDate, 'day');\r\n  });\r\n}\r\n\r\n/**\r\n * Formatea una fecha a un string en formato: \"dd de MMM de yyyy\"\r\n * @param date Fecha a formatear\r\n * @returns String formateado\r\n */\r\nexport function formatDate(date: string) {\r\n  return DateTime.fromISO(date)\r\n    .setZone('local')\r\n    .toFormat('d \\'de\\' MMMM, yyyy', { locale: 'es' });\r\n}\r\n\r\n/** \r\n * Formatea un número a un string en formato de moneda local, por default MXN\r\n * @param value Número a formatear\r\n * @returns String formateado\r\n */\r\nexport function formatCurrency(value: number, currency: string = 'MXN') {\r\n  return new Intl.NumberFormat('es-MX', { style: 'currency', currency }).format(value);\r\n\r\n}"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AAAA;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,mKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,6IAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,QAAQ,CAAC,KAAe,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AAOzE,SAAS,cAAc,OAAe;IAC3C,OAAO,uLAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,SACrB,OAAO,CAAC,SACR,OAAO,CAAC,OACR,QAAQ;AACb;AAOO,SAAS,eAAe,WAAqB;IAClD,IAAI,CAAC,eAAe,CAAC,MAAM,OAAO,CAAC,cAAc,OAAO,EAAE;IAC1D,OAAO,YAAY,GAAG,CAAC;AACzB;AAOO,SAAS,cAAc,IAAY;IACxC,OAAO,uLAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,MACrB,OAAO,CAAC,SACR,QAAQ,CAAC,4BAA4B;QAAE,QAAQ;IAAK;AACzD;AAQO,SAAS,eAAe,IAAU,EAAE,aAAqB;IAC9D,MAAM,YAAY,uLAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,MACnC,OAAO,CAAC,SACR,OAAO,CAAC;IAEX,OAAO,cAAc,IAAI,CAAC,CAAA;QACxB,MAAM,oBAAoB,uLAAA,CAAA,WAAQ,CAAC,UAAU,CAAC;QAC9C,OAAO,UAAU,OAAO,CAAC,mBAAmB;IAC9C;AACF;AAOO,SAAS,WAAW,IAAY;IACrC,OAAO,uLAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,MACrB,OAAO,CAAC,SACR,QAAQ,CAAC,uBAAuB;QAAE,QAAQ;IAAK;AACpD;AAOO,SAAS,eAAe,KAAa,EAAE,WAAmB,KAAK;IACpE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QAAE,OAAO;QAAY;IAAS,GAAG,MAAM,CAAC;AAEhF"}}, {"offset": {"line": 550, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/constants/env.ts"], "sourcesContent": ["import { z } from 'zod';\r\n\r\nexport const envSchema = z.object({\r\n  NEXT_PUBLIC_API_URL: z.string().url(),\r\n  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),\r\n  IS_DEV: z.string().optional().transform((val) => val === 'true'),\r\n});\r\n\r\nexport type Env = z.infer<typeof envSchema>;\r\n\r\n// create the same object but with process.env\r\nconst env = envSchema.parse({\r\n  NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,\r\n  NODE_ENV: process.env.NODE_ENV,\r\n  IS_DEV: process.env.IS_DEV,\r\n});\r\n\r\nexport default env;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,YAAY,4IAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,qBAAqB,4IAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;IACnC,UAAU,4IAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAe;QAAc;KAAO,EAAE,OAAO,CAAC;IAChE,QAAQ,4IAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,SAAS,CAAC,CAAC,MAAQ,QAAQ;AAC3D;AAIA,8CAA8C;AAC9C,MAAM,MAAM,UAAU,KAAK,CAAC;IAC1B,mBAAmB;IACnB,QAAQ;IACR,QAAQ,QAAQ,GAAG,CAAC,MAAM;AAC5B;uCAEe"}}, {"offset": {"line": 578, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/constants/index.ts"], "sourcesContent": ["export const BEARER_COOKIE_NAME = \"bearer_token\";\r\nexport const PENDING_INVITATION_COOKIE = 'pending_invitation';\r\nexport const PENDING_INVITATION_EMAIL_COOKIE = 'pending_invitation_email';\r\nexport const PENDING_INVITATION_ORG_ID_COOKIE = 'pending_invitation_org_id';"], "names": [], "mappings": ";;;;;;AAAO,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAClC,MAAM,kCAAkC;AACxC,MAAM,mCAAmC"}}, {"offset": {"line": 594, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/auth-client.ts"], "sourcesContent": ["import { createAuthClient } from \"better-auth/react\";\r\nimport { adminClient, /* multiSessionClient, */ organizationClient } from \"better-auth/client/plugins\";\r\nimport { getCookie } from 'cookies-next/client';\r\n\r\nimport env from \"@/constants/env\";\r\nimport { BEARER_COOKIE_NAME } from './constants';\r\n\r\nexport const authClient = createAuthClient({\r\n  baseURL: env.NEXT_PUBLIC_API_URL,\r\n\r\n  plugins: [\r\n    adminClient(),\r\n    organizationClient(),\r\n    // multiSessionClient()\r\n  ],\r\n  credentials: 'include',\r\n  fetchOptions: {\r\n    onError: (ctx) => {\r\n      console.log('Error:', ctx.error);\r\n      console.log('Response:', ctx.response.url);\r\n    },\r\n    headers: {\r\n      'x-dashboard-call': 'true'\r\n    },\r\n    auth: {\r\n      type: 'Bearer',\r\n      token: () => {\r\n        const token = getCookie(BEARER_COOKIE_NAME);\r\n        if (token) {\r\n          return token; // No truncar el token\r\n        }\r\n      }\r\n    }\r\n  },\r\n});"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AAEA;AACA;;;;;;AAEO,MAAM,aAAa,CAAA,GAAA,2KAAA,CAAA,mBAAgB,AAAD,EAAE;IACzC,SAAS,+HAAA,CAAA,UAAG,CAAC,mBAAmB;IAEhC,SAAS;QACP,CAAA,GAAA,6LAAA,CAAA,cAAW,AAAD;QACV,CAAA,GAAA,6LAAA,CAAA,qBAAkB,AAAD;KAElB;IACD,aAAa;IACb,cAAc;QACZ,SAAS,CAAC;YACR,QAAQ,GAAG,CAAC,UAAU,IAAI,KAAK;YAC/B,QAAQ,GAAG,CAAC,aAAa,IAAI,QAAQ,CAAC,GAAG;QAC3C;QACA,SAAS;YACP,oBAAoB;QACtB;QACA,MAAM;YACJ,MAAM;YACN,OAAO;gBACL,MAAM,QAAQ,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,iIAAA,CAAA,qBAAkB;gBAC1C,IAAI,OAAO;oBACT,OAAO,OAAO,sBAAsB;gBACtC;YACF;QACF;IACF;AACF"}}, {"offset": {"line": 640, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n  VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: ButtonProps) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,wKAAA,CAAA,MAAG,AAAD,EACvB,8cACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OACS;IACZ,MAAM,OAAO,UAAU,wKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf"}}, {"offset": {"line": 697, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,kMAAC,oLAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,kMAAC,oLAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,kMAAC,oLAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,kMAAC,oLAAA,CAAA,SAA4B;kBAC3B,cAAA,kMAAC,oLAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,kMAAC,oLAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,kMAAC,oLAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,kMAAC,oLAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,kMAAC;gBAAK,WAAU;0BACd,cAAA,kMAAC,oLAAA,CAAA,gBAAmC;8BAClC,cAAA,kMAAC,gNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,kMAAC,oLAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,kMAAC,oLAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,kMAAC;gBAAK,WAAU;0BACd,cAAA,kMAAC,oLAAA,CAAA,gBAAmC;8BAClC,cAAA,kMAAC,kNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,kMAAC,oLAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,kMAAC,oLAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,kMAAC,oLAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,kMAAC,oLAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,kMAAC,kOAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,kMAAC,oLAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf"}}, {"offset": {"line": 959, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        success:\r\n          // \"border-transparent bg-success text-success-foreground [a&]:hover:bg-success/90\",\r\n          \"border-transparent bg-green-600 text-green-foreground [a&]:hover:bg-green-700 text-white\",\r\n        warning:\r\n          \"border-transparent bg-warning text-warning-foreground [a&]:hover:bg-warning/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,wKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,SACE,oFAAoF;YACpF;YACF,SACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,wKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf"}}, {"offset": {"line": 1016, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 1035, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/actions/cookies.ts"], "sourcesContent": ["'use server';\nimport { cookies } from \"next/headers\";\nimport { getCookie, setCookie, deleteCookie } from \"cookies-next/server\";\n\n\nexport const getCookieServerByName = async ({\n  name,\n}: {\n  name: string;\n}) => {\n  const cookie = getCookie(name, { cookies });\n  return cookie;\n};\n\nexport const setCookieServerByName = async ({\n  name,\n  value\n}: {\n  name: string;\n  value: string;\n}) => {\n  console.log('setCookieServerByName', name, value);\n  await setCookie(name, value, { cookies });\n};\n\nexport const deleteCookieServerByName = async ({\n  name\n}: {\n  name: string;\n}) => {\n  await deleteCookie(name, { cookies });\n};"], "names": [], "mappings": ";;;;;;;;;IAKa,wBAAA,WAAA,GAAA,CAAA,GAAA,qPAAA,CAAA,wBAAA,EAAA,8CAAA,8KAAA,CAAA,aAAA,EAAA,KAAA,GAAA,4LAAA,CAAA,mBAAA,EAAA"}}, {"offset": {"line": 1051, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/actions/cookies.ts"], "sourcesContent": ["'use server';\nimport { cookies } from \"next/headers\";\nimport { getCookie, setCookie, deleteCookie } from \"cookies-next/server\";\n\n\nexport const getCookieServerByName = async ({\n  name,\n}: {\n  name: string;\n}) => {\n  const cookie = getCookie(name, { cookies });\n  return cookie;\n};\n\nexport const setCookieServerByName = async ({\n  name,\n  value\n}: {\n  name: string;\n  value: string;\n}) => {\n  console.log('setCookieServerByName', name, value);\n  await setCookie(name, value, { cookies });\n};\n\nexport const deleteCookieServerByName = async ({\n  name\n}: {\n  name: string;\n}) => {\n  await deleteCookie(name, { cookies });\n};"], "names": [], "mappings": ";;;;;;;;;IAca,wBAAA,WAAA,GAAA,CAAA,GAAA,qPAAA,CAAA,wBAAA,EAAA,8CAAA,8KAAA,CAAA,aAAA,EAAA,KAAA,GAAA,4LAAA,CAAA,mBAAA,EAAA"}}, {"offset": {"line": 1067, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/actions/cookies.ts"], "sourcesContent": ["'use server';\nimport { cookies } from \"next/headers\";\nimport { getCookie, setCookie, deleteCookie } from \"cookies-next/server\";\n\n\nexport const getCookieServerByName = async ({\n  name,\n}: {\n  name: string;\n}) => {\n  const cookie = getCookie(name, { cookies });\n  return cookie;\n};\n\nexport const setCookieServerByName = async ({\n  name,\n  value\n}: {\n  name: string;\n  value: string;\n}) => {\n  console.log('setCookieServerByName', name, value);\n  await setCookie(name, value, { cookies });\n};\n\nexport const deleteCookieServerByName = async ({\n  name\n}: {\n  name: string;\n}) => {\n  await deleteCookie(name, { cookies });\n};"], "names": [], "mappings": ";;;;;;;;;IAyBa,2BAAA,WAAA,GAAA,CAAA,GAAA,qPAAA,CAAA,wBAAA,EAAA,8CAAA,8KAAA,CAAA,aAAA,EAAA,KAAA,GAAA,4LAAA,CAAA,mBAAA,EAAA"}}, {"offset": {"line": 1111, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';\r\nimport env from \"../constants/env\";\r\nimport { BEARER_COOKIE_NAME } from '@/constants';\r\nimport { getCookie } from 'cookies-next/client';\r\n// import { sendLogToLogflare } from '@/lib/log-requests';\r\n\r\nfunction checkIfIsClient() {\r\n    return typeof window !== 'undefined';\r\n}\r\n\r\ntype ApiResponse<T> =\r\n    {\r\n        success: true;\r\n        data: T; status:\r\n        number;\r\n        statusText: string;\r\n        headers: AxiosResponse['headers'];\r\n        config: InternalAxiosRequestConfig;\r\n        request: AxiosRequestConfig;\r\n        error: undefined\r\n    }\r\n    |\r\n    {\r\n        success: false;\r\n        data: undefined;\r\n        status: number;\r\n        statusText: string;\r\n        headers: AxiosResponse['headers'];\r\n        config: AxiosRequestConfig;\r\n        request: AxiosRequestConfig;\r\n        error: any\r\n    };\r\n\r\ntype ErrorHandler = (error: any) => void;\r\n\r\nconst baseURL = env.NEXT_PUBLIC_API_URL;\r\n\r\nclass ApiService {\r\n    private axiosInstance: AxiosInstance;\r\n    baseURL: string = '';\r\n\r\n    /** \r\n     * @param version - The version of the API to use. Defaults to 'v1'.\r\n     */\r\n    constructor(\r\n        {\r\n            version,\r\n            prefix\r\n        }:\r\n            {\r\n                version?: 'v1' | 'v2',\r\n                prefix?: 'api' | 'dash-utils'\r\n            } =\r\n            {\r\n                version: 'v1',\r\n                prefix: 'api'\r\n            }) {\r\n\r\n\r\n        // Version is only available for api prefix so if prefix is dash-utils, version is not used\r\n        this.baseURL = `${baseURL}/${prefix}${prefix === 'api' ? `/${version}` : ''}`;\r\n\r\n        this.axiosInstance = axios.create({\r\n            baseURL: this.baseURL,\r\n            withCredentials: true\r\n        });\r\n    }\r\n\r\n    private async setHeaders() {\r\n        const isClient = checkIfIsClient();\r\n        if (isClient) {\r\n            return {};\r\n\r\n        };\r\n        const headers = (await import('next/headers')).headers;\r\n        const rawHeaders = await headers();\r\n        const headersObj: Record<string, string> = {};\r\n        rawHeaders.forEach((value, key) => {\r\n            headersObj[key] = value;\r\n        });\r\n        return headersObj;\r\n    }\r\n\r\n    private async request<T>(\r\n        method: 'get' | 'post' | 'patch' | 'put' | 'delete',\r\n        path: string,\r\n        config?: AxiosRequestConfig,\r\n        onError?: ErrorHandler\r\n    ): Promise<ApiResponse<T>> {\r\n        try {\r\n            const isClient = checkIfIsClient();\r\n            const headers = await this.setHeaders();\r\n            let token = ''\r\n\r\n            if (isClient) {\r\n                token = getCookie(BEARER_COOKIE_NAME) as string;\r\n            } else {\r\n                const getCookieServerByName = (await import('@/actions/cookies')).getCookieServerByName;\r\n                token = await getCookieServerByName({ name: BEARER_COOKIE_NAME }) as string;\r\n                if (path.includes('/files/download')) {\r\n                    console.log('Token on request for /api/v1/files/download: ', token);\r\n                }\r\n            }\r\n\r\n            const requestHeaders = {\r\n                    cookie: isClient ? undefined : headers.cookie,\r\n                    'x-dashboard-call': 'true',\r\n                    Authorization: `Bearer ${token}`,\r\n                    ...config?.headers\r\n                }\r\n\r\n            if (path.includes('/files/download')) {\r\n                console.log('============================================================')\r\n                console.log('============================================================')\r\n\r\n                console.log('Headers: ', headers);\r\n                console.log('Request on /api/v1/files/download: ', requestHeaders);\r\n                console.log('============================================================')\r\n                console.log('============================================================')\r\n\r\n            }\r\n\r\n            const response = await this.axiosInstance({\r\n                method,\r\n                url: path,\r\n                ...config,\r\n                headers: requestHeaders,\r\n            });\r\n            // Simplificamos el objeto de respuesta para evitar problemas de serialización\r\n            return {\r\n                success: true,\r\n                data: response.data,\r\n                status: response.status,\r\n                statusText: response.statusText,\r\n                headers: response.headers,\r\n                config: response.config,\r\n                request: response.request,\r\n                error: undefined\r\n            }\r\n        } catch (error: any) {\r\n            console.error('Error in request:', error?.config?.url);\r\n\r\n            if (onError) onError(error);\r\n            await this.handleError(error);\r\n            return {\r\n                success: false,\r\n                data: undefined,\r\n                status: error.response?.status || 500,\r\n                statusText: error.response?.statusText || 'Unknown Error',\r\n                headers: error.response?.headers || {},\r\n                config: error.config || {},\r\n                request: error.request || {},\r\n                error: error?.response?.data || error.message,\r\n            };\r\n        }\r\n    }\r\n\r\n    public async get<T>(path: string, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('get', path, config, onError);\r\n    }\r\n\r\n    public async post<T>(path: string, data?: any, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('post', path, { ...config, data }, onError);\r\n    }\r\n\r\n    public async patch<T>(path: string, data?: any, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('patch', path, { ...config, data }, onError);\r\n    }\r\n\r\n    public async put<T>(path: string, data?: any, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('put', path, { ...config, data }, onError);\r\n    }\r\n\r\n    public async delete<T>(path: string, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {\r\n        return this.request('delete', path, config, onError);\r\n    }\r\n\r\n    private async handleError(error: any) {\r\n        console.error('API Error:', {\r\n            message: error.message,\r\n            status: error.response?.status,\r\n            data: error.response?.data\r\n        });\r\n    }\r\n}\r\n\r\nexport const apiService = new ApiService();\r\n\r\nexport const dashUtilsService = new ApiService({ prefix: 'dash-utils' });\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AACA,0DAA0D;AAE1D,SAAS;IACL,OAAO,gBAAkB;AAC7B;AA2BA,MAAM,UAAU,+HAAA,CAAA,UAAG,CAAC,mBAAmB;AAEvC,MAAM;IACM,cAA6B;IACrC,UAAkB,GAAG;IAErB;;KAEC,GACD,YACI,EACI,OAAO,EACP,MAAM,EAKL,GACD;QACI,SAAS;QACT,QAAQ;IACZ,CAAC,CAAE;QAGP,2FAA2F;QAC3F,IAAI,CAAC,OAAO,GAAG,GAAG,QAAQ,CAAC,EAAE,SAAS,WAAW,QAAQ,CAAC,CAAC,EAAE,SAAS,GAAG,IAAI;QAE7E,IAAI,CAAC,aAAa,GAAG,6IAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YAC9B,SAAS,IAAI,CAAC,OAAO;YACrB,iBAAiB;QACrB;IACJ;IAEA,MAAc,aAAa;QACvB,MAAM,WAAW;QACjB,uCAAc;;QAGd;;QACA,MAAM,UAAU,CAAC,4IAA4B,EAAE,OAAO;QACtD,MAAM,aAAa,MAAM;QACzB,MAAM,aAAqC,CAAC;QAC5C,WAAW,OAAO,CAAC,CAAC,OAAO;YACvB,UAAU,CAAC,IAAI,GAAG;QACtB;QACA,OAAO;IACX;IAEA,MAAc,QACV,MAAmD,EACnD,IAAY,EACZ,MAA2B,EAC3B,OAAsB,EACC;QACvB,IAAI;YACA,MAAM,WAAW;YACjB,MAAM,UAAU,MAAM,IAAI,CAAC,UAAU;YACrC,IAAI,QAAQ;YAEZ,uCAAc;;YAEd,OAAO;gBACH,MAAM,wBAAwB,CAAC,yHAAiC,EAAE,qBAAqB;gBACvF,QAAQ,MAAM,sBAAsB;oBAAE,MAAM,iIAAA,CAAA,qBAAkB;gBAAC;gBAC/D,IAAI,KAAK,QAAQ,CAAC,oBAAoB;oBAClC,QAAQ,GAAG,CAAC,iDAAiD;gBACjE;YACJ;YAEA,MAAM,iBAAiB;gBACf,QAAQ,6EAAuB,QAAQ,MAAM;gBAC7C,oBAAoB;gBACpB,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,GAAG,QAAQ,OAAO;YACtB;YAEJ,IAAI,KAAK,QAAQ,CAAC,oBAAoB;gBAClC,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC;gBAEZ,QAAQ,GAAG,CAAC,aAAa;gBACzB,QAAQ,GAAG,CAAC,uCAAuC;gBACnD,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC;YAEhB;YAEA,MAAM,WAAW,MAAM,IAAI,CAAC,aAAa,CAAC;gBACtC;gBACA,KAAK;gBACL,GAAG,MAAM;gBACT,SAAS;YACb;YACA,8EAA8E;YAC9E,OAAO;gBACH,SAAS;gBACT,MAAM,SAAS,IAAI;gBACnB,QAAQ,SAAS,MAAM;gBACvB,YAAY,SAAS,UAAU;gBAC/B,SAAS,SAAS,OAAO;gBACzB,QAAQ,SAAS,MAAM;gBACvB,SAAS,SAAS,OAAO;gBACzB,OAAO;YACX;QACJ,EAAE,OAAO,OAAY;YACjB,QAAQ,KAAK,CAAC,qBAAqB,OAAO,QAAQ;YAElD,IAAI,SAAS,QAAQ;YACrB,MAAM,IAAI,CAAC,WAAW,CAAC;YACvB,OAAO;gBACH,SAAS;gBACT,MAAM;gBACN,QAAQ,MAAM,QAAQ,EAAE,UAAU;gBAClC,YAAY,MAAM,QAAQ,EAAE,cAAc;gBAC1C,SAAS,MAAM,QAAQ,EAAE,WAAW,CAAC;gBACrC,QAAQ,MAAM,MAAM,IAAI,CAAC;gBACzB,SAAS,MAAM,OAAO,IAAI,CAAC;gBAC3B,OAAO,OAAO,UAAU,QAAQ,MAAM,OAAO;YACjD;QACJ;IACJ;IAEA,MAAa,IAAO,IAAY,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QAC5G,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,MAAM,QAAQ;IAC7C;IAEA,MAAa,KAAQ,IAAY,EAAE,IAAU,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QACzH,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,MAAM;YAAE,GAAG,MAAM;YAAE;QAAK,GAAG;IAC3D;IAEA,MAAa,MAAS,IAAY,EAAE,IAAU,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QAC1H,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,MAAM;YAAE,GAAG,MAAM;YAAE;QAAK,GAAG;IAC5D;IAEA,MAAa,IAAO,IAAY,EAAE,IAAU,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QACxH,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,MAAM;YAAE,GAAG,MAAM;YAAE;QAAK,GAAG;IAC1D;IAEA,MAAa,OAAU,IAAY,EAAE,MAA2B,EAAE,OAAsB,EAA2B;QAC/G,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,MAAM,QAAQ;IAChD;IAEA,MAAc,YAAY,KAAU,EAAE;QAClC,QAAQ,KAAK,CAAC,cAAc;YACxB,SAAS,MAAM,OAAO;YACtB,QAAQ,MAAM,QAAQ,EAAE;YACxB,MAAM,MAAM,QAAQ,EAAE;QAC1B;IACJ;AACJ;AAEO,MAAM,aAAa,IAAI;AAEvB,MAAM,mBAAmB,IAAI,WAAW;IAAE,QAAQ;AAAa"}}, {"offset": {"line": 1263, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/api/user-roles.api.ts"], "sourcesContent": ["import { apiService } from '@/services/api';\n\nexport interface UserRoleResponse {\n  success: boolean;\n  message: string;\n  userType: 'client' | 'host';\n  availableUserTypes: string[];\n  isHostVerified: boolean;\n  newRoleAdded?: string;\n}\n\nexport const userRolesApi = {\n  // Cambiar rol actual del usuario\n  switchRole: async (userType: 'client' | 'host'): Promise<UserRoleResponse> => {\n    const result = await apiService.patch<UserRoleResponse>('/user/switch-role', {\n      userType\n    });\n    \n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error || 'Error switching role');\n    }\n  },\n\n  // Solicitar rol adicional\n  requestAdditionalRole: async (userType: 'client' | 'host'): Promise<UserRoleResponse> => {\n    const result = await apiService.post<UserRoleResponse>('/user/request-additional-role', {\n      userType\n    });\n    \n    if (result.success) {\n      return result.data;\n    } else {\n      throw new Error(result.error || 'Error requesting additional role');\n    }\n  },\n};\n"], "names": [], "mappings": ";;;AAAA;;AAWO,MAAM,eAAe;IAC1B,iCAAiC;IACjC,YAAY,OAAO;QACjB,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,KAAK,CAAmB,qBAAqB;YAC3E;QACF;QAEA,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;QAClC;IACF;IAEA,0BAA0B;IAC1B,uBAAuB,OAAO;QAC5B,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAmB,iCAAiC;YACtF;QACF;QAEA,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;QAClC;IACF;AACF"}}, {"offset": {"line": 1298, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/layout/role-switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { useRouter } from \"next/navigation\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { ChevronDown, User, UserCheck } from \"lucide-react\"\nimport { useUser } from '../../../context/user-context'\nimport { userRolesApi } from '@/lib/api/user-roles.api'\nimport toast from 'react-hot-toast'\n\nexport function RoleSwitch() {\n  const { user, setUser } = useUser()\n  const router = useRouter()\n  const [isLoading, setIsLoading] = useState(false)\n\n  console.log('availableUserTypes', user.availableUserTypes)\n\n  // Solo mostrar si el usuario tiene más de un rol disponible\n  if (!user.availableUserTypes || user.availableUserTypes.length <= 1) {\n    return null\n  }\n\n  const handleRoleSwitch = async (newRole: 'client' | 'host') => {\n    if (newRole === user.userType) return\n\n    setIsLoading(true)\n    try {\n      const response = await userRolesApi.switchRole(newRole)\n\n      // Actualizar el contexto del usuario\n      toast.promise(\n        async () => {\n          await userRolesApi.switchRole(newRole)\n          setUser((prevUser) => ({\n            ...prevUser,\n            userType: newRole,\n          }))\n          router.push('/dashboard')\n        },\n        {\n          loading: 'Cambiando de rol...',\n          success: response.message,\n          error: 'Error al cambiar de rol',\n        }\n      )\n\n\n    } catch (error) {\n      console.error('Error switching role:', error)\n      toast.error('Error al cambiar de rol')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const getRoleLabel = (role: string) => {\n    return role === 'host' ? 'Anfitrión' : 'Cliente'\n  }\n\n  const getRoleIcon = (role: string) => {\n    return role === 'host' ? UserCheck : User\n  }\n\n  console.log('user.userType', user.userType)\n  const currentRoleLabel = getRoleLabel(user.userType || 'client')\n  const CurrentRoleIcon = getRoleIcon(user.userType || 'client')\n\n  return (\n    <div className=\"px-4 py-2 border-t border-white/10\">\n      <div className=\"text-xs text-gray-400 mb-2\">Modo actual</div>\n      <DropdownMenu>\n        <DropdownMenuTrigger asChild>\n          <Button\n            variant=\"ghost\"\n            className=\"w-full justify-between p-2 h-auto text-left hover:bg-white/5\"\n            disabled={isLoading}\n          >\n            <div className=\"flex items-center\">\n              <CurrentRoleIcon className=\"h-4 w-4 mr-2 text-gray-400\" />\n              <span className=\"text-sm text-white\">{currentRoleLabel}</span>\n              {user.userType === 'host' && user.isHostVerified && (\n                <Badge variant=\"secondary\" className=\"ml-2 text-xs\">\n                  Verificado\n                </Badge>\n              )}\n            </div>\n            <ChevronDown className=\"h-4 w-4 text-gray-400\" />\n          </Button>\n        </DropdownMenuTrigger>\n        <DropdownMenuContent side=\"top\" align=\"start\" className=\"w-48 mb-2\">\n          {user.availableUserTypes.map((role) => {\n            const RoleIcon = getRoleIcon(role)\n            const isCurrentRole = role === user.userType\n\n            return (\n              <DropdownMenuItem\n                key={role}\n                onClick={() => handleRoleSwitch(role as 'client' | 'host')}\n                disabled={isCurrentRole || isLoading}\n                className={isCurrentRole ? 'bg-accent' : ''}\n              >\n                <RoleIcon className=\"mr-2 h-4 w-4\" />\n                <span>{getRoleLabel(role)}</span>\n                {isCurrentRole && (\n                  <Badge variant=\"outline\" className=\"ml-auto text-xs\">\n                    Actual\n                  </Badge>\n                )}\n                {role === 'host' && user.isHostVerified && (\n                  <Badge variant=\"secondary\" className=\"ml-auto text-xs\">\n                    Verificado\n                  </Badge>\n                )}\n              </DropdownMenuItem>\n            )\n          })}\n        </DropdownMenuContent>\n      </DropdownMenu>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAMA;AACA;AAAA;AAAA;;;;;;AAEA;AACA;AAfA;;;;;;;;;;;AAiBO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG;IAC1B,MAAM,SAAS,CAAA,GAAA,iMAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,QAAQ,GAAG,CAAC,sBAAsB,KAAK,kBAAkB;IAEzD,4DAA4D;IAC5D,IAAI,CAAC,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC,MAAM,IAAI,GAAG;QACnE,OAAO;IACT;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,YAAY,KAAK,QAAQ,EAAE;QAE/B,aAAa;QACb,IAAI;YACF,MAAM,WAAW,MAAM,iJAAA,CAAA,eAAY,CAAC,UAAU,CAAC;YAE/C,qCAAqC;YACrC,+JAAA,CAAA,UAAK,CAAC,OAAO,CACX;gBACE,MAAM,iJAAA,CAAA,eAAY,CAAC,UAAU,CAAC;gBAC9B,QAAQ,CAAC,WAAa,CAAC;wBACrB,GAAG,QAAQ;wBACX,UAAU;oBACZ,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,GACA;gBACE,SAAS;gBACT,SAAS,SAAS,OAAO;gBACzB,OAAO;YACT;QAIJ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,+JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAO,SAAS,SAAS,cAAc;IACzC;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,SAAS,SAAS,wNAAA,CAAA,YAAS,GAAG,0MAAA,CAAA,OAAI;IAC3C;IAEA,QAAQ,GAAG,CAAC,iBAAiB,KAAK,QAAQ;IAC1C,MAAM,mBAAmB,aAAa,KAAK,QAAQ,IAAI;IACvD,MAAM,kBAAkB,YAAY,KAAK,QAAQ,IAAI;IAErD,qBACE,kMAAC;QAAI,WAAU;;0BACb,kMAAC;gBAAI,WAAU;0BAA6B;;;;;;0BAC5C,kMAAC,oJAAA,CAAA,eAAY;;kCACX,kMAAC,oJAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,kMAAC,0IAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,UAAU;;8CAEV,kMAAC;oCAAI,WAAU;;sDACb,kMAAC;4CAAgB,WAAU;;;;;;sDAC3B,kMAAC;4CAAK,WAAU;sDAAsB;;;;;;wCACrC,KAAK,QAAQ,KAAK,UAAU,KAAK,cAAc,kBAC9C,kMAAC,yIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAAe;;;;;;;;;;;;8CAKxD,kMAAC,4NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG3B,kMAAC,oJAAA,CAAA,sBAAmB;wBAAC,MAAK;wBAAM,OAAM;wBAAQ,WAAU;kCACrD,KAAK,kBAAkB,CAAC,GAAG,CAAC,CAAC;4BAC5B,MAAM,WAAW,YAAY;4BAC7B,MAAM,gBAAgB,SAAS,KAAK,QAAQ;4BAE5C,qBACE,kMAAC,oJAAA,CAAA,mBAAgB;gCAEf,SAAS,IAAM,iBAAiB;gCAChC,UAAU,iBAAiB;gCAC3B,WAAW,gBAAgB,cAAc;;kDAEzC,kMAAC;wCAAS,WAAU;;;;;;kDACpB,kMAAC;kDAAM,aAAa;;;;;;oCACnB,+BACC,kMAAC,yIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;kDAAkB;;;;;;oCAItD,SAAS,UAAU,KAAK,cAAc,kBACrC,kMAAC,yIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAAkB;;;;;;;+BAbpD;;;;;wBAmBX;;;;;;;;;;;;;;;;;;AAKV"}}, {"offset": {"line": 1518, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/layout/user-profile-sidebar.tsx"], "sourcesContent": ["\"use client\"\n\n// import { useState } from \"react\"\nimport Image from \"next/image\"\nimport Link from \"next/link\"\nimport { useRouter } from \"next/navigation\"\nimport { authClient } from \"@/auth-client\"\nimport { Button } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport {\n  User,\n  Settings,\n  // CreditCard,\n  Bell,\n  LogOut,\n  ChevronUp,\n  Star,\n} from \"lucide-react\"\nimport { useUser } from '../../../context/user-context'\nimport { deleteCookie } from 'cookies-next/client'\nimport { BEARER_COOKIE_NAME } from '@/constants'\nimport { RoleSwitch } from './role-switch'\n\nexport function UserProfileSidebar() {\n  // const { data: session } = authClient.useSession()\n  const { user } = useUser()\n  const router = useRouter()\n\n  const handleLogout = async () => {\n    await authClient.signOut();\n    deleteCookie(BEARER_COOKIE_NAME);\n    router.push(\"/\")\n  }\n\n  // Determinar la ruta del perfil según el tipo de usuario\n  const getProfilePath = () => {\n    if (user.role === \"admin\") {\n      return \"/dashboard/admin/profile\"\n    } else if (user.userType === \"host\") {\n      return \"/dashboard/host/profile\"\n    } else if (user.userType === \"client\") {\n      return \"/dashboard/client/profile\"\n    }\n    return \"/dashboard/profile\"\n  }\n\n  return (\n    <div className=\"border-t border-white/10\">\n      {/* Role Switch Component */}\n      <RoleSwitch />\n\n      {/* User Profile Dropdown */}\n      <div className=\"p-4\">\n        <DropdownMenu>\n        <DropdownMenuTrigger asChild>\n          <Button variant=\"ghost\" className=\"w-full justify-start p-2 h-auto text-left hover:bg-white/5\">\n            <div className=\"flex items-center w-full\">\n              <Image\n                src={user.image || \"/placeholder.svg\"}\n                alt={user.name || \"Usuario\"}\n                width={40}\n                height={40}\n                className=\"rounded-full mr-3\"\n              />\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-white truncate\">{user.name || \"Usuario\"}</p>\n                <p className=\"text-xs text-gray-400 truncate\">{user.email || \"\"}</p>\n              </div>\n              <ChevronUp className=\"h-4 w-4 text-gray-400\" />\n            </div>\n          </Button>\n        </DropdownMenuTrigger>\n        <DropdownMenuContent side=\"top\" align=\"start\" className=\"w-56 mb-2\">\n          {/* Opcional: mostrar plan premium solo si es relevante */}\n          <DropdownMenuItem>\n            <Star className=\"mr-2 h-4 w-4\" />\n            <span>Upgrade to Pro</span>\n          </DropdownMenuItem>\n          <DropdownMenuSeparator />\n          <DropdownMenuItem asChild>\n            <Link href={getProfilePath()}>\n              <User className=\"mr-2 h-4 w-4\" />\n              <span>Mi Perfil</span>\n            </Link>\n          </DropdownMenuItem>\n          <DropdownMenuItem asChild>\n            <Link href={`${getProfilePath()}/settings`}>\n              <Settings className=\"mr-2 h-4 w-4\" />\n              <span>Configuración</span>\n            </Link>\n          </DropdownMenuItem>\n          <DropdownMenuItem>\n            <Bell className=\"mr-2 h-4 w-4\" />\n            <span>Notificaciones</span>\n          </DropdownMenuItem>\n          <DropdownMenuSeparator />\n          <DropdownMenuItem onClick={handleLogout} className=\"text-red-600\">\n            <LogOut className=\"mr-2 h-4 w-4\" />\n            <span>Cerrar sesión</span>\n          </DropdownMenuItem>\n        </DropdownMenuContent>\n      </DropdownMenu>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA,mCAAmC;AACnC;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AAUA;AACA;AACA;AA3BA;;;;;;;;;;;;;AA6BO,SAAS;IACd,oDAAoD;IACpD,MAAM,EAAE,IAAI,EAAE,GAAG;IACjB,MAAM,SAAS,CAAA,GAAA,iMAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,MAAM,6HAAA,CAAA,aAAU,CAAC,OAAO;QACxB,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE,iIAAA,CAAA,qBAAkB;QAC/B,OAAO,IAAI,CAAC;IACd;IAEA,yDAAyD;IACzD,MAAM,iBAAiB;QACrB,IAAI,KAAK,IAAI,KAAK,SAAS;YACzB,OAAO;QACT,OAAO,IAAI,KAAK,QAAQ,KAAK,QAAQ;YACnC,OAAO;QACT,OAAO,IAAI,KAAK,QAAQ,KAAK,UAAU;YACrC,OAAO;QACT;QACA,OAAO;IACT;IAEA,qBACE,kMAAC;QAAI,WAAU;;0BAEb,kMAAC,sJAAA,CAAA,aAAU;;;;;0BAGX,kMAAC;gBAAI,WAAU;0BACb,cAAA,kMAAC,oJAAA,CAAA,eAAY;;sCACb,kMAAC,oJAAA,CAAA,sBAAmB;4BAAC,OAAO;sCAC1B,cAAA,kMAAC,0IAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,WAAU;0CAChC,cAAA,kMAAC;oCAAI,WAAU;;sDACb,kMAAC,iLAAA,CAAA,UAAK;4CACJ,KAAK,KAAK,KAAK,IAAI;4CACnB,KAAK,KAAK,IAAI,IAAI;4CAClB,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;sDAEZ,kMAAC;4CAAI,WAAU;;8DACb,kMAAC;oDAAE,WAAU;8DAA2C,KAAK,IAAI,IAAI;;;;;;8DACrE,kMAAC;oDAAE,WAAU;8DAAkC,KAAK,KAAK,IAAI;;;;;;;;;;;;sDAE/D,kMAAC,wNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAI3B,kMAAC,oJAAA,CAAA,sBAAmB;4BAAC,MAAK;4BAAM,OAAM;4BAAQ,WAAU;;8CAEtD,kMAAC,oJAAA,CAAA,mBAAgB;;sDACf,kMAAC,0MAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,kMAAC;sDAAK;;;;;;;;;;;;8CAER,kMAAC,oJAAA,CAAA,wBAAqB;;;;;8CACtB,kMAAC,oJAAA,CAAA,mBAAgB;oCAAC,OAAO;8CACvB,cAAA,kMAAC,2KAAA,CAAA,UAAI;wCAAC,MAAM;;0DACV,kMAAC,0MAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,kMAAC;0DAAK;;;;;;;;;;;;;;;;;8CAGV,kMAAC,oJAAA,CAAA,mBAAgB;oCAAC,OAAO;8CACvB,cAAA,kMAAC,2KAAA,CAAA,UAAI;wCAAC,MAAM,GAAG,iBAAiB,SAAS,CAAC;;0DACxC,kMAAC,kNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,kMAAC;0DAAK;;;;;;;;;;;;;;;;;8CAGV,kMAAC,oJAAA,CAAA,mBAAgB;;sDACf,kMAAC,0MAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,kMAAC;sDAAK;;;;;;;;;;;;8CAER,kMAAC,oJAAA,CAAA,wBAAqB;;;;;8CACtB,kMAAC,oJAAA,CAAA,mBAAgB;oCAAC,SAAS;oCAAc,WAAU;;sDACjD,kMAAC,kNAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,kMAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB"}}, {"offset": {"line": 1830, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/layout/admin-sidebar.tsx"], "sourcesContent": ["\"use client\"\r\nimport Link from \"next/link\"\r\nimport Image from \"next/image\"\r\nimport { usePathname } from \"next/navigation\"\r\nimport {\r\n  LayoutDashboard,\r\n  Users,\r\n  UserCircle,\r\n  Car,\r\n  CalendarClock,\r\n  CreditCard,\r\n  // BarChart,\r\n  // LifeBuoy,\r\n  Settings,\r\n  Shield,\r\n} from \"lucide-react\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { UserProfileSidebar } from \"./user-profile-sidebar\"\r\n\r\nconst menuItems = [\r\n  {\r\n    title: \"PRINCIPAL\",\r\n    items: [\r\n      {\r\n        name: \"Dashboard\",\r\n        href: \"/dashboard/admin\",\r\n        icon: LayoutDashboard,\r\n      },\r\n      {\r\n        name: \"Anfitriones\",\r\n        href: \"/dashboard/admin/hosts\",\r\n        icon: Users,\r\n      },\r\n      {\r\n        name: \"Clientes\",\r\n        href: \"/dashboard/admin/clients\",\r\n        icon: UserCircle,\r\n      },\r\n      {\r\n        name: \"Vehícu<PERSON>\",\r\n        href: \"/dashboard/admin/vehicles\",\r\n        icon: Car,\r\n      },\r\n      {\r\n        name: \"Reservas\",\r\n        href: \"/dashboard/admin/reservations\",\r\n        icon: CalendarClock,\r\n      },\r\n      {\r\n        name: \"Gestionar Estados\",\r\n        href: \"/dashboard/admin/states\",\r\n        icon: Shield,\r\n      },\r\n      {\r\n        name: \"Pagos\",\r\n        href: \"/dashboard/admin/payouts\",\r\n        icon: CreditCard,\r\n        badge: undefined,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"GESTIÓN\",\r\n    items: [\r\n      {\r\n        name: \"Verificaciones\",\r\n        href: \"/dashboard/admin/verifications\",\r\n        icon: Shield,\r\n      },\r\n      // {\r\n      //   name: \"Reportes\",\r\n      //   href: \"/dashboard/admin/reports\",\r\n      //   icon: BarChart,\r\n      // },\r\n      // {\r\n      //   name: \"Soporte\",\r\n      //   href: \"/dashboard/admin/support\",\r\n      //   icon: LifeBuoy,\r\n      // },\r\n    ],\r\n  },\r\n  {\r\n    title: \"CONFIGURACIÓN\",\r\n    items: [\r\n      {\r\n        name: \"Configuración\",\r\n        href: \"/dashboard/admin/settings\",\r\n        icon: Settings,\r\n      },\r\n    ],\r\n  },\r\n]\r\n\r\n\r\nexport function AdminSidebar() {\r\n  const pathname = usePathname()\r\n\r\n  return (\r\n    <div className=\"w-full bg-[#0f2a5c] dark:bg-gray-900 text-white flex flex-col h-screen\">\r\n      {/* Header */}\r\n      <div className=\"p-4 flex items-center border-b border-white/10\">\r\n        <div className=\"flex items-center\">\r\n          <div className=\"bg-white rounded-md p-1 mr-2\">\r\n            <Image src=\"/placeholder.svg?height=24&width=24\" alt=\"Autoop Logo\" width={24} height={24} />\r\n          </div>\r\n          <span className=\"text-xl font-bold\">Autoop</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Navigation */}\r\n      <div className=\"flex-1 overflow-y-auto py-4\">\r\n        {menuItems.map((section, i) => (\r\n          <div key={i} className=\"px-4 py-2\">\r\n            <div className=\"text-xs font-semibold text-gray-400 mb-2\">{section.title}</div>\r\n            <ul className=\"space-y-1\">\r\n              {section.items.map((item, j) => (\r\n                <li key={j}>\r\n                  <Link\r\n                    href={item.href}\r\n                    className={cn(\r\n                      \"flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors\",\r\n                      pathname === item.href\r\n                        ? \"bg-white/10 text-white\"\r\n                        : \"text-gray-300 hover:bg-white/5 hover:text-white\",\r\n                    )}\r\n                  >\r\n                    <item.icon className=\"h-5 w-5 mr-3\" />\r\n                    <span>{item.name}</span>\r\n                    {item.badge && (\r\n                      <span className=\"ml-auto bg-primary text-white text-xs font-semibold px-2 py-0.5 rounded-full\">\r\n                        {item.badge}\r\n                      </span>\r\n                    )}\r\n                  </Link>\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      <UserProfileSidebar />\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AAjBA;;;;;;;;AAmBA,MAAM,YAAY;IAChB;QACE,OAAO;QACP,OAAO;YACL;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,oOAAA,CAAA,kBAAe;YACvB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,4MAAA,CAAA,QAAK;YACb;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,0NAAA,CAAA,aAAU;YAClB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,wMAAA,CAAA,MAAG;YACX;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,gOAAA,CAAA,gBAAa;YACrB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,8MAAA,CAAA,SAAM;YACd;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,0NAAA,CAAA,aAAU;gBAChB,OAAO;YACT;SACD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,8MAAA,CAAA,SAAM;YACd;SAWD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,kNAAA,CAAA,WAAQ;YAChB;SACD;IACH;CACD;AAGM,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,iMAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,kMAAC;QAAI,WAAU;;0BAEb,kMAAC;gBAAI,WAAU;0BACb,cAAA,kMAAC;oBAAI,WAAU;;sCACb,kMAAC;4BAAI,WAAU;sCACb,cAAA,kMAAC,iLAAA,CAAA,UAAK;gCAAC,KAAI;gCAAsC,KAAI;gCAAc,OAAO;gCAAI,QAAQ;;;;;;;;;;;sCAExF,kMAAC;4BAAK,WAAU;sCAAoB;;;;;;;;;;;;;;;;;0BAKxC,kMAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,SAAS,kBACvB,kMAAC;wBAAY,WAAU;;0CACrB,kMAAC;gCAAI,WAAU;0CAA4C,QAAQ,KAAK;;;;;;0CACxE,kMAAC;gCAAG,WAAU;0CACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,kBACxB,kMAAC;kDACC,cAAA,kMAAC,2KAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,gFACA,aAAa,KAAK,IAAI,GAClB,2BACA;;8DAGN,kMAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;8DACrB,kMAAC;8DAAM,KAAK,IAAI;;;;;;gDACf,KAAK,KAAK,kBACT,kMAAC;oDAAK,WAAU;8DACb,KAAK,KAAK;;;;;;;;;;;;uCAdV;;;;;;;;;;;uBAJL;;;;;;;;;;0BA6Bd,kMAAC,kKAAA,CAAA,qBAAkB;;;;;;;;;;;AAGzB"}}, {"offset": {"line": 2053, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/layout/host-sidebar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport Link from \"next/link\"\r\nimport Image from \"next/image\"\r\nimport { usePathname } from \"next/navigation\"\r\nimport {\r\n  LayoutDashboard,\r\n  Car,\r\n  CalendarClock,\r\n  CreditCard,\r\n  BarChart,\r\n  Settings,\r\n  // MessageSquare,\r\n} from \"lucide-react\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { UserProfileSidebar } from \"./user-profile-sidebar\"\r\n\r\nconst menuItems = [\r\n  {\r\n    title: \"PRINCIPAL\",\r\n    items: [\r\n      {\r\n        name: \"Dashboard\",\r\n        href: \"/dashboard/host\",\r\n        icon: LayoutDashboard,\r\n      },\r\n      {\r\n        name: \"Mis Vehículos\",\r\n        href: \"/dashboard/host/vehicles\",\r\n        icon: Car,\r\n      },\r\n      {\r\n        name: \"Reser<PERSON>\",\r\n        href: \"/dashboard/host/reservations\",\r\n        icon: CalendarClock,\r\n        badge: undefined,\r\n      },\r\n      {\r\n        name: \"<PERSON>ana<PERSON><PERSON>\",\r\n        href: \"/dashboard/host/earnings\",\r\n        icon: CreditCard,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"GESTIÓN\",\r\n    items: [\r\n      {\r\n        name: \"Reportes\",\r\n        href: \"/dashboard/host/reports\",\r\n        icon: Bar<PERSON><PERSON>,\r\n      },\r\n      // {\r\n      //   name: \"<PERSON><PERSON><PERSON><PERSON>\",\r\n      //   href: \"/dashboard/host/messages\",\r\n      //   icon: MessageSquare,\r\n      // },\r\n    ],\r\n  },\r\n  {\r\n    title: \"CONFIGURACIÓN\",\r\n    items: [\r\n      {\r\n        name: \"Configuración\",\r\n        href: \"/dashboard/host/settings\",\r\n        icon: Settings,\r\n      },\r\n    ],\r\n  },\r\n]\r\n\r\nexport function HostSidebar() {\r\n  const pathname = usePathname()\r\n\r\n  return (\r\n    <div className=\"w-full bg-[#0f2a5c] dark:bg-gray-900 text-white flex flex-col h-screen border-none\">\r\n      {/* Header */}\r\n      <div className=\"p-4 flex items-center border-b border-white/10\">\r\n        <div className=\"flex items-center\">\r\n          <div className=\"bg-white rounded-md p-1 mr-2\">\r\n            <Image src=\"/placeholder.svg?height=24&width=24\" alt=\"Autoop Logo\" width={24} height={24} />\r\n          </div>\r\n          <span className=\"text-xl font-bold\">Autoop</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Navigation */}\r\n      <div className=\"flex-1 overflow-y-auto py-4\">\r\n        {menuItems.map((section, i) => (\r\n          <div key={i} className=\"px-4 py-2\">\r\n            <div className=\"text-xs font-semibold text-gray-400 mb-2\">{section.title}</div>\r\n            <ul className=\"space-y-1\">\r\n              {section.items.map((item, j) => (\r\n                <li key={j}>\r\n                  <Link\r\n                    href={item.href}\r\n                    className={cn(\r\n                      \"flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors\",\r\n                      pathname === item.href\r\n                        ? \"bg-white/10 text-white\"\r\n                        : \"text-gray-300 hover:bg-white/5 hover:text-white\",\r\n                    )}\r\n                  >\r\n                    <item.icon className=\"h-5 w-5 mr-3\" />\r\n                    <span>{item.name}</span>\r\n                    {item.badge && (\r\n                      <span className=\"ml-auto bg-primary text-white text-xs font-semibold px-2 py-0.5 rounded-full\">\r\n                        {item.badge}\r\n                      </span>\r\n                    )}\r\n                  </Link>\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      <UserProfileSidebar />\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AAfA;;;;;;;;AAiBA,MAAM,YAAY;IAChB;QACE,OAAO;QACP,OAAO;YACL;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,oOAAA,CAAA,kBAAe;YACvB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,wMAAA,CAAA,MAAG;YACX;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,gOAAA,CAAA,gBAAa;gBACnB,OAAO;YACT;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,0NAAA,CAAA,aAAU;YAClB;SACD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,qPAAA,CAAA,WAAQ;YAChB;SAMD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,kNAAA,CAAA,WAAQ;YAChB;SACD;IACH;CACD;AAEM,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,iMAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,kMAAC;QAAI,WAAU;;0BAEb,kMAAC;gBAAI,WAAU;0BACb,cAAA,kMAAC;oBAAI,WAAU;;sCACb,kMAAC;4BAAI,WAAU;sCACb,cAAA,kMAAC,iLAAA,CAAA,UAAK;gCAAC,KAAI;gCAAsC,KAAI;gCAAc,OAAO;gCAAI,QAAQ;;;;;;;;;;;sCAExF,kMAAC;4BAAK,WAAU;sCAAoB;;;;;;;;;;;;;;;;;0BAKxC,kMAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,SAAS,kBACvB,kMAAC;wBAAY,WAAU;;0CACrB,kMAAC;gCAAI,WAAU;0CAA4C,QAAQ,KAAK;;;;;;0CACxE,kMAAC;gCAAG,WAAU;0CACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,kBACxB,kMAAC;kDACC,cAAA,kMAAC,2KAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,gFACA,aAAa,KAAK,IAAI,GAClB,2BACA;;8DAGN,kMAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;8DACrB,kMAAC;8DAAM,KAAK,IAAI;;;;;;gDACf,KAAK,KAAK,kBACT,kMAAC;oDAAK,WAAU;8DACb,KAAK,KAAK;;;;;;;;;;;;uCAdV;;;;;;;;;;;uBAJL;;;;;;;;;;0BA6Bd,kMAAC,kKAAA,CAAA,qBAAkB;;;;;;;;;;;AAGzB"}}, {"offset": {"line": 2259, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/layout/client-sidebar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport Link from \"next/link\"\r\nimport Image from \"next/image\"\r\nimport { usePathname } from \"next/navigation\"\r\nimport {\r\n  LayoutDashboard,\r\n  Search,\r\n  CalendarClock,\r\n  CreditCard,\r\n  Heart,\r\n  // MessageSquare,\r\n  Settings,\r\n  History,\r\n} from \"lucide-react\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { UserProfileSidebar } from \"./user-profile-sidebar\"\r\n\r\nconst menuItems = [\r\n  {\r\n    title: \"PRINCIPAL\",\r\n    items: [\r\n      {\r\n        name: \"Dashboard\",\r\n        href: \"/dashboard/client\",\r\n        icon: LayoutDashboard,\r\n      },\r\n      {\r\n        name: \"Buscar Vehículos\",\r\n        href: \"/dashboard/client/search\",\r\n        icon: Search,\r\n      },\r\n      {\r\n        name: \"Mis Reservas\",\r\n        href: \"/dashboard/client/reservations\",\r\n        icon: CalendarClock,\r\n        badge: undefined,\r\n      },\r\n      {\r\n        name: \"Historial\",\r\n        href: \"/dashboard/client/history\",\r\n        icon: History,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"GESTIÓN\",\r\n    items: [\r\n      {\r\n        name: \"Pagos\",\r\n        href: \"/dashboard/client/payments\",\r\n        icon: CreditCard,\r\n      },\r\n      {\r\n        name: \"Favori<PERSON>\",\r\n        href: \"/dashboard/client/favorites\",\r\n        icon: Heart,\r\n      },\r\n      // {\r\n      //   name: \"<PERSON><PERSON><PERSON><PERSON>\",\r\n      //   href: \"/dashboard/client/messages\",\r\n      //   icon: MessageSquare,\r\n      // },\r\n    ],\r\n  },\r\n  {\r\n    title: \"CONFIGURACIÓN\",\r\n    items: [\r\n      {\r\n        name: \"Configuración\",\r\n        href: \"/dashboard/client/settings\",\r\n        icon: Settings,\r\n      },\r\n    ],\r\n  },\r\n]\r\n\r\n\r\nexport function ClientSidebar() {\r\n  const pathname = usePathname()\r\n\r\n  return (\r\n    <div className=\"w-full bg-[#0f2a5c] dark:bg-gray-900 text-white flex flex-col h-screen\">\r\n      {/* Header */}\r\n      <div className=\"p-4 flex items-center border-b border-white/10\">\r\n        <div className=\"flex items-center\">\r\n          <div className=\"bg-white rounded-md p-1 mr-2\">\r\n            <Image src=\"/placeholder.svg?height=24&width=24\" alt=\"Autoop Logo\" width={24} height={24} />\r\n          </div>\r\n          <span className=\"text-xl font-bold\">Autoop</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Navigation */}\r\n      <div className=\"flex-1 overflow-y-auto py-4\">\r\n        {menuItems.map((section, i) => (\r\n          <div key={i} className=\"px-4 py-2\">\r\n            <div className=\"text-xs font-semibold text-gray-400 mb-2\">{section.title}</div>\r\n            <ul className=\"space-y-1\">\r\n              {section.items.map((item, j) => (\r\n                <li key={j}>\r\n                  <Link\r\n                    href={item.href}\r\n                    className={cn(\r\n                      \"flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors\",\r\n                      pathname === item.href\r\n                        ? \"bg-white/10 text-white\"\r\n                        : \"text-gray-300 hover:bg-white/5 hover:text-white\",\r\n                    )}\r\n                  >\r\n                    <item.icon className=\"h-5 w-5 mr-3\" />\r\n                    <span>{item.name}</span>\r\n                    {item.badge && (\r\n                      <span className=\"ml-auto bg-primary text-white text-xs font-semibold px-2 py-0.5 rounded-full\">\r\n                        {item.badge}\r\n                      </span>\r\n                    )}\r\n                  </Link>\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* User Profile - Usando el componente reutilizable */}\r\n      <UserProfileSidebar />\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AAhBA;;;;;;;;AAkBA,MAAM,YAAY;IAChB;QACE,OAAO;QACP,OAAO;YACL;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,oOAAA,CAAA,kBAAe;YACvB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,8MAAA,CAAA,SAAM;YACd;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,gOAAA,CAAA,gBAAa;gBACnB,OAAO;YACT;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,gNAAA,CAAA,UAAO;YACf;SACD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,0NAAA,CAAA,aAAU;YAClB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,4MAAA,CAAA,QAAK;YACb;SAMD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,kNAAA,CAAA,WAAQ;YAChB;SACD;IACH;CACD;AAGM,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,iMAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,kMAAC;QAAI,WAAU;;0BAEb,kMAAC;gBAAI,WAAU;0BACb,cAAA,kMAAC;oBAAI,WAAU;;sCACb,kMAAC;4BAAI,WAAU;sCACb,cAAA,kMAAC,iLAAA,CAAA,UAAK;gCAAC,KAAI;gCAAsC,KAAI;gCAAc,OAAO;gCAAI,QAAQ;;;;;;;;;;;sCAExF,kMAAC;4BAAK,WAAU;sCAAoB;;;;;;;;;;;;;;;;;0BAKxC,kMAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,SAAS,kBACvB,kMAAC;wBAAY,WAAU;;0CACrB,kMAAC;gCAAI,WAAU;0CAA4C,QAAQ,KAAK;;;;;;0CACxE,kMAAC;gCAAG,WAAU;0CACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,kBACxB,kMAAC;kDACC,cAAA,kMAAC,2KAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,gFACA,aAAa,KAAK,IAAI,GAClB,2BACA;;8DAGN,kMAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;8DACrB,kMAAC;8DAAM,KAAK,IAAI;;;;;;gDACf,KAAK,KAAK,kBACT,kMAAC;oDAAK,WAAU;8DACb,KAAK,KAAK;;;;;;;;;;;;uCAdV;;;;;;;;;;;uBAJL;;;;;;;;;;0BA8Bd,kMAAC,kKAAA,CAAA,qBAAkB;;;;;;;;;;;AAGzB"}}, {"offset": {"line": 2471, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,kMAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf"}}, {"offset": {"line": 2497, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Sheet = SheetPrimitive.Root\r\n\r\nconst SheetTrigger = SheetPrimitive.Trigger\r\n\r\nconst SheetClose = SheetPrimitive.Close\r\n\r\nconst SheetPortal = SheetPrimitive.Portal\r\n\r\nconst SheetOverlay = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Overlay\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  />\r\n))\r\nSheetOverlay.displayName = SheetPrimitive.Overlay.displayName\r\n\r\nconst sheetVariants = cva(\r\n  \"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\r\n  {\r\n    variants: {\r\n      side: {\r\n        top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\r\n        bottom:\r\n          \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\r\n        left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\r\n        right:\r\n          \"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      side: \"right\",\r\n    },\r\n  }\r\n)\r\n\r\ninterface SheetContentProps\r\n  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,\r\n    VariantProps<typeof sheetVariants> {}\r\n\r\nconst SheetContent = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Content>,\r\n  SheetContentProps\r\n>(({ side = \"right\", className, children, ...props }, ref) => (\r\n  <SheetPortal>\r\n    <SheetOverlay />\r\n    <SheetPrimitive.Content\r\n      ref={ref}\r\n      className={cn(sheetVariants({ side }), className)}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SheetPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </SheetPrimitive.Close>\r\n    </SheetPrimitive.Content>\r\n  </SheetPortal>\r\n))\r\nSheetContent.displayName = SheetPrimitive.Content.displayName\r\n\r\nconst SheetHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-2 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nSheetHeader.displayName = \"SheetHeader\"\r\n\r\nconst SheetFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nSheetFooter.displayName = \"SheetFooter\"\r\n\r\nconst SheetTitle = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\"text-lg font-semibold text-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSheetTitle.displayName = SheetPrimitive.Title.displayName\r\n\r\nconst SheetDescription = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSheetDescription.displayName = SheetPrimitive.Description.displayName\r\n\r\nexport {\r\n  Sheet,\r\n  SheetPortal,\r\n  SheetOverlay,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AASA,MAAM,QAAQ,0KAAA,CAAA,OAAmB;AAEjC,MAAM,eAAe,0KAAA,CAAA,UAAsB;AAE3C,MAAM,aAAa,0KAAA,CAAA,QAAoB;AAEvC,MAAM,cAAc,0KAAA,CAAA,SAAqB;AAEzC,MAAM,6BAAe,CAAA,GAAA,kKAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kMAAC,0KAAA,CAAA,UAAsB;QACrB,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;AAGT,aAAa,WAAW,GAAG,0KAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,gBAAgB,CAAA,GAAA,wKAAA,CAAA,MAAG,AAAD,EACtB,oMACA;IACE,UAAU;QACR,MAAM;YACJ,KAAK;YACL,QACE;YACF,MAAM;YACN,OACE;QACJ;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAOF,MAAM,6BAAe,CAAA,GAAA,kKAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,OAAO,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpD,kMAAC;;0BACC,kMAAC;;;;;0BACD,kMAAC,0KAAA,CAAA,UAAsB;gBACrB,KAAK;gBACL,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,cAAc;oBAAE;gBAAK,IAAI;gBACtC,GAAG,KAAK;;oBAER;kCACD,kMAAC,0KAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,kMAAC,oMAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,kMAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,aAAa,WAAW,GAAG,0KAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,kMAAC;QACC,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,kMAAC;QACC,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,kKAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kMAAC,0KAAA,CAAA,QAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACtD,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG,0KAAA,CAAA,QAAoB,CAAC,WAAW;AAEzD,MAAM,iCAAmB,CAAA,GAAA,kKAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kMAAC,0KAAA,CAAA,cAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,0KAAA,CAAA,cAA0B,CAAC,WAAW"}}, {"offset": {"line": 2646, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/layout/top-bar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useState } from \"react\"\r\n// import { use<PERSON><PERSON><PERSON> } from \"next/navigation\"\r\nimport { Search, Bell, Menu, Sun, Moon } from \"lucide-react\"\r\nimport { useTheme } from \"next-themes\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\"\r\nimport { <PERSON><PERSON>, <PERSON>etContent, Sheet<PERSON>itle, SheetTrigger } from \"@/components/ui/sheet\"\r\nimport { AdminSidebar } from \"./admin-sidebar\"\r\nimport { HostSidebar } from \"./host-sidebar\"\r\nimport { ClientSidebar } from \"./client-sidebar\"\r\nimport { useUser } from '../../../context/user-context'\r\n\r\nexport function TopBar() {\r\n  const [sidebarOpen, setSidebarOpen] = useState(false)\r\n  const { setTheme } = useTheme()\r\n  const { user } = useUser()\r\n\r\n\r\n  const getSidebarComponent = () => {\r\n\r\n    // check first if it's admin, then check usertype\r\n    if (user.role === \"admin\") {\r\n      return <AdminSidebar />\r\n    } else if (user.userType === \"host\") {\r\n      return <HostSidebar />\r\n    } else if (user.userType === \"client\") {\r\n      return <ClientSidebar />\r\n    }\r\n  }\r\n\r\n  const getSearchPlaceholder = () => {\r\n\r\n    if (user.role === \"admin\") {\r\n      return \"Buscar usuarios, vehículos...\"\r\n    } else if (user.userType === \"host\") {\r\n      return \"Buscar en mis vehículos...\"\r\n    } else if (user.userType === \"client\") {\r\n      return \"Buscar vehículos...\"\r\n    }\r\n  }\r\n\r\n  return (\r\n    <header className=\"bg-background border-b border-border px-4 py-3 flex items-center justify-between\">\r\n      <div className=\"flex items-center gap-4\">\r\n        {/* Mobile Sidebar - solo visible en mobile */}\r\n        <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>\r\n          <SheetTitle className=\"sr-only\">Menú</SheetTitle>\r\n          <SheetTrigger asChild>\r\n            <Button variant=\"ghost\" size=\"icon\" className=\"md:hidden\">\r\n              <Menu className=\"h-6 w-6\" />\r\n              <span className=\"sr-only\">Toggle Menu</span>\r\n            </Button>\r\n          </SheetTrigger>\r\n          <SheetContent side=\"left\" className=\"p-0 w-72 border-none text-white\">\r\n            {getSidebarComponent()}\r\n          </SheetContent>\r\n        </Sheet>\r\n\r\n        {/* Search */}\r\n        <div className=\"relative hidden md:flex items-center\">\r\n          <Search className=\"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\" />\r\n          <Input\r\n            type=\"search\"\r\n            placeholder={getSearchPlaceholder()}\r\n            className=\"pl-8 w-[300px] bg-background\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex items-center gap-3\">\r\n        {/* Theme Toggle */}\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <Button variant=\"ghost\" size=\"icon\">\r\n              <Sun className=\"h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\r\n              <Moon className=\"absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\r\n              <span className=\"sr-only\">Toggle theme</span>\r\n            </Button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent align=\"end\">\r\n            <DropdownMenuItem onClick={() => setTheme(\"light\")}>Light</DropdownMenuItem>\r\n            <DropdownMenuItem onClick={() => setTheme(\"dark\")}>Dark</DropdownMenuItem>\r\n            <DropdownMenuItem onClick={() => setTheme(\"system\")}>System</DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n\r\n        {/* Notifications */}\r\n        <Button variant=\"ghost\" size=\"icon\" className=\"relative\">\r\n          <Bell className=\"h-5 w-5\" />\r\n          <span className=\"absolute top-1 right-1 h-2 w-2 rounded-full bg-primary\" />\r\n          <span className=\"sr-only\">Notifications</span>\r\n        </Button>\r\n\r\n      </div>\r\n    </header>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA,8CAA8C;AAC9C;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;AACA;;;;;;AAjBA;;;;;;;;;;;;;AAoBO,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD;IAC5B,MAAM,EAAE,IAAI,EAAE,GAAG;IAGjB,MAAM,sBAAsB;QAE1B,iDAAiD;QACjD,IAAI,KAAK,IAAI,KAAK,SAAS;YACzB,qBAAO,kMAAC,wJAAA,CAAA,eAAY;;;;;QACtB,OAAO,IAAI,KAAK,QAAQ,KAAK,QAAQ;YACnC,qBAAO,kMAAC,uJAAA,CAAA,cAAW;;;;;QACrB,OAAO,IAAI,KAAK,QAAQ,KAAK,UAAU;YACrC,qBAAO,kMAAC,yJAAA,CAAA,gBAAa;;;;;QACvB;IACF;IAEA,MAAM,uBAAuB;QAE3B,IAAI,KAAK,IAAI,KAAK,SAAS;YACzB,OAAO;QACT,OAAO,IAAI,KAAK,QAAQ,KAAK,QAAQ;YACnC,OAAO;QACT,OAAO,IAAI,KAAK,QAAQ,KAAK,UAAU;YACrC,OAAO;QACT;IACF;IAEA,qBACE,kMAAC;QAAO,WAAU;;0BAChB,kMAAC;gBAAI,WAAU;;kCAEb,kMAAC,yIAAA,CAAA,QAAK;wBAAC,MAAM;wBAAa,cAAc;;0CACtC,kMAAC,yIAAA,CAAA,aAAU;gCAAC,WAAU;0CAAU;;;;;;0CAChC,kMAAC,yIAAA,CAAA,eAAY;gCAAC,OAAO;0CACnB,cAAA,kMAAC,0IAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,WAAU;;sDAC5C,kMAAC,0MAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,kMAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAG9B,kMAAC,yIAAA,CAAA,eAAY;gCAAC,MAAK;gCAAO,WAAU;0CACjC;;;;;;;;;;;;kCAKL,kMAAC;wBAAI,WAAU;;0CACb,kMAAC,8MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,kMAAC,yIAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,aAAa;gCACb,WAAU;;;;;;;;;;;;;;;;;;0BAKhB,kMAAC;gBAAI,WAAU;;kCAEb,kMAAC,oJAAA,CAAA,eAAY;;0CACX,kMAAC,oJAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,kMAAC,0IAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;;sDAC3B,kMAAC,wMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,kMAAC,0MAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,kMAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAG9B,kMAAC,oJAAA,CAAA,sBAAmB;gCAAC,OAAM;;kDACzB,kMAAC,oJAAA,CAAA,mBAAgB;wCAAC,SAAS,IAAM,SAAS;kDAAU;;;;;;kDACpD,kMAAC,oJAAA,CAAA,mBAAgB;wCAAC,SAAS,IAAM,SAAS;kDAAS;;;;;;kDACnD,kMAAC,oJAAA,CAAA,mBAAgB;wCAAC,SAAS,IAAM,SAAS;kDAAW;;;;;;;;;;;;;;;;;;kCAKzD,kMAAC,0IAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAO,WAAU;;0CAC5C,kMAAC,0MAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,kMAAC;gCAAK,WAAU;;;;;;0CAChB,kMAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMpC"}}, {"offset": {"line": 2950, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/context/user-context.tsx"], "sourcesContent": ["'use client';\r\nimport { createContext, useContext, useEffect, useState } from 'react';\r\n\r\n\r\ninterface UserContextType {\r\n  user: User;\r\n  orgId: string;\r\n  setUser: React.Dispatch<React.SetStateAction<User>>;\r\n  session: any;\r\n  setSession: React.Dispatch<React.SetStateAction<any>>;\r\n}\r\n\r\nexport const UserContext = createContext<UserContextType | undefined>(undefined);\r\n\r\nexport function UserProvider({ children, session: sessionData }: { children: React.ReactNode; session: any }) {\r\n  const [session, setSession] = useState(sessionData.session);\r\n  const [user, setUser] = useState(sessionData?.user);\r\n  const [orgId, setOrgId] = useState(sessionData.session.activeOrganizationId);\r\n\r\n  useEffect(() => {\r\n    // set orgId from session\r\n    setOrgId(sessionData.session.activeOrganizationId);\r\n    // setSession(sessionData.session);\r\n    setSession((prevSession: any) => ({ ...prevSession, activeOrganizationId: sessionData.session.activeOrganizationId }));\r\n  }, [sessionData.session.activeOrganizationId]);\r\n\r\n  return (\r\n    <UserContext.Provider value={{ user, setUser, orgId, session, setSession }}>\r\n      {children}\r\n    </UserContext.Provider>\r\n  );\r\n}\r\n\r\n\r\nexport function useUser() {\r\n  const context = useContext(UserContext);\r\n  if (!context) {\r\n    throw new Error('useUser must be used within a UserProvider');\r\n  }\r\n  return context;\r\n}"], "names": [], "mappings": ";;;;;;AACA;AADA;;;AAYO,MAAM,4BAAc,CAAA,GAAA,kKAAA,CAAA,gBAAa,AAAD,EAA+B;AAE/D,SAAS,aAAa,EAAE,QAAQ,EAAE,SAAS,WAAW,EAA+C;IAC1G,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,OAAO;IAC1D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;IAC9C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,OAAO,CAAC,oBAAoB;IAE3E,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QACR,yBAAyB;QACzB,SAAS,YAAY,OAAO,CAAC,oBAAoB;QACjD,mCAAmC;QACnC,WAAW,CAAC,cAAqB,CAAC;gBAAE,GAAG,WAAW;gBAAE,sBAAsB,YAAY,OAAO,CAAC,oBAAoB;YAAC,CAAC;IACtH,GAAG;QAAC,YAAY,OAAO,CAAC,oBAAoB;KAAC;IAE7C,qBACE,kMAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAM;YAAS;YAAO;YAAS;QAAW;kBACtE;;;;;;AAGP;AAGO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,kKAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT"}}, {"offset": {"line": 3004, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/(dashboard)/dashboard/layout-2.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport type React from \"react\"\r\nimport { useEffect } from \"react\"\r\nimport { useRouter, usePathname } from \"next/navigation\"\r\nimport { AdminSidebar } from \"@/components/layout/admin-sidebar\"\r\nimport { HostSidebar } from \"@/components/layout/host-sidebar\"\r\nimport { ClientSidebar } from \"@/components/layout/client-sidebar\"\r\nimport { TopBar } from \"@/components/layout/top-bar\"\r\nimport { useUser } from '@/context/user-context'\r\n\r\n\r\nexport default function DashboardLayout2({\r\n  children,\r\n}: {\r\n  children: React.ReactNode\r\n}) {\r\n  const router = useRouter()\r\n  const pathname = usePathname()\r\n  const { user } = useUser()\r\n\r\n  // Verificar si la ruta es válida para el rol del usuario\r\n  useEffect(() => {\r\n    const userType = user.userType;\r\n\r\n    if (user.role === \"admin\") {\r\n      if (!pathname.includes('admin')) {\r\n        router.push('/dashboard/admin')\r\n      }\r\n    } else if (userType === \"host\") {\r\n      if (!pathname.includes('host')) {\r\n        router.push('/dashboard/host')\r\n      }\r\n    } else if (userType === \"client\") {\r\n      if (!pathname.includes('client')) {\r\n        router.push('/dashboard/client')\r\n      }\r\n    } else {\r\n      router.push('/dashboard')\r\n    }\r\n\r\n  }, [pathname, user.role, user.userType, router])\r\n\r\n  const getSidebarComponent = () => {\r\n\r\n    if (user.role === \"admin\") {\r\n      return <AdminSidebar />\r\n    } else if (user.userType === \"host\") {\r\n      return <HostSidebar />\r\n    } else if (user.userType === \"client\") {\r\n      return <ClientSidebar />\r\n    }\r\n\r\n  }\r\n\r\n\r\n  return (\r\n    <>\r\n      <div className=\"flex h-screen bg-background overflow-hidden\">\r\n        {/* Sidebar - solo visible en desktop */}\r\n        <aside className=\"hidden md:block \">\r\n          {getSidebarComponent()}\r\n        </aside>\r\n\r\n        <div className=\"flex flex-col flex-1 min-w-0\">\r\n          <TopBar />\r\n\r\n          <main className=\"flex-1 overflow-y-auto p-6 bg-background\">{children}</main>\r\n        </div>\r\n      </div>\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;AAYe,SAAS,iBAAiB,EACvC,QAAQ,EAGT;IACC,MAAM,SAAS,CAAA,GAAA,iMAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,iMAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD;IAEvB,yDAAyD;IACzD,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,KAAK,QAAQ;QAE9B,IAAI,KAAK,IAAI,KAAK,SAAS;YACzB,IAAI,CAAC,SAAS,QAAQ,CAAC,UAAU;gBAC/B,OAAO,IAAI,CAAC;YACd;QACF,OAAO,IAAI,aAAa,QAAQ;YAC9B,IAAI,CAAC,SAAS,QAAQ,CAAC,SAAS;gBAC9B,OAAO,IAAI,CAAC;YACd;QACF,OAAO,IAAI,aAAa,UAAU;YAChC,IAAI,CAAC,SAAS,QAAQ,CAAC,WAAW;gBAChC,OAAO,IAAI,CAAC;YACd;QACF,OAAO;YACL,OAAO,IAAI,CAAC;QACd;IAEF,GAAG;QAAC;QAAU,KAAK,IAAI;QAAE,KAAK,QAAQ;QAAE;KAAO;IAE/C,MAAM,sBAAsB;QAE1B,IAAI,KAAK,IAAI,KAAK,SAAS;YACzB,qBAAO,kMAAC,wJAAA,CAAA,eAAY;;;;;QACtB,OAAO,IAAI,KAAK,QAAQ,KAAK,QAAQ;YACnC,qBAAO,kMAAC,uJAAA,CAAA,cAAW;;;;;QACrB,OAAO,IAAI,KAAK,QAAQ,KAAK,UAAU;YACrC,qBAAO,kMAAC,yJAAA,CAAA,gBAAa;;;;;QACvB;IAEF;IAGA,qBACE;kBACE,cAAA,kMAAC;YAAI,WAAU;;8BAEb,kMAAC;oBAAM,WAAU;8BACd;;;;;;8BAGH,kMAAC;oBAAI,WAAU;;sCACb,kMAAC,kJAAA,CAAA,SAAM;;;;;sCAEP,kMAAC;4BAAK,WAAU;sCAA4C;;;;;;;;;;;;;;;;;;;AAKtE"}}, {"offset": {"line": 3122, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  showCloseButton = true,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\r\n  showCloseButton?: boolean\r\n}) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        {showCloseButton && (\r\n          <DialogPrimitive.Close\r\n            data-slot=\"dialog-close\"\r\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\r\n          >\r\n            <XIcon />\r\n            <span className=\"sr-only\">Close</span>\r\n          </DialogPrimitive.Close>\r\n        )}\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  )\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,kMAAC,0KAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,kMAAC,0KAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,kMAAC,0KAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,kMAAC,0KAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,kMAAC,0KAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,kMAAC;QAAa,aAAU;;0BACtB,kMAAC;;;;;0BACD,kMAAC,0KAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,kMAAC,0KAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,kMAAC,wMAAA,CAAA,QAAK;;;;;0CACN,kMAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,kMAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,kMAAC,0KAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,kMAAC,0KAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf"}}, {"offset": {"line": 3296, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/radio-group.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as RadioGroupPrimitive from \"@radix-ui/react-radio-group\"\r\nimport { Circle } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst RadioGroup = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Root\r\n      className={cn(\"grid gap-2\", className)}\r\n      {...props}\r\n      ref={ref}\r\n    />\r\n  )\r\n})\r\nRadioGroup.displayName = RadioGroupPrimitive.Root.displayName\r\n\r\nconst RadioGroupItem = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Item\r\n      ref={ref}\r\n      className={cn(\r\n        \"aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <RadioGroupPrimitive.Indicator className=\"flex items-center justify-center\">\r\n        <Circle className=\"h-2.5 w-2.5 fill-current text-current\" />\r\n      </RadioGroupPrimitive.Indicator>\r\n    </RadioGroupPrimitive.Item>\r\n  )\r\n})\r\nRadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName\r\n\r\nexport { RadioGroup, RadioGroupItem }\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,2BAAa,CAAA,GAAA,kKAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,kMAAC,kLAAA,CAAA,OAAwB;QACvB,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EAAE,cAAc;QAC3B,GAAG,KAAK;QACT,KAAK;;;;;;AAGX;AACA,WAAW,WAAW,GAAG,kLAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,+BAAiB,CAAA,GAAA,kKAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,kMAAC,kLAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,4OACA;QAED,GAAG,KAAK;kBAET,cAAA,kMAAC,kLAAA,CAAA,YAA6B;YAAC,WAAU;sBACvC,cAAA,kMAAC,8MAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI1B;AACA,eAAe,WAAW,GAAG,kLAAA,CAAA,OAAwB,CAAC,WAAW"}}, {"offset": {"line": 3356, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium  group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50 !select-text cursor-text\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,kMAAC,yKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,2HAAA,CAAA,KAAE,AAAD,EACV,qOACA;QAED,GAAG,KAAK;;;;;;AAGf"}}, {"offset": {"line": 3384, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/UserTypeModal.tsx"], "sourcesContent": ["'use client';\r\nimport { useState } from 'react';\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from \"@/components/ui/dialog\";\r\nimport { RadioGroup, RadioGroupItem } from \"@/components/ui/radio-group\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { toast } from \"sonner\";\r\nimport { apiService } from '@/services/api';\r\nimport { useUser } from '@/context/user-context';\r\n\r\n\r\nexport function UserTypeModal() {\r\n  const [userType, setUserType] = useState<'client' | 'host'>('client');\r\n  const { user } = useUser();\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  const handleSetUserType = async (userType: 'client' | 'host') => {\r\n    try {\r\n      const response = await apiService.post('/user/set-user-type', { userType, userId: user?.id });\r\n      window.location.reload();\r\n      return response.data;\r\n    } catch (error) {\r\n      toast.error(\"Error al guardar el tipo de usuario\");\r\n      console.error(error);\r\n    }\r\n  };\r\n\r\n\r\n  const handleSubmit = async () => {\r\n    setLoading(true);\r\n    try {\r\n      // await onSubmit(userType);\r\n      await handleSetUserType(userType);\r\n      // No cerramos el modal aquí, se cerrará cuando el estado del usuario se actualice\r\n    } catch (error) {\r\n      toast.error(\"Error al guardar el tipo de usuario\");\r\n      console.error(error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog open={!user?.userType} onOpenChange={() => { }}>\r\n      <DialogContent className=\"sm:max-w-[425px]\" onInteractOutside={(e) => e.preventDefault()}>\r\n        <DialogHeader>\r\n          <DialogTitle>¿Cómo deseas usar nuestra plataforma?</DialogTitle>\r\n          <DialogDescription>\r\n            Selecciona tu rol principal en la plataforma. Podrás cambiar esto más adelante.\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n        <div className=\"py-4\">\r\n          <RadioGroup value={userType} onValueChange={(value) => setUserType(value as 'client' | 'host')}>\r\n            <div className=\"flex items-center space-x-2 mb-3\">\r\n              <RadioGroupItem value=\"client\" id=\"client\" />\r\n              <Label htmlFor=\"client\">Cliente (Quiero rentar autos)</Label>\r\n            </div>\r\n            <div className=\"flex items-center space-x-2\">\r\n              <RadioGroupItem value=\"host\" id=\"host\" />\r\n              <Label htmlFor=\"host\">Anfitrión (Quiero ofrecer mis autos)</Label>\r\n            </div>\r\n          </RadioGroup>\r\n        </div>\r\n        <DialogFooter>\r\n          <Button onClick={handleSubmit} disabled={loading}>\r\n            {loading ? \"Guardando...\" : \"Continuar\"}\r\n          </Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;;AAWO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAqB;IAC5D,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAC,uBAAuB;gBAAE;gBAAU,QAAQ,MAAM;YAAG;YAC3F,OAAO,QAAQ,CAAC,MAAM;YACtB,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,gJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC;QAChB;IACF;IAGA,MAAM,eAAe;QACnB,WAAW;QACX,IAAI;YACF,4BAA4B;YAC5B,MAAM,kBAAkB;QACxB,kFAAkF;QACpF,EAAE,OAAO,OAAO;YACd,gJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,kMAAC,0IAAA,CAAA,SAAM;QAAC,MAAM,CAAC,MAAM;QAAU,cAAc,KAAQ;kBACnD,cAAA,kMAAC,0IAAA,CAAA,gBAAa;YAAC,WAAU;YAAmB,mBAAmB,CAAC,IAAM,EAAE,cAAc;;8BACpF,kMAAC,0IAAA,CAAA,eAAY;;sCACX,kMAAC,0IAAA,CAAA,cAAW;sCAAC;;;;;;sCACb,kMAAC,0IAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAIrB,kMAAC;oBAAI,WAAU;8BACb,cAAA,kMAAC,kJAAA,CAAA,aAAU;wBAAC,OAAO;wBAAU,eAAe,CAAC,QAAU,YAAY;;0CACjE,kMAAC;gCAAI,WAAU;;kDACb,kMAAC,kJAAA,CAAA,iBAAc;wCAAC,OAAM;wCAAS,IAAG;;;;;;kDAClC,kMAAC,yIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAS;;;;;;;;;;;;0CAE1B,kMAAC;gCAAI,WAAU;;kDACb,kMAAC,kJAAA,CAAA,iBAAc;wCAAC,OAAM;wCAAO,IAAG;;;;;;kDAChC,kMAAC,yIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAO;;;;;;;;;;;;;;;;;;;;;;;8BAI5B,kMAAC,0IAAA,CAAA,eAAY;8BACX,cAAA,kMAAC,0IAAA,CAAA,SAAM;wBAAC,SAAS;wBAAc,UAAU;kCACtC,UAAU,iBAAiB;;;;;;;;;;;;;;;;;;;;;;AAMxC"}}]}