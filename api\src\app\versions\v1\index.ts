import { Elysia, t } from "elysia";
import { prisma } from "@/lib/prisma";
import { publicVehiclesController, hostVehiclesController, adminVehiclesController } from "../../modules/vehicles/v1/vehicles.controller";
import { adminStatesController, publicStatesController } from '@/app/modules/states/v1/states.controller';
import { 
  publicReservationsController, 
  adminReservationsController, 
  userReservationsController,
hostReservationsController 
} from '@/app/modules/reservations/v1/reservations.controller';
import { adminUsersController } from '@/app/modules/users/v1/users.controller';
import { paymentsRoute } from '@/app/modules/payments/v1/payments.controller';
import { filesController } from '@/app/modules/files/v1/files.controller';
import { adminUserVerificationController, userVerificationController } from '@/app/modules/hosts/v1/user-verification.controller';
import { switchRoleController } from '@/app/controllers/user/switch-role.controller';
import { requestAdditionalRoleController } from '@/app/controllers/user/request-additional-role.controller';

const apiV1 = new Elysia({ prefix: '/v1' })
  .get('/', () => 'Hello Autoop from v1')

  // Registrar los controladores de vehículos
  .use(publicVehiclesController)
  .use(hostVehiclesController)
  .use(adminVehiclesController)

  // Registrar los controladores de estados
  .use(publicStatesController)
  .use(adminStatesController)

  // Registrar los controladores de reservas
  .use(publicReservationsController)
  .use(userReservationsController)
  .use(hostReservationsController)
  .use(adminReservationsController)

  // Registrar los controladores de usuarios
  .use(adminUsersController)
  .use(switchRoleController)
  .use(requestAdditionalRoleController)

  // Registrar los controladores de pagos
  .use(paymentsRoute)

  // Registrar los controladores de archivos
  .use(filesController)

  // Registrar los controladores de verificación de usuarios
  .use(userVerificationController)
  .use(adminUserVerificationController);

export default apiV1;


